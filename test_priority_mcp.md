# 🧪 Priority MCP Server Testing Guide
## Post-Implementation Validation

**Testing Phase**: Post-Deployment Validation  
**Servers to Test**: Memory, Time, SQLite, Everything  
**Expected Duration**: 10-15 minutes  

---

## 🎯 **TESTING CHECKLIST**

### **✅ Pre-Test Verification**
- [ ] <PERSON> started successfully
- [ ] No error messages during startup
- [ ] Startup time under 60 seconds
- [ ] All existing functionality works

---

## 🧠 **MEMORY SERVER TESTS**

### **Test 1: Basic Memory Storage**
**Command**: 
```
Remember that I prefer trading futures over FOREX, and my primary instruments are MES and MNQ.
```
**Expected Result**: Confirmation that preference is stored

### **Test 2: Memory Retrieval**
**Command**: 
```
What trading preferences do you remember about me?
```
**Expected Result**: Should recall futures preference and MES/MNQ instruments

### **Test 3: Session Persistence**
1. **Store information**: "Remember my account balance is $100,000"
2. **Restart <PERSON>**
3. **Test recall**: "What's my account balance?"
**Expected Result**: Should remember across sessions

---

## ⏰ **TIME SERVER TESTS**

### **Test 1: Multiple Time Zones**
**Command**: 
```
What time is it right now in New York, London, Tokyo, and Sydney?
```
**Expected Result**: Accurate current times in all four zones

### **Test 2: Market Hours**
**Command**: 
```
When do the FOREX markets open and close today in ET?
```
**Expected Result**: Accurate market session times

### **Test 3: Time Calculations**
**Command**: 
```
How many hours until the next Fed announcement if it's at 2 PM ET tomorrow?
```
**Expected Result**: Accurate time calculation

### **Test 4: Trading Session Timing**
**Command**: 
```
What futures trading session are we currently in (Asian, Pre-Market, Regular, or After-Hours)?
```
**Expected Result**: Correct session identification

---

## 🗄️ **SQLITE SERVER TESTS**

### **Test 1: Database Creation**
**Command**: 
```
Create a SQLite database called 'trading_performance' with a table for tracking my trades.
```
**Expected Result**: Database and table creation confirmation

### **Test 2: Data Insertion**
**Command**: 
```
Insert a sample trade into the trading_performance database: MES, BUY, 5000.00 entry, 5030.00 exit, +$150 profit.
```
**Expected Result**: Successful data insertion

### **Test 3: Data Querying**
**Command**: 
```
Query the trading_performance database to show all trades.
```
**Expected Result**: Display of inserted trade data

### **Test 4: Data Analysis**
**Command**: 
```
Calculate the total profit from all trades in the trading_performance database.
```
**Expected Result**: Accurate profit calculation

---

## 🔍 **EVERYTHING SERVER TESTS**

### **Test 1: File Search by Name**
**Command**: 
```
Search for all files containing "IEMSS" in the filename on my system.
```
**Expected Result**: List of IEMSS-related files

### **Test 2: Document Type Search**
**Command**: 
```
Find all PDF files in my IBKR directory.
```
**Expected Result**: List of PDF files in IBKR folder

### **Test 3: Code File Search**
**Command**: 
```
Search for all Python files (.py) that contain "strategy" in the filename.
```
**Expected Result**: List of strategy-related Python files

### **Test 4: Recent File Search**
**Command**: 
```
Find files modified in the last 24 hours in my IBKR/b-team directory.
```
**Expected Result**: Recently modified files list

---

## 🔧 **INTEGRATION TESTS**

### **Test 1: Memory + Time Integration**
**Command**: 
```
Remember that I usually trade during the New York session (9:30 AM - 4:00 PM ET). What time does my preferred trading session start in my current timezone?
```
**Expected Result**: Stored preference + accurate time calculation

### **Test 2: SQLite + Memory Integration**
**Command**: 
```
Create a database table for my preferred instruments (MES and MNQ) and store my typical position sizes.
```
**Expected Result**: Database creation using remembered preferences

### **Test 3: Everything + Memory Integration**
**Command**: 
```
Search for files related to my preferred trading instruments and remember the most important ones.
```
**Expected Result**: File search + memory storage of important files

---

## 📊 **PERFORMANCE TESTS**

### **Test 1: Response Time**
- **Measure**: Time from question to complete response
- **Target**: Under 5 seconds for simple queries
- **Test**: Ask "What time is it?" and measure response time

### **Test 2: Memory Efficiency**
- **Check**: Activity Monitor for Claude Desktop memory usage
- **Baseline**: Note memory usage before tests
- **After**: Compare memory usage after all tests

### **Test 3: Concurrent Operations**
**Command**: 
```
While searching for files containing "trading", also tell me the current time in London and create a SQLite table for storing economic events.
```
**Expected Result**: All operations complete successfully

---

## 🚨 **ERROR HANDLING TESTS**

### **Test 1: Invalid SQLite Query**
**Command**: 
```
Execute this invalid SQL: SELECT * FROM nonexistent_table;
```
**Expected Result**: Graceful error message, no crash

### **Test 2: File Search with Invalid Path**
**Command**: 
```
Search for files in /nonexistent/directory/
```
**Expected Result**: Appropriate error message

### **Test 3: Memory Overflow**
**Command**: 
```
Remember this very long text: [paste 1000+ words]
```
**Expected Result**: Either successful storage or graceful handling

---

## ✅ **SUCCESS CRITERIA**

### **Functional Success**
- [ ] All 4 servers respond correctly
- [ ] Memory persists across sessions
- [ ] Time calculations are accurate
- [ ] SQLite operations work properly
- [ ] File search returns relevant results

### **Performance Success**
- [ ] Response times under 5 seconds
- [ ] No significant memory leaks
- [ ] Startup time under 60 seconds
- [ ] No crashes or freezes

### **Integration Success**
- [ ] Servers work together seamlessly
- [ ] Existing functionality unchanged
- [ ] Enhanced capabilities available
- [ ] Workflow improvements evident

---

## 🔄 **TROUBLESHOOTING**

### **If Memory Server Fails**
1. Check if data directory exists: `~/.local/share/mcp-memory/`
2. Verify write permissions
3. Restart Claude Desktop
4. Test with simple "Remember my name is [name]"

### **If Time Server Fails**
1. Check internet connection (may need NTP)
2. Verify timezone data is available
3. Test with simple "What time is it?"

### **If SQLite Server Fails**
1. Check if SQLite is installed: `sqlite3 --version`
2. Verify write permissions in working directory
3. Test with simple database creation

### **If Everything Server Fails**
1. macOS only - verify Spotlight is enabled
2. Check file system permissions
3. Test with simple filename search

---

## 📋 **TEST RESULTS TEMPLATE**

```
🧪 PRIORITY MCP SERVER TEST RESULTS
Date: [DATE]
Time: [TIME]
Claude Desktop Version: [VERSION]

MEMORY SERVER:
✅/❌ Basic storage: 
✅/❌ Retrieval: 
✅/❌ Session persistence: 
Notes: 

TIME SERVER:
✅/❌ Multiple timezones: 
✅/❌ Market hours: 
✅/❌ Time calculations: 
Notes: 

SQLITE SERVER:
✅/❌ Database creation: 
✅/❌ Data insertion: 
✅/❌ Data querying: 
Notes: 

EVERYTHING SERVER:
✅/❌ File search: 
✅/❌ Document search: 
✅/❌ Code search: 
Notes: 

PERFORMANCE:
Startup time: [X] seconds
Response time: [X] seconds average
Memory usage: [X] MB
Issues encountered: 

OVERALL RATING: ⭐⭐⭐⭐⭐ (1-5 stars)
RECOMMENDATION: PROCEED/INVESTIGATE/ROLLBACK
```

---

**Testing Status**: 🧪 **READY FOR EXECUTION**  
**Estimated Time**: 15 minutes  
**Risk Level**: Low (non-destructive tests)  
**Next Step**: Execute tests after successful deployment  

**🎯 PRIORITY MCP TESTING: COMPREHENSIVE • SYSTEMATIC • THOROUGH**
