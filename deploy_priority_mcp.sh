#!/bin/bash

# 🚀 Priority MCP Server Deployment Script
# Deploys Memory, Time, SQLite, and Everything servers to <PERSON>

set -e  # Exit on any error

echo "🚀 Starting Priority MCP Server Deployment..."
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if <PERSON> is running
check_claude_running() {
    if pgrep -f "<PERSON>" > /dev/null; then
        print_warning "<PERSON> is currently running"
        echo "Please quit Claude Des<PERSON> (Cmd+Q) before continuing."
        echo "Press Enter when <PERSON>ktop is closed..."
        read
    else
        print_success "Claude Desktop is not running - good to proceed"
    fi
}

# Create backup of current configuration
backup_config() {
    print_status "Creating backup of current Claude Desktop configuration..."
    
    BACKUP_FILE="$HOME/.config/claude_desktop/config.json.backup.$(date +%Y%m%d_%H%M%S)"
    
    if [ -f "$HOME/.config/claude_desktop/config.json" ]; then
        cp "$HOME/.config/claude_desktop/config.json" "$BACKUP_FILE"
        print_success "Backup created: $BACKUP_FILE"
    else
        print_warning "No existing config.json found - this might be a fresh installation"
    fi
}

# Install MCP server packages
install_packages() {
    print_status "Installing priority MCP server packages..."
    
    # Check if npm is available
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install Node.js and npm first."
        exit 1
    fi
    
    print_status "Installing @modelcontextprotocol/server-memory..."
    npm install -g @modelcontextprotocol/server-memory
    
    print_status "Installing @modelcontextprotocol/server-time..."
    npm install -g @modelcontextprotocol/server-time
    
    print_status "Installing @modelcontextprotocol/server-sqlite..."
    npm install -g @modelcontextprotocol/server-sqlite
    
    print_status "Installing @modelcontextprotocol/server-everything..."
    npm install -g @modelcontextprotocol/server-everything
    
    print_success "All MCP server packages installed successfully"
}

# Deploy new configuration
deploy_config() {
    print_status "Deploying new Claude Desktop configuration..."
    
    # Ensure config directory exists
    mkdir -p "$HOME/.config/claude_desktop"
    
    # Copy the priority configuration
    cp "/Users/<USER>/IBKR/b-team/claude_desktop_config_priority.json" "$HOME/.config/claude_desktop/config.json"
    
    print_success "New configuration deployed successfully"
}

# Verify configuration
verify_config() {
    print_status "Verifying configuration..."
    
    if [ -f "$HOME/.config/claude_desktop/config.json" ]; then
        # Check if jq is available for JSON validation
        if command -v jq &> /dev/null; then
            if jq empty "$HOME/.config/claude_desktop/config.json" 2>/dev/null; then
                print_success "Configuration file is valid JSON"
                
                # Count servers
                SERVER_COUNT=$(jq '.mcpServers | keys | length' "$HOME/.config/claude_desktop/config.json")
                print_success "Configuration contains $SERVER_COUNT MCP servers"
                
                # List new servers
                echo "New servers added:"
                jq -r '.mcpServers | keys[]' "$HOME/.config/claude_desktop/config.json" | grep -E "(memory|time|sqlite|everything)" | sed 's/^/  - /'
            else
                print_error "Configuration file contains invalid JSON"
                exit 1
            fi
        else
            print_warning "jq not available - skipping JSON validation"
        fi
    else
        print_error "Configuration file not found after deployment"
        exit 1
    fi
}

# Main deployment function
main() {
    echo "This script will:"
    echo "1. Backup your current Claude Desktop configuration"
    echo "2. Install 4 priority MCP servers (memory, time, sqlite, everything)"
    echo "3. Deploy the new configuration"
    echo "4. Verify the installation"
    echo ""
    echo "After completion, you'll need to restart Claude Desktop manually."
    echo ""
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Deployment cancelled by user"
        exit 0
    fi
    
    echo ""
    print_status "Starting deployment process..."
    
    # Step 1: Check if Claude Desktop is running
    check_claude_running
    
    # Step 2: Backup current configuration
    backup_config
    
    # Step 3: Install MCP packages
    install_packages
    
    # Step 4: Deploy new configuration
    deploy_config
    
    # Step 5: Verify configuration
    verify_config
    
    echo ""
    echo "================================================"
    print_success "Priority MCP Server deployment completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Start Claude Desktop"
    echo "2. Wait for initialization (may take 30-60 seconds)"
    echo "3. Test the new servers:"
    echo "   - Memory: Ask Claude to remember something"
    echo "   - Time: Ask for current time in different zones"
    echo "   - SQLite: Ask to create a simple database"
    echo "   - Everything: Ask to search for files on your system"
    echo ""
    echo "If you encounter issues, restore backup with:"
    echo "cp $BACKUP_FILE ~/.config/claude_desktop/config.json"
    echo ""
    print_success "Deployment complete! 🎉"
}

# Run main function
main "$@"
