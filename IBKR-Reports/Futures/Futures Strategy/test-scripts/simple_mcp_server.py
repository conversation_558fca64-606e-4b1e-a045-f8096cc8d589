#!/usr/bin/env python3
"""
Simple IBKR MCP Server for Claude Desktop
This is a minimal working version to test the MCP connection
"""
import asyncio
import logging
import sys
from contextlib import asynccontextmanager
from mcp.server.fastmcp import FastMCP

# Set up logging to stderr only (MCP protocol requirement)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    stream=sys.stderr  # Critical: log to stderr, not stdout
)
logger = logging.getLogger(__name__)

# Server lifespan management
@asynccontextmanager
async def lifespan(server):
    """Manage the server lifespan"""
    print("🚀 Simple IBKR MCP Server starting up", file=sys.stderr)
    yield
    print("🛑 Simple IBKR MCP Server shutting down", file=sys.stderr)

# Initialize the FastMCP server with lifespan
mcp = FastMCP("Simple IBKR Trading Server", lifespan=lifespan)

@mcp.tool()
def test_connection() -> str:
    """Test the MCP connection"""
    return "✅ MCP connection is working!"

@mcp.tool()
def get_server_status() -> dict:
    """Get the server status"""
    return {
        "status": "running",
        "server": "Simple IBKR MCP Server",
        "version": "1.0.0",
        "message": "Server is operational"
    }

@mcp.tool()
def check_dependencies() -> dict:
    """Check if required dependencies are available"""
    dependencies = {}
    
    try:
        import matplotlib
        dependencies["matplotlib"] = f"✅ {matplotlib.__version__}"
    except ImportError:
        dependencies["matplotlib"] = "❌ Not installed"
    
    try:
        import ib_async
        dependencies["ib_async"] = f"✅ {ib_async.__version__}"
    except ImportError:
        dependencies["ib_async"] = "❌ Not installed"
    
    try:
        import ibapi
        dependencies["ibapi"] = "✅ Available"
    except ImportError:
        dependencies["ibapi"] = "❌ Not installed"
    
    return dependencies

def main():
    """Main entry point"""
    # Send debug info to stderr only
    print("Starting Simple IBKR MCP Server...", file=sys.stderr)
    try:
        mcp.run()
    except KeyboardInterrupt:
        print("Server shutdown by user", file=sys.stderr)
    except Exception as e:
        print(f"Fatal error: {str(e)}", file=sys.stderr)
        raise

if __name__ == "__main__":
    main()
