#!/usr/bin/env python3
"""
Validate that all fixes have been properly applied to the IBKR MCP server codebase.
"""

import os
import re

# Color codes
GREEN = '\033[92m'
RED = '\033[91m'
YELLOW = '\033[93m'
RESET = '\033[0m'

def check_file_content(filepath, patterns, description):
    """Check if file contains expected patterns"""
    try:
        with open(filepath, 'r') as f:
            content = f.read()
        
        all_found = True
        for pattern in patterns:
            if isinstance(pattern, str):
                found = pattern in content
            else:
                # Regex pattern
                found = bool(re.search(pattern, content))
            
            if not found:
                all_found = False
                break
        
        if all_found:
            print(f"{GREEN}✅ {description}{RESET}")
            return True
        else:
            print(f"{RED}❌ {description}{RESET}")
            return False
    except FileNotFoundError:
        print(f"{RED}❌ {description} - File not found: {filepath}{RESET}")
        return False
    except Exception as e:
        print(f"{RED}❌ {description} - Error: {e}{RESET}")
        return False

def validate_fixes():
    """Validate all fixes are properly applied"""
    print("="*60)
    print("IBKR MCP Server Fix Validation")
    print("="*60)
    
    base_path = "/Users/<USER>/projects/b-team/ibkr_mcp_server"
    
    # Define checks
    checks = [
        # Phase 1: Order Management Fixes
        {
            "file": f"{base_path}/app/services/ibkr_service.py",
            "patterns": [
                "WORKAROUND: Not setting account field due to ib_async conversion bug",
                "place_bracket_order",
                "# DO NOT SET ACCOUNT"
            ],
            "description": "Phase 1: Bracket order workaround implemented"
        },
        
        # Phase 2: Contract Search Fixes
        {
            "file": f"{base_path}/app/services/ibkr_service.py",
            "patterns": [
                "contract.exchange = \"IDEALPRO\"",
                "search_forex_contracts",
                "search_futures_contracts"
            ],
            "description": "Phase 2: Contract search methods fixed"
        },
        
        # Phase 3: Real-time Bars Fixes
        {
            "file": f"{base_path}/app/implementations/realtimebars/realtimebars_impl.py",
            "patterns": [
                "self.ibkr_service.ib.reqRealTimeBars",
                "bar_list.updateEvent += on_bar_update",
                "self.ibkr_service.ib.cancelRealTimeBars"
            ],
            "description": "Phase 3: Real-time bars using correct ib_async methods"
        },
        
        # Phase 4: Scanner Fixes
        {
            "file": f"{base_path}/app/implementations/scanner/scanner_impl.py",
            "patterns": [
                "self.ibkr_service.ib.reqScannerDataAsync",
                "scanner_subscription.updateEvent += on_scanner_update",
                "self.ibkr_service.ib.cancelScannerSubscription"
            ],
            "description": "Phase 4: Scanner using correct ib_async methods"
        },
        
        # Phase 5: Risk Management Fixes
        {
            "file": f"{base_path}/app/implementations/riskmanagement/riskmanagement_impl.py",
            "patterns": [
                "portfolio_data = await self.ibkr_service.get_portfolio()",
                "self.ibkr_service.ib.reqHistoricalDataAsync",
                "market_data = await self.ibkr_service.get_market_data"
            ],
            "description": "Phase 5: Risk management using correct service methods"
        },
        
        # General IBKR Service Fixes
        {
            "file": f"{base_path}/app/services/ibkr_service.py",
            "patterns": [
                "from ib_async import IB",
                "ensure_connected",
                "print(\"DEBUG: ensure_connected method called - FIXES ARE LOADED!\")"
            ],
            "description": "IBKR service has proper imports and debug markers"
        }
    ]
    
    # Run checks
    results = []
    for check in checks:
        result = check_file_content(
            check["file"],
            check["patterns"],
            check["description"]
        )
        results.append(result)
    
    # Summary
    print("\n" + "="*60)
    print("Validation Summary")
    print("="*60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Total: {passed}/{total} checks passed")
    
    if passed == total:
        print(f"\n{GREEN}🎉 All fixes are properly applied!{RESET}")
        print("\nNext steps:")
        print("1. Start TWS or IB Gateway with API connections enabled")
        print("2. Start the MCP server")
        print("3. Run the test scripts to verify functionality")
    else:
        print(f"\n{YELLOW}⚠️  Some fixes are missing or incorrect.{RESET}")
        print("Please review the failed checks above.")
    
    return passed == total

if __name__ == "__main__":
    validate_fixes()
