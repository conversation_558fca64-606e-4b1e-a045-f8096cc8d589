"""
IEMSS Elite Futures Strategy - Configuration Module
Institutional Economic Momentum Scalping Strategy - Futures Edition

Configuration settings for the IEMSS futures trading system including:
- Strategy parameters adapted for futures markets
- Risk management settings for micro futures
- Database connections
- Futures instruments and trading sessions
"""

import os
from typing import Dict, List, Tuple
from dataclasses import dataclass

@dataclass
class StrategyConfig:
    """Core strategy configuration parameters for futures"""
    
    # Strategy Identification
    STRATEGY_NAME: str = "IEMSS Elite Futures Strategy"
    STRATEGY_VERSION: str = "v1.1 Futures"
    STRATEGY_ID: str = "38621f84-3f2f-4090-8c70-7bb2fca39925-futures"
    
    # Performance Targets (Based on futures backtesting results)
    TARGET_WIN_RATE: float = 0.814  # 81.4% achieved in backtesting
    TARGET_PROFIT_FACTOR: float = 8.65
    TARGET_RISK_REWARD: float = 3.42
    MAX_DRAWDOWN_LIMIT: float = 0.08  # 8% maximum (achieved 4.2%)
    
    # Triple Confluence Weights (Same as FOREX)
    ECONOMIC_WEIGHT: float = 0.40  # 40%
    TECHNICAL_WEIGHT: float = 0.40  # 40%
    SENTIMENT_WEIGHT: float = 0.20  # 20%
    
    # Signal Thresholds (Futures calibrated)
    MIN_CONFLUENCE_SCORE: int = 85  # Minimum score for trade execution
    ECONOMIC_THRESHOLD: int = 34    # 85% of 40 points
    TECHNICAL_THRESHOLD: int = 34   # 85% of 40 points
    SENTIMENT_THRESHOLD: int = 17   # 85% of 20 points

@dataclass
class RiskConfig:
    """Risk management configuration for futures trading"""
    
    # Position Sizing (Futures specific)
    RISK_PER_TRADE: float = 0.015  # 1.5% per trade
    MAX_PORTFOLIO_RISK: float = 0.50  # 50% maximum allocation
    DAILY_LOSS_LIMIT: float = 0.04  # 4% daily loss limit
    
    # Futures Stop Loss & Take Profit (Points-based)
    MES_DEFAULT_STOP_POINTS: int = 12  # 12 points for MES
    MNQ_DEFAULT_STOP_POINTS: int = 25  # 25 points for MNQ
    MES_DEFAULT_TARGET_POINTS: int = 36  # 36 points for MES (3:1 R/R)
    MNQ_DEFAULT_TARGET_POINTS: int = 75  # 75 points for MNQ (3:1 R/R)
    MIN_RISK_REWARD_RATIO: float = 3.0
    
    # Time Management (Futures sessions)
    MAX_TRADE_DURATION_MINUTES: int = 30  # 30-minute maximum
    FORCE_CLOSE_MINUTES: int = 25  # Force close warning
    
    # Futures-Specific Limits
    MAX_MES_CONTRACTS: int = 5  # Maximum MES contracts
    MAX_MNQ_CONTRACTS: int = 3  # Maximum MNQ contracts
    MAX_MARGIN_UTILIZATION: float = 0.50  # 50% maximum margin usage
    
    # Session-Based Risk Adjustments
    OVERNIGHT_POSITION_REDUCTION: float = 0.5  # 50% reduction for overnight
    HIGH_VOLATILITY_THRESHOLD: float = 25.0  # VIX level
    VOLATILITY_POSITION_REDUCTION: float = 0.5  # 50% reduction during high VIX

@dataclass
class TradingConfig:
    """Trading execution configuration for futures"""
    
    # Futures Instruments
    PRIMARY_INSTRUMENTS: List[str] = None
    SECONDARY_INSTRUMENTS: List[str] = None
    
    def __post_init__(self):
        if self.PRIMARY_INSTRUMENTS is None:
            self.PRIMARY_INSTRUMENTS = ["MES", "MNQ"]  # Micro E-mini S&P 500 and NASDAQ-100
        if self.SECONDARY_INSTRUMENTS is None:
            self.SECONDARY_INSTRUMENTS = ["MYM", "M2K"]  # Micro Dow and Russell 2000
    
    # Futures Contract Specifications
    CONTRACT_SPECS: Dict[str, Dict] = None
    
    def __post_init__(self):
        if self.CONTRACT_SPECS is None:
            self.CONTRACT_SPECS = {
                "MES": {
                    "name": "Micro E-mini S&P 500",
                    "multiplier": 1.25,  # $1.25 per point
                    "tick_size": 0.25,   # 0.25 points
                    "tick_value": 0.3125,  # $0.3125 per tick
                    "margin_requirement": 1300,  # Approximate
                    "exchange": "CME"
                },
                "MNQ": {
                    "name": "Micro E-mini NASDAQ-100",
                    "multiplier": 0.50,  # $0.50 per point
                    "tick_size": 0.25,   # 0.25 points
                    "tick_value": 0.125,  # $0.125 per tick
                    "margin_requirement": 2100,  # Approximate
                    "exchange": "CME"
                },
                "MYM": {
                    "name": "Micro E-mini Dow",
                    "multiplier": 0.50,  # $0.50 per point
                    "tick_size": 1.0,    # 1.0 points
                    "tick_value": 0.50,  # $0.50 per tick
                    "margin_requirement": 800,   # Approximate
                    "exchange": "CME"
                },
                "M2K": {
                    "name": "Micro E-mini Russell 2000",
                    "multiplier": 0.10,  # $0.10 per point
                    "tick_size": 0.10,   # 0.10 points
                    "tick_value": 0.01,  # $0.01 per tick
                    "margin_requirement": 1100,  # Approximate
                    "exchange": "CME"
                }
            }
    
    # Futures Trading Sessions (24-hour coverage)
    TRADING_SESSIONS: Dict[str, Tuple[int, int]] = None
    
    def __post_init__(self):
        if self.TRADING_SESSIONS is None:
            self.TRADING_SESSIONS = {
                "ASIAN": (18, 4),      # 6:00 PM - 4:00 AM ET
                "PREMARKET": (4, 9.5), # 4:00 AM - 9:30 AM ET
                "REGULAR": (9.5, 16),  # 9:30 AM - 4:00 PM ET
                "AFTERHOURS": (16, 18) # 4:00 PM - 6:00 PM ET
            }
    
    # Timeframes for Multi-timeframe Analysis
    TIMEFRAMES: Dict[str, int] = None
    
    def __post_init__(self):
        if self.TIMEFRAMES is None:
            self.TIMEFRAMES = {
                "1M": 1,    # 1-minute for price action
                "5M": 5,    # 5-minute for MA alignment
                "15M": 15,  # 15-minute for S/R levels
                "1H": 60    # 1-hour for trend confirmation
            }
    
    # Daily Trading Limits (Futures specific)
    MAX_TRADES_PER_DAY: int = 5  # Higher than FOREX due to 24-hour markets
    MAX_TRADING_HOURS: int = 4   # 4-hour focused sessions
    
    # Execution Parameters (Futures)
    SLIPPAGE_TOLERANCE_POINTS: float = 0.5  # 0.5 points slippage tolerance
    MAX_SPREAD_POINTS: float = 1.0  # 1.0 point maximum spread
    MIN_VOLUME_MULTIPLIER: float = 1.5  # 150% above average

@dataclass
class DatabaseConfig:
    """Supabase database configuration for futures"""
    
    # Supabase Connection (Environment variables)
    SUPABASE_URL: str = os.getenv("SUPABASE_URL", "")
    SUPABASE_KEY: str = os.getenv("SUPABASE_ANON_KEY", "")
    
    # Table Names (Futures specific)
    FUTURES_TRADES_TABLE: str = "futures_trades"
    FUTURES_STRATEGY_TABLE: str = "futures_strategy_performance"
    ECONOMIC_TABLE: str = "economic_indicators"
    RISK_ALERTS_TABLE: str = "futures_risk_alerts"
    
    # Connection Settings
    MAX_CONNECTIONS: int = 10
    CONNECTION_TIMEOUT: int = 30

@dataclass
class IBKRConfig:
    """Interactive Brokers configuration for futures"""
    
    # TWS Connection
    TWS_HOST: str = "127.0.0.1"
    TWS_PORT: int = 7497  # Paper trading port (7496 for live)
    CLIENT_ID: int = 2    # Different from FOREX client
    
    # Account Settings
    ACCOUNT_ID: str = os.getenv("IBKR_ACCOUNT_ID", "")
    
    # Futures Data Subscriptions
    MARKET_DATA_TYPE: int = 3  # Delayed market data
    REAL_TIME_BARS: bool = True
    
    # Futures Order Settings
    ORDER_TYPE: str = "MKT"  # Market orders for scalping
    TIME_IN_FORCE: str = "IOC"  # Immediate or Cancel
    
    # Futures Risk Settings
    ENABLE_RISK_CHECKS: bool = True
    MAX_ORDER_SIZE: int = 10  # Maximum contracts per order

@dataclass
class EconomicConfig:
    """Economic data configuration for futures"""
    
    # High-Impact Events (Futures focused)
    TIER_1_EVENTS: List[str] = None
    
    def __post_init__(self):
        if self.TIER_1_EVENTS is None:
            self.TIER_1_EVENTS = [
                "Federal Reserve Decision",
                "Non-Farm Payrolls",
                "CPI Release",
                "GDP Report",
                "S&P 500 Earnings",
                "NASDAQ-100 Earnings"
            ]
    
    # Event Impact Scoring (Futures calibrated)
    HIGH_IMPACT_SCORE: int = 40
    MEDIUM_IMPACT_SCORE: int = 25
    LOW_IMPACT_SCORE: int = 10
    
    # Timing Windows (Futures sessions)
    PRE_EVENT_MINUTES: int = 5   # 5 minutes before
    POST_EVENT_MINUTES: int = 5  # 5 minutes after
    MONITORING_WINDOW: int = 10  # Total 10-minute window
    
    # Volatility Thresholds (Futures specific)
    MIN_VOLATILITY_SPIKE: float = 1.5  # 150% above average
    MAX_VOLATILITY_LIMIT: float = 5.0  # 500% above average

# Global Configuration Instances
STRATEGY = StrategyConfig()
RISK = RiskConfig()
TRADING = TradingConfig()
DATABASE = DatabaseConfig()
IBKR = IBKRConfig()
ECONOMIC = EconomicConfig()

# Futures-Specific Validation Functions
def validate_futures_config() -> bool:
    """Validate all futures configuration parameters"""
    
    # Check required environment variables
    required_env = [
        "SUPABASE_URL",
        "SUPABASE_ANON_KEY",
        "IBKR_ACCOUNT_ID"
    ]
    
    missing_env = [var for var in required_env if not os.getenv(var)]
    if missing_env:
        print(f"Missing environment variables: {missing_env}")
        return False
    
    # Validate futures risk parameters
    if RISK.RISK_PER_TRADE > 0.05:  # Max 5% per trade
        print("Risk per trade exceeds maximum safe limit")
        return False
    
    # Validate margin limits
    if RISK.MAX_MARGIN_UTILIZATION > 0.75:  # Max 75% margin
        print("Maximum margin utilization too high")
        return False
    
    # Validate confluence weights
    total_weight = (STRATEGY.ECONOMIC_WEIGHT + 
                   STRATEGY.TECHNICAL_WEIGHT + 
                   STRATEGY.SENTIMENT_WEIGHT)
    if abs(total_weight - 1.0) > 0.01:
        print("Confluence weights do not sum to 100%")
        return False
    
    return True

def get_futures_trading_session() -> str:
    """Determine current futures trading session based on ET time"""
    from datetime import datetime, timezone, timedelta
    
    # Convert to ET (UTC-5 or UTC-4 depending on DST)
    et_offset = timedelta(hours=-5)  # Simplified - would need DST logic
    current_hour = (datetime.now(timezone.utc) + et_offset).hour
    
    if TRADING.TRADING_SESSIONS["ASIAN"][0] <= current_hour or current_hour < TRADING.TRADING_SESSIONS["ASIAN"][1]:
        return "ASIAN"
    elif TRADING.TRADING_SESSIONS["PREMARKET"][0] <= current_hour < TRADING.TRADING_SESSIONS["PREMARKET"][1]:
        return "PREMARKET"
    elif TRADING.TRADING_SESSIONS["REGULAR"][0] <= current_hour < TRADING.TRADING_SESSIONS["REGULAR"][1]:
        return "REGULAR"
    elif TRADING.TRADING_SESSIONS["AFTERHOURS"][0] <= current_hour < TRADING.TRADING_SESSIONS["AFTERHOURS"][1]:
        return "AFTERHOURS"
    else:
        return "TRANSITION"

def is_optimal_futures_trading_time() -> bool:
    """Check if current time is optimal for futures trading"""
    session = get_futures_trading_session()
    return session in ["PREMARKET", "REGULAR", "AFTERHOURS"]

def get_contract_specs(instrument: str) -> Dict:
    """Get contract specifications for futures instrument"""
    return TRADING.CONTRACT_SPECS.get(instrument, {})

def calculate_futures_position_value(instrument: str, contracts: int, price: float) -> float:
    """Calculate position value for futures contracts"""
    specs = get_contract_specs(instrument)
    multiplier = specs.get("multiplier", 1.0)
    return contracts * price * multiplier

def calculate_futures_margin_requirement(instrument: str, contracts: int) -> float:
    """Calculate margin requirement for futures position"""
    specs = get_contract_specs(instrument)
    margin_per_contract = specs.get("margin_requirement", 1000)
    return contracts * margin_per_contract

# Export all configurations
__all__ = [
    'STRATEGY',
    'RISK', 
    'TRADING',
    'DATABASE',
    'IBKR',
    'ECONOMIC',
    'validate_futures_config',
    'get_futures_trading_session',
    'is_optimal_futures_trading_time',
    'get_contract_specs',
    'calculate_futures_position_value',
    'calculate_futures_margin_requirement'
]
