"""
IEMSS Elite Futures Strategy - Main Strategy Engine
Institutional Economic Momentum Scalping Strategy - Futures Edition

Core futures strategy implementation featuring:
- Triple Confluence System adapted for equity index futures
- Signal generation and validation for MES/MNQ contracts
- Entry/exit logic with futures-specific timing
- Risk-adjusted position sizing for micro futures
- Real-time monitoring and alerts for 24-hour markets
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import asyncio
import uuid

import pandas as pd
import numpy as np

from config import STRATEGY, RISK, TRADING, get_contract_specs, calculate_futures_position_value
from database_manager import db_manager, FuturesTradeRecord
from risk_manager import FuturesRiskManager
from technical_analysis import FuturesTechnicalAnalyzer, FuturesTechnicalScore
from economic_data import FuturesEconomicDataHandler, FuturesEconomicScore
from sentiment_analyzer import FuturesSentimentAnalyzer, FuturesSentimentScore

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class FuturesConfluenceAnalysis:
    """Complete confluence analysis result for futures"""
    instrument: str  # MES, MNQ, etc.
    total_score: int  # 0-100 points
    economic_score: int  # 0-40 points
    technical_score: int  # 0-40 points
    sentiment_score: int  # 0-20 points
    signal_strength: str  # STRONG/MODERATE/WEAK
    trade_direction: str  # BUY/SELL/NONE
    confidence_level: float  # 0-100%
    entry_price: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    risk_reward_ratio: Optional[float]
    contracts: Optional[int]
    margin_required: Optional[float]
    session: str  # ASIAN/PREMARKET/REGULAR/AFTERHOURS
    analysis_timestamp: datetime
    details: Dict[str, Any]

@dataclass
class FuturesTradeSignal:
    """Complete futures trade signal with all parameters"""
    signal_id: str
    instrument: str  # MES, MNQ, etc.
    signal_type: str  # BUY/SELL
    entry_price: float
    stop_loss: float
    take_profit: float
    contracts: int
    contract_value: float  # Multiplier (1.25 for MES, 0.50 for MNQ)
    margin_required: float
    risk_amount: float
    confluence_score: int
    confidence: float
    catalyst: str
    session: str
    expiry_time: datetime
    created_at: datetime

class IEMSSFuturesStrategy:
    """Main IEMSS Elite Futures Strategy Engine"""
    
    def __init__(self, account_balance: float):
        """Initialize IEMSS futures strategy with account balance"""
        self.account_balance = account_balance
        self.risk_manager = FuturesRiskManager(account_balance)
        self.technical_analyzer = FuturesTechnicalAnalyzer()
        self.economic_handler = FuturesEconomicDataHandler()
        self.sentiment_analyzer = FuturesSentimentAnalyzer()
        
        self.active_signals: Dict[str, FuturesTradeSignal] = {}
        self.strategy_active = False
        self.last_analysis_time = None
        self.current_session = "REGULAR"
        
        logger.info(f"IEMSS Futures Strategy initialized with ${account_balance:,.2f} account balance")
    
    # Main Strategy Methods
    
    async def analyze_futures_confluence(self, 
                                       instrument: str,
                                       price_data: Dict[str, pd.DataFrame],
                                       current_time: datetime = None) -> FuturesConfluenceAnalysis:
        """Perform complete triple confluence analysis for futures"""
        
        if current_time is None:
            current_time = datetime.now(timezone.utc)
        
        # Determine current trading session
        session = self._get_current_session(current_time)
        
        logger.info(f"Starting futures confluence analysis for {instrument} during {session} session")
        
        try:
            # 1. Economic Momentum Analysis (40% weight) - Futures focused
            economic_analysis = await self.economic_handler.analyze_futures_economic_momentum(
                instrument, current_time
            )
            
            # 2. Technical Confluence Analysis (40% weight) - Futures adapted
            technical_analysis = await self.technical_analyzer.analyze_futures_confluence(
                instrument, price_data, session
            )
            
            # 3. Market Sentiment Analysis (20% weight) - Futures specific
            sentiment_analysis = await self.sentiment_analyzer.analyze_futures_sentiment(
                instrument, current_time
            )
            
            # Calculate total confluence score
            total_score = (economic_analysis.total_score + 
                          technical_analysis.total_score + 
                          sentiment_analysis.total_score)
            
            # Determine signal strength and direction
            signal_strength, trade_direction = self._evaluate_futures_signal_strength(
                total_score, economic_analysis, technical_analysis, sentiment_analysis, session
            )
            
            # Calculate confidence level
            confidence_level = min((total_score / 100) * 100, 100)
            
            # Determine entry parameters if signal is valid
            entry_price = None
            stop_loss = None
            take_profit = None
            risk_reward_ratio = None
            contracts = None
            margin_required = None
            
            if total_score >= STRATEGY.MIN_CONFLUENCE_SCORE and trade_direction != 'NONE':
                current_price = price_data['1M']['close'].iloc[-1] if '1M' in price_data else None
                if current_price:
                    entry_price = current_price
                    
                    # Calculate futures-specific stop loss and take profit
                    stop_loss, take_profit = self._calculate_futures_levels(
                        instrument, entry_price, trade_direction, session
                    )
                    
                    # Calculate position sizing for futures
                    contracts, margin_required = self._calculate_futures_position_size(
                        instrument, entry_price, stop_loss, session
                    )
                    
                    if stop_loss and take_profit:
                        risk_reward_ratio = abs(take_profit - entry_price) / abs(entry_price - stop_loss)
            
            confluence_analysis = FuturesConfluenceAnalysis(
                instrument=instrument,
                total_score=total_score,
                economic_score=economic_analysis.total_score,
                technical_score=technical_analysis.total_score,
                sentiment_score=sentiment_analysis.total_score,
                signal_strength=signal_strength,
                trade_direction=trade_direction,
                confidence_level=confidence_level,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                risk_reward_ratio=risk_reward_ratio,
                contracts=contracts,
                margin_required=margin_required,
                session=session,
                analysis_timestamp=current_time,
                details={
                    'economic': economic_analysis.details,
                    'technical': technical_analysis.details,
                    'sentiment': sentiment_analysis.details
                }
            )
            
            logger.info(f"Futures confluence analysis complete: {total_score}/100 points, "
                       f"Direction: {trade_direction}, Strength: {signal_strength}, Session: {session}")
            
            return confluence_analysis
            
        except Exception as e:
            logger.error(f"Error in futures confluence analysis for {instrument}: {e}")
            return FuturesConfluenceAnalysis(
                instrument=instrument,
                total_score=0,
                economic_score=0,
                technical_score=0,
                sentiment_score=0,
                signal_strength='WEAK',
                trade_direction='NONE',
                confidence_level=0.0,
                entry_price=None,
                stop_loss=None,
                take_profit=None,
                risk_reward_ratio=None,
                contracts=None,
                margin_required=None,
                session=session,
                analysis_timestamp=current_time,
                details={'error': str(e)}
            )
    
    def _evaluate_futures_signal_strength(self, 
                                        total_score: int,
                                        economic: FuturesEconomicScore,
                                        technical: FuturesTechnicalScore,
                                        sentiment: FuturesSentimentScore,
                                        session: str) -> Tuple[str, str]:
        """Evaluate signal strength and determine trade direction for futures"""
        
        # Check minimum thresholds for each component
        economic_valid = economic.total_score >= STRATEGY.ECONOMIC_THRESHOLD
        technical_valid = technical.total_score >= STRATEGY.TECHNICAL_THRESHOLD
        sentiment_valid = sentiment.total_score >= STRATEGY.SENTIMENT_THRESHOLD
        
        # Require at least 2 out of 3 components to be valid
        valid_components = sum([economic_valid, technical_valid, sentiment_valid])
        
        if valid_components < 2:
            return 'WEAK', 'NONE'
        
        # Session-based adjustments
        session_multiplier = self._get_session_multiplier(session)
        adjusted_score = total_score * session_multiplier
        
        # Determine signal strength based on adjusted score
        if adjusted_score >= 95:
            signal_strength = 'STRONG'
        elif adjusted_score >= STRATEGY.MIN_CONFLUENCE_SCORE:
            signal_strength = 'MODERATE'
        else:
            signal_strength = 'WEAK'
        
        # Determine trade direction based on component analysis
        if adjusted_score >= STRATEGY.MIN_CONFLUENCE_SCORE:
            # Analyze directional bias from components
            bullish_signals = 0
            bearish_signals = 0
            
            # Economic directional bias
            if economic.total_score > 25:
                if economic.details.get('fed_bias') == 'HAWKISH':
                    bearish_signals += 1  # Higher rates = lower indices
                elif economic.details.get('fed_bias') == 'DOVISH':
                    bullish_signals += 1  # Lower rates = higher indices
            
            # Technical directional bias
            if technical.total_score > 25:
                if technical.details.get('trend_direction') == 'BULLISH':
                    bullish_signals += 1
                elif technical.details.get('trend_direction') == 'BEARISH':
                    bearish_signals += 1
            
            # Sentiment directional bias
            if sentiment.total_score > 15:
                if sentiment.details.get('vix_regime') == 'LOW':
                    bullish_signals += 1  # Low VIX = risk-on
                elif sentiment.details.get('vix_regime') == 'HIGH':
                    bearish_signals += 1  # High VIX = risk-off
            
            if bullish_signals > bearish_signals:
                trade_direction = 'BUY'
            elif bearish_signals > bullish_signals:
                trade_direction = 'SELL'
            else:
                trade_direction = 'NONE'  # No clear directional bias
        else:
            trade_direction = 'NONE'
        
        return signal_strength, trade_direction
    
    def _get_session_multiplier(self, session: str) -> float:
        """Get session-based score multiplier"""
        session_multipliers = {
            'PREMARKET': 1.2,   # Enhanced for economic releases
            'REGULAR': 1.0,     # Standard session
            'AFTERHOURS': 1.1,  # Enhanced for earnings
            'ASIAN': 0.8        # Reduced for lower volatility
        }
        return session_multipliers.get(session, 1.0)
    
    def _calculate_futures_levels(self, 
                                instrument: str,
                                entry_price: float,
                                trade_direction: str,
                                session: str) -> Tuple[float, float]:
        """Calculate futures-specific stop loss and take profit levels"""
        
        specs = get_contract_specs(instrument)
        
        # Get default stop distances based on instrument
        if instrument == 'MES':
            base_stop_points = RISK.MES_DEFAULT_STOP_POINTS
            base_target_points = RISK.MES_DEFAULT_TARGET_POINTS
        elif instrument == 'MNQ':
            base_stop_points = RISK.MNQ_DEFAULT_STOP_POINTS
            base_target_points = RISK.MNQ_DEFAULT_TARGET_POINTS
        else:
            # Default for other instruments
            base_stop_points = 15
            base_target_points = 45
        
        # Session-based adjustments
        if session == 'ASIAN':
            # Tighter stops during lower volatility
            stop_points = base_stop_points * 0.8
            target_points = base_target_points * 0.8
        elif session in ['PREMARKET', 'AFTERHOURS']:
            # Wider stops during higher volatility
            stop_points = base_stop_points * 1.2
            target_points = base_target_points * 1.2
        else:
            stop_points = base_stop_points
            target_points = base_target_points
        
        # Calculate actual levels
        if trade_direction.upper() == 'BUY':
            stop_loss = entry_price - stop_points
            take_profit = entry_price + target_points
        else:  # SELL
            stop_loss = entry_price + stop_points
            take_profit = entry_price - target_points
        
        return round(stop_loss, 2), round(take_profit, 2)
    
    def _calculate_futures_position_size(self, 
                                       instrument: str,
                                       entry_price: float,
                                       stop_loss: float,
                                       session: str) -> Tuple[int, float]:
        """Calculate futures position size and margin requirement"""
        
        specs = get_contract_specs(instrument)
        multiplier = specs.get('multiplier', 1.0)
        margin_per_contract = specs.get('margin_requirement', 1000)
        
        # Calculate risk amount (1.5% of account)
        risk_amount = self.account_balance * RISK.RISK_PER_TRADE
        
        # Session-based risk adjustments
        if session == 'ASIAN':
            risk_amount *= 0.5  # 50% reduction for overnight
        elif session in ['PREMARKET', 'AFTERHOURS']:
            risk_amount *= 1.0  # Standard risk
        
        # Calculate stop distance in points
        stop_distance = abs(entry_price - stop_loss)
        
        # Calculate number of contracts
        # Risk Amount = Contracts × Stop Distance × Multiplier
        contracts = int(risk_amount / (stop_distance * multiplier))
        
        # Apply instrument-specific limits
        if instrument == 'MES':
            contracts = min(contracts, RISK.MAX_MES_CONTRACTS)
        elif instrument == 'MNQ':
            contracts = min(contracts, RISK.MAX_MNQ_CONTRACTS)
        
        # Ensure at least 1 contract if risk allows
        contracts = max(1, contracts) if risk_amount >= (stop_distance * multiplier) else 0
        
        # Calculate margin requirement
        margin_required = contracts * margin_per_contract
        
        return contracts, margin_required
    
    def _get_current_session(self, current_time: datetime) -> str:
        """Determine current futures trading session"""
        
        # Convert to ET for session determination
        et_hour = (current_time.hour - 5) % 24  # Simplified ET conversion
        
        if 18 <= et_hour or et_hour < 4:
            return 'ASIAN'
        elif 4 <= et_hour < 9.5:
            return 'PREMARKET'
        elif 9.5 <= et_hour < 16:
            return 'REGULAR'
        elif 16 <= et_hour < 18:
            return 'AFTERHOURS'
        else:
            return 'TRANSITION'
    
    # Signal Generation Methods
    
    async def generate_futures_trade_signal(self, 
                                          instrument: str,
                                          price_data: Dict[str, pd.DataFrame],
                                          current_time: datetime = None) -> Optional[FuturesTradeSignal]:
        """Generate complete futures trade signal if confluence criteria are met"""
        
        if current_time is None:
            current_time = datetime.now(timezone.utc)
        
        try:
            # Perform confluence analysis
            confluence = await self.analyze_futures_confluence(
                instrument, price_data, current_time
            )
            
            # Check if signal meets minimum requirements
            if (confluence.total_score < STRATEGY.MIN_CONFLUENCE_SCORE or 
                confluence.trade_direction == 'NONE' or
                confluence.contracts is None or
                confluence.contracts <= 0):
                logger.info(f"No valid futures signal for {instrument}: "
                           f"Score {confluence.total_score}, Direction {confluence.trade_direction}")
                return None
            
            # Validate with risk management
            if not confluence.entry_price or not confluence.stop_loss:
                logger.warning(f"Invalid price parameters for {instrument}")
                return None
            
            # Validate margin requirements
            if confluence.margin_required > self.account_balance * RISK.MAX_MARGIN_UTILIZATION:
                logger.warning(f"Margin requirement too high for {instrument}: "
                             f"${confluence.margin_required:,.2f}")
                return None
            
            # Validate trade risk with futures risk manager
            risk_valid, risk_warnings = await self.risk_manager.validate_futures_trade_risk(
                instrument, confluence.contracts, confluence.entry_price, 
                confluence.stop_loss, confluence.session
            )
            
            if not risk_valid:
                logger.warning(f"Futures trade risk validation failed for {instrument}: {risk_warnings}")
                return None
            
            # Create futures trade signal
            signal_id = str(uuid.uuid4())
            
            # Generate catalyst description
            catalyst = self._generate_futures_catalyst_description(confluence)
            
            # Get contract specifications
            specs = get_contract_specs(instrument)
            contract_value = specs.get('multiplier', 1.0)
            
            futures_trade_signal = FuturesTradeSignal(
                signal_id=signal_id,
                instrument=instrument,
                signal_type=confluence.trade_direction,
                entry_price=confluence.entry_price,
                stop_loss=confluence.stop_loss,
                take_profit=confluence.take_profit,
                contracts=confluence.contracts,
                contract_value=contract_value,
                margin_required=confluence.margin_required,
                risk_amount=confluence.contracts * abs(confluence.entry_price - confluence.stop_loss) * contract_value,
                confluence_score=confluence.total_score,
                confidence=confluence.confidence_level,
                catalyst=catalyst,
                session=confluence.session,
                expiry_time=current_time + timedelta(minutes=30),  # 30-minute expiry
                created_at=current_time
            )
            
            # Store active signal
            self.active_signals[signal_id] = futures_trade_signal
            
            logger.info(f"Futures trade signal generated for {instrument}: "
                       f"{confluence.trade_direction} {confluence.contracts} contracts at {confluence.entry_price:.2f}, "
                       f"Score: {confluence.total_score}/100, Session: {confluence.session}")
            
            return futures_trade_signal
            
        except Exception as e:
            logger.error(f"Error generating futures trade signal for {instrument}: {e}")
            return None
    
    def _generate_futures_catalyst_description(self, confluence: FuturesConfluenceAnalysis) -> str:
        """Generate human-readable catalyst description for futures"""
        
        catalysts = []
        
        # Economic catalysts
        if confluence.economic_score >= STRATEGY.ECONOMIC_THRESHOLD:
            catalysts.append("Economic momentum")
        
        # Technical catalysts
        if confluence.technical_score >= STRATEGY.TECHNICAL_THRESHOLD:
            catalysts.append("Technical confluence")
        
        # Sentiment catalysts
        if confluence.sentiment_score >= STRATEGY.SENTIMENT_THRESHOLD:
            catalysts.append("Market sentiment alignment")
        
        # Add session context
        catalysts.append(f"{confluence.session} session")
        
        # Add specific details if available
        economic_details = confluence.details.get('economic', {})
        if 'fed_events' in economic_details:
            events = economic_details['fed_events']
            if events:
                catalysts.append(f"Fed event: {events[0]}")
        
        technical_details = confluence.details.get('technical', {})
        if 'index_level' in technical_details:
            level_type = technical_details['index_level']
            if level_type:
                catalysts.append(f"Key {level_type} level")
        
        return " + ".join(catalysts) if catalysts else f"Futures triple confluence signal ({confluence.session})"
    
    # Strategy Control Methods
    
    async def start_futures_strategy(self) -> bool:
        """Start the IEMSS futures strategy"""
        
        try:
            # Validate configuration
            if not self._validate_futures_strategy_config():
                return False
            
            # Initialize components
            await self.economic_handler._update_futures_economic_calendar()
            await self.sentiment_analyzer._update_futures_sentiment_data()
            
            # Reset daily metrics
            self.risk_manager.reset_daily_metrics()
            
            self.strategy_active = True
            logger.info("IEMSS Futures Strategy started successfully")
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting futures strategy: {e}")
            return False
    
    async def stop_futures_strategy(self):
        """Stop the IEMSS futures strategy"""
        
        self.strategy_active = False
        
        # Clear active signals
        self.active_signals.clear()
        
        logger.info("IEMSS Futures Strategy stopped")
    
    def _validate_futures_strategy_config(self) -> bool:
        """Validate futures strategy configuration"""
        
        # Check account balance
        if self.account_balance <= 0:
            logger.error("Invalid account balance")
            return False
        
        # Check minimum balance for futures trading
        min_balance = 25000  # Minimum recommended for futures
        if self.account_balance < min_balance:
            logger.warning(f"Account balance ${self.account_balance:,.2f} below recommended minimum ${min_balance:,.2f}")
        
        # Check risk parameters
        if RISK.RISK_PER_TRADE <= 0 or RISK.RISK_PER_TRADE > 0.05:
            logger.error("Invalid risk per trade parameter")
            return False
        
        # Check margin limits
        if RISK.MAX_MARGIN_UTILIZATION > 0.75:
            logger.error("Maximum margin utilization too high")
            return False
        
        return True
    
    def get_futures_strategy_status(self) -> Dict[str, Any]:
        """Get comprehensive futures strategy status"""
        
        return {
            'strategy_active': self.strategy_active,
            'account_balance': self.account_balance,
            'active_signals': len(self.active_signals),
            'current_session': self.current_session,
            'daily_trades': self.risk_manager.daily_trades,
            'daily_pnl': self.risk_manager.daily_pnl,
            'margin_utilization': self.risk_manager.get_margin_utilization(),
            'last_analysis': self.last_analysis_time.isoformat() if self.last_analysis_time else None,
            'risk_summary': self.risk_manager.get_futures_risk_summary(),
            'signal_details': [
                {
                    'instrument': signal.instrument,
                    'signal_type': signal.signal_type,
                    'contracts': signal.contracts,
                    'confluence_score': signal.confluence_score,
                    'confidence': signal.confidence,
                    'session': signal.session,
                    'created_at': signal.created_at.isoformat(),
                    'expires_at': signal.expiry_time.isoformat()
                }
                for signal in self.active_signals.values()
            ]
        }

# Export main class
__all__ = [
    'IEMSSFuturesStrategy',
    'FuturesConfluenceAnalysis',
    'FuturesTradeSignal'
]
