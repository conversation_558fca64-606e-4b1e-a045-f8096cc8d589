<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IEMSS Elite FOREX 5-Min Scalping Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #0a0e27;
            color: #e0e6ed;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
            max-width: 1800px;
            margin: 0 auto;
        }

        .header {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: pulse 4s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(0.8); opacity: 0.5; }
            50% { transform: scale(1.2); opacity: 0.8; }
        }

        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #64b5f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
        }

        .subtitle {
            font-size: 1.2em;
            color: #b3d4fc;
            position: relative;
            z-index: 1;
        }

        .widget {
            background: #151a35;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
            border: 1px solid #1e2749;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .widget:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            border-color: #3a4b8c;
        }

        .widget-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #1e2749;
        }

        .widget-title {
            font-size: 1.3em;
            color: #64b5f6;
            font-weight: 600;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: rgba(30, 39, 73, 0.5);
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .metric:hover {
            background: rgba(58, 75, 140, 0.3);
            transform: translateX(5px);
        }

        .metric-label {
            color: #8892b0;
            font-size: 0.9em;
        }

        .metric-value {
            font-size: 1.2em;
            font-weight: 600;
            color: #fff;
        }

        .metric-value.positive {
            color: #4caf50;
        }

        .metric-value.negative {
            color: #f44336;
        }

        .positions-table {
            width: 100%;
            margin-top: 20px;
            border-collapse: collapse;
        }

        .positions-table th {
            background: #1e2749;
            padding: 12px;
            text-align: left;
            font-weight: 600;
            color: #64b5f6;
            border-bottom: 2px solid #3a4b8c;
        }

        .positions-table td {
            padding: 10px;
            border-bottom: 1px solid #1e2749;
        }

        .positions-table tr:hover {
            background: rgba(58, 75, 140, 0.2);
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 600;
        }

        .status-active {
            background: #4caf50;
            color: #fff;
        }

        .status-pending {
            background: #ff9800;
            color: #fff;
        }

        .chart-container {
            grid-column: span 2;
            height: 400px;
            position: relative;
        }

        .performance-chart {
            width: 100%;
            height: 100%;
            background: rgba(30, 39, 73, 0.3);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #8892b0;
        }

        .alert-section {
            grid-column: 1 / -1;
            background: rgba(255, 152, 0, 0.1);
            border: 1px solid #ff9800;
            border-radius: 10px;
            padding: 15px;
            display: none;
        }

        .alert-section.active {
            display: block;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .connection-status {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4caf50;
            animation: pulse-dot 2s infinite;
        }

        @keyframes pulse-dot {
            0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
            100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
        }

        .loading-spinner {
            border: 3px solid #1e2749;
            border-top: 3px solid #64b5f6;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .chart-container {
                grid-column: 1;
            }
            
            h1 {
                font-size: 1.8em;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <!-- Header -->
        <div class="header">
            <h1>IEMSS Elite FOREX 5-Min Scalping</h1>
            <p class="subtitle">Institutional-Grade High-Frequency Trading System</p>
            <div class="connection-status">
                <div class="status-dot"></div>
                <span>Connected to IBKR</span>
            </div>
        </div>

        <!-- Account Summary -->
        <div class="widget">
            <div class="widget-header">
                <h2 class="widget-title">💰 Account Summary</h2>
                <span id="last-update">--:--:--</span>
            </div>
            <div class="metric">
                <span class="metric-label">Net Liquidation</span>
                <span class="metric-value" id="net-liquidation">$0.00</span>
            </div>
            <div class="metric">
                <span class="metric-label">Daily P&L</span>
                <span class="metric-value" id="daily-pnl">$0.00</span>
            </div>
            <div class="metric">
                <span class="metric-label">Daily Return</span>
                <span class="metric-value" id="daily-return">0.00%</span>
            </div>
            <div class="metric">
                <span class="metric-label">Buying Power</span>
                <span class="metric-value" id="buying-power">$0.00</span>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="widget">
            <div class="widget-header">
                <h2 class="widget-title">📊 Performance Metrics</h2>
            </div>
            <div class="metric">
                <span class="metric-label">Total Trades</span>
                <span class="metric-value" id="total-trades">0</span>
            </div>
            <div class="metric">
                <span class="metric-label">Win Rate</span>
                <span class="metric-value" id="win-rate">0.0%</span>
            </div>
            <div class="metric">
                <span class="metric-label">Profit Factor</span>
                <span class="metric-value" id="profit-factor">0.00</span>
            </div>
            <div class="metric">
                <span class="metric-label">Active Positions</span>
                <span class="metric-value" id="active-positions">0</span>
            </div>
        </div>

        <!-- Risk Metrics -->
        <div class="widget">
            <div class="widget-header">
                <h2 class="widget-title">⚠️ Risk Management</h2>
            </div>
            <div class="metric">
                <span class="metric-label">Daily Drawdown</span>
                <span class="metric-value" id="daily-drawdown">0.00%</span>
            </div>
            <div class="metric">
                <span class="metric-label">Margin Used</span>
                <span class="metric-value" id="margin-used">0.00%</span>
            </div>
            <div class="metric">
                <span class="metric-label">Risk Exposure</span>
                <span class="metric-value" id="risk-exposure">0.00%</span>
            </div>
            <div class="metric">
                <span class="metric-label">Sharpe Ratio</span>
                <span class="metric-value" id="sharpe-ratio">0.00</span>
            </div>
        </div>

        <!-- Performance Chart -->
        <div class="widget chart-container">
            <div class="widget-header">
                <h2 class="widget-title">📈 Equity Curve</h2>
            </div>
            <div class="performance-chart" id="equity-chart">
                <canvas id="equityCanvas"></canvas>
            </div>
        </div>

        <!-- Active Positions -->
        <div class="widget" style="grid-column: 1 / -1;">
            <div class="widget-header">
                <h2 class="widget-title">🔄 Active Positions</h2>
            </div>
            <table class="positions-table">
                <thead>
                    <tr>
                        <th>Symbol</th>
                        <th>Direction</th>
                        <th>Entry Price</th>
                        <th>Current Price</th>
                        <th>Unrealized P&L</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody id="positions-tbody">
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 20px; color: #8892b0;">
                            No active positions
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Alerts Section -->
        <div class="alert-section" id="alerts">
            <h3 style="margin-bottom: 10px;">⚡ System Alerts</h3>
            <div id="alert-messages"></div>
        </div>
    </div>

    <script>
        // WebSocket connection for real-time updates
        let ws;
        let reconnectInterval;

        function connectWebSocket() {
            ws = new WebSocket('ws://localhost:8080/ws');

            ws.onopen = function() {
                console.log('Connected to dashboard server');
                document.querySelector('.status-dot').style.background = '#4caf50';
                clearInterval(reconnectInterval);
            };

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                updateDashboard(data);
            };

            ws.onclose = function() {
                console.log('Disconnected from dashboard server');
                document.querySelector('.status-dot').style.background = '#f44336';
                // Attempt to reconnect every 5 seconds
                reconnectInterval = setInterval(connectWebSocket, 5000);
            };

            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
            };
        }

        function updateDashboard(data) {
            // Update timestamp
            document.getElementById('last-update').textContent = 
                new Date(data.timestamp).toLocaleTimeString();

            // Update account summary
            updateValue('net-liquidation', `$${formatNumber(data.account.balance)}`);
            updateValue('daily-pnl', `$${formatNumber(data.account.daily_pnl)}`, 
                data.account.daily_pnl >= 0);
            updateValue('daily-return', `${data.account.daily_return.toFixed(2)}%`, 
                data.account.daily_return >= 0);
            updateValue('buying-power', `$${formatNumber(data.account.buying_power)}`);

            // Update performance metrics
            updateValue('total-trades', data.performance.total_trades);
            updateValue('win-rate', `${data.performance.win_rate.toFixed(1)}%`);
            updateValue('profit-factor', data.performance.profit_factor?.toFixed(2) || '0.00');
            updateValue('active-positions', data.performance.active_positions);

            // Update positions table
            updatePositionsTable(data.positions);

            // Update equity chart
            if (data.equity_curve) {
                updateEquityChart(data.equity_curve);
            }
        }

        function updateValue(elementId, value, isPositive = null) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value;
                if (isPositive !== null) {
                    element.classList.toggle('positive', isPositive);
                    element.classList.toggle('negative', !isPositive);
                }
            }
        }

        function formatNumber(num) {
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(num);
        }

        function updatePositionsTable(positions) {
            const tbody = document.getElementById('positions-tbody');
            
            if (positions.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 20px; color: #8892b0;">
                            No active positions
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = positions.map(pos => `
                <tr>
                    <td>${pos.symbol}</td>
                    <td>${pos.direction}</td>
                    <td>${pos.entry_price.toFixed(5)}</td>
                    <td>${pos.current_price.toFixed(5)}</td>
                    <td class="${pos.unrealized_pnl >= 0 ? 'positive' : 'negative'}">
                        $${formatNumber(pos.unrealized_pnl)}
                    </td>
                    <td>
                        <span class="status-badge status-${pos.status.toLowerCase()}">
                            ${pos.status}
                        </span>
                    </td>
                </tr>
            `).join('');
        }

        function updateEquityChart(data) {
            const canvas = document.getElementById('equityCanvas');
            const ctx = canvas.getContext('2d');
            
            // Simple line chart implementation
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
            
            // Draw chart (simplified - you'd want to use Chart.js in production)
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.strokeStyle = '#64b5f6';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            // Draw equity curve
            const points = data.slice(-100); // Last 100 points
            const maxValue = Math.max(...points);
            const minValue = Math.min(...points);
            const range = maxValue - minValue;
            
            points.forEach((value, index) => {
                const x = (index / (points.length - 1)) * canvas.width;
                const y = canvas.height - ((value - minValue) / range) * canvas.height;
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            
            ctx.stroke();
        }

        // Initialize connection
        connectWebSocket();

        // Initialize equity chart canvas
        window.addEventListener('resize', () => {
            const canvas = document.getElementById('equityCanvas');
            if (canvas) {
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
            }
        });
    </script>
</body>
</html>
