# IEMSS Elite FOREX 5-Minute Scalping - Implementation Guide

## Quick Start Guide

### Prerequisites
- Interactive Brokers account with API access enabled
- Python 3.9 or higher installed
- TWS or IB Gateway running
- Minimum $25,000 account balance (recommended)

### Step 1: Environment Setup

```bash
# Navigate to the strategy directory
cd "/Users/<USER>/IBKR/b-team/IBKR-Reports/IEMSS Elite FOREX 5-min Scalping"

# Create virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r "Solution App/requirements.txt"
```

### Step 2: Configure Interactive Brokers

1. **Enable API Access in TWS/Gateway:**
   - File → Global Configuration → API → Settings
   - Enable "Enable ActiveX and Socket Clients"
   - Enable "Download open orders on connection"
   - Set "Socket port" to 7497 (paper) or 7496 (live)
   - Add 127.0.0.1 to "Trusted IP Addresses"

2. **Set Market Data Permissions:**
   - Ensure you have FOREX data subscriptions
   - Enable "Ideal Pro" trading permissions

### Step 3: Configuration

Edit `Solution App/config.py` to customize:

```python
# Key settings to review:
RISK_PER_TRADE = 0.5  # Start conservative
MIN_CONFIDENCE = 0.65  # Signal confidence threshold
SYMBOLS = ['EUR.USD', 'GBP.USD']  # Start with major pairs
```

### Step 4: Run Backtesting

```bash
# Run comprehensive backtest
cd "Solution App"
python backtest.py

# Check results in Results/Backtest_Results.md
```

### Step 5: Paper Trading

```bash
# Start paper trading
python main.py --env development

# Monitor the dashboard
open http://localhost:8080
```

### Step 6: Live Trading (After Successful Paper Trading)

```bash
# Update configuration
export IBKR_PORT=7496  # Live trading port

# Start live trading
python main.py --env production
```

## Monitoring and Management

### Dashboard Access
- Open browser to `http://localhost:8080`
- Real-time monitoring of:
  - Account balance and P&L
  - Active positions
  - Performance metrics
  - Risk indicators

### Log Files
- Strategy logs: `Solution App/scalping_strategy.log`
- Trade history: `Solution App/trades_history.csv`
- Performance metrics: `Solution App/performance_metrics.json`

### Emergency Procedures

1. **Stop All Trading:**
   ```bash
   # Press Ctrl+C in terminal
   # Or create stop file:
   touch EMERGENCY_STOP
   ```

2. **Close All Positions:**
   - Dashboard: Click "Close All Positions"
   - Manual: Use TWS to flatten all positions

3. **Risk Limits Hit:**
   - Strategy automatically stops on:
     - 2% daily loss
     - 5% weekly drawdown
     - 3 consecutive losses

## Best Practices

### 1. Start Small
- Begin with 1-2 currency pairs
- Use minimum position sizes
- Gradually increase as confidence grows

### 2. Monitor Closely
- Check dashboard every hour initially
- Review daily performance
- Keep detailed trade journal

### 3. Market Conditions
- Best performance during:
  - London open (3 AM - 11 AM ET)
  - NY open (8 AM - 5 PM ET)
  - London/NY overlap (8 AM - 11 AM ET)

### 4. Avoid Trading During:
- Major news releases (NFP, FOMC, ECB)
- Low liquidity periods
- Market holidays

### 5. Regular Maintenance
- Weekly performance review
- Monthly strategy optimization
- Quarterly parameter adjustment

## Troubleshooting

### Connection Issues
```bash
# Check TWS/Gateway is running
# Verify API settings
# Test connection:
python -c "from trade_executor import IBKRTradeExecutor; exec = IBKRTradeExecutor(Config()); exec.connect()"
```

### No Signals Generated
- Check market hours
- Verify data feed
- Review confidence threshold
- Check volatility filters

### Performance Issues
- Reduce number of symbols
- Check system resources
- Optimize indicator calculations
- Review log files for errors

## Performance Optimization

### 1. Symbol Selection
- Focus on major pairs with tight spreads
- Avoid exotic pairs initially
- Monitor correlation between pairs

### 2. Time Optimization
- Trade during high volume periods
- Avoid first/last 30 minutes of sessions
- Skip low volatility periods

### 3. Risk Management
- Never exceed 2% risk per trade
- Maintain position correlation limits
- Use trailing stops in trending markets

## Advanced Features

### 1. Machine Learning Enhancement
```python
# Enable in config.py
USE_MACHINE_LEARNING = True
ML_MODEL_PATH = 'models/scalping_ml_model.pkl'
```

### 2. Sentiment Analysis
```python
# Enable news sentiment
USE_SENTIMENT_ANALYSIS = True
```

### 3. Custom Indicators
- Add to `technical_analysis.py`
- Test thoroughly in backtest
- Validate with paper trading

## Compliance and Reporting

### Daily Reports
- Generated automatically at market close
- Stored in `Results/` directory
- Includes:
  - Trade summary
  - P&L breakdown
  - Risk metrics

### Tax Reporting
- Export trades: `Results/trades_export.csv`
- Compatible with major tax software
- Consult tax professional

## Support and Updates

### Getting Help
1. Check documentation first
2. Review log files
3. Test in paper trading
4. Contact IEMSS support

### Staying Updated
- Monitor strategy performance
- Review market conditions
- Update parameters quarterly
- Stay informed on forex markets

---

*Remember: Trading involves substantial risk. Always use proper risk management and never risk more than you can afford to lose.*
