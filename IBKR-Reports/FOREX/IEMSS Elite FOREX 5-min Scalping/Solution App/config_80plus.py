"""
IEMSS Elite FOREX 5-Minute Scalping - Configuration
==================================================
Enhanced configuration for 80%+ Win Rate Strategy

Author: IEMSS Trading Desk
Version: 3.0.0 - 80%+ Win Rate Edition
"""

import os
from typing import List, Dict, Tuple


class Config:
    """
    Configuration settings for IEMSS Elite 5-Minute Scalping 80%+ Win Rate
    """
    
    # IBKR Connection Settings
    IBKR_HOST = os.getenv('IBKR_HOST', '127.0.0.1')
    IBKR_PORT = int(os.getenv('IBKR_PORT', '7497'))  # 7497 for paper, 7496 for live
    IBKR_CLIENT_ID = int(os.getenv('IBKR_CLIENT_ID', '1'))
    
    # Trading Symbols - Focus on most liquid pairs
    SYMBOLS: List[str] = [
        'EUR.USD',  # Euro/US Dollar - Primary
        'GBP.USD',  # British Pound/US Dollar - Primary
        'USD.JPY',  # US Dollar/Japanese Yen - Primary
        'AUD.USD',  # Australian Dollar/US Dollar
        'USD.CHF',  # US Dollar/Swiss Franc
    ]
    
    # 80%+ Win Rate Parameters
    MIN_CONFIRMATIONS = 8  # Minimum confirmations required
    MIN_CONFIDENCE = 0.85  # 85% minimum confidence
    MIN_RISK_REWARD = 1.5  # Minimum 1:1.5 R:R ratio
    MAX_SPREAD_PIPS = 1.5  # Tighter spread requirement
    
    # Correlation Settings
    CORRELATION_THRESHOLD = 0.7  # High correlation threshold
    MAX_CORRELATION_EXPOSURE = 0.7  # Maximum correlated exposure
    CORRELATION_POSITION_REDUCTION = 0.5  # Reduce position by 50%
    
    # Currency Correlations
    FOREX_CORRELATIONS = {
        'positive': [
            ('EUR.USD', 'GBP.USD', 0.8727),
            ('EUR.USD', 'AUD.USD', 0.7866),
            ('AUD.USD', 'GBP.USD', 0.8168),
            ('USD.CHF', 'USD.JPY', 0.7567),
        ],
        'negative': [
            ('EUR.USD', 'USD.CHF', -0.7559),
            ('GBP.USD', 'USD.JPY', -0.7559),
            ('AUD.USD', 'USD.JPY', -0.7559),
        ]
    }
    
    # Session Quality Scores
    SESSION_WEIGHTS = {
        'london_ny_overlap': 1.0,   # 8 AM - 12 PM ET
        'london': 0.9,              # 3 AM - 12 PM ET
        'newyork': 0.85,            # 8 AM - 5 PM ET
        'asian': 0.6,               # 7 PM - 3 AM ET
        'other': 0.5                # Other times
    }
    
    # Risk Management - Enhanced for 80%+ Win Rate
    RISK_PER_TRADE = 0.5  # Base risk (reduced with correlations)
    MAX_DAILY_LOSS = 1.0  # Stricter daily loss limit
    MAX_WEEKLY_LOSS = 2.5  # Weekly loss limit
    MAX_POSITIONS = 2  # Reduced concurrent positions
    MAX_CORRELATED_POSITIONS = 1  # Max positions in correlated pairs
    CONSECUTIVE_LOSS_LIMIT = 3  # Stop after 3 losses
    
    # Position Sizing Adjustments
    CORRELATION_SIZE_REDUCTION = 0.5  # 50% reduction
    LOSING_DAY_SIZE_REDUCTION = 0.5   # 50% reduction
    FRIDAY_SIZE_REDUCTION = 0.5       # 50% reduction
    LOW_WIN_RATE_REDUCTION = 0.5      # 50% reduction if < 80%
    
    # Trading Parameters - Tightened
    MIN_PIP_TARGET = 5  # Minimum pip target
    MAX_PIP_TARGET = 20  # Maximum pip target
    STOP_LOSS_PIPS = 7  # Maximum stop loss
    TRAILING_STOP_ACTIVATION = 1.5  # Activate at 1.5R
    TIME_STOP_CANDLES = 20  # Exit after 100 minutes
    
    # Technical Indicators - Enhanced
    EMA_FAST = 9
    EMA_MEDIUM = 21
    EMA_SLOW = 50
    RSI_PERIOD = 9  # Faster for 5-min
    RSI_NEUTRAL_LOW = 45  # Neutral zone
    RSI_NEUTRAL_HIGH = 55
    MACD_FAST = 12
    MACD_SLOW = 26
    MACD_SIGNAL = 9
    BB_PERIOD = 20
    BB_STD = 2
    ATR_PERIOD = 14
    
    # Pattern Recognition Scores
    PATTERN_SCORES = {
        'pin_bar': 0.85,
        'engulfing': 0.90,
        'double_bottom_top': 0.95,
        'flag': 0.88,
        'inside_bar': 0.82,
        'morning_evening_star': 0.87
    }
    
    # Time Settings - Optimized for Sessions
    TRADING_SESSIONS = {
        'london_ny_overlap': (8, 12),  # Best session
        'london': (3, 12),
        'newyork': (8, 17),
        'asian': (19, 3)  # Avoid if possible
    }
    AVOID_NEWS_MINUTES = 30
    AVOID_FRIDAY_AFTER = 14  # Avoid Friday afternoon
    
    # Performance Tracking
    WIN_RATE_TARGET = 0.80  # 80% target
    WIN_RATE_WARNING = 0.75  # Warning level
    PERFORMANCE_WINDOW = 100  # Last 100 trades
    
    # Dashboard Settings
    DASHBOARD_PORT = 8080
    DASHBOARD_UPDATE_INTERVAL = 2
    SHOW_CORRELATION_MATRIX = True
    SHOW_WIN_RATE_CHART = True
    SHOW_SESSION_ANALYSIS = True
    
    # Database Settings
    SUPABASE_URL = os.getenv('SUPABASE_URL', '')
    SUPABASE_KEY = os.getenv('SUPABASE_KEY', '')
    USE_DATABASE = bool(SUPABASE_URL and SUPABASE_KEY)
    TRACK_CONFIRMATIONS = True  # Track all confirmation scores
    
    # Alert Settings - Enhanced
    ENABLE_ALERTS = True
    ALERT_ON_LOW_WIN_RATE = True
    ALERT_ON_CORRELATION_CONFLICT = True
    ALERT_ON_CONSECUTIVE_LOSSES = True
    ALERT_EMAIL = os.getenv('ALERT_EMAIL', '')
    ALERT_WEBHOOK = os.getenv('ALERT_WEBHOOK', '')
    
    # Backtesting
    BACKTEST_START_DATE = '2024-01-01'
    BACKTEST_END_DATE = '2024-12-31'
    BACKTEST_INITIAL_BALANCE = 100000
    BACKTEST_SPREAD_MODEL = 'variable'  # realistic spreads
    
    @classmethod
    def get_session_quality(cls, hour: int) -> float:
        """Get quality score for current session"""
        # London/NY overlap (8-12 ET)
        if 8 <= hour < 12:
            return cls.SESSION_WEIGHTS['london_ny_overlap']
        # London session (3-12 ET)
        elif 3 <= hour < 12:
            return cls.SESSION_WEIGHTS['london']
        # NY session (8-17 ET)
        elif 8 <= hour < 17:
            return cls.SESSION_WEIGHTS['newyork']
        # Asian session (19-3 ET)
        elif hour >= 19 or hour < 3:
            return cls.SESSION_WEIGHTS['asian']
        else:
            return cls.SESSION_WEIGHTS['other']
    
    @classmethod
    def check_correlation(cls, symbol1: str, symbol2: str) -> Tuple[float, str]:
        """Check correlation between two symbols"""
        # Check positive correlations
        for pair1, pair2, corr in cls.FOREX_CORRELATIONS['positive']:
            if (symbol1 == pair1 and symbol2 == pair2) or \
               (symbol1 == pair2 and symbol2 == pair1):
                return corr, 'positive'
        
        # Check negative correlations
        for pair1, pair2, corr in cls.FOREX_CORRELATIONS['negative']:
            if (symbol1 == pair1 and symbol2 == pair2) or \
               (symbol1 == pair2 and symbol2 == pair1):
                return corr, 'negative'
        
        return 0.0, 'none'
    
    @classmethod
    def validate(cls):
        """Validate configuration settings"""
        errors = []
        
        if cls.MIN_CONFIRMATIONS < 7:
            errors.append("MIN_CONFIRMATIONS should be at least 7 for 80%+ win rate")
        
        if cls.MIN_CONFIDENCE < 0.80:
            errors.append("MIN_CONFIDENCE should be at least 0.80")
        
        if cls.RISK_PER_TRADE > 1.0:
            errors.append("RISK_PER_TRADE should not exceed 1% for high win rate")
        
        if cls.MAX_POSITIONS > 3:
            errors.append("MAX_POSITIONS should not exceed 3 for better control")
        
        if not cls.SYMBOLS:
            errors.append("At least one symbol must be configured")
        
        if errors:
            raise ValueError(f"Configuration errors: {', '.join(errors)}")
        
        return True


# Development configuration - 80%+ Win Rate
class DevelopmentConfig(Config):
    """Development settings for 80%+ win rate testing"""
    IBKR_PORT = 7497  # Paper trading
    LOG_LEVEL = 'DEBUG'
    RISK_PER_TRADE = 0.25  # Even lower for testing
    MIN_CONFIRMATIONS = 7  # Slightly relaxed for testing
    

# Production configuration - 80%+ Win Rate
class ProductionConfig(Config):
    """Production settings for 80%+ win rate"""
    IBKR_PORT = 7496  # Live trading
    LOG_LEVEL = 'INFO'
    MIN_CONFIRMATIONS = 8  # Strict requirements
    MIN_CONFIDENCE = 0.85
    ENABLE_ALERTS = True
    

# Test configuration
class TestConfig(Config):
    """Test settings"""
    SYMBOLS = ['EUR.USD', 'GBP.USD']
    RISK_PER_TRADE = 0.1
    MAX_POSITIONS = 1
    MIN_CONFIRMATIONS = 6
    

def get_config(env: str = 'development') -> Config:
    """Get configuration based on environment"""
    configs = {
        'development': DevelopmentConfig,
        'production': ProductionConfig,
        'test': TestConfig
    }
    
    config_class = configs.get(env, DevelopmentConfig)
    config = config_class()
    config.validate()
    
    return config
