"""
IEMSS Elite FOREX 5-Minute Scalping Engine
==========================================
Institutional-grade scalping strategy combining multiple timeframe analysis,
advanced technical indicators, and machine learning-enhanced signals.

Author: IEMSS Trading Desk
Version: 1.0.0
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import talib
from typing import Dict, List, Tuple, Optional
import asyncio
from dataclasses import dataclass
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ScalpingSignal:
    """Data class for scalping trade signals"""
    timestamp: datetime
    symbol: str
    direction: str  # 'BUY' or 'SELL'
    entry_price: float
    stop_loss: float
    take_profit: float
    confidence: float
    indicators: Dict[str, float]
    timeframe: str = '5min'
    
    
class IEMSS5MinScalpingEngine:
    """
    Elite 5-minute scalping engine implementing institutional strategies
    """
    
    def __init__(self, symbols: List[str], risk_percent: float = 0.5):
        """
        Initialize the scalping engine
        
        Args:
            symbols: List of forex pairs to trade
            risk_percent: Risk per trade as percentage of account
        """
        self.symbols = symbols
        self.risk_percent = risk_percent
        self.active_trades = {}
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0
        }
        
        # Technical indicator parameters
        self.ema_fast = 9
        self.ema_medium = 21
        self.ema_slow = 50
        self.rsi_period = 14
        self.macd_fast = 12
        self.macd_slow = 26
        self.macd_signal = 9
        self.bb_period = 20
        self.bb_std = 2
        self.atr_period = 14
        
        # Scalping thresholds
        self.min_pip_target = 5  # Minimum pips for take profit
        self.max_spread_pips = 2  # Maximum spread allowed
        self.confidence_threshold = 0.65  # Minimum signal confidence
        
    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate all technical indicators for the strategy
        
        Args:
            df: OHLCV dataframe
            
        Returns:
            DataFrame with indicators added
        """
        # Moving averages
        df['ema_fast'] = talib.EMA(df['close'], timeperiod=self.ema_fast)
        df['ema_medium'] = talib.EMA(df['close'], timeperiod=self.ema_medium)
        df['ema_slow'] = talib.EMA(df['close'], timeperiod=self.ema_slow)
        
        # RSI
        df['rsi'] = talib.RSI(df['close'], timeperiod=self.rsi_period)
        
        # MACD
        df['macd'], df['macd_signal'], df['macd_hist'] = talib.MACD(
            df['close'], 
            fastperiod=self.macd_fast,
            slowperiod=self.macd_slow,
            signalperiod=self.macd_signal
        )
        
        # Bollinger Bands
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = talib.BBANDS(
            df['close'],
            timeperiod=self.bb_period,
            nbdevup=self.bb_std,
            nbdevdn=self.bb_std
        )
        
        # ATR for volatility
        df['atr'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=self.atr_period)
        
        # Stochastic
        df['stoch_k'], df['stoch_d'] = talib.STOCH(
            df['high'], df['low'], df['close'],
            fastk_period=14, slowk_period=3, slowd_period=3
        )
        
        # Volume indicators
        df['volume_sma'] = df['volume'].rolling(window=20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        # Price action
        df['body_size'] = abs(df['close'] - df['open'])
        df['upper_wick'] = df['high'] - df[['close', 'open']].max(axis=1)
        df['lower_wick'] = df[['close', 'open']].min(axis=1) - df['low']
        
        # Support and Resistance
        df['resistance'] = df['high'].rolling(window=20).max()
        df['support'] = df['low'].rolling(window=20).min()
        
        return df
    
    def generate_scalping_signal(self, df: pd.DataFrame, symbol: str) -> Optional[ScalpingSignal]:
        """
        Generate scalping signals based on multiple confirmations
        
        Args:
            df: DataFrame with indicators
            symbol: Trading symbol
            
        Returns:
            ScalpingSignal if conditions met, None otherwise
        """
        if len(df) < 100:
            return None
            
        latest = df.iloc[-1]
        prev = df.iloc[-2]
        
        # Initialize signal components
        buy_signals = 0
        sell_signals = 0
        total_signals = 7  # Number of indicators we check
        
        # 1. EMA Alignment
        if latest['ema_fast'] > latest['ema_medium'] > latest['ema_slow']:
            buy_signals += 1
        elif latest['ema_fast'] < latest['ema_medium'] < latest['ema_slow']:
            sell_signals += 1
            
        # 2. RSI Conditions
        if 30 < latest['rsi'] < 50 and prev['rsi'] < latest['rsi']:
            buy_signals += 1
        elif 50 < latest['rsi'] < 70 and prev['rsi'] > latest['rsi']:
            sell_signals += 1
            
        # 3. MACD Cross
        if latest['macd'] > latest['macd_signal'] and prev['macd'] <= prev['macd_signal']:
            buy_signals += 1
        elif latest['macd'] < latest['macd_signal'] and prev['macd'] >= prev['macd_signal']:
            sell_signals += 1
            
        # 4. Bollinger Band Position
        bb_position = (latest['close'] - latest['bb_lower']) / (latest['bb_upper'] - latest['bb_lower'])
        if 0.2 < bb_position < 0.5:
            buy_signals += 1
        elif 0.5 < bb_position < 0.8:
            sell_signals += 1
            
        # 5. Stochastic
        if latest['stoch_k'] > latest['stoch_d'] and latest['stoch_k'] < 80:
            buy_signals += 1
        elif latest['stoch_k'] < latest['stoch_d'] and latest['stoch_k'] > 20:
            sell_signals += 1
            
        # 6. Volume Confirmation
        if latest['volume_ratio'] > 1.2:
            if latest['close'] > latest['open']:
                buy_signals += 1
            else:
                sell_signals += 1
                
        # 7. Price Action
        if latest['lower_wick'] > 2 * latest['body_size'] and latest['close'] > latest['open']:
            buy_signals += 1  # Hammer pattern
        elif latest['upper_wick'] > 2 * latest['body_size'] and latest['close'] < latest['open']:
            sell_signals += 1  # Shooting star pattern
            
        # Calculate confidence
        buy_confidence = buy_signals / total_signals
        sell_confidence = sell_signals / total_signals
        
        # Generate signal if confidence threshold met
        if buy_confidence >= self.confidence_threshold:
            return self._create_signal(
                symbol, 'BUY', latest, df, buy_confidence
            )
        elif sell_confidence >= self.confidence_threshold:
            return self._create_signal(
                symbol, 'SELL', latest, df, sell_confidence
            )
            
        return None
    
    def _create_signal(
        self, 
        symbol: str, 
        direction: str, 
        latest: pd.Series, 
        df: pd.DataFrame,
        confidence: float
    ) -> ScalpingSignal:
        """
        Create a detailed trading signal with stops and targets
        
        Args:
            symbol: Trading symbol
            direction: 'BUY' or 'SELL'
            latest: Latest candle data
            df: Full dataframe
            confidence: Signal confidence
            
        Returns:
            ScalpingSignal object
        """
        atr = latest['atr']
        current_price = latest['close']
        
        # Dynamic position sizing based on volatility
        if direction == 'BUY':
            stop_loss = current_price - (1.5 * atr)
            take_profit = current_price + (2.5 * atr)
        else:
            stop_loss = current_price + (1.5 * atr)
            take_profit = current_price - (2.5 * atr)
            
        # Ensure minimum pip target
        pip_value = 0.0001 if 'JPY' not in symbol else 0.01
        min_target = self.min_pip_target * pip_value
        
        if abs(take_profit - current_price) < min_target:
            if direction == 'BUY':
                take_profit = current_price + min_target
            else:
                take_profit = current_price - min_target
                
        indicators = {
            'ema_fast': latest['ema_fast'],
            'ema_medium': latest['ema_medium'],
            'ema_slow': latest['ema_slow'],
            'rsi': latest['rsi'],
            'macd': latest['macd'],
            'stoch_k': latest['stoch_k'],
            'atr': atr,
            'volume_ratio': latest['volume_ratio']
        }
        
        return ScalpingSignal(
            timestamp=datetime.now(),
            symbol=symbol,
            direction=direction,
            entry_price=current_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            confidence=confidence,
            indicators=indicators
        )
    
    def calculate_position_size(
        self, 
        signal: ScalpingSignal, 
        account_balance: float
    ) -> float:
        """
        Calculate position size based on Kelly Criterion and risk management
        
        Args:
            signal: Trading signal
            account_balance: Current account balance
            
        Returns:
            Position size in units
        """
        # Risk amount
        risk_amount = account_balance * (self.risk_percent / 100)
        
        # Pip risk
        pip_risk = abs(signal.entry_price - signal.stop_loss)
        
        # Kelly Criterion adjustment
        kelly_fraction = (signal.confidence - (1 - signal.confidence)) / 1
        kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Cap at 25%
        
        # Base position size
        position_size = risk_amount / pip_risk
        
        # Apply Kelly adjustment
        position_size *= kelly_fraction
        
        # Apply maximum position size limit (2% of account)
        max_position = account_balance * 0.02 / signal.entry_price
        position_size = min(position_size, max_position)
        
        return round(position_size, 2)
    
    def validate_market_conditions(self, df: pd.DataFrame) -> bool:
        """
        Check if market conditions are suitable for scalping
        
        Args:
            df: Market data
            
        Returns:
            True if conditions are suitable
        """
        latest = df.iloc[-1]
        
        # Check volatility
        if latest['atr'] < 0.0005:  # Too low volatility
            return False
            
        if latest['atr'] > 0.0050:  # Too high volatility
            return False
            
        # Check volume
        if latest['volume_ratio'] < 0.5:  # Low volume
            return False
            
        # Check time of day (avoid low liquidity periods)
        current_hour = datetime.now().hour
        if current_hour >= 20 or current_hour <= 2:  # Avoid Asian session for majors
            return False
            
        return True
    
    def backtest(
        self, 
        historical_data: Dict[str, pd.DataFrame], 
        initial_balance: float = 100000
    ) -> Dict:
        """
        Backtest the strategy on historical data
        
        Args:
            historical_data: Dict of symbol -> DataFrame
            initial_balance: Starting balance
            
        Returns:
            Backtest results
        """
        balance = initial_balance
        trades = []
        equity_curve = [initial_balance]
        
        for symbol, df in historical_data.items():
            df = self.calculate_indicators(df)
            
            for i in range(100, len(df)):
                window = df.iloc[:i+1]
                
                if not self.validate_market_conditions(window):
                    continue
                    
                signal = self.generate_scalping_signal(window, symbol)
                
                if signal:
                    position_size = self.calculate_position_size(signal, balance)
                    
                    # Simulate trade
                    trade_result = self._simulate_trade(
                        signal, 
                        df.iloc[i+1:min(i+20, len(df))],
                        position_size
                    )
                    
                    if trade_result:
                        trades.append(trade_result)
                        balance += trade_result['pnl']
                        equity_curve.append(balance)
                        
        # Calculate performance metrics
        results = self._calculate_backtest_metrics(
            trades, equity_curve, initial_balance
        )
        
        return results
    
    def _simulate_trade(
        self, 
        signal: ScalpingSignal, 
        future_data: pd.DataFrame,
        position_size: float
    ) -> Optional[Dict]:
        """
        Simulate a trade execution
        
        Args:
            signal: Trade signal
            future_data: Future price data
            position_size: Position size
            
        Returns:
            Trade result or None
        """
        if len(future_data) == 0:
            return None
            
        entry_price = signal.entry_price
        stop_loss = signal.stop_loss
        take_profit = signal.take_profit
        
        for idx, row in future_data.iterrows():
            # Check stop loss
            if signal.direction == 'BUY':
                if row['low'] <= stop_loss:
                    exit_price = stop_loss
                    pnl = (exit_price - entry_price) * position_size
                    return {
                        'symbol': signal.symbol,
                        'direction': signal.direction,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'pnl': pnl,
                        'position_size': position_size,
                        'result': 'LOSS',
                        'duration': idx
                    }
                elif row['high'] >= take_profit:
                    exit_price = take_profit
                    pnl = (exit_price - entry_price) * position_size
                    return {
                        'symbol': signal.symbol,
                        'direction': signal.direction,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'pnl': pnl,
                        'position_size': position_size,
                        'result': 'WIN',
                        'duration': idx
                    }
            else:  # SELL
                if row['high'] >= stop_loss:
                    exit_price = stop_loss
                    pnl = (entry_price - exit_price) * position_size
                    return {
                        'symbol': signal.symbol,
                        'direction': signal.direction,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'pnl': pnl,
                        'position_size': position_size,
                        'result': 'LOSS',
                        'duration': idx
                    }
                elif row['low'] <= take_profit:
                    exit_price = take_profit
                    pnl = (entry_price - exit_price) * position_size
                    return {
                        'symbol': signal.symbol,
                        'direction': signal.direction,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'pnl': pnl,
                        'position_size': position_size,
                        'result': 'WIN',
                        'duration': idx
                    }
                    
        return None
    
    def _calculate_backtest_metrics(
        self, 
        trades: List[Dict], 
        equity_curve: List[float],
        initial_balance: float
    ) -> Dict:
        """
        Calculate comprehensive backtest metrics
        
        Args:
            trades: List of trade results
            equity_curve: Equity curve
            initial_balance: Starting balance
            
        Returns:
            Performance metrics
        """
        if not trades:
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0,
                'total_return': 0,
                'sharpe_ratio': 0,
                'max_drawdown': 0,
                'profit_factor': 0
            }
            
        # Basic metrics
        winning_trades = [t for t in trades if t['result'] == 'WIN']
        losing_trades = [t for t in trades if t['result'] == 'LOSS']
        
        total_trades = len(trades)
        win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0
        
        # PnL metrics
        gross_profit = sum(t['pnl'] for t in winning_trades)
        gross_loss = abs(sum(t['pnl'] for t in losing_trades))
        total_pnl = sum(t['pnl'] for t in trades)
        
        # Return metrics
        total_return = (equity_curve[-1] - initial_balance) / initial_balance * 100
        
        # Risk metrics
        returns = pd.Series(equity_curve).pct_change().dropna()
        sharpe_ratio = np.sqrt(252) * returns.mean() / returns.std() if returns.std() > 0 else 0
        
        # Drawdown
        peak = pd.Series(equity_curve).expanding(min_periods=1).max()
        drawdown = (pd.Series(equity_curve) - peak) / peak
        max_drawdown = drawdown.min() * 100
        
        # Profit factor
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Average trade metrics
        avg_win = gross_profit / len(winning_trades) if winning_trades else 0
        avg_loss = gross_loss / len(losing_trades) if losing_trades else 0
        
        return {
            'total_trades': total_trades,
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': win_rate * 100,
            'total_return': total_return,
            'total_pnl': total_pnl,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'final_balance': equity_curve[-1]
        }
    
    async def run_live(self, data_provider, trade_executor):
        """
        Run the strategy in live/paper trading mode
        
        Args:
            data_provider: Real-time data provider
            trade_executor: Trade execution interface
        """
        logger.info("Starting IEMSS Elite 5-Minute Scalping Strategy")
        
        while True:
            try:
                for symbol in self.symbols:
                    # Get latest data
                    df = await data_provider.get_latest_bars(symbol, '5min', 100)
                    
                    if df is None or len(df) < 100:
                        continue
                        
                    # Calculate indicators
                    df = self.calculate_indicators(df)
                    
                    # Check market conditions
                    if not self.validate_market_conditions(df):
                        logger.info(f"Market conditions not suitable for {symbol}")
                        continue
                        
                    # Generate signal
                    signal = self.generate_scalping_signal(df, symbol)
                    
                    if signal:
                        logger.info(f"Signal generated for {symbol}: {signal.direction} @ {signal.entry_price}")
                        
                        # Get account info
                        account_info = await trade_executor.get_account_info()
                        balance = account_info['net_liquidation']
                        
                        # Calculate position size
                        position_size = self.calculate_position_size(signal, balance)
                        
                        # Execute trade
                        order_result = await trade_executor.place_order(
                            symbol=signal.symbol,
                            action=signal.direction,
                            quantity=position_size,
                            order_type='LMT',
                            limit_price=signal.entry_price
                        )
                        
                        if order_result['status'] == 'success':
                            # Place stop loss and take profit orders
                            await self._place_bracket_orders(
                                trade_executor,
                                signal,
                                position_size,
                                order_result['order_id']
                            )
                            
                            # Update performance tracking
                            self.active_trades[symbol] = {
                                'signal': signal,
                                'position_size': position_size,
                                'order_id': order_result['order_id']
                            }
                            
                # Sleep for next candle
                await asyncio.sleep(300)  # 5 minutes
                
            except Exception as e:
                logger.error(f"Error in live trading loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
                
    async def _place_bracket_orders(
        self, 
        trade_executor,
        signal: ScalpingSignal,
        position_size: float,
        parent_order_id: int
    ):
        """
        Place stop loss and take profit orders
        
        Args:
            trade_executor: Trade execution interface
            signal: Trade signal
            position_size: Position size
            parent_order_id: Parent order ID
        """
        # Stop loss order
        stop_action = 'SELL' if signal.direction == 'BUY' else 'BUY'
        await trade_executor.place_order(
            symbol=signal.symbol,
            action=stop_action,
            quantity=position_size,
            order_type='STP',
            stop_price=signal.stop_loss,
            parent_id=parent_order_id
        )
        
        # Take profit order
        await trade_executor.place_order(
            symbol=signal.symbol,
            action=stop_action,
            quantity=position_size,
            order_type='LMT',
            limit_price=signal.take_profit,
            parent_id=parent_order_id
        )


# Example usage
if __name__ == "__main__":
    # Initialize strategy for major forex pairs
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD']
    scalping_engine = IEMSS5MinScalpingEngine(symbols)
    
    # Run backtesting example
    # historical_data = load_historical_data(symbols, '5min')
    # results = scalping_engine.backtest(historical_data)
    # print("Backtest Results:", results)
