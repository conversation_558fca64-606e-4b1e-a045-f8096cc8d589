"""
IEMSS Elite FOREX 5-Minute Scalping - Main Application
=====================================================
Main entry point for the IEMSS Elite FOREX 5-minute scalping strategy.
Integrates with Interactive Brokers for live trading.

Author: IEMSS Trading Desk
Version: 1.0.0
"""

import asyncio
import sys
import os
import logging
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from typing import Dict, List, Optional
import json

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scalping_engine import IEMSS5MinScalpingEngine, ScalpingSignal
from config import Config
from trade_executor import IBKRTradeExecutor
from data_provider import IBKRDataProvider
from risk_manager import RiskManager
from performance_tracker import PerformanceTracker
from dashboard_server import DashboardServer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scalping_strategy.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class IEMSS5MinScalpingApp:
    """
    Main application class for IEMSS Elite 5-minute scalping
    """
    
    def __init__(self, config: Config):
        """
        Initialize the scalping application
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.engine = IEMSS5MinScalpingEngine(
            symbols=config.SYMBOLS,
            risk_percent=config.RISK_PER_TRADE
        )
        self.trade_executor = IBKRTradeExecutor(config)
        self.data_provider = IBKRDataProvider(config)
        self.risk_manager = RiskManager(config)
        self.performance_tracker = PerformanceTracker()
        self.dashboard = DashboardServer(port=config.DASHBOARD_PORT)
        
        self.is_running = False
        self.active_positions = {}
        self.daily_stats = {
            'trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'pnl': 0.0,
            'start_balance': 0.0
        }
        
    async def initialize(self):
        """
        Initialize all components and connections
        """
        logger.info("Initializing IEMSS Elite 5-Minute Scalping System...")
        
        try:
            # Connect to IBKR
            await self.trade_executor.connect()
            await self.data_provider.connect()
            
            # Get initial account info
            account_info = await self.trade_executor.get_account_info()
            self.daily_stats['start_balance'] = account_info['net_liquidation']
            
            # Initialize risk manager
            self.risk_manager.set_account_balance(account_info['net_liquidation'])
            
            # Start dashboard
            await self.dashboard.start()
            
            # Subscribe to market data
            for symbol in self.config.SYMBOLS:
                await self.data_provider.subscribe_market_data(symbol)
                
            logger.info("Initialization complete. System ready for trading.")
            
        except Exception as e:
            logger.error(f"Initialization failed: {e}")
            raise
            
    async def run(self):
        """
        Main trading loop
        """
        self.is_running = True
        logger.info("Starting main trading loop...")
        
        # Create tasks for different components
        tasks = [
            asyncio.create_task(self._trading_loop()),
            asyncio.create_task(self._monitor_positions()),
            asyncio.create_task(self._update_dashboard()),
            asyncio.create_task(self._risk_monitoring())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except KeyboardInterrupt:
            logger.info("Shutting down...")
            self.is_running = False
            for task in tasks:
                task.cancel()
                
    async def _trading_loop(self):
        """
        Main trading logic loop
        """
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # Check if market is open
                if not self._is_market_open(current_time):
                    logger.info("Market is closed. Waiting...")
                    await asyncio.sleep(60)
                    continue
                    
                # Check daily limits
                if self.risk_manager.daily_loss_limit_reached(self.daily_stats['pnl']):
                    logger.warning("Daily loss limit reached. No new trades today.")
                    await asyncio.sleep(300)
                    continue
                    
                # Process each symbol
                for symbol in self.config.SYMBOLS:
                    await self._process_symbol(symbol)
                    
                # Wait for next 5-minute candle
                next_candle = self._get_next_candle_time()
                wait_seconds = (next_candle - datetime.now()).total_seconds()
                await asyncio.sleep(max(1, wait_seconds))
                
            except Exception as e:
                logger.error(f"Error in trading loop: {e}", exc_info=True)
                await asyncio.sleep(60)
                
    async def _process_symbol(self, symbol: str):
        """
        Process trading logic for a single symbol
        
        Args:
            symbol: Trading symbol
        """
        try:
            # Check if we already have a position
            if symbol in self.active_positions:
                return
                
            # Get latest market data
            df = await self.data_provider.get_latest_bars(symbol, '5min', 100)
            if df is None or len(df) < 100:
                return
                
            # Calculate indicators
            df = self.engine.calculate_indicators(df)
            
            # Validate market conditions
            if not self.engine.validate_market_conditions(df):
                logger.debug(f"Market conditions not suitable for {symbol}")
                return
                
            # Generate signal
            signal = self.engine.generate_scalping_signal(df, symbol)
            
            if signal and signal.confidence >= self.config.MIN_CONFIDENCE:
                await self._execute_trade(signal)
                
        except Exception as e:
            logger.error(f"Error processing {symbol}: {e}")
            
    async def _execute_trade(self, signal: ScalpingSignal):
        """
        Execute a trade based on signal
        
        Args:
            signal: Trading signal
        """
        try:
            # Get current account info
            account_info = await self.trade_executor.get_account_info()
            balance = account_info['net_liquidation']
            
            # Check risk limits
            if not self.risk_manager.can_take_position(signal, balance):
                logger.warning(f"Risk limits prevent trading {signal.symbol}")
                return
                
            # Calculate position size
            position_size = self.engine.calculate_position_size(signal, balance)
            
            # Validate position size
            if position_size < self.config.MIN_POSITION_SIZE:
                logger.warning(f"Position size too small for {signal.symbol}")
                return
                
            # Log trade attempt
            logger.info(f"""
            Trade Signal Generated:
            Symbol: {signal.symbol}
            Direction: {signal.direction}
            Entry: {signal.entry_price}
            Stop Loss: {signal.stop_loss}
            Take Profit: {signal.take_profit}
            Confidence: {signal.confidence:.2%}
            Position Size: {position_size}
            """)
            
            # Place the order
            order_result = await self.trade_executor.place_bracket_order(
                symbol=signal.symbol,
                action=signal.direction,
                quantity=position_size,
                entry_price=signal.entry_price,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit
            )
            
            if order_result['status'] == 'success':
                # Track the position
                self.active_positions[signal.symbol] = {
                    'signal': signal,
                    'position_size': position_size,
                    'order_id': order_result['order_id'],
                    'entry_time': datetime.now(),
                    'status': 'PENDING'
                }
                
                # Update daily stats
                self.daily_stats['trades'] += 1
                
                # Log to performance tracker
                self.performance_tracker.log_trade_entry(signal, position_size)
                
                logger.info(f"Order placed successfully for {signal.symbol}")
            else:
                logger.error(f"Failed to place order: {order_result['message']}")
                
        except Exception as e:
            logger.error(f"Error executing trade: {e}", exc_info=True)
            
    async def _monitor_positions(self):
        """
        Monitor active positions and update status
        """
        while self.is_running:
            try:
                for symbol, position in list(self.active_positions.items()):
                    # Get position status from IBKR
                    status = await self.trade_executor.get_position_status(
                        symbol, position['order_id']
                    )
                    
                    if status['filled']:
                        if position['status'] == 'PENDING':
                            position['status'] = 'ACTIVE'
                            position['fill_price'] = status['fill_price']
                            logger.info(f"Position filled for {symbol} at {status['fill_price']}")
                            
                    if status['closed']:
                        # Calculate P&L
                        exit_price = status['exit_price']
                        entry_price = position['fill_price']
                        position_size = position['position_size']
                        
                        if position['signal'].direction == 'BUY':
                            pnl = (exit_price - entry_price) * position_size
                        else:
                            pnl = (entry_price - exit_price) * position_size
                            
                        # Update stats
                        self.daily_stats['pnl'] += pnl
                        if pnl > 0:
                            self.daily_stats['winning_trades'] += 1
                        else:
                            self.daily_stats['losing_trades'] += 1
                            
                        # Log to performance tracker
                        self.performance_tracker.log_trade_exit(
                            symbol, exit_price, pnl
                        )
                        
                        # Remove from active positions
                        del self.active_positions[symbol]
                        
                        logger.info(f"Position closed for {symbol}. P&L: ${pnl:.2f}")
                        
                await asyncio.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                logger.error(f"Error monitoring positions: {e}")
                await asyncio.sleep(5)
                
    async def _update_dashboard(self):
        """
        Update dashboard with real-time data
        """
        while self.is_running:
            try:
                # Get account info
                account_info = await self.trade_executor.get_account_info()
                
                # Calculate metrics
                current_balance = account_info['net_liquidation']
                daily_return = ((current_balance - self.daily_stats['start_balance']) / 
                               self.daily_stats['start_balance'] * 100)
                
                win_rate = (self.daily_stats['winning_trades'] / 
                           self.daily_stats['trades'] * 100 
                           if self.daily_stats['trades'] > 0 else 0)
                
                # Prepare dashboard data
                dashboard_data = {
                    'timestamp': datetime.now().isoformat(),
                    'account': {
                        'balance': current_balance,
                        'daily_pnl': self.daily_stats['pnl'],
                        'daily_return': daily_return,
                        'buying_power': account_info['buying_power']
                    },
                    'performance': {
                        'total_trades': self.daily_stats['trades'],
                        'winning_trades': self.daily_stats['winning_trades'],
                        'losing_trades': self.daily_stats['losing_trades'],
                        'win_rate': win_rate,
                        'active_positions': len(self.active_positions)
                    },
                    'positions': [
                        {
                            'symbol': symbol,
                            'direction': pos['signal'].direction,
                            'entry_price': pos.get('fill_price', pos['signal'].entry_price),
                            'current_price': await self._get_current_price(symbol),
                            'unrealized_pnl': await self._calculate_unrealized_pnl(symbol, pos),
                            'status': pos['status']
                        }
                        for symbol, pos in self.active_positions.items()
                    ]
                }
                
                # Update dashboard
                await self.dashboard.update_data(dashboard_data)
                
                await asyncio.sleep(2)  # Update every 2 seconds
                
            except Exception as e:
                logger.error(f"Error updating dashboard: {e}")
                await asyncio.sleep(5)
                
    async def _risk_monitoring(self):
        """
        Continuous risk monitoring
        """
        while self.is_running:
            try:
                # Check account health
                account_info = await self.trade_executor.get_account_info()
                
                # Check margin usage
                margin_used = account_info.get('margin_used', 0)
                margin_available = account_info.get('margin_available', 0)
                
                if margin_available > 0:
                    margin_usage = margin_used / (margin_used + margin_available) * 100
                    if margin_usage > 80:
                        logger.warning(f"High margin usage: {margin_usage:.1f}%")
                        
                # Check drawdown
                current_balance = account_info['net_liquidation']
                drawdown = ((self.daily_stats['start_balance'] - current_balance) / 
                           self.daily_stats['start_balance'] * 100)
                
                if drawdown > self.config.MAX_DAILY_DRAWDOWN:
                    logger.error(f"Maximum drawdown exceeded: {drawdown:.1f}%")
                    await self._close_all_positions()
                    
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"Error in risk monitoring: {e}")
                await asyncio.sleep(10)
                
    async def _close_all_positions(self):
        """
        Emergency close all positions
        """
        logger.warning("Closing all positions...")
        
        for symbol, position in self.active_positions.items():
            try:
                await self.trade_executor.close_position(
                    symbol, position['position_size']
                )
            except Exception as e:
                logger.error(f"Error closing position for {symbol}: {e}")
                
    def _is_market_open(self, current_time: datetime) -> bool:
        """
        Check if forex market is open
        
        Args:
            current_time: Current datetime
            
        Returns:
            True if market is open
        """
        # Forex market is open from Sunday 5 PM ET to Friday 5 PM ET
        weekday = current_time.weekday()
        hour = current_time.hour
        
        # Market closed on Saturday
        if weekday == 5:
            return False
            
        # Market opens Sunday 5 PM ET
        if weekday == 6 and hour < 17:
            return False
            
        # Market closes Friday 5 PM ET
        if weekday == 4 and hour >= 17:
            return False
            
        return True
        
    def _get_next_candle_time(self) -> datetime:
        """
        Get the next 5-minute candle start time
        
        Returns:
            Next candle datetime
        """
        now = datetime.now()
        minutes = now.minute
        
        # Round up to next 5-minute interval
        next_minutes = ((minutes // 5) + 1) * 5
        if next_minutes >= 60:
            next_minutes = 0
            next_time = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
        else:
            next_time = now.replace(minute=next_minutes, second=0, microsecond=0)
            
        return next_time
        
    async def _get_current_price(self, symbol: str) -> float:
        """
        Get current price for a symbol
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Current price
        """
        quote = await self.data_provider.get_quote(symbol)
        return quote.get('last', 0.0)
        
    async def _calculate_unrealized_pnl(self, symbol: str, position: Dict) -> float:
        """
        Calculate unrealized P&L for a position
        
        Args:
            symbol: Trading symbol
            position: Position data
            
        Returns:
            Unrealized P&L
        """
        if position['status'] != 'ACTIVE':
            return 0.0
            
        current_price = await self._get_current_price(symbol)
        entry_price = position.get('fill_price', position['signal'].entry_price)
        position_size = position['position_size']
        
        if position['signal'].direction == 'BUY':
            return (current_price - entry_price) * position_size
        else:
            return (entry_price - current_price) * position_size
            
    async def shutdown(self):
        """
        Gracefully shutdown the application
        """
        logger.info("Shutting down IEMSS Elite 5-Minute Scalping System...")
        
        self.is_running = False
        
        # Close all positions
        if self.active_positions:
            await self._close_all_positions()
            
        # Disconnect from IBKR
        await self.trade_executor.disconnect()
        await self.data_provider.disconnect()
        
        # Stop dashboard
        await self.dashboard.stop()
        
        # Save final performance report
        self.performance_tracker.save_report()
        
        logger.info("Shutdown complete.")


async def main():
    """
    Main entry point
    """
    # Load configuration
    config = Config()
    
    # Create application
    app = IEMSS5MinScalpingApp(config)
    
    try:
        # Initialize
        await app.initialize()
        
        # Run
        await app.run()
        
    except KeyboardInterrupt:
        logger.info("Received shutdown signal...")
    except Exception as e:
        logger.error(f"Fatal error: {e}", exc_info=True)
    finally:
        await app.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
