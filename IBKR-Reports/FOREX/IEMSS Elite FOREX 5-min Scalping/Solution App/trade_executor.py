"""
IEMSS Elite FOREX 5-Minute Scalping - Trade Executor
===================================================
Handles order execution through Interactive Brokers API.

Author: IEMSS Trading Desk
Version: 1.0.0
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Optional, List
from ib_insync import IB, Contract, MarketOrder, LimitOrder, StopOrder, BracketOrder
import pandas as pd

logger = logging.getLogger(__name__)


class IBKRTradeExecutor:
    """
    Executes trades through Interactive Brokers
    """
    
    def __init__(self, config):
        """
        Initialize the trade executor
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.ib = IB()
        self.connected = False
        self.orders = {}
        self.positions = {}
        
    async def connect(self):
        """
        Connect to Interactive Brokers
        """
        try:
            await self.ib.connectAsync(
                host=self.config.IBKR_HOST,
                port=self.config.IBKR_PORT,
                clientId=self.config.IBKR_CLIENT_ID
            )
            self.connected = True
            logger.info(f"Connected to IBKR at {self.config.IBKR_HOST}:{self.config.IBKR_PORT}")
            
            # Request account updates
            self.ib.reqAccountUpdates()
            
        except Exception as e:
            logger.error(f"Failed to connect to IBKR: {e}")
            raise
            
    async def disconnect(self):
        """
        Disconnect from Interactive Brokers
        """
        if self.connected:
            self.ib.disconnect()
            self.connected = False
            logger.info("Disconnected from IBKR")
            
    def create_forex_contract(self, symbol: str) -> Contract:
        """
        Create a forex contract
        
        Args:
            symbol: Trading symbol (e.g., 'EUR.USD')
            
        Returns:
            IB Contract object
        """
        pair = symbol.replace('.', '')
        base = pair[:3]
        quote = pair[3:]
        
        contract = Contract()
        contract.symbol = base
        contract.secType = 'CASH'
        contract.currency = quote
        contract.exchange = 'IDEALPRO'
        
        return contract
        
    async def get_account_info(self) -> Dict:
        """
        Get account information
        
        Returns:
            Account information dictionary
        """
        if not self.connected:
            raise Exception("Not connected to IBKR")
            
        account_values = self.ib.accountValues()
        account_info = {
            'net_liquidation': 0.0,
            'buying_power': 0.0,
            'cash_balance': 0.0,
            'margin_used': 0.0,
            'margin_available': 0.0
        }
        
        for av in account_values:
            if av.tag == 'NetLiquidation':
                account_info['net_liquidation'] = float(av.value)
            elif av.tag == 'BuyingPower':
                account_info['buying_power'] = float(av.value)
            elif av.tag == 'TotalCashBalance':
                account_info['cash_balance'] = float(av.value)
            elif av.tag == 'MaintMarginReq':
                account_info['margin_used'] = float(av.value)
            elif av.tag == 'AvailableFunds':
                account_info['margin_available'] = float(av.value)
                
        return account_info
        
    async def place_bracket_order(
        self,
        symbol: str,
        action: str,
        quantity: int,
        entry_price: float,
        stop_loss: float,
        take_profit: float
    ) -> Dict:
        """
        Place a bracket order (entry + stop loss + take profit)
        
        Args:
            symbol: Trading symbol
            action: 'BUY' or 'SELL'
            quantity: Position size
            entry_price: Entry limit price
            stop_loss: Stop loss price
            take_profit: Take profit price
            
        Returns:
            Order result dictionary
        """
        try:
            contract = self.create_forex_contract(symbol)
            
            # Create bracket order
            bracket = BracketOrder(
                action=action,
                quantity=quantity,
                limitPrice=entry_price,
                stopLossPrice=stop_loss,
                takeProfitPrice=take_profit
            )
            
            # Place the order
            trades = []
            for order in bracket:
                trade = self.ib.placeOrder(contract, order)
                trades.append(trade)
                
            # Wait for parent order to be submitted
            parent_trade = trades[0]
            await asyncio.sleep(0.5)
            
            # Store order info
            order_id = parent_trade.order.orderId
            self.orders[order_id] = {
                'symbol': symbol,
                'trades': trades,
                'status': 'SUBMITTED',
                'timestamp': datetime.now()
            }
            
            return {
                'status': 'success',
                'order_id': order_id,
                'message': f"Bracket order placed for {symbol}"
            }
            
        except Exception as e:
            logger.error(f"Failed to place bracket order: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
            
    async def place_order(
        self,
        symbol: str,
        action: str,
        quantity: int,
        order_type: str = 'LMT',
        limit_price: Optional[float] = None,
        stop_price: Optional[float] = None
    ) -> Dict:
        """
        Place a single order
        
        Args:
            symbol: Trading symbol
            action: 'BUY' or 'SELL'
            quantity: Position size
            order_type: 'MKT', 'LMT', or 'STP'
            limit_price: Limit price for limit orders
            stop_price: Stop price for stop orders
            
        Returns:
            Order result dictionary
        """
        try:
            contract = self.create_forex_contract(symbol)
            
            # Create order based on type
            if order_type == 'MKT':
                order = MarketOrder(action, quantity)
            elif order_type == 'LMT':
                if limit_price is None:
                    raise ValueError("Limit price required for limit orders")
                order = LimitOrder(action, quantity, limit_price)
            elif order_type == 'STP':
                if stop_price is None:
                    raise ValueError("Stop price required for stop orders")
                order = StopOrder(action, quantity, stop_price)
            else:
                raise ValueError(f"Unknown order type: {order_type}")
                
            # Place the order
            trade = self.ib.placeOrder(contract, order)
            
            # Store order info
            order_id = trade.order.orderId
            self.orders[order_id] = {
                'symbol': symbol,
                'trade': trade,
                'status': 'SUBMITTED',
                'timestamp': datetime.now()
            }
            
            return {
                'status': 'success',
                'order_id': order_id,
                'message': f"Order placed for {symbol}"
            }
            
        except Exception as e:
            logger.error(f"Failed to place order: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
            
    async def cancel_order(self, order_id: int) -> Dict:
        """
        Cancel an order
        
        Args:
            order_id: Order ID to cancel
            
        Returns:
            Cancellation result
        """
        try:
            if order_id not in self.orders:
                return {
                    'status': 'error',
                    'message': 'Order not found'
                }
                
            order_info = self.orders[order_id]
            
            if 'trades' in order_info:  # Bracket order
                for trade in order_info['trades']:
                    self.ib.cancelOrder(trade.order)
            else:  # Single order
                self.ib.cancelOrder(order_info['trade'].order)
                
            return {
                'status': 'success',
                'message': f"Order {order_id} cancelled"
            }
            
        except Exception as e:
            logger.error(f"Failed to cancel order: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
            
    async def get_position_status(self, symbol: str, order_id: int) -> Dict:
        """
        Get status of a position
        
        Args:
            symbol: Trading symbol
            order_id: Order ID
            
        Returns:
            Position status dictionary
        """
        try:
            if order_id not in self.orders:
                return {
                    'filled': False,
                    'closed': True,
                    'message': 'Order not found'
                }
                
            order_info = self.orders[order_id]
            
            # Check if it's a bracket order
            if 'trades' in order_info:
                parent_trade = order_info['trades'][0]
                
                # Check if parent order is filled
                filled = parent_trade.orderStatus.status == 'Filled'
                fill_price = parent_trade.orderStatus.avgFillPrice if filled else None
                
                # Check if position is closed (stop or target hit)
                closed = False
                exit_price = None
                
                for trade in order_info['trades'][1:]:  # Check child orders
                    if trade.orderStatus.status == 'Filled':
                        closed = True
                        exit_price = trade.orderStatus.avgFillPrice
                        break
                        
                return {
                    'filled': filled,
                    'fill_price': fill_price,
                    'closed': closed,
                    'exit_price': exit_price,
                    'status': parent_trade.orderStatus.status
                }
            else:
                # Single order
                trade = order_info['trade']
                filled = trade.orderStatus.status == 'Filled'
                
                return {
                    'filled': filled,
                    'fill_price': trade.orderStatus.avgFillPrice if filled else None,
                    'closed': filled,  # Single orders close when filled
                    'status': trade.orderStatus.status
                }
                
        except Exception as e:
            logger.error(f"Failed to get position status: {e}")
            return {
                'filled': False,
                'closed': True,
                'error': str(e)
            }
            
    async def close_position(self, symbol: str, quantity: int) -> Dict:
        """
        Close a position with a market order
        
        Args:
            symbol: Trading symbol
            quantity: Position size to close
            
        Returns:
            Order result
        """
        try:
            # Get current position
            positions = self.ib.positions()
            position = None
            
            for pos in positions:
                if pos.contract.symbol == symbol[:3]:
                    position = pos
                    break
                    
            if position is None:
                return {
                    'status': 'error',
                    'message': f"No position found for {symbol}"
                }
                
            # Determine action (opposite of current position)
            action = 'SELL' if position.position > 0 else 'BUY'
            
            # Place market order to close
            return await self.place_order(
                symbol=symbol,
                action=action,
                quantity=abs(quantity),
                order_type='MKT'
            )
            
        except Exception as e:
            logger.error(f"Failed to close position: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
            
    async def get_open_orders(self) -> List[Dict]:
        """
        Get all open orders
        
        Returns:
            List of open orders
        """
        try:
            open_orders = []
            trades = self.ib.openTrades()
            
            for trade in trades:
                order_info = {
                    'order_id': trade.order.orderId,
                    'symbol': trade.contract.symbol + '.' + trade.contract.currency,
                    'action': trade.order.action,
                    'quantity': trade.order.totalQuantity,
                    'order_type': trade.order.orderType,
                    'status': trade.orderStatus.status,
                    'filled': trade.orderStatus.filled,
                    'remaining': trade.orderStatus.remaining
                }
                
                if trade.order.orderType == 'LMT':
                    order_info['limit_price'] = trade.order.lmtPrice
                elif trade.order.orderType == 'STP':
                    order_info['stop_price'] = trade.order.auxPrice
                    
                open_orders.append(order_info)
                
            return open_orders
            
        except Exception as e:
            logger.error(f"Failed to get open orders: {e}")
            return []
            
    async def get_positions(self) -> List[Dict]:
        """
        Get all current positions
        
        Returns:
            List of positions
        """
        try:
            positions = []
            ib_positions = self.ib.positions()
            
            for pos in ib_positions:
                if pos.contract.secType == 'CASH':  # Forex positions only
                    position_info = {
                        'symbol': pos.contract.symbol + '.' + pos.contract.currency,
                        'position': pos.position,
                        'avg_cost': pos.avgCost,
                        'market_value': pos.marketValue,
                        'unrealized_pnl': pos.unrealizedPNL,
                        'realized_pnl': pos.realizedPNL
                    }
                    positions.append(position_info)
                    
            return positions
            
        except Exception as e:
            logger.error(f"Failed to get positions: {e}")
            return []
            
    def get_contract_details(self, symbol: str) -> Optional[Dict]:
        """
        Get contract details for a symbol
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Contract details or None
        """
        try:
            contract = self.create_forex_contract(symbol)
            details = self.ib.reqContractDetails(contract)
            
            if details:
                detail = details[0]
                return {
                    'symbol': symbol,
                    'min_tick': detail.minTick,
                    'trading_hours': detail.tradingHours,
                    'liquid_hours': detail.liquidHours,
                    'contract_month': detail.contractMonth
                }
                
            return None
            
        except Exception as e:
            logger.error(f"Failed to get contract details: {e}")
            return None
