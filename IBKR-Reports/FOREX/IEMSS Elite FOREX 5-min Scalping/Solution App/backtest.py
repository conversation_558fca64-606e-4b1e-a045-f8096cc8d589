"""
IEMSS Elite FOREX 5-Minute Scalping - Backtesting Engine
========================================================
Comprehensive backtesting with IBKR historical data integration.

Author: IEMSS Trading Desk
Version: 1.0.0
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import asyncio
import logging
from typing import Dict, List, Tuple
import json
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

from scalping_engine import IEMSS5MinScalpingEngine
from config import Config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BacktestEngine:
    """
    Backtesting engine for IEMSS Elite 5-minute scalping strategy
    """
    
    def __init__(self, config: Config):
        """
        Initialize backtesting engine
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.engine = IEMSS5MinScalpingEngine(
            symbols=config.SYMBOLS,
            risk_percent=config.RISK_PER_TRADE
        )
        self.results = {}
        
    def prepare_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        Prepare historical data for backtesting
        
        Args:
            symbol: Trading symbol
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            Prepared DataFrame
        """
        # For demonstration, create synthetic data
        # In production, this would fetch from IBKR
        date_range = pd.date_range(
            start=start_date, 
            end=end_date, 
            freq='5min'
        )
        
        # Filter for forex trading hours (Sunday 5 PM - Friday 5 PM ET)
        date_range = [d for d in date_range if self._is_forex_trading_time(d)]
        
        # Generate realistic forex data
        np.random.seed(42)  # For reproducibility
        
        # Base price for the symbol
        base_prices = {
            'EUR.USD': 1.0800,
            'GBP.USD': 1.2500,
            'USD.JPY': 150.00,
            'AUD.USD': 0.6500,
            'USD.CHF': 0.9000,
            'NZD.USD': 0.6000,
            'USD.CAD': 1.3500,
            'EUR.GBP': 0.8640
        }
        
        base_price = base_prices.get(symbol, 1.0000)
        
        # Generate price movements
        returns = np.random.normal(0, 0.0002, len(date_range))
        price_series = base_price * np.exp(np.cumsum(returns))
        
        # Add realistic OHLCV data
        df = pd.DataFrame(index=date_range)
        df['close'] = price_series
        
        # Generate OHLC from close
        df['open'] = df['close'].shift(1).fillna(df['close'].iloc[0])
        df['high'] = df[['open', 'close']].max(axis=1) * (1 + np.random.uniform(0, 0.0001, len(df)))
        df['low'] = df[['open', 'close']].min(axis=1) * (1 - np.random.uniform(0, 0.0001, len(df)))
        
        # Generate volume (higher during major sessions)
        df['volume'] = self._generate_volume(df.index)
        
        return df.reset_index().rename(columns={'index': 'timestamp'})
    
    def _is_forex_trading_time(self, dt: datetime) -> bool:
        """
        Check if datetime is during forex trading hours
        
        Args:
            dt: Datetime to check
            
        Returns:
            True if trading hours
        """
        # Forex trades Sunday 5 PM ET to Friday 5 PM ET
        weekday = dt.weekday()
        
        # Saturday - market closed
        if weekday == 5:
            return False
            
        # Sunday - only after 5 PM ET
        if weekday == 6 and dt.hour < 17:
            return False
            
        # Friday - only before 5 PM ET
        if weekday == 4 and dt.hour >= 17:
            return False
            
        return True
    
    def _generate_volume(self, timestamps: pd.DatetimeIndex) -> np.ndarray:
        """
        Generate realistic volume based on trading sessions
        
        Args:
            timestamps: Datetime index
            
        Returns:
            Volume array
        """
        volumes = np.zeros(len(timestamps))
        
        for i, ts in enumerate(timestamps):
            hour = ts.hour
            
            # Asian session (low volume)
            if 19 <= hour or hour < 2:
                base_volume = 10000
            # London session (high volume)
            elif 2 <= hour < 11:
                base_volume = 50000
            # NY session (high volume)
            elif 8 <= hour < 17:
                base_volume = 45000
            # Off hours
            else:
                base_volume = 15000
                
            # Add randomness
            volumes[i] = base_volume * (1 + np.random.uniform(-0.3, 0.3))
            
        return volumes
    
    async def run_backtest(self) -> Dict:
        """
        Run complete backtest on all configured symbols
        
        Returns:
            Backtest results
        """
        logger.info("Starting backtest...")
        
        # Prepare data for all symbols
        historical_data = {}
        
        for symbol in self.config.SYMBOLS:
            logger.info(f"Preparing data for {symbol}...")
            df = self.prepare_data(
                symbol,
                self.config.BACKTEST_START_DATE,
                self.config.BACKTEST_END_DATE
            )
            historical_data[symbol] = df
            
        # Run backtest
        logger.info("Running strategy backtest...")
        results = self.engine.backtest(
            historical_data,
            self.config.BACKTEST_INITIAL_BALANCE
        )
        
        # Store results
        self.results = results
        
        # Generate detailed report
        self._generate_report(results)
        
        return results
    
    def _generate_report(self, results: Dict):
        """
        Generate comprehensive backtest report
        
        Args:
            results: Backtest results
        """
        logger.info("Generating backtest report...")
        
        report = f"""
# IEMSS Elite FOREX 5-Minute Scalping - Backtest Results

## Summary
- **Period**: {self.config.BACKTEST_START_DATE} to {self.config.BACKTEST_END_DATE}
- **Initial Balance**: ${self.config.BACKTEST_INITIAL_BALANCE:,.2f}
- **Final Balance**: ${results['final_balance']:,.2f}
- **Total Return**: {results['total_return']:.2f}%
- **Sharpe Ratio**: {results['sharpe_ratio']:.2f}
- **Maximum Drawdown**: {results['max_drawdown']:.2f}%

## Performance Metrics
- **Total Trades**: {results['total_trades']}
- **Winning Trades**: {results['winning_trades']}
- **Losing Trades**: {results['losing_trades']}
- **Win Rate**: {results['win_rate']:.2f}%
- **Profit Factor**: {results['profit_factor']:.2f}
- **Average Win**: ${results['avg_win']:,.2f}
- **Average Loss**: ${results['avg_loss']:,.2f}

## Risk Analysis
- **Daily Value at Risk (95%)**: ${results.get('daily_var_95', 0):,.2f}
- **Maximum Consecutive Losses**: {results.get('max_consecutive_losses', 0)}
- **Recovery Factor**: {results.get('recovery_factor', 0):.2f}

## Statistical Significance
- **Total Trading Days**: {results.get('trading_days', 0)}
- **Average Daily Return**: {results.get('avg_daily_return', 0):.2f}%
- **Return Standard Deviation**: {results.get('return_std', 0):.2f}%
- **Sortino Ratio**: {results.get('sortino_ratio', 0):.2f}

---
*Report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # Save report
        report_path = Path(__file__).parent.parent / "Results" / "Backtest_Results.md"
        with open(report_path, 'w') as f:
            f.write(report)
            
        logger.info(f"Report saved to {report_path}")
        
        # Generate charts
        self._generate_charts(results)
    
    def _generate_charts(self, results: Dict):
        """
        Generate performance visualization charts
        
        Args:
            results: Backtest results
        """
        # Set style
        plt.style.use('dark_background')
        sns.set_palette("husl")
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('IEMSS Elite FOREX 5-Min Scalping - Backtest Performance', 
                     fontsize=16, color='white')
        
        # 1. Equity Curve
        ax1 = axes[0, 0]
        if 'equity_curve' in results:
            ax1.plot(results['equity_curve'], color='#4CAF50', linewidth=2)
            ax1.fill_between(range(len(results['equity_curve'])), 
                           results['equity_curve'], 
                           alpha=0.3, color='#4CAF50')
        ax1.set_title('Equity Curve', color='white')
        ax1.set_xlabel('Trade Number')
        ax1.set_ylabel('Account Balance ($)')
        ax1.grid(True, alpha=0.3)
        
        # 2. Drawdown Chart
        ax2 = axes[0, 1]
        if 'drawdown_series' in results:
            ax2.fill_between(range(len(results['drawdown_series'])), 
                           results['drawdown_series'], 
                           color='#F44336', alpha=0.7)
        ax2.set_title('Drawdown', color='white')
        ax2.set_xlabel('Trade Number')
        ax2.set_ylabel('Drawdown (%)')
        ax2.grid(True, alpha=0.3)
        
        # 3. Win/Loss Distribution
        ax3 = axes[1, 0]
        if 'trade_returns' in results:
            wins = [r for r in results['trade_returns'] if r > 0]
            losses = [r for r in results['trade_returns'] if r < 0]
            ax3.hist([wins, losses], bins=30, label=['Wins', 'Losses'], 
                    color=['#4CAF50', '#F44336'], alpha=0.7)
        ax3.set_title('Return Distribution', color='white')
        ax3.set_xlabel('Return ($)')
        ax3.set_ylabel('Frequency')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. Monthly Returns Heatmap
        ax4 = axes[1, 1]
        if 'monthly_returns' in results:
            monthly_data = results['monthly_returns']
            im = ax4.imshow(monthly_data, cmap='RdYlGn', aspect='auto')
            plt.colorbar(im, ax=ax4)
        ax4.set_title('Monthly Returns Heatmap', color='white')
        ax4.set_xlabel('Month')
        ax4.set_ylabel('Year')
        
        # Save figure
        plt.tight_layout()
        chart_path = Path(__file__).parent.parent / "Results" / "backtest_charts.png"
        plt.savefig(chart_path, dpi=300, bbox_inches='tight', facecolor='#0a0e27')
        plt.close()
        
        logger.info(f"Charts saved to {chart_path}")


async def main():
    """
    Run backtest
    """
    # Load configuration
    config = Config()
    
    # Create backtest engine
    backtest = BacktestEngine(config)
    
    # Run backtest
    results = await backtest.run_backtest()
    
    # Print summary
    print("\n" + "="*50)
    print("BACKTEST COMPLETE")
    print("="*50)
    print(f"Total Return: {results['total_return']:.2f}%")
    print(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
    print(f"Win Rate: {results['win_rate']:.2f}%")
    print(f"Max Drawdown: {results['max_drawdown']:.2f}%")
    print("="*50)


if __name__ == "__main__":
    asyncio.run(main())
