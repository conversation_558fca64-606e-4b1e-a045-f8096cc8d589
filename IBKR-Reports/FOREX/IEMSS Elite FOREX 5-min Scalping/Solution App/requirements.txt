# IEMSS Elite FOREX 5-Minute Scalping - Requirements
# Python 3.9+ required

# Core dependencies
pandas>=1.5.0
numpy>=1.23.0
ib_insync>=0.9.70
asyncio-throttle>=1.0.2

# Technical analysis
TA-Lib>=0.4.25
scipy>=1.9.0

# Database and API
supabase>=1.0.0
python-dotenv>=0.21.0
aiohttp>=3.8.0
websockets>=11.0

# Data visualization
matplotlib>=3.6.0
seaborn>=0.12.0
plotly>=5.11.0

# Performance and monitoring
psutil>=5.9.0
prometheus-client>=0.15.0

# Logging and utilities
colorlog>=6.7.0
python-json-logger>=2.0.4
schedule>=1.1.0

# Testing
pytest>=7.2.0
pytest-asyncio>=0.20.0
pytest-cov>=4.0.0

# Development
black>=22.10.0
flake8>=6.0.0
mypy>=0.990
