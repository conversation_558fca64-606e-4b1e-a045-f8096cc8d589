"""
IEMSS Elite FOREX 5-Minute Scalping - Configuration
==================================================
Configuration settings for the scalping strategy.

Author: IEMSS Trading Desk
Version: 1.0.0
"""

import os
from typing import List


class Config:
    """
    Configuration settings for IEMSS Elite 5-Minute Scalping
    """
    
    # IBKR Connection Settings
    IBKR_HOST = os.getenv('IBKR_HOST', '127.0.0.1')
    IBKR_PORT = int(os.getenv('IBKR_PORT', '7497'))  # 7497 for paper, 7496 for live
    IBKR_CLIENT_ID = int(os.getenv('IBKR_CLIENT_ID', '1'))
    
    # Trading Symbols
    SYMBOLS: List[str] = [
        'EUR.USD',  # Euro/US Dollar
        'GBP.USD',  # British Pound/US Dollar
        'USD.JPY',  # US Dollar/Japanese Yen
        'AUD.USD',  # Australian Dollar/US Dollar
        'USD.CHF',  # US Dollar/Swiss Franc
        'NZD.USD',  # New Zealand Dollar/US Dollar
        'USD.CAD',  # US Dollar/Canadian Dollar
        'EUR.GBP',  # Euro/British Pound
    ]
    
    # Risk Management
    RISK_PER_TRADE = 0.5  # Percentage of account to risk per trade
    MAX_DAILY_LOSS = 2.0  # Maximum daily loss percentage
    MAX_DAILY_DRAWDOWN = 3.0  # Maximum drawdown percentage
    MAX_POSITIONS = 3  # Maximum concurrent positions
    MIN_POSITION_SIZE = 1000  # Minimum position size in units
    MAX_POSITION_SIZE = 100000  # Maximum position size in units
    
    # Trading Parameters
    MIN_CONFIDENCE = 0.65  # Minimum signal confidence to trade
    MIN_PIP_TARGET = 5  # Minimum pip target for trades
    MAX_SPREAD_PIPS = 2  # Maximum spread allowed
    SLIPPAGE_PIPS = 0.5  # Expected slippage
    
    # Time Settings
    TRADING_START_HOUR = 2  # Start trading at 2 AM ET (London open)
    TRADING_END_HOUR = 16  # Stop trading at 4 PM ET
    AVOID_NEWS_MINUTES = 30  # Minutes to avoid trading before/after news
    
    # Technical Indicators
    EMA_FAST = 9
    EMA_MEDIUM = 21
    EMA_SLOW = 50
    RSI_PERIOD = 14
    RSI_OVERSOLD = 30
    RSI_OVERBOUGHT = 70
    BB_PERIOD = 20
    BB_STD = 2
    ATR_PERIOD = 14
    
    # Performance Tracking
    SAVE_TRADES = True
    TRADES_FILE = 'trades_history.csv'
    PERFORMANCE_FILE = 'performance_metrics.json'
    LOG_LEVEL = 'INFO'
    
    # Dashboard Settings
    DASHBOARD_PORT = 8080
    DASHBOARD_UPDATE_INTERVAL = 2  # seconds
    
    # Backtesting
    BACKTEST_START_DATE = '2024-01-01'
    BACKTEST_END_DATE = '2024-12-31'
    BACKTEST_INITIAL_BALANCE = 100000
    
    # Database Settings (Supabase)
    SUPABASE_URL = os.getenv('SUPABASE_URL', '')
    SUPABASE_KEY = os.getenv('SUPABASE_KEY', '')
    USE_DATABASE = bool(SUPABASE_URL and SUPABASE_KEY)
    
    # Alert Settings
    ENABLE_ALERTS = True
    ALERT_EMAIL = os.getenv('ALERT_EMAIL', '')
    ALERT_WEBHOOK = os.getenv('ALERT_WEBHOOK', '')
    
    # Advanced Settings
    USE_MACHINE_LEARNING = False  # Enable ML signal enhancement
    ML_MODEL_PATH = 'models/scalping_ml_model.pkl'
    USE_SENTIMENT_ANALYSIS = True  # Enable news sentiment
    
    @classmethod
    def validate(cls):
        """
        Validate configuration settings
        """
        errors = []
        
        if cls.RISK_PER_TRADE > 2.0:
            errors.append("RISK_PER_TRADE should not exceed 2%")
            
        if cls.MAX_POSITIONS > 5:
            errors.append("MAX_POSITIONS should not exceed 5")
            
        if cls.MIN_CONFIDENCE < 0.5:
            errors.append("MIN_CONFIDENCE should be at least 0.5")
            
        if not cls.SYMBOLS:
            errors.append("At least one symbol must be configured")
            
        if errors:
            raise ValueError(f"Configuration errors: {', '.join(errors)}")
            
        return True


# Development configuration
class DevelopmentConfig(Config):
    """Development-specific settings"""
    IBKR_PORT = 7497  # Paper trading port
    LOG_LEVEL = 'DEBUG'
    RISK_PER_TRADE = 0.25  # Lower risk for testing
    

# Production configuration
class ProductionConfig(Config):
    """Production-specific settings"""
    IBKR_PORT = 7496  # Live trading port
    LOG_LEVEL = 'INFO'
    ENABLE_ALERTS = True
    

# Test configuration
class TestConfig(Config):
    """Test-specific settings"""
    SYMBOLS = ['EUR.USD', 'GBP.USD']  # Fewer symbols for testing
    RISK_PER_TRADE = 0.1
    MAX_POSITIONS = 1
    

def get_config(env: str = 'development') -> Config:
    """
    Get configuration based on environment
    
    Args:
        env: Environment name
        
    Returns:
        Configuration object
    """
    configs = {
        'development': DevelopmentConfig,
        'production': ProductionConfig,
        'test': TestConfig
    }
    
    config_class = configs.get(env, DevelopmentConfig)
    config = config_class()
    config.validate()
    
    return config
