"""
IEMSS Elite FOREX 5-Min Scalping - 80%+ Win Rate Engine
=======================================================
Core scalping engine enhanced for 80-85% win rate through
multiple confirmations and correlation analysis.

Author: IEMSS Trading Desk
Version: 3.0.0 - 80%+ Win Rate Edition
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import logging
from config_80plus import Config

logger = logging.getLogger(__name__)


@dataclass
class ScalpingSignal80Plus:
    """Enhanced scalping signal for 80%+ win rate"""
    timestamp: datetime
    symbol: str
    direction: str
    entry_price: float
    stop_loss: float
    take_profit_1: float
    take_profit_2: float
    confidence: float
    confirmations: int
    confirmation_details: Dict[str, float]
    correlation_score: float
    session_score: float
    pattern_score: float
    momentum_score: float
    volume_score: float
    trend_score: float
    sr_score: float
    structure_score: float
    risk_reward_ratio: float
    spread: float
    

class ScalpingEngine80Plus:
    """
    Enhanced 5-Minute Scalping Engine for 80%+ Win Rate
    """
    
    def __init__(self, config: Config):
        """Initialize the 80%+ win rate scalping engine"""
        self.config = config
        self.active_positions = {}
        self.daily_trades = 0
        self.consecutive_losses = 0
        self.recent_trades = []  # Track last 100 trades
        self.current_win_rate = 0.0
        
        # Track performance by session
        self.session_performance = {
            'london_ny_overlap': {'trades': 0, 'wins': 0},
            'london': {'trades': 0, 'wins': 0},
            'newyork': {'trades': 0, 'wins': 0},
            'asian': {'trades': 0, 'wins': 0}
        }
        
    def analyze_market(
        self, 
        market_data: pd.DataFrame,
        symbol: str,
        spread: float
    ) -> Optional[ScalpingSignal80Plus]:
        """
        Analyze market with 8+ confirmation system
        """
        # Pre-flight checks
        if not self._pre_trade_checks(symbol, spread):
            return None
            
        # Ensure sufficient data
        if len(market_data) < 200:
            logger.debug("Insufficient data for analysis")
            return None
            
        # Calculate all confirmation scores
        confirmation_scores = self._calculate_all_confirmations(market_data, symbol)
        
        # Count confirmations
        confirmations = sum(1 for score in confirmation_scores.values() 
                          if score >= self.config.PATTERN_SCORES.get('minimum', 0.7))
        
        # Check minimum confirmations
        if confirmations < self.config.MIN_CONFIRMATIONS:
            logger.debug(f"Insufficient confirmations: {confirmations}")
            return None
            
        # Determine direction with high confidence
        direction = self._determine_direction(market_data, confirmation_scores)
        if direction is None:
            return None
            
        # Calculate entry and exit levels
        entry_price = market_data['close'].iloc[-1]
        levels = self._calculate_entry_exit_levels(market_data, entry_price, direction)
        
        # Check risk/reward ratio
        risk = abs(entry_price - levels['stop_loss'])
        reward = abs(levels['take_profit_2'] - entry_price)
        risk_reward_ratio = reward / risk if risk > 0 else 0
        
        if risk_reward_ratio < self.config.MIN_RISK_REWARD:
            logger.debug(f"Risk/Reward too low: {risk_reward_ratio:.2f}")
            return None
            
        # Calculate composite confidence
        confidence = self._calculate_composite_confidence(
            confirmations, confirmation_scores
        )
        
        # Final confidence check
        if confidence < self.config.MIN_CONFIDENCE:
            logger.debug(f"Confidence too low: {confidence:.2%}")
            return None
            
        # Create signal
        return ScalpingSignal80Plus(
            timestamp=datetime.now(),
            symbol=symbol,
            direction=direction,
            entry_price=entry_price,
            stop_loss=levels['stop_loss'],
            take_profit_1=levels['take_profit_1'],
            take_profit_2=levels['take_profit_2'],
            confidence=confidence,
            confirmations=confirmations,
            confirmation_details=confirmation_scores,
            correlation_score=confirmation_scores['correlation'],
            session_score=confirmation_scores['session'],
            pattern_score=confirmation_scores['pattern'],
            momentum_score=confirmation_scores['momentum'],
            volume_score=confirmation_scores['volume'],
            trend_score=confirmation_scores['trend'],
            sr_score=confirmation_scores['support_resistance'],
            structure_score=confirmation_scores['structure'],
            risk_reward_ratio=risk_reward_ratio,
            spread=spread
        )
        
    def _pre_trade_checks(self, symbol: str, spread: float) -> bool:
        """Perform pre-trade checks"""
        # Check spread
        if spread > self.config.MAX_SPREAD_PIPS:
            logger.info(f"Spread too high: {spread} pips")
            return False
            
        # Check daily trade limit
        if self.daily_trades >= self.config.MAX_TRADES_PER_SESSION:
            logger.info("Daily trade limit reached")
            return False
            
        # Check concurrent positions
        if len(self.active_positions) >= self.config.MAX_POSITIONS:
            logger.info("Max concurrent positions reached")
            return False
            
        # Check consecutive losses
        if self.consecutive_losses >= self.config.CONSECUTIVE_LOSS_LIMIT:
            logger.warning("Consecutive loss limit reached - stopping trading")
            return False
            
        # Check current win rate
        if len(self.recent_trades) >= 20:  # Need minimum trades
            if self.current_win_rate < self.config.WIN_RATE_WARNING:
                logger.warning(f"Win rate below threshold: {self.current_win_rate:.1%}")
                return False
                
        # Check session quality
        hour = datetime.now().hour
        session_quality = self.config.get_session_quality(hour)
        if session_quality < 0.6:
            logger.info(f"Poor session quality: {session_quality}")
            return False
            
        # Friday afternoon check
        if datetime.now().weekday() == 4 and hour >= self.config.AVOID_FRIDAY_AFTER:
            logger.info("Avoiding Friday afternoon trading")
            return False
            
        return True
        
    def _calculate_all_confirmations(
        self, 
        market_data: pd.DataFrame,
        symbol: str
    ) -> Dict[str, float]:
        """Calculate all confirmation scores"""
        scores = {}
        
        # 1. Multi-timeframe trend alignment
        scores['trend'] = self._calculate_trend_score(market_data)
        
        # 2. Momentum confluence
        scores['momentum'] = self._calculate_momentum_score(market_data)
        
        # 3. Volume confirmation
        scores['volume'] = self._calculate_volume_score(market_data)
        
        # 4. Pattern recognition
        scores['pattern'] = self._detect_high_probability_patterns(market_data)
        
        # 5. Support/Resistance confluence
        scores['support_resistance'] = self._calculate_sr_score(market_data)
        
        # 6. Session quality
        scores['session'] = self.config.get_session_quality(datetime.now().hour)
        
        # 7. Correlation analysis
        scores['correlation'] = self._calculate_correlation_score(symbol)
        
        # 8. Market structure
        scores['structure'] = self._analyze_market_structure(market_data)
        
        return scores
        
    def _calculate_trend_score(self, df: pd.DataFrame) -> float:
        """Calculate multi-timeframe trend alignment"""
        score = 0.0
        
        # EMA alignment (9, 21, 50)
        ema_9 = df['close'].ewm(span=9).mean()
        ema_21 = df['close'].ewm(span=21).mean()
        ema_50 = df['close'].ewm(span=50).mean()
        
        # Check alignment
        if ema_9.iloc[-1] > ema_21.iloc[-1] > ema_50.iloc[-1]:
            score += 0.4  # Bullish alignment
        elif ema_9.iloc[-1] < ema_21.iloc[-1] < ema_50.iloc[-1]:
            score += 0.4  # Bearish alignment
            
        # Check trend strength
        if len(df) >= 50:
            price_change = (df['close'].iloc[-1] - ema_50.iloc[-1]) / ema_50.iloc[-1]
            trend_strength = min(abs(price_change) * 100, 0.3)
            score += trend_strength
            
        # Check trend consistency
        recent_closes = df['close'].iloc[-10:]
        if all(recent_closes.iloc[i] <= recent_closes.iloc[i+1] 
               for i in range(len(recent_closes)-1)):
            score += 0.3  # Consistent uptrend
        elif all(recent_closes.iloc[i] >= recent_closes.iloc[i+1] 
                 for i in range(len(recent_closes)-1)):
            score += 0.3  # Consistent downtrend
            
        return min(score, 1.0)
        
    def _calculate_momentum_score(self, df: pd.DataFrame) -> float:
        """Calculate momentum confluence"""
        score = 0.0
        
        # RSI (9 period for 5-min)
        rsi = self._calculate_rsi(df['close'], period=9)
        if self.config.RSI_NEUTRAL_LOW < rsi < self.config.RSI_NEUTRAL_HIGH:
            score += 0.25  # Neutral zone good for continuation
            
        # MACD
        macd_line, signal_line, histogram = self._calculate_macd(df['close'])
        if (histogram > 0 and histogram > df['close'].iloc[-2]):  # Increasing
            score += 0.25
        elif (histogram < 0 and histogram < df['close'].iloc[-2]):  # Decreasing
            score += 0.25
            
        # Stochastic
        k_percent, d_percent = self._calculate_stochastic(df)
        if 20 < k_percent < 80 and k_percent > d_percent:
            score += 0.25
        elif 20 < k_percent < 80 and k_percent < d_percent:
            score += 0.25
            
        # Price momentum
        momentum = (df['close'].iloc[-1] - df['close'].iloc[-5]) / df['close'].iloc[-5]
        if abs(momentum) > 0.001:  # Significant movement
            score += 0.25
            
        return min(score, 1.0)
        
    def _calculate_volume_score(self, df: pd.DataFrame) -> float:
        """Calculate volume confirmation"""
        if 'volume' not in df.columns:
            return 0.5  # Neutral if no volume data
            
        recent_vol = df['volume'].iloc[-5:].mean()
        avg_vol = df['volume'].iloc[-50:].mean()
        
        if recent_vol > avg_vol * self.config.MIN_VOLUME_RATIO:
            return 0.9
        elif recent_vol > avg_vol:
            return 0.7
        else:
            return 0.3
            
    def _detect_high_probability_patterns(self, df: pd.DataFrame) -> float:
        """Detect high probability patterns"""
        max_score = 0.0
        
        # Check each pattern
        if self._is_pin_bar(df.iloc[-1]):
            max_score = max(max_score, self.config.PATTERN_SCORES['pin_bar'])
            
        if len(df) >= 2 and self._is_engulfing(df.iloc[-2], df.iloc[-1]):
            max_score = max(max_score, self.config.PATTERN_SCORES['engulfing'])
            
        if self._is_double_bottom_top(df):
            max_score = max(max_score, self.config.PATTERN_SCORES['double_bottom_top'])
            
        if self._is_flag_pattern(df):
            max_score = max(max_score, self.config.PATTERN_SCORES['flag'])
            
        return max_score
        
    def _calculate_sr_score(self, df: pd.DataFrame) -> float:
        """Calculate support/resistance confluence"""
        current_price = df['close'].iloc[-1]
        
        # Recent highs and lows
        recent_high = df['high'].iloc[-20:].max()
        recent_low = df['low'].iloc[-20:].min()
        
        # Pivot points
        pivot = (df['high'].iloc[-1] + df['low'].iloc[-1] + df['close'].iloc[-1]) / 3
        r1 = 2 * pivot - df['low'].iloc[-1]
        s1 = 2 * pivot - df['high'].iloc[-1]
        
        # Check proximity to levels
        distances = [
            abs(current_price - recent_high) / current_price,
            abs(current_price - recent_low) / current_price,
            abs(current_price - pivot) / current_price,
            abs(current_price - r1) / current_price,
            abs(current_price - s1) / current_price
        ]
        
        min_distance = min(distances)
        
        if min_distance < 0.0005:  # Very close to level
            return 0.95
        elif min_distance < 0.001:
            return 0.85
        elif min_distance < 0.002:
            return 0.7
        else:
            return 0.5
            
    def _calculate_correlation_score(self, symbol: str) -> float:
        """Calculate correlation score based on active positions"""
        if not self.active_positions:
            return 1.0  # No correlation issues
            
        conflict_score = 0
        support_score = 0
        
        for pos_symbol, pos_data in self.active_positions.items():
            correlation, corr_type = self.config.check_correlation(symbol, pos_symbol)
            
            if abs(correlation) > self.config.CORRELATION_THRESHOLD:
                if corr_type == 'positive':
                    # Same direction is good, opposite is bad
                    support_score += correlation
                else:  # negative correlation
                    # Opposite direction is good (hedge)
                    support_score += abs(correlation)
                    
        # Normalize score
        total_correlations = len(self.active_positions)
        if total_correlations == 0:
            return 1.0
            
        avg_support = support_score / total_correlations
        return min(avg_support, 1.0)
        
    def _analyze_market_structure(self, df: pd.DataFrame) -> float:
        """Analyze market structure"""
        if len(df) < 20:
            return 0.5
            
        # Check for trending structure
        highs = df['high'].iloc[-15:]
        lows = df['low'].iloc[-15:]
        
        # Higher highs and higher lows (uptrend)
        hh_count = sum(highs.iloc[i] < highs.iloc[i+1] 
                      for i in range(len(highs)-1))
        hl_count = sum(lows.iloc[i] < lows.iloc[i+1] 
                      for i in range(len(lows)-1))
        
        # Lower highs and lower lows (downtrend)
        lh_count = sum(highs.iloc[i] > highs.iloc[i+1] 
                      for i in range(len(highs)-1))
        ll_count = sum(lows.iloc[i] > lows.iloc[i+1] 
                      for i in range(len(lows)-1))
        
        # Strong trend structure
        if (hh_count > 10 and hl_count > 10) or (lh_count > 10 and ll_count > 10):
            return 0.95
        elif (hh_count > 8 and hl_count > 8) or (lh_count > 8 and ll_count > 8):
            return 0.85
        elif (hh_count > 6 and hl_count > 6) or (lh_count > 6 and ll_count > 6):
            return 0.7
        else:
            return 0.5  # Ranging market
            
    def _determine_direction(
        self, 
        df: pd.DataFrame,
        scores: Dict[str, float]
    ) -> Optional[str]:
        """Determine trade direction with high confidence"""
        buy_score = 0
        sell_score = 0
        
        # Trend direction
        ema_9 = df['close'].ewm(span=9).mean()
        ema_21 = df['close'].ewm(span=21).mean()
        
        if ema_9.iloc[-1] > ema_21.iloc[-1]:
            buy_score += scores['trend']
        else:
            sell_score += scores['trend']
            
        # Momentum direction
        momentum = df['close'].iloc[-1] - df['close'].iloc[-5]
        if momentum > 0:
            buy_score += scores['momentum']
        else:
            sell_score += scores['momentum']
            
        # Pattern direction
        if scores['pattern'] > 0.8:
            # Determine based on recent price action
            if df['close'].iloc[-1] > df['open'].iloc[-1]:
                buy_score += scores['pattern']
            else:
                sell_score += scores['pattern']
                
        # Volume direction
        if 'volume' in df.columns:
            vol_ma = df['volume'].rolling(20).mean()
            if df['volume'].iloc[-1] > vol_ma.iloc[-1]:
                if df['close'].iloc[-1] > df['open'].iloc[-1]:
                    buy_score += scores['volume'] * 0.5
                else:
                    sell_score += scores['volume'] * 0.5
                    
        # Need clear direction for high win rate
        if buy_score > sell_score * 1.5:
            return 'BUY'
        elif sell_score > buy_score * 1.5:
            return 'SELL'
        else:
            return None  # No clear direction
            
    def _calculate_entry_exit_levels(
        self, 
        df: pd.DataFrame,
        entry_price: float,
        direction: str
    ) -> Dict[str, float]:
        """Calculate entry and exit levels for 5-min scalping"""
        atr = self._calculate_atr(df)
        
        # Convert ATR to pips (assuming 4 decimal places for most pairs)
        atr_pips = atr * 10000
        
        # Ensure minimum pip targets
        atr_pips = max(atr_pips, self.config.MIN_PIP_TARGET)
        
        # Calculate levels based on direction
        if direction == 'BUY':
            stop_loss = entry_price - (min(atr_pips, self.config.STOP_LOSS_PIPS) / 10000)
            take_profit_1 = entry_price + (atr_pips * 0.75 / 10000)  # 75% of ATR
            take_profit_2 = entry_price + (atr_pips * 1.5 / 10000)   # 150% of ATR
        else:
            stop_loss = entry_price + (min(atr_pips, self.config.STOP_LOSS_PIPS) / 10000)
            take_profit_1 = entry_price - (atr_pips * 0.75 / 10000)
            take_profit_2 = entry_price - (atr_pips * 1.5 / 10000)
            
        return {
            'stop_loss': stop_loss,
            'take_profit_1': take_profit_1,
            'take_profit_2': take_profit_2
        }
        
    def _calculate_composite_confidence(
        self, 
        confirmations: int,
        scores: Dict[str, float]
    ) -> float:
        """Calculate composite confidence score"""
        # Weight the scores
        weighted_score = (
            confirmations / 12.0 * 0.3 +
            scores['trend'] * 0.2 +
            scores['momentum'] * 0.15 +
            scores['pattern'] * 0.15 +
            scores['correlation'] * 0.1 +
            scores['session'] * 0.1
        )
        
        # Bonus for perfect alignment
        if confirmations >= 10:
            weighted_score *= 1.1
            
        return min(weighted_score, 1.0)
        
    def update_trade_result(self, symbol: str, profit: float, won: bool):
        """Update trade tracking and performance metrics"""
        # Update recent trades
        self.recent_trades.append({'symbol': symbol, 'profit': profit, 'won': won})
        if len(self.recent_trades) > self.config.PERFORMANCE_WINDOW:
            self.recent_trades.pop(0)
            
        # Update win rate
        if len(self.recent_trades) > 0:
            wins = sum(1 for trade in self.recent_trades if trade['won'])
            self.current_win_rate = wins / len(self.recent_trades)
            
        # Update consecutive losses
        if won:
            self.consecutive_losses = 0
        else:
            self.consecutive_losses += 1
            
        # Update session performance
        hour = datetime.now().hour
        session = self._get_current_session(hour)
        if session in self.session_performance:
            self.session_performance[session]['trades'] += 1
            if won:
                self.session_performance[session]['wins'] += 1
                
    def _get_current_session(self, hour: int) -> str:
        """Get current trading session"""
        if 8 <= hour < 12:
            return 'london_ny_overlap'
        elif 3 <= hour < 12:
            return 'london'
        elif 8 <= hour < 17:
            return 'newyork'
        else:
            return 'asian'
            
    def get_position_size_adjustment(self, base_size: float, symbol: str) -> float:
        """Calculate position size adjustments for risk management"""
        adjustment = 1.0
        
        # Correlation adjustment
        if self.active_positions:
            for pos_symbol in self.active_positions:
                correlation, _ = self.config.check_correlation(symbol, pos_symbol)
                if abs(correlation) > self.config.CORRELATION_THRESHOLD:
                    adjustment *= self.config.CORRELATION_SIZE_REDUCTION
                    
        # Losing day adjustment
        daily_pnl = sum(trade['profit'] for trade in self.recent_trades 
                       if trade['profit'] < 0)
        if daily_pnl < 0:
            adjustment *= self.config.LOSING_DAY_SIZE_REDUCTION
            
        # Friday afternoon adjustment
        if datetime.now().weekday() == 4 and datetime.now().hour >= 14:
            adjustment *= self.config.FRIDAY_SIZE_REDUCTION
            
        # Low win rate adjustment
        if self.current_win_rate < self.config.WIN_RATE_TARGET:
            adjustment *= self.config.LOW_WIN_RATE_REDUCTION
            
        return base_size * adjustment
        
    # Technical indicator calculations
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """Calculate RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi.iloc[-1]
        
    def _calculate_macd(self, prices: pd.Series) -> Tuple[float, float, float]:
        """Calculate MACD"""
        ema_fast = prices.ewm(span=self.config.MACD_FAST).mean()
        ema_slow = prices.ewm(span=self.config.MACD_SLOW).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=self.config.MACD_SIGNAL).mean()
        histogram = macd_line - signal_line
        
        return macd_line.iloc[-1], signal_line.iloc[-1], histogram.iloc[-1]
        
    def _calculate_stochastic(self, df: pd.DataFrame, period: int = 14) -> Tuple[float, float]:
        """Calculate Stochastic"""
        low_min = df['low'].rolling(period).min()
        high_max = df['high'].rolling(period).max()
        
        k_percent = 100 * ((df['close'] - low_min) / (high_max - low_min))
        d_percent = k_percent.rolling(3).mean()
        
        return k_percent.iloc[-1], d_percent.iloc[-1]
        
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> float:
        """Calculate ATR"""
        high = df['high']
        low = df['low']
        close = df['close']
        
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(period).mean()
        
        return atr.iloc[-1]
        
    # Pattern detection methods
    def _is_pin_bar(self, candle: pd.Series) -> bool:
        """Detect pin bar pattern"""
        body = abs(candle['close'] - candle['open'])
        upper_wick = candle['high'] - max(candle['close'], candle['open'])
        lower_wick = min(candle['close'], candle['open']) - candle['low']
        
        # Bullish pin bar
        if lower_wick > body * 2 and upper_wick < body * 0.5:
            return True
        # Bearish pin bar
        elif upper_wick > body * 2 and lower_wick < body * 0.5:
            return True
            
        return False
        
    def _is_engulfing(self, prev: pd.Series, curr: pd.Series) -> bool:
        """Detect engulfing pattern"""
        # Bullish engulfing
        if (prev['close'] < prev['open'] and
            curr['close'] > curr['open'] and
            curr['open'] < prev['close'] and
            curr['close'] > prev['open']):
            return True
            
        # Bearish engulfing
        if (prev['close'] > prev['open'] and
            curr['close'] < curr['open'] and
            curr['open'] > prev['close'] and
            curr['close'] < prev['open']):
            return True
            
        return False
        
    def _is_double_bottom_top(self, df: pd.DataFrame) -> bool:
        """Detect double bottom or top"""
        if len(df) < 40:
            return False
            
        lows = df['low'].iloc[-40:]
        highs = df['high'].iloc[-40:]
        
        # Find two similar lows or highs
        low_min = lows.min()
        high_max = highs.max()
        
        # Count touches within 0.2%
        low_touches = sum(abs(lows - low_min) / low_min < 0.002)
        high_touches = sum(abs(highs - high_max) / high_max < 0.002)
        
        return low_touches >= 2 or high_touches >= 2
        
    def _is_flag_pattern(self, df: pd.DataFrame) -> bool:
        """Detect flag pattern"""
        if len(df) < 20:
            return False
            
        # Strong move followed by consolidation
        recent_move = abs(df['close'].iloc[-20] - df['close'].iloc[-10])
        consolidation_range = df['high'].iloc[-10:].max() - df['low'].iloc[-10:].min()
        
        return recent_move > consolidation_range * 3
