"""
IEMSS Elite FOREX Strategy - Technical Analysis Engine
Institutional Economic Momentum Scalping Strategy

Multi-timeframe technical analysis system including:
- Support and resistance identification
- Moving average alignment analysis
- Volume confirmation
- Fibonacci confluence
- Price action patterns
- Multi-timeframe confluence scoring
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import asyncio

import pandas as pd
import numpy as np
import talib

from config import TRADING, STRATEGY

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TechnicalSignal:
    """Technical analysis signal structure"""
    currency_pair: str
    timeframe: str
    signal_type: str  # BUY/SELL/NEUTRAL
    confidence: float  # 0-100
    entry_price: float
    stop_loss: float
    take_profit: float
    confluence_factors: List[str]
    timestamp: str

@dataclass
class SupportResistance:
    """Support and resistance level structure"""
    level: float
    strength: int  # 1-5 scale
    touches: int
    level_type: str  # SUPPORT/RESISTANCE
    timeframe: str

@dataclass
class TechnicalScore:
    """Technical confluence scoring result"""
    total_score: int  # 0-40 points (40% of total confluence)
    ma_alignment_score: int
    sr_level_score: int
    volume_score: int
    fibonacci_score: int
    price_action_score: int
    details: Dict[str, Any]

class TechnicalAnalyzer:
    """Multi-timeframe technical analysis engine"""
    
    def __init__(self):
        """Initialize technical analyzer"""
        self.timeframes = TRADING.TIMEFRAMES
        self.support_resistance_levels: Dict[str, List[SupportResistance]] = {}
        self.fibonacci_levels: Dict[str, Dict] = {}
        
    # Main Analysis Methods
    
    async def analyze_confluence(self, 
                               currency_pair: str,
                               price_data: Dict[str, pd.DataFrame]) -> TechnicalScore:
        """Perform comprehensive multi-timeframe technical analysis"""
        
        # Initialize scoring components
        ma_score = 0
        sr_score = 0
        volume_score = 0
        fibonacci_score = 0
        price_action_score = 0
        details = {}
        
        try:
            # 1. Moving Average Alignment Analysis (10 points max)
            ma_analysis = await self._analyze_ma_alignment(currency_pair, price_data)
            ma_score = ma_analysis['score']
            details['moving_averages'] = ma_analysis
            
            # 2. Support/Resistance Level Analysis (10 points max)
            sr_analysis = await self._analyze_support_resistance(currency_pair, price_data)
            sr_score = sr_analysis['score']
            details['support_resistance'] = sr_analysis
            
            # 3. Volume Confirmation Analysis (8 points max)
            volume_analysis = await self._analyze_volume_confirmation(currency_pair, price_data)
            volume_score = volume_analysis['score']
            details['volume'] = volume_analysis
            
            # 4. Fibonacci Confluence Analysis (7 points max)
            fib_analysis = await self._analyze_fibonacci_levels(currency_pair, price_data)
            fibonacci_score = fib_analysis['score']
            details['fibonacci'] = fib_analysis
            
            # 5. Price Action Pattern Analysis (5 points max)
            pa_analysis = await self._analyze_price_action(currency_pair, price_data)
            price_action_score = pa_analysis['score']
            details['price_action'] = pa_analysis
            
            total_score = ma_score + sr_score + volume_score + fibonacci_score + price_action_score
            
            logger.info(f"Technical analysis complete for {currency_pair}: {total_score}/40 points")
            
            return TechnicalScore(
                total_score=total_score,
                ma_alignment_score=ma_score,
                sr_level_score=sr_score,
                volume_score=volume_score,
                fibonacci_score=fibonacci_score,
                price_action_score=price_action_score,
                details=details
            )
            
        except Exception as e:
            logger.error(f"Error in technical analysis for {currency_pair}: {e}")
            return TechnicalScore(0, 0, 0, 0, 0, 0, {'error': str(e)})
    
    # Moving Average Analysis
    
    async def _analyze_ma_alignment(self, 
                                  currency_pair: str,
                                  price_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Analyze moving average alignment across timeframes"""
        
        score = 0
        alignment_details = {}
        
        try:
            for timeframe, df in price_data.items():
                if len(df) < 50:  # Need sufficient data
                    continue
                
                # Calculate multiple moving averages
                df['EMA_8'] = talib.EMA(df['close'].values, timeperiod=8)
                df['EMA_21'] = talib.EMA(df['close'].values, timeperiod=21)
                df['SMA_50'] = talib.SMA(df['close'].values, timeperiod=50)
                
                current_price = df['close'].iloc[-1]
                ema_8 = df['EMA_8'].iloc[-1]
                ema_21 = df['EMA_21'].iloc[-1]
                sma_50 = df['SMA_50'].iloc[-1]
                
                # Check bullish alignment
                bullish_alignment = (current_price > ema_8 > ema_21 > sma_50)
                bearish_alignment = (current_price < ema_8 < ema_21 < sma_50)
                
                timeframe_score = 0
                direction = "NEUTRAL"
                
                if bullish_alignment:
                    timeframe_score = 3 if timeframe == "15M" else 2 if timeframe == "5M" else 1
                    direction = "BULLISH"
                elif bearish_alignment:
                    timeframe_score = 3 if timeframe == "15M" else 2 if timeframe == "5M" else 1
                    direction = "BEARISH"
                
                score += timeframe_score
                alignment_details[timeframe] = {
                    'direction': direction,
                    'score': timeframe_score,
                    'current_price': current_price,
                    'ema_8': ema_8,
                    'ema_21': ema_21,
                    'sma_50': sma_50,
                    'alignment': bullish_alignment or bearish_alignment
                }
            
            # Bonus for multi-timeframe agreement
            directions = [details['direction'] for details in alignment_details.values()]
            if len(set(directions)) == 1 and directions[0] != "NEUTRAL":
                score += 2  # Bonus for agreement
                alignment_details['multi_timeframe_agreement'] = True
            
            return {
                'score': min(score, 10),  # Cap at 10 points
                'details': alignment_details,
                'summary': f"MA alignment score: {min(score, 10)}/10"
            }
            
        except Exception as e:
            logger.error(f"Error in MA alignment analysis: {e}")
            return {'score': 0, 'error': str(e)}
    
    # Support and Resistance Analysis
    
    async def _analyze_support_resistance(self, 
                                        currency_pair: str,
                                        price_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Identify and analyze support/resistance levels"""
        
        score = 0
        sr_details = {}
        
        try:
            # Use 15M timeframe for S/R identification
            if "15M" not in price_data:
                return {'score': 0, 'error': 'No 15M data available'}
            
            df = price_data["15M"].copy()
            current_price = df['close'].iloc[-1]
            
            # Identify pivot points
            highs = df['high'].rolling(window=5, center=True).max()
            lows = df['low'].rolling(window=5, center=True).min()
            
            # Find resistance levels (pivot highs)
            resistance_levels = []
            for i in range(2, len(df) - 2):
                if df['high'].iloc[i] == highs.iloc[i]:
                    level = df['high'].iloc[i]
                    # Count touches
                    touches = sum(1 for price in df['high'] if abs(price - level) < level * 0.0005)
                    if touches >= 2:
                        resistance_levels.append({
                            'level': level,
                            'touches': touches,
                            'strength': min(touches, 5),
                            'distance_pips': abs(current_price - level) / self._get_pip_value(currency_pair)
                        })
            
            # Find support levels (pivot lows)
            support_levels = []
            for i in range(2, len(df) - 2):
                if df['low'].iloc[i] == lows.iloc[i]:
                    level = df['low'].iloc[i]
                    touches = sum(1 for price in df['low'] if abs(price - level) < level * 0.0005)
                    if touches >= 2:
                        support_levels.append({
                            'level': level,
                            'touches': touches,
                            'strength': min(touches, 5),
                            'distance_pips': abs(current_price - level) / self._get_pip_value(currency_pair)
                        })
            
            # Score based on proximity to strong levels
            nearby_resistance = [r for r in resistance_levels if r['distance_pips'] <= 20]
            nearby_support = [s for s in support_levels if s['distance_pips'] <= 20]
            
            # Higher score for stronger nearby levels
            for resistance in nearby_resistance:
                if resistance['distance_pips'] <= 5:  # Very close
                    score += resistance['strength']
                elif resistance['distance_pips'] <= 10:  # Close
                    score += resistance['strength'] // 2
            
            for support in nearby_support:
                if support['distance_pips'] <= 5:
                    score += support['strength']
                elif support['distance_pips'] <= 10:
                    score += support['strength'] // 2
            
            sr_details = {
                'current_price': current_price,
                'resistance_levels': resistance_levels[:5],  # Top 5
                'support_levels': support_levels[:5],  # Top 5
                'nearby_resistance': nearby_resistance,
                'nearby_support': nearby_support
            }
            
            return {
                'score': min(score, 10),  # Cap at 10 points
                'details': sr_details,
                'summary': f"S/R analysis score: {min(score, 10)}/10"
            }
            
        except Exception as e:
            logger.error(f"Error in S/R analysis: {e}")
            return {'score': 0, 'error': str(e)}
    
    # Volume Analysis
    
    async def _analyze_volume_confirmation(self, 
                                         currency_pair: str,
                                         price_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Analyze volume confirmation for price movements"""
        
        score = 0
        volume_details = {}
        
        try:
            # Use 5M timeframe for volume analysis
            if "5M" not in price_data:
                return {'score': 0, 'error': 'No 5M data available'}
            
            df = price_data["5M"].copy()
            
            if 'volume' not in df.columns:
                # If no volume data, use tick volume approximation
                df['volume'] = df['high'] - df['low']  # Range as proxy
            
            # Calculate volume moving average
            df['volume_ma'] = df['volume'].rolling(window=20).mean()
            
            current_volume = df['volume'].iloc[-1]
            avg_volume = df['volume_ma'].iloc[-1]
            
            # Volume surge detection
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
            
            # Score based on volume confirmation
            if volume_ratio >= TRADING.MIN_VOLUME_MULTIPLIER:  # 150% above average
                if volume_ratio >= 2.0:  # 200% above average
                    score = 8
                elif volume_ratio >= 1.75:  # 175% above average
                    score = 6
                else:  # 150% above average
                    score = 4
            
            # Check volume trend
            recent_volumes = df['volume'].tail(5)
            volume_trend = "INCREASING" if recent_volumes.is_monotonic_increasing else \
                          "DECREASING" if recent_volumes.is_monotonic_decreasing else "MIXED"
            
            if volume_trend == "INCREASING" and score > 0:
                score += 1  # Bonus for increasing volume
            
            volume_details = {
                'current_volume': current_volume,
                'average_volume': avg_volume,
                'volume_ratio': volume_ratio,
                'volume_trend': volume_trend,
                'volume_surge': volume_ratio >= TRADING.MIN_VOLUME_MULTIPLIER
            }
            
            return {
                'score': min(score, 8),  # Cap at 8 points
                'details': volume_details,
                'summary': f"Volume confirmation score: {min(score, 8)}/8"
            }
            
        except Exception as e:
            logger.error(f"Error in volume analysis: {e}")
            return {'score': 0, 'error': str(e)}
    
    # Fibonacci Analysis
    
    async def _analyze_fibonacci_levels(self, 
                                      currency_pair: str,
                                      price_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Analyze Fibonacci retracement and extension levels"""
        
        score = 0
        fib_details = {}
        
        try:
            # Use 15M timeframe for Fibonacci analysis
            if "15M" not in price_data:
                return {'score': 0, 'error': 'No 15M data available'}
            
            df = price_data["15M"].copy()
            current_price = df['close'].iloc[-1]
            
            # Find recent swing high and low (last 50 bars)
            recent_data = df.tail(50)
            swing_high = recent_data['high'].max()
            swing_low = recent_data['low'].min()
            
            # Calculate Fibonacci levels
            fib_range = swing_high - swing_low
            fib_levels = {
                '0.0': swing_low,
                '23.6': swing_low + (fib_range * 0.236),
                '38.2': swing_low + (fib_range * 0.382),
                '50.0': swing_low + (fib_range * 0.500),
                '61.8': swing_low + (fib_range * 0.618),
                '78.6': swing_low + (fib_range * 0.786),
                '100.0': swing_high
            }
            
            # Check proximity to key Fibonacci levels
            pip_value = self._get_pip_value(currency_pair)
            
            for level_name, level_price in fib_levels.items():
                distance_pips = abs(current_price - level_price) / pip_value
                
                if distance_pips <= 5:  # Within 5 pips
                    if level_name in ['38.2', '50.0', '61.8']:  # Key levels
                        score += 3
                    elif level_name in ['23.6', '78.6']:  # Secondary levels
                        score += 2
                    else:  # Other levels
                        score += 1
                elif distance_pips <= 10:  # Within 10 pips
                    if level_name in ['38.2', '50.0', '61.8']:
                        score += 1
            
            fib_details = {
                'swing_high': swing_high,
                'swing_low': swing_low,
                'current_price': current_price,
                'fibonacci_levels': fib_levels,
                'closest_level': min(fib_levels.items(), 
                                   key=lambda x: abs(current_price - x[1]))
            }
            
            return {
                'score': min(score, 7),  # Cap at 7 points
                'details': fib_details,
                'summary': f"Fibonacci confluence score: {min(score, 7)}/7"
            }
            
        except Exception as e:
            logger.error(f"Error in Fibonacci analysis: {e}")
            return {'score': 0, 'error': str(e)}
    
    # Price Action Analysis
    
    async def _analyze_price_action(self, 
                                  currency_pair: str,
                                  price_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Analyze price action patterns"""
        
        score = 0
        pa_details = {}
        
        try:
            # Use 1M timeframe for price action
            if "1M" not in price_data:
                return {'score': 0, 'error': 'No 1M data available'}
            
            df = price_data["1M"].copy()
            
            # Calculate candlestick patterns
            patterns = {}
            
            # Doji pattern
            body_size = abs(df['close'] - df['open'])
            total_range = df['high'] - df['low']
            doji_threshold = total_range * 0.1
            
            patterns['doji'] = (body_size <= doji_threshold).iloc[-1]
            
            # Hammer/Hanging Man
            lower_shadow = df['open'].combine(df['close'], min) - df['low']
            upper_shadow = df['high'] - df['open'].combine(df['close'], max)
            
            patterns['hammer'] = (lower_shadow >= 2 * body_size).iloc[-1] and \
                                (upper_shadow <= body_size * 0.5).iloc[-1]
            
            # Engulfing patterns
            if len(df) >= 2:
                prev_body = abs(df['close'].iloc[-2] - df['open'].iloc[-2])
                curr_body = abs(df['close'].iloc[-1] - df['open'].iloc[-1])
                
                patterns['engulfing'] = curr_body > prev_body * 1.5
            
            # Score based on patterns
            if patterns.get('doji', False):
                score += 2  # Indecision pattern
            if patterns.get('hammer', False):
                score += 3  # Reversal pattern
            if patterns.get('engulfing', False):
                score += 2  # Momentum pattern
            
            pa_details = {
                'patterns': patterns,
                'latest_candle': {
                    'open': df['open'].iloc[-1],
                    'high': df['high'].iloc[-1],
                    'low': df['low'].iloc[-1],
                    'close': df['close'].iloc[-1],
                    'body_size': body_size.iloc[-1],
                    'total_range': total_range.iloc[-1]
                }
            }
            
            return {
                'score': min(score, 5),  # Cap at 5 points
                'details': pa_details,
                'summary': f"Price action score: {min(score, 5)}/5"
            }
            
        except Exception as e:
            logger.error(f"Error in price action analysis: {e}")
            return {'score': 0, 'error': str(e)}
    
    # Utility Methods
    
    def _get_pip_value(self, currency_pair: str) -> float:
        """Get pip value for currency pair"""
        pip_values = {
            'EUR/USD': 0.0001,
            'GBP/USD': 0.0001,
            'USD/JPY': 0.01,
            'USD/CHF': 0.0001,
            'AUD/USD': 0.0001,
            'USD/CAD': 0.0001
        }
        return pip_values.get(currency_pair, 0.0001)
    
    async def generate_trade_signal(self, 
                                  currency_pair: str,
                                  technical_score: TechnicalScore,
                                  current_price: float) -> Optional[TechnicalSignal]:
        """Generate trade signal based on technical analysis"""
        
        if technical_score.total_score < STRATEGY.TECHNICAL_THRESHOLD:
            return None
        
        # Determine signal direction based on analysis
        ma_details = technical_score.details.get('moving_averages', {}).get('details', {})
        
        # Get consensus direction from timeframes
        directions = []
        for tf_data in ma_details.values():
            if isinstance(tf_data, dict) and 'direction' in tf_data:
                directions.append(tf_data['direction'])
        
        if not directions:
            return None
        
        # Majority vote for direction
        bullish_count = directions.count('BULLISH')
        bearish_count = directions.count('BEARISH')
        
        if bullish_count > bearish_count:
            signal_type = 'BUY'
        elif bearish_count > bullish_count:
            signal_type = 'SELL'
        else:
            return None  # No clear direction
        
        # Calculate confidence based on score
        confidence = min((technical_score.total_score / 40) * 100, 100)
        
        # Calculate stop loss and take profit
        pip_value = self._get_pip_value(currency_pair)
        
        if signal_type == 'BUY':
            stop_loss = current_price - (10 * pip_value)  # 10 pip stop
            take_profit = current_price + (30 * pip_value)  # 30 pip target
        else:
            stop_loss = current_price + (10 * pip_value)
            take_profit = current_price - (30 * pip_value)
        
        confluence_factors = []
        if technical_score.ma_alignment_score >= 6:
            confluence_factors.append("Strong MA alignment")
        if technical_score.sr_level_score >= 5:
            confluence_factors.append("Key S/R level")
        if technical_score.volume_score >= 4:
            confluence_factors.append("Volume confirmation")
        if technical_score.fibonacci_score >= 3:
            confluence_factors.append("Fibonacci confluence")
        
        return TechnicalSignal(
            currency_pair=currency_pair,
            timeframe="Multi-TF",
            signal_type=signal_type,
            confidence=confidence,
            entry_price=current_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            confluence_factors=confluence_factors,
            timestamp=datetime.now().isoformat()
        )

# Export main classes
__all__ = [
    'TechnicalAnalyzer',
    'TechnicalSignal',
    'TechnicalScore',
    'SupportResistance'
]
