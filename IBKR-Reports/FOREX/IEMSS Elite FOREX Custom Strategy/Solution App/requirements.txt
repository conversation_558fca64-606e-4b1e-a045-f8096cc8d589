# IEMSS Elite FOREX Strategy - Python Dependencies
# Institutional Economic Momentum Scalping Strategy

# Core Trading Libraries
ibapi>=9.81.1.post1          # Interactive Brokers API
pandas>=2.0.0                # Data manipulation and analysis
numpy>=1.24.0                # Numerical computing
scipy>=1.10.0                # Scientific computing

# Database Integration
supabase>=1.0.0              # Supabase Python client
psycopg2-binary>=2.9.0       # PostgreSQL adapter

# Technical Analysis
TA-Lib>=0.4.25               # Technical Analysis Library
pandas-ta>=0.3.14b           # Pandas Technical Analysis

# Economic Data
yfinance>=0.2.0              # Yahoo Finance data
fredapi>=0.5.0               # Federal Reserve Economic Data
investpy>=1.0.8              # Economic calendar data

# Web Scraping & APIs
requests>=2.31.0             # HTTP library
beautifulsoup4>=4.12.0       # HTML parsing
selenium>=4.10.0             # Web automation (for economic calendars)

# Data Processing
python-dateutil>=2.8.0      # Date/time utilities
pytz>=2023.3                 # Timezone handling
schedule>=1.2.0              # Job scheduling

# Logging & Monitoring
loguru>=0.7.0                # Advanced logging
python-telegram-bot>=20.0   # Telegram notifications (optional)

# Configuration & Environment
python-dotenv>=1.0.0         # Environment variable management
pydantic>=2.0.0              # Data validation
dataclasses-json>=0.5.0     # JSON serialization for dataclasses

# Mathematical & Statistical
scikit-learn>=1.3.0         # Machine learning utilities
statsmodels>=0.14.0         # Statistical modeling

# Visualization (for analysis)
matplotlib>=3.7.0            # Plotting library
plotly>=5.15.0               # Interactive plots
seaborn>=0.12.0              # Statistical visualization

# Async Programming
asyncio>=3.4.3               # Asynchronous I/O
aiohttp>=3.8.0               # Async HTTP client

# Development & Testing
pytest>=7.4.0               # Testing framework
black>=23.0.0                # Code formatting
flake8>=6.0.0                # Code linting
mypy>=1.4.0                  # Type checking

# Performance Monitoring
psutil>=5.9.0                # System monitoring
memory-profiler>=0.60.0     # Memory usage profiling

# Optional: Advanced Features
redis>=4.6.0                 # Caching (optional)
celery>=5.3.0                # Task queue (optional)
fastapi>=0.100.0             # Web API (optional)
uvicorn>=0.23.0              # ASGI server (optional)
