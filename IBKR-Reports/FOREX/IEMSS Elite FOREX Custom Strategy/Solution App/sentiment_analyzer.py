"""
IEMSS Elite FOREX Strategy - Market Sentiment Analyzer
Institutional Economic Momentum Scalping Strategy

Market sentiment analysis system including:
- VIX volatility analysis
- Risk-on/Risk-off assessment
- Currency correlation monitoring
- Market flow analysis
- Central bank sentiment tracking
- Institutional positioning analysis
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import asyncio

import pandas as pd
import numpy as np
import yfinance as yf

from config import TRADING, STRATEGY

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SentimentIndicator:
    """Individual sentiment indicator structure"""
    name: str
    value: float
    normalized_score: int  # 0-10 scale
    direction: str  # BULLISH/BEARISH/NEUTRAL
    confidence: float  # 0-1 scale
    last_updated: datetime

@dataclass
class SentimentScore:
    """Market sentiment scoring result"""
    total_score: int  # 0-20 points (20% of total confluence)
    vix_score: int
    risk_sentiment_score: int
    correlation_score: int
    flow_score: int
    details: Dict[str, Any]

class SentimentAnalyzer:
    """Analyzes market sentiment for FOREX trading decisions"""
    
    def __init__(self):
        """Initialize sentiment analyzer"""
        self.sentiment_cache = {}
        self.last_update = None
        self.vix_data = None
        self.currency_flows = {}
        self.correlation_matrix = self._initialize_correlation_matrix()
        
    def _initialize_correlation_matrix(self) -> Dict[str, Dict[str, float]]:
        """Initialize asset correlation matrix"""
        # Correlations between currencies and major assets
        correlations = {
            'EUR': {'SPY': 0.65, 'GLD': 0.45, 'TLT': -0.35, 'DXY': -0.85},
            'GBP': {'SPY': 0.60, 'GLD': 0.40, 'TLT': -0.30, 'DXY': -0.80},
            'JPY': {'SPY': -0.25, 'GLD': 0.15, 'TLT': 0.45, 'DXY': 0.35},
            'CHF': {'SPY': -0.30, 'GLD': 0.50, 'TLT': 0.40, 'DXY': -0.60},
            'AUD': {'SPY': 0.70, 'GLD': 0.60, 'TLT': -0.40, 'DXY': -0.75},
            'CAD': {'SPY': 0.55, 'GLD': 0.35, 'TLT': -0.25, 'DXY': -0.65}
        }
        return correlations
    
    # Main Analysis Methods
    
    async def analyze_market_sentiment(self, 
                                     currency_pair: str,
                                     current_time: datetime = None) -> SentimentScore:
        """Perform comprehensive market sentiment analysis"""
        
        if current_time is None:
            current_time = datetime.now(timezone.utc)
        
        # Initialize scoring components
        vix_score = 0
        risk_sentiment_score = 0
        correlation_score = 0
        flow_score = 0
        details = {}
        
        try:
            # Update sentiment data if needed
            await self._update_sentiment_data()
            
            # Get relevant currencies
            base_currency, quote_currency = currency_pair.split('/')
            
            # 1. VIX Volatility Analysis (6 points max)
            vix_analysis = await self._analyze_vix_sentiment()
            vix_score = vix_analysis['score']
            details['vix'] = vix_analysis
            
            # 2. Risk-On/Risk-Off Analysis (6 points max)
            risk_analysis = await self._analyze_risk_sentiment(base_currency, quote_currency)
            risk_sentiment_score = risk_analysis['score']
            details['risk_sentiment'] = risk_analysis
            
            # 3. Correlation Analysis (4 points max)
            correlation_analysis = await self._analyze_correlation_sentiment(
                base_currency, quote_currency
            )
            correlation_score = correlation_analysis['score']
            details['correlation'] = correlation_analysis
            
            # 4. Currency Flow Analysis (4 points max)
            flow_analysis = await self._analyze_currency_flows(base_currency, quote_currency)
            flow_score = flow_analysis['score']
            details['flows'] = flow_analysis
            
            total_score = vix_score + risk_sentiment_score + correlation_score + flow_score
            
            logger.info(f"Sentiment analysis complete for {currency_pair}: {total_score}/20 points")
            
            return SentimentScore(
                total_score=total_score,
                vix_score=vix_score,
                risk_sentiment_score=risk_sentiment_score,
                correlation_score=correlation_score,
                flow_score=flow_score,
                details=details
            )
            
        except Exception as e:
            logger.error(f"Error in sentiment analysis for {currency_pair}: {e}")
            return SentimentScore(0, 0, 0, 0, 0, {'error': str(e)})
    
    # Data Update Methods
    
    async def _update_sentiment_data(self):
        """Update all sentiment-related data"""
        
        # Check if update is needed (every 15 minutes)
        if (self.last_update and 
            datetime.now(timezone.utc) - self.last_update < timedelta(minutes=15)):
            return
        
        try:
            # Update VIX data
            await self._fetch_vix_data()
            
            # Update major asset prices for correlation analysis
            await self._fetch_asset_prices()
            
            # Update currency flow indicators
            await self._update_currency_flows()
            
            self.last_update = datetime.now(timezone.utc)
            logger.info("Sentiment data updated successfully")
            
        except Exception as e:
            logger.error(f"Error updating sentiment data: {e}")
    
    async def _fetch_vix_data(self):
        """Fetch VIX volatility index data"""
        
        try:
            # Fetch VIX data from Yahoo Finance
            vix = yf.Ticker("^VIX")
            hist = vix.history(period="5d", interval="1h")
            
            if not hist.empty:
                self.vix_data = {
                    'current': hist['Close'].iloc[-1],
                    'previous': hist['Close'].iloc[-2] if len(hist) > 1 else hist['Close'].iloc[-1],
                    'ma_20': hist['Close'].rolling(20).mean().iloc[-1],
                    'high_5d': hist['High'].max(),
                    'low_5d': hist['Low'].min(),
                    'timestamp': datetime.now(timezone.utc)
                }
                
                logger.info(f"VIX data updated: Current={self.vix_data['current']:.2f}")
            
        except Exception as e:
            logger.error(f"Error fetching VIX data: {e}")
            # Use mock data if fetch fails
            self.vix_data = {
                'current': 18.5,
                'previous': 19.2,
                'ma_20': 19.8,
                'high_5d': 22.1,
                'low_5d': 16.8,
                'timestamp': datetime.now(timezone.utc)
            }
    
    async def _fetch_asset_prices(self):
        """Fetch major asset prices for correlation analysis"""
        
        try:
            # Fetch key assets
            assets = ['SPY', 'GLD', 'TLT', 'DX-Y.NYB']  # S&P500, Gold, Bonds, Dollar Index
            
            for asset in assets:
                try:
                    ticker = yf.Ticker(asset)
                    hist = ticker.history(period="2d", interval="1h")
                    
                    if not hist.empty:
                        self.sentiment_cache[asset] = {
                            'current': hist['Close'].iloc[-1],
                            'previous': hist['Close'].iloc[-2] if len(hist) > 1 else hist['Close'].iloc[-1],
                            'change_pct': ((hist['Close'].iloc[-1] / hist['Close'].iloc[-2]) - 1) * 100 if len(hist) > 1 else 0,
                            'timestamp': datetime.now(timezone.utc)
                        }
                except Exception as e:
                    logger.warning(f"Failed to fetch {asset}: {e}")
                    
        except Exception as e:
            logger.error(f"Error fetching asset prices: {e}")
    
    async def _update_currency_flows(self):
        """Update currency flow indicators"""
        
        try:
            # Mock currency flow data - in production, this would come from
            # institutional flow data, central bank operations, etc.
            
            currencies = ['EUR', 'GBP', 'JPY', 'CHF', 'AUD', 'CAD']
            
            for currency in currencies:
                self.currency_flows[currency] = {
                    'institutional_flow': np.random.normal(0, 1),  # Mock flow indicator
                    'retail_sentiment': np.random.uniform(-1, 1),  # Mock retail sentiment
                    'central_bank_activity': np.random.choice(['NEUTRAL', 'HAWKISH', 'DOVISH']),
                    'timestamp': datetime.now(timezone.utc)
                }
                
        except Exception as e:
            logger.error(f"Error updating currency flows: {e}")
    
    # VIX Analysis
    
    async def _analyze_vix_sentiment(self) -> Dict[str, Any]:
        """Analyze VIX for market sentiment"""
        
        score = 0
        vix_details = {}
        
        try:
            if not self.vix_data:
                return {'score': 0, 'error': 'No VIX data available'}
            
            current_vix = self.vix_data['current']
            vix_ma = self.vix_data['ma_20']
            vix_change = current_vix - self.vix_data['previous']
            
            # Optimal VIX range for FOREX scalping: 15-25
            optimal_range = (15, 25)
            
            # Score based on VIX level
            if optimal_range[0] <= current_vix <= optimal_range[1]:
                score += 4  # Optimal volatility
            elif current_vix < optimal_range[0]:
                score += 2  # Low volatility (less opportunity)
            elif current_vix > optimal_range[1] * 1.5:
                score += 1  # Very high volatility (risky)
            else:
                score += 3  # Moderate volatility
            
            # Score based on VIX trend
            if current_vix < vix_ma:  # Below average (good for risk-taking)
                score += 2
            elif abs(current_vix - vix_ma) / vix_ma < 0.05:  # Near average
                score += 1
            
            vix_details = {
                'current_vix': round(current_vix, 2),
                'vix_ma_20': round(vix_ma, 2),
                'vix_change': round(vix_change, 2),
                'optimal_range': optimal_range,
                'market_regime': self._classify_vix_regime(current_vix),
                'trading_environment': 'FAVORABLE' if score >= 4 else 'MODERATE' if score >= 2 else 'CHALLENGING'
            }
            
            return {
                'score': min(score, 6),  # Cap at 6 points
                'details': vix_details,
                'summary': f"VIX sentiment score: {min(score, 6)}/6"
            }
            
        except Exception as e:
            logger.error(f"Error in VIX analysis: {e}")
            return {'score': 0, 'error': str(e)}
    
    def _classify_vix_regime(self, vix_level: float) -> str:
        """Classify market regime based on VIX level"""
        
        if vix_level < 12:
            return "COMPLACENCY"
        elif vix_level < 20:
            return "LOW_VOLATILITY"
        elif vix_level < 30:
            return "MODERATE_VOLATILITY"
        elif vix_level < 40:
            return "HIGH_VOLATILITY"
        else:
            return "EXTREME_VOLATILITY"
    
    # Risk Sentiment Analysis
    
    async def _analyze_risk_sentiment(self, 
                                    base_currency: str,
                                    quote_currency: str) -> Dict[str, Any]:
        """Analyze risk-on/risk-off sentiment"""
        
        score = 0
        risk_details = {}
        
        try:
            # Get asset performance for risk sentiment
            spy_data = self.sentiment_cache.get('SPY', {})
            gld_data = self.sentiment_cache.get('GLD', {})
            tlt_data = self.sentiment_cache.get('TLT', {})
            
            if not all([spy_data, gld_data, tlt_data]):
                return {'score': 0, 'error': 'Insufficient asset data'}
            
            # Calculate risk sentiment indicators
            spy_change = spy_data.get('change_pct', 0)
            gld_change = gld_data.get('change_pct', 0)
            tlt_change = tlt_data.get('change_pct', 0)
            
            # Risk-on indicators: SPY up, TLT down
            # Risk-off indicators: SPY down, TLT up, GLD up
            
            risk_on_score = 0
            risk_off_score = 0
            
            # SPY performance
            if spy_change > 0.5:  # Strong equity performance
                risk_on_score += 2
            elif spy_change < -0.5:  # Weak equity performance
                risk_off_score += 2
            
            # Bond performance (inverse relationship)
            if tlt_change < -0.2:  # Bonds selling off
                risk_on_score += 1
            elif tlt_change > 0.2:  # Bonds rallying
                risk_off_score += 1
            
            # Gold performance
            if gld_change > 0.3:  # Gold rallying (safe haven)
                risk_off_score += 1
            elif gld_change < -0.3:  # Gold selling off
                risk_on_score += 1
            
            # Determine dominant sentiment
            if risk_on_score > risk_off_score:
                sentiment = "RISK_ON"
                sentiment_strength = risk_on_score
            elif risk_off_score > risk_on_score:
                sentiment = "RISK_OFF"
                sentiment_strength = risk_off_score
            else:
                sentiment = "MIXED"
                sentiment_strength = 0
            
            # Score based on currency characteristics
            risk_currencies = ['AUD', 'EUR', 'GBP']  # Risk-on currencies
            safe_currencies = ['JPY', 'CHF', 'USD']  # Safe-haven currencies
            
            if sentiment == "RISK_ON":
                if base_currency in risk_currencies and quote_currency in safe_currencies:
                    score = sentiment_strength + 2  # Favorable for risk currency
                elif base_currency in safe_currencies and quote_currency in risk_currencies:
                    score = max(0, 4 - sentiment_strength)  # Unfavorable
                else:
                    score = sentiment_strength
            elif sentiment == "RISK_OFF":
                if base_currency in safe_currencies and quote_currency in risk_currencies:
                    score = sentiment_strength + 2  # Favorable for safe haven
                elif base_currency in risk_currencies and quote_currency in safe_currencies:
                    score = max(0, 4 - sentiment_strength)  # Unfavorable
                else:
                    score = sentiment_strength
            
            risk_details = {
                'sentiment': sentiment,
                'sentiment_strength': sentiment_strength,
                'spy_change': round(spy_change, 2),
                'gld_change': round(gld_change, 2),
                'tlt_change': round(tlt_change, 2),
                'risk_on_score': risk_on_score,
                'risk_off_score': risk_off_score,
                'currency_alignment': self._assess_currency_alignment(
                    base_currency, quote_currency, sentiment
                )
            }
            
            return {
                'score': min(score, 6),  # Cap at 6 points
                'details': risk_details,
                'summary': f"Risk sentiment score: {min(score, 6)}/6"
            }
            
        except Exception as e:
            logger.error(f"Error in risk sentiment analysis: {e}")
            return {'score': 0, 'error': str(e)}
    
    def _assess_currency_alignment(self, 
                                 base_currency: str,
                                 quote_currency: str,
                                 sentiment: str) -> str:
        """Assess if currency pair aligns with risk sentiment"""
        
        risk_currencies = ['AUD', 'EUR', 'GBP']
        safe_currencies = ['JPY', 'CHF', 'USD']
        
        if sentiment == "RISK_ON":
            if base_currency in risk_currencies and quote_currency in safe_currencies:
                return "STRONGLY_ALIGNED"
            elif base_currency in risk_currencies or quote_currency in safe_currencies:
                return "PARTIALLY_ALIGNED"
            else:
                return "NEUTRAL"
        elif sentiment == "RISK_OFF":
            if base_currency in safe_currencies and quote_currency in risk_currencies:
                return "STRONGLY_ALIGNED"
            elif base_currency in safe_currencies or quote_currency in risk_currencies:
                return "PARTIALLY_ALIGNED"
            else:
                return "NEUTRAL"
        else:
            return "MIXED_SIGNALS"
    
    # Correlation Analysis
    
    async def _analyze_correlation_sentiment(self, 
                                           base_currency: str,
                                           quote_currency: str) -> Dict[str, Any]:
        """Analyze correlation-based sentiment"""
        
        score = 0
        correlation_details = {}
        
        try:
            # Check correlations with major assets
            base_correlations = self.correlation_matrix.get(base_currency, {})
            quote_correlations = self.correlation_matrix.get(quote_currency, {})
            
            # Analyze asset movements vs expected correlations
            correlation_confirmations = 0
            total_correlations = 0
            
            for asset in ['SPY', 'GLD', 'TLT']:
                if asset in self.sentiment_cache:
                    asset_change = self.sentiment_cache[asset].get('change_pct', 0)
                    
                    # Check base currency correlation
                    if base_currency in self.correlation_matrix:
                        expected_corr = self.correlation_matrix[base_currency].get(asset, 0)
                        if abs(expected_corr) > 0.3:  # Significant correlation
                            if (expected_corr > 0 and asset_change > 0) or \
                               (expected_corr < 0 and asset_change < 0):
                                correlation_confirmations += 1
                            total_correlations += 1
            
            # Score based on correlation confirmation
            if total_correlations > 0:
                confirmation_rate = correlation_confirmations / total_correlations
                score = int(confirmation_rate * 4)  # Max 4 points
            
            correlation_details = {
                'correlation_confirmations': correlation_confirmations,
                'total_correlations': total_correlations,
                'confirmation_rate': round(confirmation_rate * 100, 1) if total_correlations > 0 else 0,
                'base_currency_correlations': base_correlations,
                'quote_currency_correlations': quote_correlations
            }
            
            return {
                'score': score,
                'details': correlation_details,
                'summary': f"Correlation sentiment score: {score}/4"
            }
            
        except Exception as e:
            logger.error(f"Error in correlation analysis: {e}")
            return {'score': 0, 'error': str(e)}
    
    # Currency Flow Analysis
    
    async def _analyze_currency_flows(self, 
                                    base_currency: str,
                                    quote_currency: str) -> Dict[str, Any]:
        """Analyze currency-specific flows and positioning"""
        
        score = 0
        flow_details = {}
        
        try:
            base_flow = self.currency_flows.get(base_currency, {})
            quote_flow = self.currency_flows.get(quote_currency, {})
            
            if not base_flow or not quote_flow:
                return {'score': 0, 'error': 'No flow data available'}
            
            # Analyze institutional flows
            base_inst_flow = base_flow.get('institutional_flow', 0)
            quote_inst_flow = quote_flow.get('institutional_flow', 0)
            
            # Score based on flow divergence
            flow_divergence = base_inst_flow - quote_inst_flow
            
            if abs(flow_divergence) > 1.5:  # Strong divergence
                score += 3
            elif abs(flow_divergence) > 1.0:  # Moderate divergence
                score += 2
            elif abs(flow_divergence) > 0.5:  # Weak divergence
                score += 1
            
            # Central bank sentiment
            base_cb = base_flow.get('central_bank_activity', 'NEUTRAL')
            quote_cb = quote_flow.get('central_bank_activity', 'NEUTRAL')
            
            if base_cb != quote_cb and base_cb != 'NEUTRAL' and quote_cb != 'NEUTRAL':
                score += 1  # Divergent central bank stance
            
            flow_details = {
                'base_currency': base_currency,
                'quote_currency': quote_currency,
                'base_institutional_flow': round(base_inst_flow, 2),
                'quote_institutional_flow': round(quote_inst_flow, 2),
                'flow_divergence': round(flow_divergence, 2),
                'base_cb_stance': base_cb,
                'quote_cb_stance': quote_cb,
                'flow_direction': 'FAVORS_BASE' if flow_divergence > 0 else 'FAVORS_QUOTE' if flow_divergence < 0 else 'NEUTRAL'
            }
            
            return {
                'score': min(score, 4),  # Cap at 4 points
                'details': flow_details,
                'summary': f"Currency flow score: {min(score, 4)}/4"
            }
            
        except Exception as e:
            logger.error(f"Error in flow analysis: {e}")
            return {'score': 0, 'error': str(e)}
    
    # Utility Methods
    
    async def get_sentiment_summary(self, currency_pair: str) -> Dict[str, Any]:
        """Get comprehensive sentiment summary"""
        
        sentiment_score = await self.analyze_market_sentiment(currency_pair)
        
        return {
            'currency_pair': currency_pair,
            'total_score': sentiment_score.total_score,
            'max_possible': 20,
            'score_percentage': round((sentiment_score.total_score / 20) * 100, 1),
            'components': {
                'vix': sentiment_score.vix_score,
                'risk_sentiment': sentiment_score.risk_sentiment_score,
                'correlation': sentiment_score.correlation_score,
                'flows': sentiment_score.flow_score
            },
            'overall_sentiment': self._classify_overall_sentiment(sentiment_score.total_score),
            'details': sentiment_score.details,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    
    def _classify_overall_sentiment(self, score: int) -> str:
        """Classify overall sentiment based on score"""
        
        if score >= 17:  # 85%+
            return "VERY_FAVORABLE"
        elif score >= 14:  # 70%+
            return "FAVORABLE"
        elif score >= 10:  # 50%+
            return "NEUTRAL"
        elif score >= 6:   # 30%+
            return "UNFAVORABLE"
        else:
            return "VERY_UNFAVORABLE"

# Export main classes
__all__ = [
    'SentimentAnalyzer',
    'SentimentScore',
    'SentimentIndicator'
]
