"""
IEMSS Elite FOREX Strategy - Risk Manager
Institutional Economic Momentum Scalping Strategy

Comprehensive risk management system including:
- Position sizing calculations
- Stop loss and take profit management
- Portfolio-level risk controls
- Correlation monitoring
- Drawdown protection
- Real-time risk alerts
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import asyncio

import pandas as pd
import numpy as np

from config import RISK, TRADING, STRATEGY
from database_manager import db_manager, TradeRecord

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PositionSize:
    """Position sizing calculation result"""
    currency_pair: str
    risk_amount: float
    position_size: int
    stop_loss_distance: float
    max_loss_pips: float
    leverage_used: float
    risk_percentage: float

@dataclass
class RiskMetrics:
    """Current portfolio risk metrics"""
    total_exposure: float
    used_margin: float
    available_margin: float
    portfolio_risk: float
    daily_pnl: float
    max_drawdown: float
    correlation_risk: float
    open_positions: int
    risk_alerts: List[str]

class RiskManager:
    """Comprehensive risk management for IEMSS strategy"""
    
    def __init__(self, account_balance: float):
        """Initialize risk manager with account balance"""
        self.account_balance = account_balance
        self.daily_start_balance = account_balance
        self.open_positions: Dict[str, Dict] = {}
        self.daily_trades = 0
        self.daily_pnl = 0.0
        self.max_daily_loss = account_balance * RISK.DAILY_LOSS_LIMIT
        self.correlation_matrix = self._initialize_correlation_matrix()
        
    def _initialize_correlation_matrix(self) -> Dict[str, Dict[str, float]]:
        """Initialize currency pair correlation matrix"""
        # Typical FOREX correlations (simplified)
        correlations = {
            'EUR/USD': {'GBP/USD': 0.85, 'USD/CHF': -0.75, 'AUD/USD': 0.65, 'USD/JPY': -0.15, 'USD/CAD': -0.25},
            'GBP/USD': {'EUR/USD': 0.85, 'USD/CHF': -0.70, 'AUD/USD': 0.70, 'USD/JPY': -0.10, 'USD/CAD': -0.20},
            'USD/JPY': {'EUR/USD': -0.15, 'GBP/USD': -0.10, 'USD/CHF': 0.25, 'AUD/USD': -0.05, 'USD/CAD': 0.30},
            'USD/CHF': {'EUR/USD': -0.75, 'GBP/USD': -0.70, 'USD/JPY': 0.25, 'AUD/USD': -0.60, 'USD/CAD': 0.40},
            'AUD/USD': {'EUR/USD': 0.65, 'GBP/USD': 0.70, 'USD/JPY': -0.05, 'USD/CHF': -0.60, 'USD/CAD': -0.85},
            'USD/CAD': {'EUR/USD': -0.25, 'GBP/USD': -0.20, 'USD/JPY': 0.30, 'USD/CHF': 0.40, 'AUD/USD': -0.85}
        }
        return correlations
    
    # Position Sizing Methods
    
    def calculate_position_size(self, 
                              currency_pair: str,
                              entry_price: float,
                              stop_loss_price: float,
                              account_currency: str = 'USD') -> PositionSize:
        """Calculate optimal position size based on risk parameters"""
        
        # Calculate risk amount (1.5% of account balance)
        risk_amount = self.account_balance * RISK.RISK_PER_TRADE
        
        # Calculate stop loss distance in pips
        pip_value = self._get_pip_value(currency_pair)
        stop_loss_distance = abs(entry_price - stop_loss_price)
        stop_loss_pips = stop_loss_distance / pip_value
        
        # Calculate position size
        # Position Size = Risk Amount / (Stop Loss Distance * Pip Value * Lot Size)
        standard_lot_size = 100000
        position_size_lots = risk_amount / (stop_loss_pips * self._get_pip_value_usd(currency_pair))
        position_size_units = int(position_size_lots * standard_lot_size)
        
        # Calculate leverage used
        notional_value = position_size_units * entry_price
        leverage_used = notional_value / self.account_balance
        
        return PositionSize(
            currency_pair=currency_pair,
            risk_amount=risk_amount,
            position_size=position_size_units,
            stop_loss_distance=stop_loss_distance,
            max_loss_pips=stop_loss_pips,
            leverage_used=leverage_used,
            risk_percentage=RISK.RISK_PER_TRADE * 100
        )
    
    def _get_pip_value(self, currency_pair: str) -> float:
        """Get pip value for currency pair"""
        # Simplified pip values (normally would get from broker)
        pip_values = {
            'EUR/USD': 0.0001,
            'GBP/USD': 0.0001,
            'USD/JPY': 0.01,
            'USD/CHF': 0.0001,
            'AUD/USD': 0.0001,
            'USD/CAD': 0.0001
        }
        return pip_values.get(currency_pair, 0.0001)
    
    def _get_pip_value_usd(self, currency_pair: str) -> float:
        """Get pip value in USD for position sizing"""
        # Simplified USD pip values for standard lot
        usd_pip_values = {
            'EUR/USD': 10.0,
            'GBP/USD': 10.0,
            'USD/JPY': 10.0,
            'USD/CHF': 10.0,
            'AUD/USD': 10.0,
            'USD/CAD': 10.0
        }
        return usd_pip_values.get(currency_pair, 10.0)
    
    # Risk Validation Methods
    
    def validate_trade_risk(self, 
                           currency_pair: str,
                           position_size: int,
                           entry_price: float,
                           stop_loss_price: float) -> Tuple[bool, List[str]]:
        """Validate if trade meets risk management criteria"""
        
        warnings = []
        
        # Check daily trade limit
        if self.daily_trades >= TRADING.MAX_TRADES_PER_DAY:
            warnings.append(f"Daily trade limit reached ({TRADING.MAX_TRADES_PER_DAY})")
            return False, warnings
        
        # Check daily loss limit
        if self.daily_pnl <= -self.max_daily_loss:
            warnings.append(f"Daily loss limit reached (${self.max_daily_loss:,.2f})")
            return False, warnings
        
        # Check position size limits
        max_position_value = self.account_balance * RISK.MAX_PORTFOLIO_RISK
        position_value = position_size * entry_price
        
        if position_value > max_position_value:
            warnings.append(f"Position size exceeds maximum allocation ({RISK.MAX_PORTFOLIO_RISK*100}%)")
            return False, warnings
        
        # Check correlation limits
        correlation_risk = self._calculate_correlation_risk(currency_pair)
        if correlation_risk > RISK.CORRELATION_THRESHOLD:
            warnings.append(f"High correlation risk detected ({correlation_risk:.2f})")
            return False, warnings
        
        # Check risk-reward ratio
        stop_distance = abs(entry_price - stop_loss_price)
        min_take_profit = entry_price + (stop_distance * RISK.MIN_RISK_REWARD_RATIO)
        
        # Validate stop loss distance
        pip_value = self._get_pip_value(currency_pair)
        stop_pips = stop_distance / pip_value
        
        if stop_pips > RISK.DEFAULT_STOP_LOSS_PIPS * 2:  # Max 2x default
            warnings.append(f"Stop loss too wide ({stop_pips:.1f} pips)")
            return False, warnings
        
        return True, warnings
    
    def _calculate_correlation_risk(self, new_pair: str) -> float:
        """Calculate correlation risk with existing positions"""
        if not self.open_positions:
            return 0.0
        
        max_correlation = 0.0
        correlated_positions = 0
        
        for existing_pair in self.open_positions.keys():
            if existing_pair in self.correlation_matrix.get(new_pair, {}):
                correlation = abs(self.correlation_matrix[new_pair][existing_pair])
                max_correlation = max(max_correlation, correlation)
                
                if correlation > RISK.CORRELATION_THRESHOLD:
                    correlated_positions += 1
        
        # Block if too many correlated positions
        if correlated_positions >= RISK.MAX_CORRELATED_POSITIONS:
            return 1.0  # Maximum risk
        
        return max_correlation
    
    # Portfolio Risk Monitoring
    
    async def get_portfolio_risk_metrics(self) -> RiskMetrics:
        """Calculate current portfolio risk metrics"""
        
        # Get open positions from database
        open_trades = await db_manager.get_open_trades()
        
        total_exposure = 0.0
        used_margin = 0.0
        risk_alerts = []
        
        for trade in open_trades:
            position_value = trade['position_size'] * trade['entry_price']
            total_exposure += position_value
            used_margin += position_value * 0.02  # Assume 50:1 leverage (2% margin)
        
        # Calculate portfolio risk percentage
        portfolio_risk = (total_exposure / self.account_balance) * 100
        
        # Check for risk alerts
        if portfolio_risk > RISK.MAX_PORTFOLIO_RISK * 100:
            risk_alerts.append(f"Portfolio risk exceeds limit: {portfolio_risk:.1f}%")
        
        if self.daily_pnl <= -self.max_daily_loss * 0.8:  # 80% of limit
            risk_alerts.append(f"Approaching daily loss limit: ${abs(self.daily_pnl):,.2f}")
        
        if len(open_trades) > TRADING.MAX_TRADES_PER_DAY:
            risk_alerts.append(f"Too many open positions: {len(open_trades)}")
        
        # Calculate max drawdown
        performance_metrics = await db_manager.get_performance_metrics(30)
        max_drawdown = performance_metrics.get('max_drawdown', 0.0)
        
        return RiskMetrics(
            total_exposure=total_exposure,
            used_margin=used_margin,
            available_margin=self.account_balance - used_margin,
            portfolio_risk=portfolio_risk,
            daily_pnl=self.daily_pnl,
            max_drawdown=max_drawdown,
            correlation_risk=self._get_max_correlation_risk(),
            open_positions=len(open_trades),
            risk_alerts=risk_alerts
        )
    
    def _get_max_correlation_risk(self) -> float:
        """Get maximum correlation risk across all positions"""
        if len(self.open_positions) < 2:
            return 0.0
        
        max_correlation = 0.0
        pairs = list(self.open_positions.keys())
        
        for i, pair1 in enumerate(pairs):
            for pair2 in pairs[i+1:]:
                if pair1 in self.correlation_matrix and pair2 in self.correlation_matrix[pair1]:
                    correlation = abs(self.correlation_matrix[pair1][pair2])
                    max_correlation = max(max_correlation, correlation)
        
        return max_correlation
    
    # Stop Loss and Take Profit Management
    
    def calculate_stop_loss(self, 
                           currency_pair: str,
                           entry_price: float,
                           trade_direction: str,
                           atr_value: Optional[float] = None) -> float:
        """Calculate dynamic stop loss based on volatility"""
        
        pip_value = self._get_pip_value(currency_pair)
        
        # Use ATR if available, otherwise use default
        if atr_value:
            stop_distance = atr_value * 1.5  # 1.5x ATR
        else:
            stop_distance = RISK.DEFAULT_STOP_LOSS_PIPS * pip_value
        
        if trade_direction.upper() == 'BUY':
            stop_loss = entry_price - stop_distance
        else:  # SELL
            stop_loss = entry_price + stop_distance
        
        return round(stop_loss, 5)
    
    def calculate_take_profit(self, 
                             entry_price: float,
                             stop_loss_price: float,
                             trade_direction: str,
                             risk_reward_ratio: float = None) -> float:
        """Calculate take profit based on risk-reward ratio"""
        
        if risk_reward_ratio is None:
            risk_reward_ratio = RISK.MIN_RISK_REWARD_RATIO
        
        stop_distance = abs(entry_price - stop_loss_price)
        profit_distance = stop_distance * risk_reward_ratio
        
        if trade_direction.upper() == 'BUY':
            take_profit = entry_price + profit_distance
        else:  # SELL
            take_profit = entry_price - profit_distance
        
        return round(take_profit, 5)
    
    # Position Management
    
    def add_position(self, trade_record: TradeRecord):
        """Add position to risk tracking"""
        self.open_positions[trade_record.currency_pair] = {
            'entry_price': trade_record.entry_price,
            'position_size': trade_record.position_size,
            'stop_loss': trade_record.stop_loss,
            'take_profit': trade_record.take_profit,
            'entry_time': trade_record.entry_time,
            'trade_type': trade_record.trade_type
        }
        self.daily_trades += 1
        logger.info(f"Position added: {trade_record.currency_pair} {trade_record.trade_type}")
    
    def remove_position(self, currency_pair: str, pnl: float):
        """Remove position from risk tracking"""
        if currency_pair in self.open_positions:
            del self.open_positions[currency_pair]
            self.daily_pnl += pnl
            logger.info(f"Position removed: {currency_pair}, PnL: ${pnl:.2f}")
    
    def update_daily_pnl(self, pnl_change: float):
        """Update daily P&L tracking"""
        self.daily_pnl += pnl_change
    
    # Emergency Risk Controls
    
    async def check_emergency_stop(self) -> Tuple[bool, str]:
        """Check if emergency stop conditions are met"""
        
        # Daily loss limit breach
        if self.daily_pnl <= -self.max_daily_loss:
            await db_manager.log_risk_alert(
                "EMERGENCY_STOP", 
                f"Daily loss limit breached: ${abs(self.daily_pnl):,.2f}",
                "CRITICAL"
            )
            return True, "Daily loss limit exceeded"
        
        # Maximum drawdown breach
        performance = await db_manager.get_performance_metrics(7)  # Weekly
        if performance.get('max_drawdown', 0) > RISK.MAX_DRAWDOWN_LIMIT * 100:
            await db_manager.log_risk_alert(
                "EMERGENCY_STOP",
                f"Maximum drawdown exceeded: {performance['max_drawdown']:.2f}%",
                "CRITICAL"
            )
            return True, "Maximum drawdown exceeded"
        
        # Too many losing trades in a row
        recent_trades = await db_manager.get_trades(
            start_date=(datetime.now() - timedelta(hours=4)).strftime("%Y-%m-%d %H:%M:%S")
        )
        
        if len(recent_trades) >= 3:
            recent_losses = [t for t in recent_trades[-3:] if t['pnl'] < 0]
            if len(recent_losses) == 3:
                await db_manager.log_risk_alert(
                    "EMERGENCY_STOP",
                    "Three consecutive losses detected",
                    "HIGH"
                )
                return True, "Three consecutive losses"
        
        return False, ""
    
    def reset_daily_metrics(self):
        """Reset daily tracking metrics (call at start of each day)"""
        self.daily_start_balance = self.account_balance
        self.daily_trades = 0
        self.daily_pnl = 0.0
        self.max_daily_loss = self.account_balance * RISK.DAILY_LOSS_LIMIT
        logger.info("Daily risk metrics reset")
    
    # Utility Methods
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk summary"""
        return {
            'account_balance': self.account_balance,
            'daily_pnl': self.daily_pnl,
            'daily_trades': self.daily_trades,
            'max_daily_loss': self.max_daily_loss,
            'remaining_daily_risk': self.max_daily_loss + self.daily_pnl,
            'open_positions': len(self.open_positions),
            'position_details': self.open_positions,
            'risk_per_trade': RISK.RISK_PER_TRADE * 100,
            'max_portfolio_risk': RISK.MAX_PORTFOLIO_RISK * 100
        }

# Export main classes
__all__ = [
    'RiskManager',
    'PositionSize',
    'RiskMetrics'
]
