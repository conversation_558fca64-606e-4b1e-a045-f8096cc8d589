"""
IEMSS Elite FOREX Strategy - Database Manager
Institutional Economic Momentum Scalping Strategy

Handles all database operations including:
- Supabase integration
- Trade logging and retrieval
- Performance metrics storage
- Risk alert management
- Economic indicator tracking
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
import json

from supabase import create_client, Client
import pandas as pd

from config import DATABASE, STRATEGY

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TradeRecord:
    """Trade record structure for database storage"""
    strategy_id: str
    trade_date: str
    currency_pair: str
    trade_type: str  # BUY/SELL
    entry_price: float
    stop_loss: float
    take_profit: float
    position_size: int
    risk_amount: float
    pnl: float
    pips: float
    execution_status: str  # OPEN/CLOSED/CANCELLED
    catalyst: str
    entry_time: Optional[str] = None
    exit_time: Optional[str] = None
    duration_minutes: Optional[int] = None
    confluence_score: Optional[int] = None
    economic_score: Optional[int] = None
    technical_score: Optional[int] = None
    sentiment_score: Optional[int] = None

@dataclass
class PerformanceMetrics:
    """Performance metrics structure"""
    strategy_id: str
    date: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_pips: float
    total_pnl: float
    profit_factor: float
    max_drawdown: float
    sharpe_ratio: float
    daily_return: float

@dataclass
class EconomicIndicator:
    """Economic indicator structure"""
    event_id: str
    event_name: str
    currency: str
    impact_level: str  # HIGH/MEDIUM/LOW
    actual_value: Optional[float]
    forecast_value: Optional[float]
    previous_value: Optional[float]
    release_time: str
    market_reaction: Optional[str]
    volatility_spike: Optional[float]

class DatabaseManager:
    """Manages all database operations for IEMSS strategy"""
    
    def __init__(self):
        """Initialize database connection"""
        self.supabase: Optional[Client] = None
        self.connected = False
        
    async def connect(self) -> bool:
        """Establish connection to Supabase"""
        try:
            self.supabase = create_client(
                DATABASE.SUPABASE_URL,
                DATABASE.SUPABASE_KEY
            )
            
            # Test connection
            response = self.supabase.table(DATABASE.TRADES_TABLE).select("*").limit(1).execute()
            self.connected = True
            logger.info("Successfully connected to Supabase database")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            self.connected = False
            return False
    
    async def disconnect(self):
        """Close database connection"""
        self.supabase = None
        self.connected = False
        logger.info("Disconnected from database")
    
    # Trade Management Methods
    
    async def log_trade(self, trade: TradeRecord) -> bool:
        """Log a trade to the database"""
        if not self.connected:
            logger.error("Database not connected")
            return False
        
        try:
            trade_data = asdict(trade)
            response = self.supabase.table(DATABASE.TRADES_TABLE).insert(trade_data).execute()
            
            if response.data:
                logger.info(f"Trade logged successfully: {trade.currency_pair} {trade.trade_type}")
                return True
            else:
                logger.error("Failed to log trade - no data returned")
                return False
                
        except Exception as e:
            logger.error(f"Error logging trade: {e}")
            return False
    
    async def update_trade(self, trade_id: str, updates: Dict[str, Any]) -> bool:
        """Update an existing trade record"""
        if not self.connected:
            return False
        
        try:
            response = self.supabase.table(DATABASE.TRADES_TABLE)\
                .update(updates)\
                .eq("id", trade_id)\
                .execute()
            
            return len(response.data) > 0
            
        except Exception as e:
            logger.error(f"Error updating trade: {e}")
            return False
    
    async def get_trades(self, 
                        start_date: Optional[str] = None,
                        end_date: Optional[str] = None,
                        currency_pair: Optional[str] = None) -> List[Dict]:
        """Retrieve trades with optional filters"""
        if not self.connected:
            return []
        
        try:
            query = self.supabase.table(DATABASE.TRADES_TABLE).select("*")
            
            if start_date:
                query = query.gte("trade_date", start_date)
            if end_date:
                query = query.lte("trade_date", end_date)
            if currency_pair:
                query = query.eq("currency_pair", currency_pair)
            
            response = query.order("trade_date", desc=True).execute()
            return response.data
            
        except Exception as e:
            logger.error(f"Error retrieving trades: {e}")
            return []
    
    async def get_open_trades(self) -> List[Dict]:
        """Get all currently open trades"""
        if not self.connected:
            return []
        
        try:
            response = self.supabase.table(DATABASE.TRADES_TABLE)\
                .select("*")\
                .eq("execution_status", "OPEN")\
                .execute()
            
            return response.data
            
        except Exception as e:
            logger.error(f"Error retrieving open trades: {e}")
            return []
    
    # Performance Metrics Methods
    
    async def log_performance(self, metrics: PerformanceMetrics) -> bool:
        """Log daily performance metrics"""
        if not self.connected:
            return False
        
        try:
            metrics_data = asdict(metrics)
            response = self.supabase.table(DATABASE.STRATEGY_TABLE)\
                .insert(metrics_data)\
                .execute()
            
            return len(response.data) > 0
            
        except Exception as e:
            logger.error(f"Error logging performance: {e}")
            return False
    
    async def get_performance_metrics(self, days: int = 30) -> Dict[str, Any]:
        """Calculate and return performance metrics"""
        if not self.connected:
            return {}
        
        try:
            # Get recent trades
            end_date = datetime.now(timezone.utc).strftime("%Y-%m-%d")
            start_date = (datetime.now(timezone.utc) - 
                         pd.Timedelta(days=days)).strftime("%Y-%m-%d")
            
            trades = await self.get_trades(start_date, end_date)
            
            if not trades:
                return {}
            
            df = pd.DataFrame(trades)
            
            # Calculate metrics
            total_trades = len(df)
            winning_trades = len(df[df['pnl'] > 0])
            losing_trades = len(df[df['pnl'] < 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            total_pnl = df['pnl'].sum()
            total_pips = df['pips'].sum()
            
            # Profit factor calculation
            gross_profit = df[df['pnl'] > 0]['pnl'].sum()
            gross_loss = abs(df[df['pnl'] < 0]['pnl'].sum())
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
            # Calculate drawdown
            cumulative_pnl = df['pnl'].cumsum()
            running_max = cumulative_pnl.expanding().max()
            drawdown = (cumulative_pnl - running_max) / running_max
            max_drawdown = drawdown.min()
            
            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': round(win_rate * 100, 2),
                'total_pnl': round(total_pnl, 2),
                'total_pips': round(total_pips, 1),
                'profit_factor': round(profit_factor, 2),
                'max_drawdown': round(abs(max_drawdown) * 100, 2),
                'average_win': round(df[df['pnl'] > 0]['pnl'].mean(), 2) if winning_trades > 0 else 0,
                'average_loss': round(df[df['pnl'] < 0]['pnl'].mean(), 2) if losing_trades > 0 else 0,
                'largest_win': round(df['pnl'].max(), 2),
                'largest_loss': round(df['pnl'].min(), 2)
            }
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return {}
    
    # Economic Data Methods
    
    async def log_economic_event(self, indicator: EconomicIndicator) -> bool:
        """Log economic indicator data"""
        if not self.connected:
            return False
        
        try:
            indicator_data = asdict(indicator)
            response = self.supabase.table(DATABASE.ECONOMIC_TABLE)\
                .insert(indicator_data)\
                .execute()
            
            return len(response.data) > 0
            
        except Exception as e:
            logger.error(f"Error logging economic event: {e}")
            return False
    
    async def get_upcoming_events(self, hours_ahead: int = 24) -> List[Dict]:
        """Get upcoming economic events"""
        if not self.connected:
            return []
        
        try:
            current_time = datetime.now(timezone.utc)
            future_time = current_time + pd.Timedelta(hours=hours_ahead)
            
            response = self.supabase.table(DATABASE.ECONOMIC_TABLE)\
                .select("*")\
                .gte("release_time", current_time.isoformat())\
                .lte("release_time", future_time.isoformat())\
                .eq("impact_level", "HIGH")\
                .order("release_time")\
                .execute()
            
            return response.data
            
        except Exception as e:
            logger.error(f"Error retrieving upcoming events: {e}")
            return []
    
    # Risk Management Methods
    
    async def log_risk_alert(self, alert_type: str, message: str, severity: str = "MEDIUM") -> bool:
        """Log risk management alert"""
        if not self.connected:
            return False
        
        try:
            alert_data = {
                'strategy_id': STRATEGY.STRATEGY_ID,
                'alert_type': alert_type,
                'message': message,
                'severity': severity,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'acknowledged': False
            }
            
            response = self.supabase.table(DATABASE.RISK_ALERTS_TABLE)\
                .insert(alert_data)\
                .execute()
            
            return len(response.data) > 0
            
        except Exception as e:
            logger.error(f"Error logging risk alert: {e}")
            return False
    
    async def get_active_alerts(self) -> List[Dict]:
        """Get unacknowledged risk alerts"""
        if not self.connected:
            return []
        
        try:
            response = self.supabase.table(DATABASE.RISK_ALERTS_TABLE)\
                .select("*")\
                .eq("acknowledged", False)\
                .order("timestamp", desc=True)\
                .execute()
            
            return response.data
            
        except Exception as e:
            logger.error(f"Error retrieving risk alerts: {e}")
            return []
    
    # Utility Methods
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform database health check"""
        if not self.connected:
            return {'status': 'disconnected', 'error': 'No database connection'}
        
        try:
            # Test each table
            tables_status = {}
            
            for table_name in [DATABASE.TRADES_TABLE, DATABASE.STRATEGY_TABLE, 
                             DATABASE.ECONOMIC_TABLE, DATABASE.RISK_ALERTS_TABLE]:
                try:
                    response = self.supabase.table(table_name).select("*").limit(1).execute()
                    tables_status[table_name] = 'OK'
                except Exception as e:
                    tables_status[table_name] = f'ERROR: {str(e)}'
            
            return {
                'status': 'connected',
                'tables': tables_status,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            return {'status': 'error', 'error': str(e)}

# Global database manager instance
db_manager = DatabaseManager()

# Async context manager for database operations
class DatabaseContext:
    """Context manager for database operations"""
    
    async def __aenter__(self):
        await db_manager.connect()
        return db_manager
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await db_manager.disconnect()

# Export main classes and functions
__all__ = [
    'DatabaseManager',
    'TradeRecord',
    'PerformanceMetrics', 
    'EconomicIndicator',
    'db_manager',
    'DatabaseContext'
]
