"""
IEMSS Elite FOREX Strategy - Trade Executor
Institutional Economic Momentum Scalping Strategy

IBKR TWS integration for trade execution including:
- Real-time market data handling
- Order management and execution
- Position monitoring
- Risk management integration
- Trade logging and reporting
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass
import asyncio
import threading
import time

from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.order import Order
from ibapi.common import TickerId, OrderId
import pandas as pd

from config import IBKR, TRADING
from database_manager import db_manager, TradeRecord
from risk_manager import RiskManager
from iemss_strategy import TradeSignal

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MarketData:
    """Market data structure"""
    symbol: str
    bid: float
    ask: float
    last: float
    volume: int
    timestamp: datetime

@dataclass
class Position:
    """Position tracking structure"""
    symbol: str
    position: int
    avg_cost: float
    market_price: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float

@dataclass
class ExecutionReport:
    """Trade execution report"""
    order_id: int
    symbol: str
    side: str
    quantity: int
    price: float
    commission: float
    execution_time: datetime
    status: str

class IBKRWrapper(EWrapper):
    """IBKR API Wrapper for handling callbacks"""
    
    def __init__(self, executor):
        EWrapper.__init__(self)
        self.executor = executor
    
    def error(self, reqId: TickerId, errorCode: int, errorString: str):
        """Handle API errors"""
        logger.error(f"IBKR Error {errorCode}: {errorString}")
        if errorCode in [502, 503, 504]:  # Connection errors
            self.executor.connected = False
    
    def connectAck(self):
        """Connection acknowledgment"""
        logger.info("Connected to IBKR TWS")
        self.executor.connected = True
    
    def nextValidId(self, orderId: OrderId):
        """Receive next valid order ID"""
        self.executor.next_order_id = orderId
        logger.info(f"Next valid order ID: {orderId}")
    
    def tickPrice(self, reqId: TickerId, tickType, price: float, attrib):
        """Handle price ticks"""
        if reqId in self.executor.market_data_requests:
            symbol = self.executor.market_data_requests[reqId]
            
            if symbol not in self.executor.market_data:
                self.executor.market_data[symbol] = MarketData(
                    symbol=symbol, bid=0, ask=0, last=0, volume=0,
                    timestamp=datetime.now(timezone.utc)
                )
            
            # Update price based on tick type
            if tickType == 1:  # Bid
                self.executor.market_data[symbol].bid = price
            elif tickType == 2:  # Ask
                self.executor.market_data[symbol].ask = price
            elif tickType == 4:  # Last
                self.executor.market_data[symbol].last = price
            
            self.executor.market_data[symbol].timestamp = datetime.now(timezone.utc)
    
    def orderStatus(self, orderId: OrderId, status: str, filled: float,
                   remaining: float, avgFillPrice: float, permId: int,
                   parentId: int, lastFillPrice: float, clientId: int,
                   whyHeld: str, mktCapPrice: float):
        """Handle order status updates"""
        
        if orderId in self.executor.active_orders:
            order_info = self.executor.active_orders[orderId]
            order_info['status'] = status
            order_info['filled'] = filled
            order_info['remaining'] = remaining
            order_info['avg_fill_price'] = avgFillPrice
            
            logger.info(f"Order {orderId} status: {status}, "
                       f"Filled: {filled}, Avg Price: {avgFillPrice}")

class IBKRClient(EClient):
    """IBKR API Client"""
    
    def __init__(self, wrapper):
        EClient.__init__(self, wrapper)

class TradeExecutor:
    """Handles trade execution through IBKR TWS"""
    
    def __init__(self, risk_manager: RiskManager):
        """Initialize trade executor"""
        self.risk_manager = risk_manager
        
        # IBKR connection
        self.wrapper = IBKRWrapper(self)
        self.client = IBKRClient(self.wrapper)
        self.connected = False
        self.next_order_id = 1
        
        # Data tracking
        self.market_data: Dict[str, MarketData] = {}
        self.market_data_requests: Dict[int, str] = {}
        self.positions: Dict[str, Position] = {}
        self.active_orders: Dict[int, Dict] = {}
        self.execution_reports: Dict[str, ExecutionReport] = {}
        
        # Request ID counters
        self.next_req_id = 1000
        
        # Threading
        self.api_thread = None
        
    # Connection Management
    
    async def connect(self) -> bool:
        """Connect to IBKR TWS"""
        
        try:
            logger.info(f"Connecting to IBKR TWS at {IBKR.TWS_HOST}:{IBKR.TWS_PORT}")
            
            # Start connection in separate thread
            self.api_thread = threading.Thread(target=self._run_api_thread)
            self.api_thread.daemon = True
            self.api_thread.start()
            
            # Wait for connection
            for _ in range(30):  # 30 second timeout
                if self.connected:
                    break
                await asyncio.sleep(1)
            
            if not self.connected:
                logger.error("Failed to connect to IBKR TWS")
                return False
            
            logger.info("Successfully connected to IBKR TWS")
            return True
            
        except Exception as e:
            logger.error(f"Error connecting to IBKR: {e}")
            return False
    
    def _run_api_thread(self):
        """Run IBKR API in separate thread"""
        
        try:
            self.client.connect(IBKR.TWS_HOST, IBKR.TWS_PORT, IBKR.CLIENT_ID)
            self.client.run()
        except Exception as e:
            logger.error(f"Error in API thread: {e}")
            self.connected = False
    
    # Market Data Methods
    
    async def subscribe_market_data(self, currency_pairs: List[str]):
        """Subscribe to market data for currency pairs"""
        
        if not self.connected:
            logger.error("Not connected to IBKR")
            return False
        
        try:
            for pair in currency_pairs:
                contract = self._create_forex_contract(pair)
                req_id = self.next_req_id
                self.next_req_id += 1
                
                self.market_data_requests[req_id] = pair
                self.client.reqMktData(req_id, contract, "", False, False, [])
                
                logger.info(f"Subscribed to market data for {pair}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error subscribing to market data: {e}")
            return False
    
    def _create_forex_contract(self, currency_pair: str) -> Contract:
        """Create FOREX contract for IBKR"""
        
        base, quote = currency_pair.split('/')
        
        contract = Contract()
        contract.symbol = base
        contract.secType = "CASH"
        contract.currency = quote
        contract.exchange = "IDEALPRO"
        
        return contract
    
    # Trade Execution Methods
    
    async def execute_trade_signal(self, signal: TradeSignal) -> bool:
        """Execute a trade signal"""
        
        if not self.connected:
            logger.error("Not connected to IBKR")
            return False
        
        try:
            # Validate signal
            if not self._validate_signal(signal):
                return False
            
            # Create contract
            contract = self._create_forex_contract(signal.currency_pair)
            
            # Create main order
            main_order = self._create_market_order(
                signal.signal_type, signal.position_size
            )
            
            # Create bracket orders (stop loss and take profit)
            stop_order = self._create_stop_order(
                'SELL' if signal.signal_type == 'BUY' else 'BUY',
                signal.position_size,
                signal.stop_loss
            )
            
            profit_order = self._create_limit_order(
                'SELL' if signal.signal_type == 'BUY' else 'BUY',
                signal.position_size,
                signal.take_profit
            )
            
            # Submit bracket order
            order_id = self.next_order_id
            self.next_order_id += 3  # Reserve 3 IDs for bracket
            
            # Store order information
            self.active_orders[order_id] = {
                'signal_id': signal.signal_id,
                'currency_pair': signal.currency_pair,
                'signal_type': signal.signal_type,
                'position_size': signal.position_size,
                'entry_price': signal.entry_price,
                'stop_loss': signal.stop_loss,
                'take_profit': signal.take_profit,
                'status': 'SUBMITTED',
                'filled': 0,
                'remaining': signal.position_size,
                'avg_fill_price': 0,
                'submit_time': datetime.now(timezone.utc)
            }
            
            # Submit orders
            self.client.placeOrder(order_id, contract, main_order)
            self.client.placeOrder(order_id + 1, contract, stop_order)
            self.client.placeOrder(order_id + 2, contract, profit_order)
            
            logger.info(f"Trade signal executed: {signal.currency_pair} "
                       f"{signal.signal_type} {signal.position_size} units")
            
            # Log trade to database
            await self._log_trade_execution(signal, order_id)
            
            return True
            
        except Exception as e:
            logger.error(f"Error executing trade signal: {e}")
            return False
    
    def _validate_signal(self, signal: TradeSignal) -> bool:
        """Validate trade signal before execution"""
        
        # Check if signal is expired
        if datetime.now(timezone.utc) > signal.expiry_time:
            logger.warning(f"Signal expired: {signal.currency_pair}")
            return False
        
        # Check position size
        if signal.position_size <= 0:
            logger.warning(f"Invalid position size: {signal.position_size}")
            return False
        
        # Check prices
        if signal.entry_price <= 0 or signal.stop_loss <= 0 or signal.take_profit <= 0:
            logger.warning(f"Invalid prices in signal: {signal.currency_pair}")
            return False
        
        return True
    
    def _create_market_order(self, action: str, quantity: int) -> Order:
        """Create market order"""
        
        order = Order()
        order.action = action
        order.orderType = "MKT"
        order.totalQuantity = quantity
        order.tif = "IOC"  # Immediate or Cancel
        
        return order
    
    def _create_stop_order(self, action: str, quantity: int, stop_price: float) -> Order:
        """Create stop loss order"""
        
        order = Order()
        order.action = action
        order.orderType = "STP"
        order.totalQuantity = quantity
        order.auxPrice = stop_price
        order.tif = "GTC"  # Good Till Cancelled
        
        return order
    
    def _create_limit_order(self, action: str, quantity: int, limit_price: float) -> Order:
        """Create limit order for take profit"""
        
        order = Order()
        order.action = action
        order.orderType = "LMT"
        order.totalQuantity = quantity
        order.lmtPrice = limit_price
        order.tif = "GTC"  # Good Till Cancelled
        
        return order
    
    # Position Management
    
    async def get_current_positions(self) -> Dict[str, Position]:
        """Get current positions"""
        
        if self.connected:
            self.client.reqPositions()
            await asyncio.sleep(1)  # Wait for position updates
        
        return self.positions.copy()
    
    async def close_position(self, currency_pair: str, reason: str = "Manual close") -> bool:
        """Close existing position"""
        
        if not self.connected:
            logger.error("Not connected to IBKR")
            return False
        
        try:
            if currency_pair not in self.positions:
                logger.warning(f"No position found for {currency_pair}")
                return False
            
            position = self.positions[currency_pair]
            
            if position.position == 0:
                logger.warning(f"Position size is zero for {currency_pair}")
                return False
            
            # Create closing order
            contract = self._create_forex_contract(currency_pair)
            
            # Determine action (opposite of current position)
            action = "SELL" if position.position > 0 else "BUY"
            quantity = abs(position.position)
            
            order = self._create_market_order(action, quantity)
            
            # Submit order
            order_id = self.next_order_id
            self.next_order_id += 1
            
            self.active_orders[order_id] = {
                'currency_pair': currency_pair,
                'signal_type': 'CLOSE',
                'position_size': quantity,
                'status': 'SUBMITTED',
                'reason': reason,
                'submit_time': datetime.now(timezone.utc)
            }
            
            self.client.placeOrder(order_id, contract, order)
            
            logger.info(f"Position close order submitted: {currency_pair} {action} {quantity}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error closing position for {currency_pair}: {e}")
            return False
    
    # Monitoring and Reporting
    
    async def _log_trade_execution(self, signal: TradeSignal, order_id: int):
        """Log trade execution to database"""
        
        try:
            trade_record = TradeRecord(
                strategy_id=signal.signal_id,
                trade_date=datetime.now(timezone.utc).strftime("%Y-%m-%d"),
                currency_pair=signal.currency_pair,
                trade_type=signal.signal_type,
                entry_price=signal.entry_price,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit,
                position_size=signal.position_size,
                risk_amount=signal.risk_amount,
                pnl=0.0,  # Will be updated when trade closes
                pips=0.0,  # Will be calculated when trade closes
                execution_status='OPEN',
                catalyst=signal.catalyst,
                entry_time=datetime.now(timezone.utc).isoformat(),
                confluence_score=signal.confluence_score
            )
            
            await db_manager.log_trade(trade_record)
            
            # Update risk manager
            self.risk_manager.add_position(trade_record)
            
        except Exception as e:
            logger.error(f"Error logging trade execution: {e}")
    
    def get_execution_status(self) -> Dict[str, Any]:
        """Get comprehensive execution status"""
        
        return {
            'connected': self.connected,
            'active_orders': len(self.active_orders),
            'positions': len(self.positions),
            'market_data_subscriptions': len(self.market_data_requests),
            'last_update': datetime.now(timezone.utc).isoformat(),
            'order_details': [
                {
                    'order_id': order_id,
                    'currency_pair': info.get('currency_pair'),
                    'signal_type': info.get('signal_type'),
                    'status': info.get('status'),
                    'filled': info.get('filled', 0),
                    'remaining': info.get('remaining', 0)
                }
                for order_id, info in self.active_orders.items()
            ],
            'position_details': [
                {
                    'symbol': pos.symbol,
                    'position': pos.position,
                    'avg_cost': pos.avg_cost,
                    'unrealized_pnl': pos.unrealized_pnl
                }
                for pos in self.positions.values()
            ]
        }

# Export main class
__all__ = [
    'TradeExecutor',
    'MarketData',
    'Position',
    'ExecutionReport'
]
