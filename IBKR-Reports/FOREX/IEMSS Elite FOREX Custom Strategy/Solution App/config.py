"""
IEMSS Elite FOREX Strategy - Configuration Module
Institutional Economic Momentum Scalping Strategy

Configuration settings for the IEMSS trading system including:
- Strategy parameters
- Risk management settings
- Database connections
- Trading pairs and timeframes
"""

import os
from typing import Dict, List, Tuple
from dataclasses import dataclass

@dataclass
class StrategyConfig:
    """Core strategy configuration parameters"""
    
    # Strategy Identification
    STRATEGY_NAME: str = "IEMSS Elite FOREX Strategy"
    STRATEGY_VERSION: str = "v1.1"
    STRATEGY_ID: str = "38621f84-3f2f-4090-8c70-7bb2fca39925"
    
    # Performance Targets (Based on backtesting results)
    TARGET_WIN_RATE: float = 0.797  # 79.7% achieved
    TARGET_PROFIT_FACTOR: float = 11.80
    TARGET_RISK_REWARD: float = 3.0
    MAX_DRAWDOWN_LIMIT: float = 0.08  # 8% maximum
    
    # Triple Confluence Weights
    ECONOMIC_WEIGHT: float = 0.40  # 40%
    TECHNICAL_WEIGHT: float = 0.40  # 40%
    SENTIMENT_WEIGHT: float = 0.20  # 20%
    
    # Signal Thresholds
    MIN_CONFLUENCE_SCORE: int = 85  # Minimum score for trade execution
    ECONOMIC_THRESHOLD: int = 34    # 85% of 40 points
    TECHNICAL_THRESHOLD: int = 34   # 85% of 40 points
    SENTIMENT_THRESHOLD: int = 17   # 85% of 20 points

@dataclass
class RiskConfig:
    """Risk management configuration"""
    
    # Position Sizing
    RISK_PER_TRADE: float = 0.015  # 1.5% per trade
    MAX_PORTFOLIO_RISK: float = 0.50  # 50% maximum allocation
    DAILY_LOSS_LIMIT: float = 0.04  # 4% daily loss limit
    
    # Stop Loss & Take Profit
    DEFAULT_STOP_LOSS_PIPS: int = 10
    DEFAULT_TAKE_PROFIT_PIPS: int = 30
    MIN_RISK_REWARD_RATIO: float = 2.0
    
    # Time Management
    MAX_TRADE_DURATION_MINUTES: int = 30  # 30-minute maximum
    FORCE_CLOSE_MINUTES: int = 25  # Force close warning
    
    # Correlation Limits
    MAX_CORRELATED_POSITIONS: int = 2
    CORRELATION_THRESHOLD: float = 0.7
    
    # Volatility Adjustments
    HIGH_VOLATILITY_THRESHOLD: float = 25.0  # VIX level
    VOLATILITY_POSITION_REDUCTION: float = 0.5  # 50% reduction

@dataclass
class TradingConfig:
    """Trading execution configuration"""
    
    # Currency Pairs (Major pairs with optimal liquidity)
    PRIMARY_PAIRS: List[str] = None
    SECONDARY_PAIRS: List[str] = None
    
    def __post_init__(self):
        if self.PRIMARY_PAIRS is None:
            self.PRIMARY_PAIRS = ["EUR/USD", "GBP/USD", "USD/JPY"]
        if self.SECONDARY_PAIRS is None:
            self.SECONDARY_PAIRS = ["USD/CHF", "AUD/USD", "USD/CAD"]
    
    # Trading Sessions (GMT)
    LONDON_OPEN: Tuple[int, int] = (8, 10)   # 8:00-10:00 GMT
    NY_OPEN: Tuple[int, int] = (13, 16)      # 13:00-16:00 GMT (Optimal)
    ASIAN_SESSION: Tuple[int, int] = (0, 8)   # 0:00-8:00 GMT
    
    # Timeframes for Multi-timeframe Analysis
    TIMEFRAMES: Dict[str, int] = None
    
    def __post_init__(self):
        if self.TIMEFRAMES is None:
            self.TIMEFRAMES = {
                "1M": 1,    # 1-minute for price action
                "5M": 5,    # 5-minute for MA alignment
                "15M": 15   # 15-minute for S/R levels
            }
    
    # Daily Trading Limits
    MAX_TRADES_PER_DAY: int = 3
    MAX_TRADING_HOURS: int = 2  # 2-hour focused sessions
    
    # Execution Parameters
    SLIPPAGE_TOLERANCE_PIPS: float = 1.0
    MAX_SPREAD_PIPS: float = 2.0
    MIN_VOLUME_MULTIPLIER: float = 1.5  # 150% above average

@dataclass
class DatabaseConfig:
    """Supabase database configuration"""
    
    # Supabase Connection (Environment variables)
    SUPABASE_URL: str = os.getenv("SUPABASE_URL", "")
    SUPABASE_KEY: str = os.getenv("SUPABASE_ANON_KEY", "")
    
    # Table Names
    TRADES_TABLE: str = "forex_trades"
    STRATEGY_TABLE: str = "strategy_performance"
    ECONOMIC_TABLE: str = "economic_indicators"
    RISK_ALERTS_TABLE: str = "risk_alerts"
    
    # Connection Settings
    MAX_CONNECTIONS: int = 10
    CONNECTION_TIMEOUT: int = 30

@dataclass
class IBKRConfig:
    """Interactive Brokers configuration"""
    
    # TWS Connection
    TWS_HOST: str = "127.0.0.1"
    TWS_PORT: int = 7497  # Paper trading port (7496 for live)
    CLIENT_ID: int = 1
    
    # Account Settings
    ACCOUNT_ID: str = os.getenv("IBKR_ACCOUNT_ID", "")
    
    # Data Subscriptions
    MARKET_DATA_TYPE: int = 3  # Delayed market data
    REAL_TIME_BARS: bool = True
    
    # Order Settings
    ORDER_TYPE: str = "MKT"  # Market orders for scalping
    TIME_IN_FORCE: str = "IOC"  # Immediate or Cancel
    
    # Risk Settings
    ENABLE_RISK_CHECKS: bool = True
    MAX_ORDER_SIZE: int = 100000  # Maximum position size

@dataclass
class EconomicConfig:
    """Economic data configuration"""
    
    # High-Impact Events (Tier 1)
    TIER_1_EVENTS: List[str] = None
    
    def __post_init__(self):
        if self.TIER_1_EVENTS is None:
            self.TIER_1_EVENTS = [
                "Non-Farm Payrolls",
                "Federal Reserve Decision",
                "ECB Rate Decision",
                "CPI Release",
                "GDP Report"
            ]
    
    # Event Impact Scoring
    HIGH_IMPACT_SCORE: int = 40
    MEDIUM_IMPACT_SCORE: int = 25
    LOW_IMPACT_SCORE: int = 10
    
    # Timing Windows
    PRE_EVENT_MINUTES: int = 5   # 5 minutes before
    POST_EVENT_MINUTES: int = 5  # 5 minutes after
    MONITORING_WINDOW: int = 10  # Total 10-minute window
    
    # Volatility Thresholds
    MIN_VOLATILITY_SPIKE: float = 1.5  # 150% above average
    MAX_VOLATILITY_LIMIT: float = 5.0  # 500% above average

# Global Configuration Instances
STRATEGY = StrategyConfig()
RISK = RiskConfig()
TRADING = TradingConfig()
DATABASE = DatabaseConfig()
IBKR = IBKRConfig()
ECONOMIC = EconomicConfig()

# Validation Functions
def validate_config() -> bool:
    """Validate all configuration parameters"""
    
    # Check required environment variables
    required_env = [
        "SUPABASE_URL",
        "SUPABASE_ANON_KEY",
        "IBKR_ACCOUNT_ID"
    ]
    
    missing_env = [var for var in required_env if not os.getenv(var)]
    if missing_env:
        print(f"Missing environment variables: {missing_env}")
        return False
    
    # Validate risk parameters
    if RISK.RISK_PER_TRADE > 0.05:  # Max 5% per trade
        print("Risk per trade exceeds maximum safe limit")
        return False
    
    # Validate confluence weights
    total_weight = (STRATEGY.ECONOMIC_WEIGHT + 
                   STRATEGY.TECHNICAL_WEIGHT + 
                   STRATEGY.SENTIMENT_WEIGHT)
    if abs(total_weight - 1.0) > 0.01:
        print("Confluence weights do not sum to 100%")
        return False
    
    return True

def get_trading_session() -> str:
    """Determine current trading session based on GMT time"""
    from datetime import datetime, timezone
    
    current_hour = datetime.now(timezone.utc).hour
    
    if TRADING.LONDON_OPEN[0] <= current_hour < TRADING.LONDON_OPEN[1]:
        return "LONDON_OPEN"
    elif TRADING.NY_OPEN[0] <= current_hour < TRADING.NY_OPEN[1]:
        return "NY_OPEN"
    elif TRADING.ASIAN_SESSION[0] <= current_hour < TRADING.ASIAN_SESSION[1]:
        return "ASIAN_SESSION"
    else:
        return "OFF_HOURS"

def is_optimal_trading_time() -> bool:
    """Check if current time is optimal for trading"""
    session = get_trading_session()
    return session in ["LONDON_OPEN", "NY_OPEN"]

# Export all configurations
__all__ = [
    'STRATEGY',
    'RISK', 
    'TRADING',
    'DATABASE',
    'IBKR',
    'ECONOMIC',
    'validate_config',
    'get_trading_session',
    'is_optimal_trading_time'
]
