"""
IEMSS Elite FOREX Strategy - Main Application
Institutional Economic Momentum Scalping Strategy

Main orchestration application that coordinates:
- Strategy execution and monitoring
- Real-time market data processing
- Trade signal generation and execution
- Risk management and alerts
- Performance tracking and reporting
- Dashboard interface
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
import json
import os

import pandas as pd
from dataclasses import asdict

# Import IEMSS components
from config import (
    STRATEGY, RISK, TRADING, DATABASE, IBKR, ECONOMIC,
    validate_config, is_optimal_trading_time, get_trading_session
)
from database_manager import db_manager, DatabaseContext
from risk_manager import RiskManager
from iemss_strategy import IEMSSStrategy, TradeSignal
from trade_executor import TradeExecutor
from technical_analysis import TechnicalAnalyzer
from economic_data import EconomicDataHandler
from sentiment_analyzer import SentimentAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('iemss_strategy.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class PriceDataProvider:
    """Mock price data provider - replace with real data source"""
    
    def __init__(self):
        self.price_cache = {}
        
    async def get_price_data(self, currency_pair: str) -> Dict[str, pd.DataFrame]:
        """Get multi-timeframe price data for currency pair"""
        
        try:
            # Mock price data generation
            # In production, this would fetch from IBKR or other data provider
            
            timeframes = ['1M', '5M', '15M']
            price_data = {}
            
            for tf in timeframes:
                # Generate mock OHLCV data
                periods = 100 if tf == '1M' else 50 if tf == '5M' else 25
                
                dates = pd.date_range(
                    end=datetime.now(timezone.utc),
                    periods=periods,
                    freq='1min' if tf == '1M' else '5min' if tf == '5M' else '15min'
                )
                
                # Mock price around realistic levels
                base_price = self._get_base_price(currency_pair)
                
                # Generate realistic price movements
                returns = pd.Series(np.random.normal(0, 0.0001, periods)).cumsum()
                prices = base_price * (1 + returns)
                
                # Create OHLCV data
                df = pd.DataFrame({
                    'datetime': dates,
                    'open': prices,
                    'high': prices * (1 + abs(np.random.normal(0, 0.0002, periods))),
                    'low': prices * (1 - abs(np.random.normal(0, 0.0002, periods))),
                    'close': prices,
                    'volume': np.random.randint(1000, 10000, periods)
                })
                
                df['high'] = df[['open', 'close', 'high']].max(axis=1)
                df['low'] = df[['open', 'close', 'low']].min(axis=1)
                
                price_data[tf] = df
            
            return price_data
            
        except Exception as e:
            logger.error(f"Error getting price data for {currency_pair}: {e}")
            return {}
    
    def _get_base_price(self, currency_pair: str) -> float:
        """Get base price for currency pair"""
        
        base_prices = {
            'EUR/USD': 1.0850,
            'GBP/USD': 1.2700,
            'USD/JPY': 150.00,
            'USD/CHF': 0.9150,
            'AUD/USD': 0.6700,
            'USD/CAD': 1.3550
        }
        
        return base_prices.get(currency_pair, 1.0000)

class IEMSSApplication:
    """Main IEMSS application orchestrator"""
    
    def __init__(self, account_balance: float = 100000):
        """Initialize IEMSS application"""
        
        self.account_balance = account_balance
        self.running = False
        self.shutdown_event = asyncio.Event()
        
        # Initialize components
        self.risk_manager = RiskManager(account_balance)
        self.strategy = IEMSSStrategy(account_balance)
        self.trade_executor = TradeExecutor(self.risk_manager)
        self.price_provider = PriceDataProvider()
        
        # Trading configuration
        self.currency_pairs = TRADING.PRIMARY_PAIRS + TRADING.SECONDARY_PAIRS
        self.analysis_interval = 60  # seconds
        self.last_analysis = None
        
        # Performance tracking
        self.daily_stats = {
            'signals_generated': 0,
            'trades_executed': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'start_time': datetime.now(timezone.utc)
        }
        
        logger.info(f"IEMSS Application initialized with ${account_balance:,.2f}")
    
    async def startup(self) -> bool:
        """Initialize and start the IEMSS application"""
        
        try:
            logger.info("Starting IEMSS Elite FOREX Strategy...")
            
            # 1. Validate configuration
            if not validate_config():
                logger.error("Configuration validation failed")
                return False
            
            # 2. Connect to database
            if not await db_manager.connect():
                logger.error("Database connection failed")
                return False
            
            # 3. Connect to IBKR (if not in simulation mode)
            if not await self._connect_to_broker():
                logger.warning("Broker connection failed - running in simulation mode")
            
            # 4. Subscribe to market data
            if not await self._setup_market_data():
                logger.error("Market data setup failed")
                return False
            
            # 5. Start strategy
            if not await self.strategy.start_strategy():
                logger.error("Strategy startup failed")
                return False
            
            # 6. Setup signal handlers for graceful shutdown
            self._setup_signal_handlers()
            
            self.running = True
            logger.info("IEMSS Application started successfully")
            
            return True
            
        except Exception as e:
            logger.error(f"Error during startup: {e}")
            return False
    
    async def _connect_to_broker(self) -> bool:
        """Connect to IBKR broker"""
        
        try:
            return await self.trade_executor.connect()
        except Exception as e:
            logger.error(f"Broker connection error: {e}")
            return False
    
    async def _setup_market_data(self) -> bool:
        """Setup market data subscriptions"""
        
        try:
            if self.trade_executor.connected:
                return await self.trade_executor.subscribe_market_data(self.currency_pairs)
            else:
                logger.info("Using mock market data (simulation mode)")
                return True
        except Exception as e:
            logger.error(f"Market data setup error: {e}")
            return False
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def run(self):
        """Main application run loop"""
        
        logger.info("Starting main trading loop...")
        
        try:
            while self.running and not self.shutdown_event.is_set():
                
                # Check if we're in optimal trading hours
                if not is_optimal_trading_time():
                    session = get_trading_session()
                    logger.info(f"Outside optimal trading hours (Current: {session})")
                    await asyncio.sleep(300)  # Wait 5 minutes
                    continue
                
                # Run strategy analysis cycle
                await self._run_analysis_cycle()
                
                # Update performance metrics
                await self._update_performance_metrics()
                
                # Check for emergency conditions
                if await self._check_emergency_conditions():
                    logger.critical("Emergency conditions detected - stopping strategy")
                    break
                
                # Wait for next analysis cycle
                await asyncio.sleep(self.analysis_interval)
                
        except Exception as e:
            logger.error(f"Error in main run loop: {e}")
        finally:
            await self.shutdown()
    
    async def _run_analysis_cycle(self):
        """Run complete strategy analysis cycle"""
        
        try:
            current_time = datetime.now(timezone.utc)
            
            logger.info(f"Running analysis cycle at {current_time.strftime('%H:%M:%S')}")
            
            # Run strategy cycle to generate signals
            signals = await self.strategy.run_strategy_cycle(
                self.currency_pairs, self.price_provider
            )
            
            # Execute generated signals
            for signal in signals:
                success = await self._execute_signal(signal)
                if success:
                    self.daily_stats['trades_executed'] += 1
                    logger.info(f"Signal executed: {signal.currency_pair} {signal.signal_type}")
                else:
                    logger.warning(f"Signal execution failed: {signal.currency_pair}")
            
            self.daily_stats['signals_generated'] += len(signals)
            self.last_analysis = current_time
            
            # Log cycle completion
            if signals:
                logger.info(f"Analysis cycle complete: {len(signals)} signals generated")
            
        except Exception as e:
            logger.error(f"Error in analysis cycle: {e}")
    
    async def _execute_signal(self, signal: TradeSignal) -> bool:
        """Execute a trade signal"""
        
        try:
            # Final risk validation
            risk_valid, warnings = self.risk_manager.validate_trade_risk(
                signal.currency_pair, signal.position_size,
                signal.entry_price, signal.stop_loss
            )
            
            if not risk_valid:
                logger.warning(f"Risk validation failed for {signal.currency_pair}: {warnings}")
                return False
            
            # Execute through trade executor
            if self.trade_executor.connected:
                return await self.trade_executor.execute_trade_signal(signal)
            else:
                # Simulation mode - just log the trade
                logger.info(f"SIMULATION: Would execute {signal.currency_pair} "
                           f"{signal.signal_type} at {signal.entry_price:.5f}")
                return True
                
        except Exception as e:
            logger.error(f"Error executing signal: {e}")
            return False
    
    async def _update_performance_metrics(self):
        """Update daily performance metrics"""
        
        try:
            # Get latest performance from database
            performance = await db_manager.get_performance_metrics(1)  # Last day
            
            if performance:
                self.daily_stats.update({
                    'total_pnl': performance.get('total_pnl', 0.0),
                    'win_rate': performance.get('win_rate', 0.0)
                })
            
            # Log performance every hour
            if (self.last_analysis and 
                datetime.now(timezone.utc) - self.last_analysis > timedelta(hours=1)):
                
                await self._log_performance_summary()
                
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
    
    async def _log_performance_summary(self):
        """Log comprehensive performance summary"""
        
        try:
            runtime = datetime.now(timezone.utc) - self.daily_stats['start_time']
            
            summary = {
                'runtime_hours': round(runtime.total_seconds() / 3600, 2),
                'signals_generated': self.daily_stats['signals_generated'],
                'trades_executed': self.daily_stats['trades_executed'],
                'total_pnl': self.daily_stats['total_pnl'],
                'win_rate': self.daily_stats['win_rate'],
                'account_balance': self.account_balance,
                'strategy_status': self.strategy.get_strategy_status(),
                'execution_status': self.trade_executor.get_execution_status()
            }
            
            logger.info(f"Performance Summary: {json.dumps(summary, indent=2)}")
            
        except Exception as e:
            logger.error(f"Error logging performance summary: {e}")
    
    async def _check_emergency_conditions(self) -> bool:
        """Check for emergency stop conditions"""
        
        try:
            # Check risk manager emergency conditions
            emergency_stop, reason = await self.risk_manager.check_emergency_stop()
            
            if emergency_stop:
                logger.critical(f"Emergency stop condition: {reason}")
                await db_manager.log_risk_alert("EMERGENCY_STOP", reason, "CRITICAL")
                return True
            
            # Check connection health
            if self.trade_executor.connected:
                # Add connection health checks here
                pass
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking emergency conditions: {e}")
            return False
    
    async def shutdown(self):
        """Graceful shutdown of the application"""
        
        logger.info("Initiating graceful shutdown...")
        
        try:
            self.running = False
            self.shutdown_event.set()
            
            # Stop strategy
            await self.strategy.stop_strategy()
            
            # Close any open positions (if configured)
            if self.trade_executor.connected:
                positions = await self.trade_executor.get_current_positions()
                for currency_pair in positions:
                    await self.trade_executor.close_position(currency_pair, "Shutdown")
            
            # Disconnect from broker
            await self.trade_executor.disconnect()
            
            # Disconnect from database
            await db_manager.disconnect()
            
            # Final performance log
            await self._log_performance_summary()
            
            logger.info("IEMSS Application shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

async def main():
    """Main entry point"""
    
    # Load account balance from environment or use default
    account_balance = float(os.getenv('ACCOUNT_BALANCE', '100000'))
    
    # Create and run application
    app = IEMSSApplication(account_balance)
    
    try:
        # Startup
        if not await app.startup():
            logger.error("Application startup failed")
            return 1
        
        # Run main loop
        await app.run()
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
        await app.shutdown()
        return 0
    except Exception as e:
        logger.error(f"Unhandled exception: {e}")
        await app.shutdown()
        return 1

if __name__ == "__main__":
    # Import numpy for mock data generation
    import numpy as np
    
    # Run the application
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
