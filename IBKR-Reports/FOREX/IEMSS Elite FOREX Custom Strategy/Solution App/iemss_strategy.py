"""
IEMSS Elite FOREX Strategy - Main Strategy Engine
Institutional Economic Momentum Scalping Strategy

Core strategy implementation featuring:
- Triple Confluence System (Economic 40%, Technical 40%, Sentiment 20%)
- Signal generation and validation
- Entry/exit logic with precise timing
- Risk-adjusted position sizing
- Real-time monitoring and alerts
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import asyncio
import uuid

import pandas as pd
import numpy as np

from config import STRATEGY, RISK, TRADING
from database_manager import db_manager, TradeRecord
from risk_manager import RiskManager
from technical_analysis import TechnicalAnalyzer, TechnicalScore
from economic_data import EconomicDataHandler, EconomicScore
from sentiment_analyzer import SentimentAnalyzer, SentimentScore

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ConfluenceAnalysis:
    """Complete confluence analysis result"""
    currency_pair: str
    total_score: int  # 0-100 points
    economic_score: int  # 0-40 points
    technical_score: int  # 0-40 points
    sentiment_score: int  # 0-20 points
    signal_strength: str  # STRONG/MODERATE/WEAK
    trade_direction: str  # BUY/SELL/NONE
    confidence_level: float  # 0-100%
    entry_price: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    risk_reward_ratio: Optional[float]
    analysis_timestamp: datetime
    details: Dict[str, Any]

@dataclass
class TradeSignal:
    """Complete trade signal with all parameters"""
    signal_id: str
    currency_pair: str
    signal_type: str  # BUY/SELL
    entry_price: float
    stop_loss: float
    take_profit: float
    position_size: int
    risk_amount: float
    confluence_score: int
    confidence: float
    catalyst: str
    expiry_time: datetime
    created_at: datetime

class IEMSSStrategy:
    """Main IEMSS Elite FOREX Strategy Engine"""
    
    def __init__(self, account_balance: float):
        """Initialize IEMSS strategy with account balance"""
        self.account_balance = account_balance
        self.risk_manager = RiskManager(account_balance)
        self.technical_analyzer = TechnicalAnalyzer()
        self.economic_handler = EconomicDataHandler()
        self.sentiment_analyzer = SentimentAnalyzer()
        
        self.active_signals: Dict[str, TradeSignal] = {}
        self.strategy_active = False
        self.last_analysis_time = None
        
        logger.info(f"IEMSS Strategy initialized with ${account_balance:,.2f} account balance")
    
    # Main Strategy Methods
    
    async def analyze_market_confluence(self, 
                                      currency_pair: str,
                                      price_data: Dict[str, pd.DataFrame],
                                      current_time: datetime = None) -> ConfluenceAnalysis:
        """Perform complete triple confluence analysis"""
        
        if current_time is None:
            current_time = datetime.now(timezone.utc)
        
        logger.info(f"Starting confluence analysis for {currency_pair}")
        
        try:
            # 1. Economic Momentum Analysis (40% weight)
            economic_analysis = await self.economic_handler.analyze_economic_momentum(
                currency_pair, current_time
            )
            
            # 2. Technical Confluence Analysis (40% weight)
            technical_analysis = await self.technical_analyzer.analyze_confluence(
                currency_pair, price_data
            )
            
            # 3. Market Sentiment Analysis (20% weight)
            sentiment_analysis = await self.sentiment_analyzer.analyze_market_sentiment(
                currency_pair, current_time
            )
            
            # Calculate total confluence score
            total_score = (economic_analysis.total_score + 
                          technical_analysis.total_score + 
                          sentiment_analysis.total_score)
            
            # Determine signal strength and direction
            signal_strength, trade_direction = self._evaluate_signal_strength(
                total_score, economic_analysis, technical_analysis, sentiment_analysis
            )
            
            # Calculate confidence level
            confidence_level = min((total_score / 100) * 100, 100)
            
            # Determine entry parameters if signal is valid
            entry_price = None
            stop_loss = None
            take_profit = None
            risk_reward_ratio = None
            
            if total_score >= STRATEGY.MIN_CONFLUENCE_SCORE and trade_direction != 'NONE':
                current_price = price_data['1M']['close'].iloc[-1] if '1M' in price_data else None
                if current_price:
                    entry_price = current_price
                    stop_loss = self.risk_manager.calculate_stop_loss(
                        currency_pair, entry_price, trade_direction
                    )
                    take_profit = self.risk_manager.calculate_take_profit(
                        entry_price, stop_loss, trade_direction
                    )
                    risk_reward_ratio = abs(take_profit - entry_price) / abs(entry_price - stop_loss)
            
            confluence_analysis = ConfluenceAnalysis(
                currency_pair=currency_pair,
                total_score=total_score,
                economic_score=economic_analysis.total_score,
                technical_score=technical_analysis.total_score,
                sentiment_score=sentiment_analysis.total_score,
                signal_strength=signal_strength,
                trade_direction=trade_direction,
                confidence_level=confidence_level,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                risk_reward_ratio=risk_reward_ratio,
                analysis_timestamp=current_time,
                details={
                    'economic': economic_analysis.details,
                    'technical': technical_analysis.details,
                    'sentiment': sentiment_analysis.details
                }
            )
            
            logger.info(f"Confluence analysis complete: {total_score}/100 points, "
                       f"Direction: {trade_direction}, Strength: {signal_strength}")
            
            return confluence_analysis
            
        except Exception as e:
            logger.error(f"Error in confluence analysis for {currency_pair}: {e}")
            return ConfluenceAnalysis(
                currency_pair=currency_pair,
                total_score=0,
                economic_score=0,
                technical_score=0,
                sentiment_score=0,
                signal_strength='WEAK',
                trade_direction='NONE',
                confidence_level=0.0,
                entry_price=None,
                stop_loss=None,
                take_profit=None,
                risk_reward_ratio=None,
                analysis_timestamp=current_time,
                details={'error': str(e)}
            )
    
    def _evaluate_signal_strength(self, 
                                total_score: int,
                                economic: EconomicScore,
                                technical: TechnicalScore,
                                sentiment: SentimentScore) -> Tuple[str, str]:
        """Evaluate signal strength and determine trade direction"""
        
        # Check minimum thresholds for each component
        economic_valid = economic.total_score >= STRATEGY.ECONOMIC_THRESHOLD
        technical_valid = technical.total_score >= STRATEGY.TECHNICAL_THRESHOLD
        sentiment_valid = sentiment.total_score >= STRATEGY.SENTIMENT_THRESHOLD
        
        # Require at least 2 out of 3 components to be valid
        valid_components = sum([economic_valid, technical_valid, sentiment_valid])
        
        if valid_components < 2:
            return 'WEAK', 'NONE'
        
        # Determine signal strength based on total score
        if total_score >= 90:
            signal_strength = 'STRONG'
        elif total_score >= STRATEGY.MIN_CONFLUENCE_SCORE:
            signal_strength = 'MODERATE'
        else:
            signal_strength = 'WEAK'
        
        # Determine trade direction based on component analysis
        # This is simplified - in practice, would analyze individual component signals
        if total_score >= STRATEGY.MIN_CONFLUENCE_SCORE:
            # Mock direction determination - would be based on actual signal analysis
            if economic.total_score > 25 and technical.total_score > 25:
                trade_direction = 'BUY'  # Simplified logic
            elif economic.total_score > 20 and technical.total_score > 20:
                trade_direction = 'SELL'  # Simplified logic
            else:
                trade_direction = 'NONE'
        else:
            trade_direction = 'NONE'
        
        return signal_strength, trade_direction
    
    # Signal Generation Methods
    
    async def generate_trade_signal(self, 
                                  currency_pair: str,
                                  price_data: Dict[str, pd.DataFrame],
                                  current_time: datetime = None) -> Optional[TradeSignal]:
        """Generate complete trade signal if confluence criteria are met"""
        
        if current_time is None:
            current_time = datetime.now(timezone.utc)
        
        try:
            # Perform confluence analysis
            confluence = await self.analyze_market_confluence(
                currency_pair, price_data, current_time
            )
            
            # Check if signal meets minimum requirements
            if (confluence.total_score < STRATEGY.MIN_CONFLUENCE_SCORE or 
                confluence.trade_direction == 'NONE'):
                logger.info(f"No valid signal for {currency_pair}: "
                           f"Score {confluence.total_score}, Direction {confluence.trade_direction}")
                return None
            
            # Validate with risk management
            if not confluence.entry_price or not confluence.stop_loss:
                logger.warning(f"Invalid price parameters for {currency_pair}")
                return None
            
            # Calculate position size
            position_size_calc = self.risk_manager.calculate_position_size(
                currency_pair, confluence.entry_price, confluence.stop_loss
            )
            
            # Validate trade risk
            risk_valid, risk_warnings = self.risk_manager.validate_trade_risk(
                currency_pair, position_size_calc.position_size,
                confluence.entry_price, confluence.stop_loss
            )
            
            if not risk_valid:
                logger.warning(f"Trade risk validation failed for {currency_pair}: {risk_warnings}")
                return None
            
            # Create trade signal
            signal_id = str(uuid.uuid4())
            
            # Generate catalyst description
            catalyst = self._generate_catalyst_description(confluence)
            
            trade_signal = TradeSignal(
                signal_id=signal_id,
                currency_pair=currency_pair,
                signal_type=confluence.trade_direction,
                entry_price=confluence.entry_price,
                stop_loss=confluence.stop_loss,
                take_profit=confluence.take_profit,
                position_size=position_size_calc.position_size,
                risk_amount=position_size_calc.risk_amount,
                confluence_score=confluence.total_score,
                confidence=confluence.confidence_level,
                catalyst=catalyst,
                expiry_time=current_time + timedelta(minutes=30),  # 30-minute expiry
                created_at=current_time
            )
            
            # Store active signal
            self.active_signals[signal_id] = trade_signal
            
            logger.info(f"Trade signal generated for {currency_pair}: "
                       f"{confluence.trade_direction} at {confluence.entry_price:.5f}, "
                       f"Score: {confluence.total_score}/100")
            
            return trade_signal
            
        except Exception as e:
            logger.error(f"Error generating trade signal for {currency_pair}: {e}")
            return None
    
    def _generate_catalyst_description(self, confluence: ConfluenceAnalysis) -> str:
        """Generate human-readable catalyst description"""
        
        catalysts = []
        
        # Economic catalysts
        if confluence.economic_score >= STRATEGY.ECONOMIC_THRESHOLD:
            catalysts.append("Economic momentum")
        
        # Technical catalysts
        if confluence.technical_score >= STRATEGY.TECHNICAL_THRESHOLD:
            catalysts.append("Technical confluence")
        
        # Sentiment catalysts
        if confluence.sentiment_score >= STRATEGY.SENTIMENT_THRESHOLD:
            catalysts.append("Market sentiment alignment")
        
        # Add specific details if available
        economic_details = confluence.details.get('economic', {})
        if 'event_impact' in economic_details:
            events = economic_details['event_impact'].get('details', {}).get('relevant_events', [])
            if events:
                event_names = [e['name'] for e in events[:2]]  # Top 2 events
                catalysts.append(f"Economic events: {', '.join(event_names)}")
        
        technical_details = confluence.details.get('technical', {})
        if 'moving_averages' in technical_details:
            ma_details = technical_details['moving_averages'].get('details', {})
            if ma_details.get('multi_timeframe_agreement'):
                catalysts.append("Multi-timeframe MA alignment")
        
        return " + ".join(catalysts) if catalysts else "Triple confluence signal"
    
    # Strategy Execution Methods
    
    async def run_strategy_cycle(self, 
                               currency_pairs: List[str],
                               price_data_provider) -> List[TradeSignal]:
        """Run complete strategy analysis cycle for multiple currency pairs"""
        
        if not self.strategy_active:
            logger.warning("Strategy is not active")
            return []
        
        current_time = datetime.now(timezone.utc)
        generated_signals = []
        
        try:
            # Check emergency stop conditions
            emergency_stop, stop_reason = await self.risk_manager.check_emergency_stop()
            if emergency_stop:
                logger.critical(f"Emergency stop triggered: {stop_reason}")
                await self.stop_strategy()
                return []
            
            # Clean up expired signals
            await self._cleanup_expired_signals(current_time)
            
            # Analyze each currency pair
            for currency_pair in currency_pairs:
                try:
                    # Skip if we already have an active signal for this pair
                    if self._has_active_signal(currency_pair):
                        continue
                    
                    # Get price data
                    price_data = await price_data_provider.get_price_data(currency_pair)
                    if not price_data:
                        logger.warning(f"No price data available for {currency_pair}")
                        continue
                    
                    # Generate signal
                    signal = await self.generate_trade_signal(
                        currency_pair, price_data, current_time
                    )
                    
                    if signal:
                        generated_signals.append(signal)
                        logger.info(f"New signal generated: {signal.currency_pair} "
                                   f"{signal.signal_type} (Score: {signal.confluence_score})")
                    
                except Exception as e:
                    logger.error(f"Error analyzing {currency_pair}: {e}")
                    continue
            
            self.last_analysis_time = current_time
            
            # Log strategy performance
            await self._log_strategy_performance()
            
            return generated_signals
            
        except Exception as e:
            logger.error(f"Error in strategy cycle: {e}")
            return []
    
    async def _cleanup_expired_signals(self, current_time: datetime):
        """Remove expired signals"""
        
        expired_signals = [
            signal_id for signal_id, signal in self.active_signals.items()
            if current_time > signal.expiry_time
        ]
        
        for signal_id in expired_signals:
            expired_signal = self.active_signals.pop(signal_id)
            logger.info(f"Signal expired: {expired_signal.currency_pair} {expired_signal.signal_type}")
    
    def _has_active_signal(self, currency_pair: str) -> bool:
        """Check if there's an active signal for currency pair"""
        
        return any(signal.currency_pair == currency_pair 
                  for signal in self.active_signals.values())
    
    # Strategy Control Methods
    
    async def start_strategy(self) -> bool:
        """Start the IEMSS strategy"""
        
        try:
            # Validate configuration
            if not self._validate_strategy_config():
                return False
            
            # Initialize components
            await self.economic_handler._update_economic_calendar()
            await self.sentiment_analyzer._update_sentiment_data()
            
            # Reset daily metrics
            self.risk_manager.reset_daily_metrics()
            
            self.strategy_active = True
            logger.info("IEMSS Strategy started successfully")
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting strategy: {e}")
            return False
    
    async def stop_strategy(self):
        """Stop the IEMSS strategy"""
        
        self.strategy_active = False
        
        # Clear active signals
        self.active_signals.clear()
        
        logger.info("IEMSS Strategy stopped")
    
    def _validate_strategy_config(self) -> bool:
        """Validate strategy configuration"""
        
        # Check account balance
        if self.account_balance <= 0:
            logger.error("Invalid account balance")
            return False
        
        # Check risk parameters
        if RISK.RISK_PER_TRADE <= 0 or RISK.RISK_PER_TRADE > 0.05:
            logger.error("Invalid risk per trade parameter")
            return False
        
        # Check confluence weights
        total_weight = (STRATEGY.ECONOMIC_WEIGHT + 
                       STRATEGY.TECHNICAL_WEIGHT + 
                       STRATEGY.SENTIMENT_WEIGHT)
        if abs(total_weight - 1.0) > 0.01:
            logger.error("Confluence weights do not sum to 100%")
            return False
        
        return True
    
    # Monitoring and Reporting Methods
    
    async def _log_strategy_performance(self):
        """Log current strategy performance metrics"""
        
        try:
            # Get portfolio risk metrics
            risk_metrics = await self.risk_manager.get_portfolio_risk_metrics()
            
            # Log to database if significant alerts
            if risk_metrics.risk_alerts:
                for alert in risk_metrics.risk_alerts:
                    await db_manager.log_risk_alert("STRATEGY_MONITORING", alert, "MEDIUM")
            
            # Log performance summary every hour
            if (self.last_analysis_time and 
                datetime.now(timezone.utc) - self.last_analysis_time > timedelta(hours=1)):
                
                performance = await db_manager.get_performance_metrics(1)  # Last day
                logger.info(f"Strategy Performance - Win Rate: {performance.get('win_rate', 0):.1f}%, "
                           f"Daily P&L: ${performance.get('total_pnl', 0):.2f}")
                
        except Exception as e:
            logger.error(f"Error logging strategy performance: {e}")
    
    def get_strategy_status(self) -> Dict[str, Any]:
        """Get comprehensive strategy status"""
        
        return {
            'strategy_active': self.strategy_active,
            'account_balance': self.account_balance,
            'active_signals': len(self.active_signals),
            'daily_trades': self.risk_manager.daily_trades,
            'daily_pnl': self.risk_manager.daily_pnl,
            'last_analysis': self.last_analysis_time.isoformat() if self.last_analysis_time else None,
            'risk_summary': self.risk_manager.get_risk_summary(),
            'signal_details': [
                {
                    'currency_pair': signal.currency_pair,
                    'signal_type': signal.signal_type,
                    'confluence_score': signal.confluence_score,
                    'confidence': signal.confidence,
                    'created_at': signal.created_at.isoformat(),
                    'expires_at': signal.expiry_time.isoformat()
                }
                for signal in self.active_signals.values()
            ]
        }

# Export main class
__all__ = [
    'IEMSSStrategy',
    'ConfluenceAnalysis',
    'TradeSignal'
]
