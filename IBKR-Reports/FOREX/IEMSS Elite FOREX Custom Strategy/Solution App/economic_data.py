"""
IEMSS Elite FOREX Strategy - Economic Data Handler
Institutional Economic Momentum Scalping Strategy

Economic data monitoring and analysis system including:
- Economic calendar integration
- High-impact event detection
- Market momentum capture
- Volatility spike analysis
- Central bank monitoring
- Data release impact scoring
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import asyncio
import json

import pandas as pd
import numpy as np
import requests
from bs4 import BeautifulSoup

from config import ECONOMIC, STRATEGY
from database_manager import db_manager, EconomicIndicator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class EconomicEvent:
    """Economic event structure"""
    event_id: str
    event_name: str
    currency: str
    impact_level: str  # HIGH/MEDIUM/LOW
    actual_value: Optional[float]
    forecast_value: Optional[float]
    previous_value: Optional[float]
    release_time: datetime
    time_to_release: timedelta
    market_reaction_expected: str
    volatility_impact: int  # 1-10 scale

@dataclass
class EconomicScore:
    """Economic momentum scoring result"""
    total_score: int  # 0-40 points (40% of total confluence)
    event_impact_score: int
    timing_score: int
    surprise_factor_score: int
    momentum_score: int
    details: Dict[str, Any]

class EconomicDataHandler:
    """Handles economic data monitoring and analysis"""
    
    def __init__(self):
        """Initialize economic data handler"""
        self.upcoming_events: List[EconomicEvent] = []
        self.recent_releases: List[EconomicEvent] = []
        self.economic_calendar_cache = {}
        self.last_update = None
        
    # Main Analysis Methods
    
    async def analyze_economic_momentum(self, 
                                      currency_pair: str,
                                      current_time: datetime = None) -> EconomicScore:
        """Analyze economic momentum for currency pair"""
        
        if current_time is None:
            current_time = datetime.now(timezone.utc)
        
        # Initialize scoring components
        event_impact_score = 0
        timing_score = 0
        surprise_factor_score = 0
        momentum_score = 0
        details = {}
        
        try:
            # Update economic calendar if needed
            await self._update_economic_calendar()
            
            # Get relevant currencies for the pair
            base_currency, quote_currency = currency_pair.split('/')
            relevant_currencies = [base_currency, quote_currency, 'USD']  # USD always relevant
            
            # 1. Event Impact Analysis (15 points max)
            impact_analysis = await self._analyze_event_impact(
                relevant_currencies, current_time
            )
            event_impact_score = impact_analysis['score']
            details['event_impact'] = impact_analysis
            
            # 2. Timing Analysis (10 points max)
            timing_analysis = await self._analyze_release_timing(
                relevant_currencies, current_time
            )
            timing_score = timing_analysis['score']
            details['timing'] = timing_analysis
            
            # 3. Surprise Factor Analysis (10 points max)
            surprise_analysis = await self._analyze_surprise_factor(
                relevant_currencies, current_time
            )
            surprise_factor_score = surprise_analysis['score']
            details['surprise_factor'] = surprise_analysis
            
            # 4. Economic Momentum Analysis (5 points max)
            momentum_analysis = await self._analyze_economic_momentum_trend(
                relevant_currencies
            )
            momentum_score = momentum_analysis['score']
            details['momentum'] = momentum_analysis
            
            total_score = (event_impact_score + timing_score + 
                          surprise_factor_score + momentum_score)
            
            logger.info(f"Economic analysis complete for {currency_pair}: {total_score}/40 points")
            
            return EconomicScore(
                total_score=total_score,
                event_impact_score=event_impact_score,
                timing_score=timing_score,
                surprise_factor_score=surprise_factor_score,
                momentum_score=momentum_score,
                details=details
            )
            
        except Exception as e:
            logger.error(f"Error in economic analysis for {currency_pair}: {e}")
            return EconomicScore(0, 0, 0, 0, 0, {'error': str(e)})
    
    # Economic Calendar Management
    
    async def _update_economic_calendar(self):
        """Update economic calendar data"""
        
        # Check if update is needed (every 30 minutes)
        if (self.last_update and 
            datetime.now(timezone.utc) - self.last_update < timedelta(minutes=30)):
            return
        
        try:
            # Get upcoming events from multiple sources
            await self._fetch_investing_com_calendar()
            await self._fetch_forexfactory_calendar()
            
            # Update database with new events
            for event in self.upcoming_events:
                indicator = EconomicIndicator(
                    event_id=event.event_id,
                    event_name=event.event_name,
                    currency=event.currency,
                    impact_level=event.impact_level,
                    actual_value=event.actual_value,
                    forecast_value=event.forecast_value,
                    previous_value=event.previous_value,
                    release_time=event.release_time.isoformat(),
                    market_reaction=event.market_reaction_expected,
                    volatility_spike=None
                )
                await db_manager.log_economic_event(indicator)
            
            self.last_update = datetime.now(timezone.utc)
            logger.info(f"Economic calendar updated: {len(self.upcoming_events)} events")
            
        except Exception as e:
            logger.error(f"Error updating economic calendar: {e}")
    
    async def _fetch_investing_com_calendar(self):
        """Fetch economic calendar from Investing.com"""
        
        try:
            # This is a simplified version - in production, you'd use proper API
            # or web scraping with proper headers and rate limiting
            
            # Mock data for demonstration
            mock_events = [
                {
                    'name': 'Non-Farm Payrolls',
                    'currency': 'USD',
                    'impact': 'HIGH',
                    'time': datetime.now(timezone.utc) + timedelta(hours=2),
                    'forecast': 180000,
                    'previous': 175000
                },
                {
                    'name': 'ECB Interest Rate Decision',
                    'currency': 'EUR',
                    'impact': 'HIGH',
                    'time': datetime.now(timezone.utc) + timedelta(hours=6),
                    'forecast': 4.50,
                    'previous': 4.50
                }
            ]
            
            for event_data in mock_events:
                event = EconomicEvent(
                    event_id=f"inv_{hash(event_data['name'] + str(event_data['time']))}",
                    event_name=event_data['name'],
                    currency=event_data['currency'],
                    impact_level=event_data['impact'],
                    actual_value=None,
                    forecast_value=event_data.get('forecast'),
                    previous_value=event_data.get('previous'),
                    release_time=event_data['time'],
                    time_to_release=event_data['time'] - datetime.now(timezone.utc),
                    market_reaction_expected=self._predict_market_reaction(event_data),
                    volatility_impact=self._calculate_volatility_impact(event_data)
                )
                
                if event not in self.upcoming_events:
                    self.upcoming_events.append(event)
                    
        except Exception as e:
            logger.error(f"Error fetching Investing.com calendar: {e}")
    
    async def _fetch_forexfactory_calendar(self):
        """Fetch economic calendar from ForexFactory"""
        
        try:
            # Mock implementation - replace with actual API calls
            # ForexFactory has specific terms of service for data usage
            
            logger.info("ForexFactory calendar fetch completed (mock)")
            
        except Exception as e:
            logger.error(f"Error fetching ForexFactory calendar: {e}")
    
    # Event Impact Analysis
    
    async def _analyze_event_impact(self, 
                                  currencies: List[str],
                                  current_time: datetime) -> Dict[str, Any]:
        """Analyze impact of upcoming/recent economic events"""
        
        score = 0
        impact_details = {}
        
        try:
            # Get events within monitoring window
            window_start = current_time - timedelta(minutes=ECONOMIC.PRE_EVENT_MINUTES)
            window_end = current_time + timedelta(minutes=ECONOMIC.POST_EVENT_MINUTES)
            
            relevant_events = []
            for event in self.upcoming_events + self.recent_releases:
                if (event.currency in currencies and 
                    window_start <= event.release_time <= window_end):
                    relevant_events.append(event)
            
            # Score based on event impact levels
            for event in relevant_events:
                if event.impact_level == 'HIGH':
                    if event.event_name in ECONOMIC.TIER_1_EVENTS:
                        score += 15  # Maximum impact
                    else:
                        score += 10
                elif event.impact_level == 'MEDIUM':
                    score += 5
                elif event.impact_level == 'LOW':
                    score += 2
            
            # Bonus for multiple high-impact events
            high_impact_count = sum(1 for e in relevant_events if e.impact_level == 'HIGH')
            if high_impact_count > 1:
                score += 3  # Confluence bonus
            
            impact_details = {
                'relevant_events': [
                    {
                        'name': e.event_name,
                        'currency': e.currency,
                        'impact': e.impact_level,
                        'time': e.release_time.isoformat(),
                        'minutes_away': int(e.time_to_release.total_seconds() / 60)
                    }
                    for e in relevant_events
                ],
                'high_impact_count': high_impact_count,
                'total_events': len(relevant_events)
            }
            
            return {
                'score': min(score, 15),  # Cap at 15 points
                'details': impact_details,
                'summary': f"Event impact score: {min(score, 15)}/15"
            }
            
        except Exception as e:
            logger.error(f"Error in event impact analysis: {e}")
            return {'score': 0, 'error': str(e)}
    
    async def _analyze_release_timing(self, 
                                    currencies: List[str],
                                    current_time: datetime) -> Dict[str, Any]:
        """Analyze timing relative to economic releases"""
        
        score = 0
        timing_details = {}
        
        try:
            # Find events within optimal timing window
            optimal_events = []
            
            for event in self.upcoming_events:
                if event.currency not in currencies:
                    continue
                
                minutes_to_release = int(event.time_to_release.total_seconds() / 60)
                
                # Optimal timing: T-5 to T+5 minutes
                if -ECONOMIC.POST_EVENT_MINUTES <= minutes_to_release <= ECONOMIC.PRE_EVENT_MINUTES:
                    optimal_events.append(event)
                    
                    # Score based on proximity to release
                    if abs(minutes_to_release) <= 2:  # Within 2 minutes
                        score += 10
                    elif abs(minutes_to_release) <= 5:  # Within 5 minutes
                        score += 7
                    else:  # Within monitoring window
                        score += 4
            
            timing_details = {
                'optimal_timing_events': [
                    {
                        'name': e.event_name,
                        'currency': e.currency,
                        'minutes_to_release': int(e.time_to_release.total_seconds() / 60),
                        'optimal_window': True
                    }
                    for e in optimal_events
                ],
                'current_time': current_time.isoformat(),
                'monitoring_window': f"T-{ECONOMIC.PRE_EVENT_MINUTES} to T+{ECONOMIC.POST_EVENT_MINUTES}"
            }
            
            return {
                'score': min(score, 10),  # Cap at 10 points
                'details': timing_details,
                'summary': f"Timing score: {min(score, 10)}/10"
            }
            
        except Exception as e:
            logger.error(f"Error in timing analysis: {e}")
            return {'score': 0, 'error': str(e)}
    
    async def _analyze_surprise_factor(self, 
                                     currencies: List[str],
                                     current_time: datetime) -> Dict[str, Any]:
        """Analyze surprise factor in recent releases"""
        
        score = 0
        surprise_details = {}
        
        try:
            # Look at recent releases (last 30 minutes)
            recent_cutoff = current_time - timedelta(minutes=30)
            
            surprise_events = []
            for event in self.recent_releases:
                if (event.currency in currencies and 
                    event.release_time >= recent_cutoff and
                    event.actual_value is not None and
                    event.forecast_value is not None):
                    
                    # Calculate surprise factor
                    if event.forecast_value != 0:
                        surprise_pct = abs(event.actual_value - event.forecast_value) / abs(event.forecast_value)
                    else:
                        surprise_pct = 1.0 if event.actual_value != 0 else 0.0
                    
                    if surprise_pct >= 0.05:  # 5% or more surprise
                        surprise_events.append({
                            'event': event,
                            'surprise_pct': surprise_pct,
                            'direction': 'POSITIVE' if event.actual_value > event.forecast_value else 'NEGATIVE'
                        })
                        
                        # Score based on surprise magnitude
                        if surprise_pct >= 0.20:  # 20%+ surprise
                            score += 10
                        elif surprise_pct >= 0.10:  # 10%+ surprise
                            score += 7
                        elif surprise_pct >= 0.05:  # 5%+ surprise
                            score += 4
            
            surprise_details = {
                'surprise_events': [
                    {
                        'name': s['event'].event_name,
                        'currency': s['event'].currency,
                        'actual': s['event'].actual_value,
                        'forecast': s['event'].forecast_value,
                        'surprise_pct': round(s['surprise_pct'] * 100, 2),
                        'direction': s['direction']
                    }
                    for s in surprise_events
                ],
                'total_surprises': len(surprise_events)
            }
            
            return {
                'score': min(score, 10),  # Cap at 10 points
                'details': surprise_details,
                'summary': f"Surprise factor score: {min(score, 10)}/10"
            }
            
        except Exception as e:
            logger.error(f"Error in surprise factor analysis: {e}")
            return {'score': 0, 'error': str(e)}
    
    async def _analyze_economic_momentum_trend(self, currencies: List[str]) -> Dict[str, Any]:
        """Analyze overall economic momentum trend"""
        
        score = 0
        momentum_details = {}
        
        try:
            # Analyze recent economic data trends
            # This would typically involve analyzing multiple indicators
            # For now, simplified implementation
            
            recent_data = {}
            for currency in currencies:
                # Mock momentum analysis
                recent_data[currency] = {
                    'trend': 'POSITIVE',  # POSITIVE/NEGATIVE/NEUTRAL
                    'strength': 0.7,      # 0-1 scale
                    'consistency': 0.8    # 0-1 scale
                }
            
            # Score based on momentum alignment
            positive_momentum = sum(1 for data in recent_data.values() 
                                  if data['trend'] == 'POSITIVE')
            negative_momentum = sum(1 for data in recent_data.values() 
                                  if data['trend'] == 'NEGATIVE')
            
            if positive_momentum > negative_momentum:
                score = min(positive_momentum * 2, 5)
            elif negative_momentum > positive_momentum:
                score = min(negative_momentum * 2, 5)
            
            momentum_details = {
                'currency_momentum': recent_data,
                'overall_trend': 'POSITIVE' if positive_momentum > negative_momentum else 
                               'NEGATIVE' if negative_momentum > positive_momentum else 'MIXED',
                'momentum_strength': sum(data['strength'] for data in recent_data.values()) / len(recent_data)
            }
            
            return {
                'score': score,
                'details': momentum_details,
                'summary': f"Economic momentum score: {score}/5"
            }
            
        except Exception as e:
            logger.error(f"Error in momentum analysis: {e}")
            return {'score': 0, 'error': str(e)}
    
    # Utility Methods
    
    def _predict_market_reaction(self, event_data: Dict) -> str:
        """Predict expected market reaction to economic event"""
        
        # Simplified prediction logic
        if event_data['impact'] == 'HIGH':
            if 'Rate' in event_data['name'] or 'NFP' in event_data['name']:
                return 'HIGH_VOLATILITY'
            else:
                return 'MODERATE_VOLATILITY'
        else:
            return 'LOW_VOLATILITY'
    
    def _calculate_volatility_impact(self, event_data: Dict) -> int:
        """Calculate expected volatility impact (1-10 scale)"""
        
        impact_scores = {
            'HIGH': 8,
            'MEDIUM': 5,
            'LOW': 2
        }
        
        base_score = impact_scores.get(event_data['impact'], 2)
        
        # Adjust for specific events
        if any(keyword in event_data['name'] for keyword in 
               ['NFP', 'Rate Decision', 'CPI', 'GDP']):
            base_score += 2
        
        return min(base_score, 10)
    
    async def get_next_high_impact_events(self, 
                                        currencies: List[str],
                                        hours_ahead: int = 24) -> List[EconomicEvent]:
        """Get upcoming high-impact events for specified currencies"""
        
        await self._update_economic_calendar()
        
        cutoff_time = datetime.now(timezone.utc) + timedelta(hours=hours_ahead)
        
        return [
            event for event in self.upcoming_events
            if (event.currency in currencies and 
                event.impact_level == 'HIGH' and
                event.release_time <= cutoff_time)
        ]
    
    async def is_economic_window_active(self, 
                                      currency_pair: str,
                                      current_time: datetime = None) -> Tuple[bool, str]:
        """Check if we're in an active economic release window"""
        
        if current_time is None:
            current_time = datetime.now(timezone.utc)
        
        base_currency, quote_currency = currency_pair.split('/')
        relevant_currencies = [base_currency, quote_currency, 'USD']
        
        # Check for events within monitoring window
        window_start = current_time - timedelta(minutes=ECONOMIC.PRE_EVENT_MINUTES)
        window_end = current_time + timedelta(minutes=ECONOMIC.POST_EVENT_MINUTES)
        
        active_events = []
        for event in self.upcoming_events + self.recent_releases:
            if (event.currency in relevant_currencies and 
                window_start <= event.release_time <= window_end and
                event.impact_level == 'HIGH'):
                active_events.append(event)
        
        if active_events:
            event_names = [e.event_name for e in active_events]
            return True, f"Active events: {', '.join(event_names)}"
        
        return False, "No active economic events"

# Export main classes
__all__ = [
    'EconomicDataHandler',
    'EconomicEvent',
    'EconomicScore'
]
