# IEMSS Strategy Implementation Guide
## Institutional Economic Momentum Scalping Strategy

### Technical Implementation Details

#### Entry Signal Generation Algorithm
```
1. Monitor Economic Calendar (T-5 minutes to T+5 minutes)
2. IF (Data Impact = HIGH AND Actual ≠ Forecast by threshold)
   THEN Scan for Technical Setups
3. Multi-Timeframe Confluence Check:
   - 1M: Price action direction
   - 5M: Moving average alignment  
   - 15M: Support/resistance levels
4. IF (≥2 timeframes aligned) THEN Generate Signal
5. Calculate Position Size = (Account × 2%) ÷ (Entry - Stop Loss)
6. Execute Trade with pre-defined stop/target
```

#### Risk Calculation Matrix
```
Position Size = (Risk Amount) ÷ (Entry Price - Stop Loss Price) × FX Rate
Where:
- Risk Amount = Account Balance × 2%
- Stop Loss = Dynamic based on ATR(14) × 1.5
- Take Profit = Stop Loss Distance × 2.0 (minimum)
```

#### Economic Data Priority Matrix
```
TIER 1 (Immediate Action):
- Non-Farm Payrolls (USA) - First Friday, 8:30 EST
- Federal Reserve Decisions - 8 times yearly, 2:00 PM EST
- ECB Rate Decisions - Monthly, 7:45 AM EST
- CPI Releases (US/EU/UK) - Monthly, various times

TIER 2 (Secondary Signals):
- GDP Reports - Quarterly
- Employment Data (ex-NFP) - Monthly
- Manufacturing PMI - Monthly
- Central Bank Speeches - Ongoing
```

### Live Trading Protocols

#### Pre-Market Checklist (Daily)
1. ✅ Economic calendar review (next 4 hours)
2. ✅ Major support/resistance level identification
3. ✅ Currency correlation matrix update
4. ✅ Account risk exposure check (<50% allocated)
5. ✅ Technology systems verification

#### Intra-Trade Management
- **Minute 0-5**: Monitor for initial momentum
- **Minute 5-15**: Assess continuation vs reversal
- **Minute 15-25**: Prepare exit strategy
- **Minute 25-30**: Force close if no progress

#### Post-Trade Analysis
1. Log all trade details in Supabase database
2. Calculate actual vs expected performance
3. Update strategy parameters if needed
4. Review correlation impact on portfolio

### Technology Stack Integration

#### IBKR TWS Setup
- Real-time Level 2 data subscription
- Economic calendar API integration
- Advanced order types configuration
- Risk management alerts setup

#### Supabase Database Schema
- forex_trades: Complete trade logging
- economic_indicators: Release tracking
- strategy_performance: Daily/weekly metrics
- risk_alerts: Automated monitoring

### Performance Monitoring Dashboard

#### Daily KPIs
- Trades Executed vs Planned
- Win Rate (Target: >65%)
- Average R:R Achieved (Target: >2.0)
- Daily P&L vs Risk Budget

#### Weekly Reviews
- Strategy Parameter Adjustments
- Market Condition Analysis
- Risk Management Effectiveness
- Technology Performance Assessment

---
*Strategy Last Updated: June 11, 2025*
*Implementation Status: Ready for Backtesting*
