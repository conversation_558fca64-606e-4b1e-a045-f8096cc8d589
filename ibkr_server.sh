#!/bin/bash

# IBKR MCP Server Startup Script
# This script ensures the server runs with the correct virtual environment
# and tries both live and paper trading ports

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Path to the virtual environment
VENV_PATH="$SCRIPT_DIR/ibkr_mcp_server/source/.venv"
VENV_PYTHON="$VENV_PATH/bin/python3"

# Check if virtual environment exists
if [ ! -f "$VENV_PYTHON" ]; then
    echo "Error: Virtual environment not found at $VENV_PATH"
    echo "Please ensure the virtual environment is set up correctly."
    exit 1
fi

# Check if MCP package is installed in the virtual environment
if ! "$VENV_PYTHON" -c "import mcp.server" 2>/dev/null; then
    echo "Error: MCP package not found in virtual environment"
    echo "Installing missing dependencies..."
    "$VENV_PATH/bin/pip" install mcp==1.8.0
    if [ $? -ne 0 ]; then
        echo "Failed to install MCP package"
        exit 1
    fi
fi

# Parse arguments to extract host, port, and client-id
HOST="127.0.0.1"
PORT=""
CLIENT_ID="1"
OTHER_ARGS=""

while [ $# -gt 0 ]; do
  case $1 in
    --host)
      HOST="$2"
      shift 2
      ;;
    --port)
      PORT="$2"
      shift 2
      ;;
    --client-id)
      CLIENT_ID="$2"
      shift 2
      ;;
    *)
      OTHER_ARGS="$OTHER_ARGS $1"
      shift
      ;;
  esac
done

# Try different server files in order of preference
SERVER_FILES=(
    "$SCRIPT_DIR/ibkr_mcp_server.py"
    "$SCRIPT_DIR/full_mcp_server.py"
    "$SCRIPT_DIR/final_ibkr_mcp_server.py"
    "$SCRIPT_DIR/mcp_server_main.py"
)

for SERVER_FILE in "${SERVER_FILES[@]}"; do
    if [ -f "$SERVER_FILE" ]; then
        echo "Found server file: $SERVER_FILE" >&2

        # Test if the server file can be imported (simple syntax check)
        if "$VENV_PYTHON" -m py_compile "$SERVER_FILE" 2>/dev/null; then
            echo "Server file validation passed, starting server..." >&2
            if [ -n "$PORT" ]; then
                echo "Starting IBKR MCP Server with specified port $PORT..." >&2
                exec "$VENV_PYTHON" "$SERVER_FILE" --host "$HOST" --port "$PORT" --client-id "$CLIENT_ID" $OTHER_ARGS
            else
                echo "Starting IBKR MCP Server with smart port detection..." >&2
                exec "$VENV_PYTHON" "$SERVER_FILE" --host "$HOST" --client-id "$CLIENT_ID" $OTHER_ARGS
            fi
        else
            echo "Server file $SERVER_FILE failed syntax validation, trying next..." >&2
        fi
    else
        echo "Server file $SERVER_FILE not found, trying next..." >&2
    fi
done

echo "Error: No working server file found!" >&2
exit 1
