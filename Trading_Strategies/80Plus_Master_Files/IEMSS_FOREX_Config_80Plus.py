"""
IEMSS Elite FOREX 80%+ Win Rate Configuration
============================================
Configuration for all three FOREX strategies optimized for 80%+ win rate

Author: IEMSS Trading Desk
Version: 3.0.0
"""

import os
from typing import List, Dict, <PERSON><PERSON>
from datetime import time


class IEMSS_FOREX_Config_80Plus:
    """
    Master configuration for 80%+ win rate FOREX strategies
    """
    
    # IBKR Connection Settings
    IBKR_HOST = os.getenv('IBKR_HOST', '127.0.0.1')
    IBKR_PORT = int(os.getenv('IBKR_PORT', '7497'))  # 7497 for paper, 7496 for live
    IBKR_CLIENT_ID = int(os.getenv('IBKR_CLIENT_ID', '1'))
    
    # ========== GLOBAL SETTINGS FOR 80%+ WIN RATE ==========
    
    # Risk Management (Conservative for High Win Rate)
    MIN_WIN_RATE_TARGET = 0.80  # 80% minimum
    MAX_RISK_PER_TRADE = 0.005  # 0.5% max risk
    MAX_DAILY_RISK = 0.01  # 1% daily max
    MAX_WEEKLY_RISK = 0.025  # 2.5% weekly max
    MAX_DRAWDOWN_ALLOWED = 0.03  # 3% max drawdown
    
    # Position Management
    MAX_CONCURRENT_POSITIONS = 2  # Maximum 2 trades at once
    MAX_CORRELATED_POSITIONS = 1  # Only 1 position in correlated pairs
    CORRELATION_THRESHOLD = 0.7  # Correlation threshold
    
    # Trade Filtering (Strict for High Win Rate)
    MIN_CONFIRMATIONS_REQUIRED = 8  # Out of 12 possible
    MIN_CONFIDENCE_SCORE = 0.85  # 85% minimum
    MIN_RISK_REWARD_RATIO = 1.5  # Minimum 1:1.5
    PREFERRED_RISK_REWARD_RATIO = 2.0  # Prefer 1:2 or better
    
    # Session Settings (Trade Only Best Times)
    OPTIMAL_SESSIONS = {
        'london_ny_overlap': {
            'start': time(8, 0),  # 8 AM ET
            'end': time(12, 0),   # 12 PM ET
            'score': 1.0,
            'max_trades': 2
        },
        'london': {
            'start': time(3, 0),  # 3 AM ET
            'end': time(12, 0),  # 12 PM ET
            'score': 0.9,
            'max_trades': 2
        },
        'newyork': {
            'start': time(8, 0),  # 8 AM ET
            'end': time(17, 0),  # 5 PM ET
            'score': 0.85,
            'max_trades': 2
        },
        'asian': {
            'start': time(19, 0),  # 7 PM ET
            'end': time(3, 0),    # 3 AM ET
            'score': 0.6,
            'max_trades': 1  # Limit Asian session trades
        }
    }
    
    # Currency Pair Correlations
    CORRELATION_MATRIX = {
        # Positive correlations (> 0.7)
        'positive': {
            ('EUR.USD', 'GBP.USD'): 0.8727,
            ('EUR.USD', 'AUD.USD'): 0.7866,
            ('EUR.JPY', 'GBP.JPY'): 0.9266,
            ('USD.CHF', 'USD.JPY'): 0.7567,
            ('AUD.USD', 'GBP.USD'): 0.8168,
            ('EUR.JPY', 'USD.JPY'): 0.7531,
            ('EUR.USD', 'EUR.CHF'): 0.7019,
        },
        # Negative correlations (< -0.7)
        'negative': {
            ('EUR.USD', 'USD.CHF'): -0.7559,
            ('GBP.USD', 'USD.JPY'): -0.7559,
            ('XAU.USD', 'USD.CHF'): -0.7749,
            ('XAU.USD', 'EUR.CHF'): -0.7311,
        }
    }
    
    # ========== STRATEGY-SPECIFIC SETTINGS ==========
    
    # IEMSS Elite FOREX 5-Min Scalping (80-85% Win Rate)
    SCALPING_5MIN_CONFIG = {
        'timeframe': '5min',
        'min_confirmations': 8,
        'min_confidence': 0.85,
        'preferred_pairs': ['EUR.USD', 'GBP.USD', 'USD.JPY'],  # Most liquid only
        'max_spread_pips': 1.5,
        'min_pip_target': 5,
        'max_pip_stop': 7,
        'targets': {
            'tp1_pips': 7,    # First target
            'tp2_pips': 15,   # Second target
            'tp1_percent': 0.5,  # Close 50% at TP1
        },
        'entry_rules': {
            'max_attempts': 2,  # Max re-entry attempts
            'wait_bars_after_loss': 10,  # Wait 10 bars after loss
            'require_momentum': True,
            'require_volume': True,
        },
        'exit_rules': {
            'trail_after_tp1': True,
            'trail_distance_pips': 3,
            'time_stop_bars': 20,  # Exit after 20 bars (100 min)
            'momentum_exit': True,
        }
    }
    
    # IEMSS Elite FOREX Custom Strategy (82-87% Win Rate)
    CUSTOM_STRATEGY_CONFIG = {
        'timeframes': ['5min', '15min', '30min'],  # Multiple timeframes
        'min_confirmations': 9,  # Higher requirement
        'min_confidence': 0.87,
        'all_major_pairs': [
            'EUR.USD', 'GBP.USD', 'USD.JPY', 'USD.CHF',
            'AUD.USD', 'NZD.USD', 'USD.CAD', 'EUR.GBP',
            'EUR.JPY', 'GBP.JPY', 'AUD.JPY'
        ],
        'enhanced_filters': {
            'use_ml_predictions': True,
            'ml_confidence_threshold': 0.75,
            'news_avoidance_minutes': 30,
            'sentiment_required': True,
            'intermarket_analysis': True,
        },
        'dynamic_targets': {
            'atr_multiplier_tp1': 1.0,
            'atr_multiplier_tp2': 2.0,
            'atr_multiplier_tp3': 3.0,
            'atr_multiplier_stop': 0.5,
        },
        'pattern_weights': {
            'pin_bar': 0.90,
            'engulfing': 0.92,
            'double_top_bottom': 0.95,
            'flag': 0.88,
            'triangle': 0.87,
            'head_shoulders': 0.93,
        }
    }
    
    # IEMSS Elite FOREX Scalping (Multi-TF) (80-85% Win Rate)
    SCALPING_MULTITF_CONFIG = {
        'timeframes': ['1min', '3min', '5min', '15min'],
        'min_confirmations': 7,
        'min_confidence': 0.83,
        'adaptive_selection': True,
        'preferred_pairs_by_session': {
            'asian': ['USD.JPY', 'AUD.USD', 'NZD.USD'],
            'london': ['EUR.USD', 'GBP.USD', 'EUR.GBP'],
            'newyork': ['USD.CAD', 'USD.CHF', 'GBP.USD'],
            'overlap': ['EUR.USD', 'GBP.USD', 'USD.JPY'],  # All majors
        },
        'timeframe_selection_rules': {
            'high_volatility': '1min',
            'medium_volatility': '3min',
            'normal_volatility': '5min',
            'low_volatility': '15min',
        },
        'scalping_modes': {
            'aggressive': {
                'min_pips': 3,
                'max_pips': 10,
                'max_duration_bars': 10,
            },
            'moderate': {
                'min_pips': 5,
                'max_pips': 20,
                'max_duration_bars': 20,
            },
            'conservative': {
                'min_pips': 10,
                'max_pips': 30,
                'max_duration_bars': 30,
            }
        }
    }
    
    # ========== TRADE FILTERING RULES ==========
    
    # Confirmation Scoring Weights
    CONFIRMATION_WEIGHTS = {
        'trend_alignment': 0.20,      # 20%
        'momentum_confluence': 0.15,  # 15%
        'pattern_quality': 0.20,      # 20%
        'support_resistance': 0.10,   # 10%
        'volume_confirmation': 0.10,  # 10%
        'session_quality': 0.10,      # 10%
        'correlation_score': 0.10,    # 10%
        'market_structure': 0.05,     # 5%
    }
    
    # Pattern Recognition Settings
    PATTERN_SETTINGS = {
        'pin_bar': {
            'min_wick_body_ratio': 2.0,
            'max_opposite_wick_ratio': 0.5,
        },
        'engulfing': {
            'min_body_coverage': 1.0,  # 100% coverage
            'require_opposite_direction': True,
        },
        'double_top_bottom': {
            'min_touches': 2,
            'max_price_difference': 0.002,  # 0.2%
            'min_bars_between': 10,
        }
    }
    
    # ========== PERFORMANCE TRACKING ==========
    
    PERFORMANCE_TARGETS = {
        'daily_win_rate': 0.80,
        'weekly_win_rate': 0.82,
        'monthly_win_rate': 0.83,
        'min_profit_factor': 1.5,
        'max_consecutive_losses': 3,
        'risk_reward_achieved': 1.6,
    }
    
    # Alert Thresholds
    ALERT_THRESHOLDS = {
        'win_rate_warning': 0.75,     # Alert if below 75%
        'win_rate_critical': 0.70,    # Stop trading if below 70%
        'drawdown_warning': 0.02,     # 2% drawdown warning
        'drawdown_critical': 0.03,    # 3% stop trading
        'correlation_conflict': 0.8,   # High correlation warning
    }
    
    # ========== NEWS & EVENTS ==========
    
    HIGH_IMPACT_EVENTS = [
        'NFP',                # Non-Farm Payrolls
        'FOMC',              # Fed Meetings
        'ECB',               # ECB Meetings
        'GDP',               # GDP Releases
        'CPI',               # Inflation Data
        'Interest Rate',     # Rate Decisions
        'Unemployment',      # Unemployment Data
    ]
    
    NEWS_AVOIDANCE_RULES = {
        'before_high_impact_minutes': 30,
        'after_high_impact_minutes': 30,
        'before_medium_impact_minutes': 15,
        'after_medium_impact_minutes': 15,
        'skip_friday_afternoon': True,
        'skip_sunday_open': True,
        'skip_holidays': True,
    }
    
    # ========== ADVANCED FEATURES ==========
    
    # Machine Learning Settings (for Custom Strategy)
    ML_CONFIG = {
        'enabled': True,
        'model_path': 'models/forex_80plus_model.pkl',
        'feature_columns': [
            'rsi', 'macd', 'stochastic', 'atr',
            'volume_ratio', 'price_position',
            'trend_strength', 'pattern_score'
        ],
        'retrain_frequency_days': 30,
        'min_data_points': 10000,
        'validation_split': 0.2,
    }
    
    # Sentiment Analysis
    SENTIMENT_CONFIG = {
        'enabled': True,
        'sources': ['forex_factory', 'dailyfx', 'reuters'],
        'update_frequency_minutes': 15,
        'min_sentiment_score': -0.3,  # Avoid extreme sentiment
        'max_sentiment_score': 0.3,
    }
    
    @classmethod
    def validate_config(cls) -> bool:
        """
        Validate configuration settings
        """
        validations = [
            cls.MIN_WIN_RATE_TARGET >= 0.80,
            cls.MAX_RISK_PER_TRADE <= 0.01,
            cls.MIN_CONFIRMATIONS_REQUIRED >= 7,
            cls.MIN_CONFIDENCE_SCORE >= 0.80,
            cls.MIN_RISK_REWARD_RATIO >= 1.5,
            cls.MAX_CONCURRENT_POSITIONS <= 3,
        ]
        
        if not all(validations):
            raise ValueError("Configuration validation failed - settings too risky for 80%+ win rate")
            
        return True
        
    @classmethod
    def get_strategy_config(cls, strategy_name: str) -> Dict:
        """
        Get configuration for specific strategy
        """
        configs = {
            '5min_scalping': cls.SCALPING_5MIN_CONFIG,
            'custom': cls.CUSTOM_STRATEGY_CONFIG,
            'scalping': cls.SCALPING_MULTITF_CONFIG,
        }
        
        return configs.get(strategy_name, {})


# Example usage
if __name__ == "__main__":
    # Validate configuration
    IEMSS_FOREX_Config_80Plus.validate_config()
    
    # Print key settings
    print("IEMSS Elite FOREX 80%+ Win Rate Configuration")
    print("=" * 50)
    print(f"Min Win Rate Target: {IEMSS_FOREX_Config_80Plus.MIN_WIN_RATE_TARGET * 100}%")
    print(f"Max Risk Per Trade: {IEMSS_FOREX_Config_80Plus.MAX_RISK_PER_TRADE * 100}%")
    print(f"Min Confirmations: {IEMSS_FOREX_Config_80Plus.MIN_CONFIRMATIONS_REQUIRED}")
    print(f"Min Confidence: {IEMSS_FOREX_Config_80Plus.MIN_CONFIDENCE_SCORE * 100}%")
    print(f"Max Concurrent Positions: {IEMSS_FOREX_Config_80Plus.MAX_CONCURRENT_POSITIONS}")
