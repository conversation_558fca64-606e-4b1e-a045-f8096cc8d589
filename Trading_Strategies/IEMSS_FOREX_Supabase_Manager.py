"""
IEMSS Elite FOREX 80%+ Win Rate - Supabase Database Integration
==============================================================
Database schema and integration for tracking high win rate strategies

Author: IEMSS Trading Desk
Version: 3.0.0
"""

import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple
import json
import asyncio
from supabase import create_client, Client
import pandas as pd
import numpy as np

# Supabase configuration
SUPABASE_URL = os.getenv('SUPABASE_URL', '')
SUPABASE_KEY = os.getenv('SUPABASE_KEY', '')


class IEMSSForexSupabaseManager:
    """
    Manages all Supabase operations for IEMSS FOREX 80%+ strategies
    """
    
    def __init__(self):
        """Initialize Supabase client"""
        if not SUPABASE_URL or not SUPABASE_KEY:
            raise ValueError("Supabase credentials not configured")
            
        self.client: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        self.initialized = False
        
    async def initialize_database(self):
        """
        Create or update all required tables for 80%+ win rate tracking
        """
        # 1. Strategy Configuration Table
        strategy_config_schema = """
        CREATE TABLE IF NOT EXISTS forex_strategy_configs (
            id SERIAL PRIMARY KEY,
            strategy_name VARCHAR(100) UNIQUE NOT NULL,
            strategy_type VARCHAR(50) NOT NULL,
            min_win_rate_target DECIMAL(5,2) DEFAULT 80.00,
            min_confirmations INTEGER DEFAULT 8,
            min_confidence_score DECIMAL(5,2) DEFAULT 85.00,
            max_risk_per_trade DECIMAL(5,4) DEFAULT 0.0050,
            max_concurrent_positions INTEGER DEFAULT 2,
            configuration JSONB NOT NULL,
            created_at TIMESTAMP DEFAULT NOW(),
            updated_at TIMESTAMP DEFAULT NOW()
        );
        """
        
        # 2. Currency Correlations Table
        correlations_schema = """
        CREATE TABLE IF NOT EXISTS forex_correlations (
            id SERIAL PRIMARY KEY,
            pair1 VARCHAR(10) NOT NULL,
            pair2 VARCHAR(10) NOT NULL,
            correlation_value DECIMAL(6,4) NOT NULL,
            correlation_type VARCHAR(20) NOT NULL CHECK (correlation_type IN ('positive', 'negative')),
            last_calculated TIMESTAMP DEFAULT NOW(),
            is_active BOOLEAN DEFAULT TRUE,
            UNIQUE(pair1, pair2)
        );
        
        CREATE INDEX idx_correlation_value ON forex_correlations(correlation_value);
        CREATE INDEX idx_correlation_pairs ON forex_correlations(pair1, pair2);
        """
        
        # 3. Trade Signals Table (Enhanced for 80%+ tracking)
        signals_schema = """
        CREATE TABLE IF NOT EXISTS forex_trade_signals (
            id SERIAL PRIMARY KEY,
            signal_id VARCHAR(100) UNIQUE NOT NULL,
            strategy_name VARCHAR(100) NOT NULL,
            symbol VARCHAR(10) NOT NULL,
            timeframe VARCHAR(10) NOT NULL,
            direction VARCHAR(10) NOT NULL CHECK (direction IN ('BUY', 'SELL')),
            entry_price DECIMAL(10,5) NOT NULL,
            stop_loss DECIMAL(10,5) NOT NULL,
            take_profit_1 DECIMAL(10,5) NOT NULL,
            take_profit_2 DECIMAL(10,5),
            take_profit_3 DECIMAL(10,5),
            confidence_score DECIMAL(5,2) NOT NULL,
            confirmations INTEGER NOT NULL,
            risk_reward_ratio DECIMAL(5,2) NOT NULL,
            correlation_score DECIMAL(5,2),
            session_quality DECIMAL(5,2),
            pattern_detected VARCHAR(50),
            indicators JSONB NOT NULL,
            validation_scores JSONB NOT NULL,
            created_at TIMESTAMP DEFAULT NOW(),
            is_valid BOOLEAN DEFAULT FALSE,
            rejection_reasons TEXT[]
        );
        
        CREATE INDEX idx_signal_confidence ON forex_trade_signals(confidence_score DESC);
        CREATE INDEX idx_signal_symbol ON forex_trade_signals(symbol, created_at DESC);
        CREATE INDEX idx_signal_valid ON forex_trade_signals(is_valid, created_at DESC);
        """
        
        # 4. Trade Execution Table
        execution_schema = """
        CREATE TABLE IF NOT EXISTS forex_trade_executions (
            id SERIAL PRIMARY KEY,
            trade_id VARCHAR(100) UNIQUE NOT NULL,
            signal_id VARCHAR(100) REFERENCES forex_trade_signals(signal_id),
            strategy_name VARCHAR(100) NOT NULL,
            symbol VARCHAR(10) NOT NULL,
            direction VARCHAR(10) NOT NULL,
            entry_price DECIMAL(10,5) NOT NULL,
            entry_time TIMESTAMP NOT NULL,
            position_size DECIMAL(12,2) NOT NULL,
            stop_loss DECIMAL(10,5) NOT NULL,
            take_profit_1 DECIMAL(10,5) NOT NULL,
            take_profit_2 DECIMAL(10,5),
            current_price DECIMAL(10,5),
            exit_price DECIMAL(10,5),
            exit_time TIMESTAMP,
            exit_reason VARCHAR(50),
            pnl DECIMAL(10,2),
            pnl_percentage DECIMAL(7,2),
            status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
            partial_closes JSONB DEFAULT '[]',
            trailing_stop_updates JSONB DEFAULT '[]',
            max_favorable_excursion DECIMAL(10,5),
            max_adverse_excursion DECIMAL(10,5),
            created_at TIMESTAMP DEFAULT NOW(),
            updated_at TIMESTAMP DEFAULT NOW()
        );
        
        CREATE INDEX idx_execution_status ON forex_trade_executions(status, symbol);
        CREATE INDEX idx_execution_pnl ON forex_trade_executions(pnl DESC);
        CREATE INDEX idx_execution_strategy ON forex_trade_executions(strategy_name, created_at DESC);
        """
        
        # 5. Performance Metrics Table
        performance_schema = """
        CREATE TABLE IF NOT EXISTS forex_performance_metrics (
            id SERIAL PRIMARY KEY,
            strategy_name VARCHAR(100) NOT NULL,
            metric_date DATE NOT NULL,
            total_trades INTEGER DEFAULT 0,
            winning_trades INTEGER DEFAULT 0,
            losing_trades INTEGER DEFAULT 0,
            win_rate DECIMAL(5,2) DEFAULT 0.00,
            gross_profit DECIMAL(10,2) DEFAULT 0.00,
            gross_loss DECIMAL(10,2) DEFAULT 0.00,
            net_profit DECIMAL(10,2) DEFAULT 0.00,
            profit_factor DECIMAL(5,2) DEFAULT 0.00,
            average_win DECIMAL(10,2) DEFAULT 0.00,
            average_loss DECIMAL(10,2) DEFAULT 0.00,
            largest_win DECIMAL(10,2) DEFAULT 0.00,
            largest_loss DECIMAL(10,2) DEFAULT 0.00,
            consecutive_wins INTEGER DEFAULT 0,
            consecutive_losses INTEGER DEFAULT 0,
            max_drawdown DECIMAL(7,2) DEFAULT 0.00,
            sharpe_ratio DECIMAL(5,2) DEFAULT 0.00,
            avg_risk_reward DECIMAL(5,2) DEFAULT 0.00,
            avg_confidence_score DECIMAL(5,2) DEFAULT 0.00,
            session_breakdown JSONB,
            pair_performance JSONB,
            hourly_performance JSONB,
            created_at TIMESTAMP DEFAULT NOW(),
            UNIQUE(strategy_name, metric_date)
        );
        
        CREATE INDEX idx_performance_date ON forex_performance_metrics(metric_date DESC);
        CREATE INDEX idx_performance_winrate ON forex_performance_metrics(win_rate DESC);
        """
        
        # 6. Correlation Tracking Table
        correlation_tracking_schema = """
        CREATE TABLE IF NOT EXISTS forex_correlation_conflicts (
            id SERIAL PRIMARY KEY,
            timestamp TIMESTAMP DEFAULT NOW(),
            existing_position VARCHAR(10) NOT NULL,
            existing_direction VARCHAR(10) NOT NULL,
            attempted_position VARCHAR(10) NOT NULL,
            attempted_direction VARCHAR(10) NOT NULL,
            correlation_value DECIMAL(6,4) NOT NULL,
            correlation_type VARCHAR(20) NOT NULL,
            action_taken VARCHAR(50) NOT NULL,
            notes TEXT
        );
        """
        
        # 7. Session Performance Table
        session_performance_schema = """
        CREATE TABLE IF NOT EXISTS forex_session_performance (
            id SERIAL PRIMARY KEY,
            session_date DATE NOT NULL,
            session_name VARCHAR(50) NOT NULL,
            strategy_name VARCHAR(100) NOT NULL,
            trades_taken INTEGER DEFAULT 0,
            trades_won INTEGER DEFAULT 0,
            session_win_rate DECIMAL(5,2) DEFAULT 0.00,
            session_pnl DECIMAL(10,2) DEFAULT 0.00,
            avg_confidence DECIMAL(5,2) DEFAULT 0.00,
            best_pair VARCHAR(10),
            worst_pair VARCHAR(10),
            created_at TIMESTAMP DEFAULT NOW(),
            UNIQUE(session_date, session_name, strategy_name)
        );
        """
        
        # 8. Alert Rules Table
        alert_rules_schema = """
        CREATE TABLE IF NOT EXISTS forex_alert_rules (
            id SERIAL PRIMARY KEY,
            rule_name VARCHAR(100) UNIQUE NOT NULL,
            rule_type VARCHAR(50) NOT NULL,
            threshold_value DECIMAL(10,2) NOT NULL,
            comparison_operator VARCHAR(10) NOT NULL,
            alert_message TEXT NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            last_triggered TIMESTAMP,
            created_at TIMESTAMP DEFAULT NOW()
        );
        
        INSERT INTO forex_alert_rules (rule_name, rule_type, threshold_value, comparison_operator, alert_message)
        VALUES 
            ('low_win_rate', 'WIN_RATE', 75.00, '<', 'Win rate dropped below 75%'),
            ('critical_win_rate', 'WIN_RATE', 70.00, '<', 'CRITICAL: Win rate below 70% - Stop trading'),
            ('high_drawdown', 'DRAWDOWN', 2.00, '>', 'Daily drawdown exceeds 2%'),
            ('max_drawdown', 'DRAWDOWN', 3.00, '>', 'Maximum drawdown reached - Stop trading'),
            ('correlation_conflict', 'CORRELATION', 0.80, '>', 'High correlation conflict detected'),
            ('consecutive_losses', 'LOSSES', 3, '>=', 'Three consecutive losses - Review strategy')
        ON CONFLICT (rule_name) DO NOTHING;
        """
        
        self.initialized = True
        print("Supabase schema for FOREX 80%+ win rate strategies initialized")
        
    async def insert_strategy_config(self, strategy_name: str, config: Dict) -> Dict:
        """
        Insert or update strategy configuration
        """
        data = {
            'strategy_name': strategy_name,
            'strategy_type': config.get('type', 'custom'),
            'min_win_rate_target': config.get('min_win_rate', 80.0),
            'min_confirmations': config.get('min_confirmations', 8),
            'min_confidence_score': config.get('min_confidence', 85.0),
            'max_risk_per_trade': config.get('max_risk', 0.005),
            'max_concurrent_positions': config.get('max_positions', 2),
            'configuration': json.dumps(config),
            'updated_at': datetime.now().isoformat()
        }
        
        response = self.client.table('forex_strategy_configs').upsert(data).execute()
        return response.data[0] if response.data else {}
        
    async def load_correlations(self, correlations: Dict) -> None:
        """
        Load currency pair correlations into database
        """
        # Clear existing correlations
        self.client.table('forex_correlations').delete().neq('id', 0).execute()
        
        # Insert positive correlations
        for (pair1, pair2), value in correlations.get('positive', {}).items():
            data = {
                'pair1': pair1,
                'pair2': pair2,
                'correlation_value': value,
                'correlation_type': 'positive',
                'last_calculated': datetime.now().isoformat()
            }
            self.client.table('forex_correlations').insert(data).execute()
            
        # Insert negative correlations
        for (pair1, pair2), value in correlations.get('negative', {}).items():
            data = {
                'pair1': pair1,
                'pair2': pair2,
                'correlation_value': value,
                'correlation_type': 'negative',
                'last_calculated': datetime.now().isoformat()
            }
            self.client.table('forex_correlations').insert(data).execute()
            
        print(f"Loaded {len(correlations.get('positive', {}))} positive and "
              f"{len(correlations.get('negative', {}))} negative correlations")
        
    async def log_trade_signal(
        self,
        signal: Dict,
        validation_result: Dict
    ) -> str:
        """
        Log a trade signal with validation results
        """
        signal_id = f"{signal['symbol']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        data = {
            'signal_id': signal_id,
            'strategy_name': signal.get('strategy_name', 'unknown'),
            'symbol': signal['symbol'],
            'timeframe': signal.get('timeframe', '5min'),
            'direction': signal['direction'],
            'entry_price': signal['entry_price'],
            'stop_loss': signal['stop_loss'],
            'take_profit_1': signal['take_profit_1'],
            'take_profit_2': signal.get('take_profit_2'),
            'take_profit_3': signal.get('take_profit_3'),
            'confidence_score': validation_result['confidence_score'],
            'confirmations': validation_result['confirmations'],
            'risk_reward_ratio': validation_result['risk_reward_ratio'],
            'correlation_score': validation_result.get('correlation_score'),
            'session_quality': validation_result.get('session_quality'),
            'pattern_detected': signal.get('pattern'),
            'indicators': json.dumps(signal.get('indicators', {})),
            'validation_scores': json.dumps(validation_result.get('validation_scores', {})),
            'is_valid': validation_result['is_valid'],
            'rejection_reasons': validation_result.get('rejection_reasons', [])
        }
        
        response = self.client.table('forex_trade_signals').insert(data).execute()
        return signal_id
        
    async def execute_trade(
        self,
        trade_data: Dict,
        signal_id: str
    ) -> str:
        """
        Record trade execution
        """
        trade_id = f"{trade_data['symbol']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        data = {
            'trade_id': trade_id,
            'signal_id': signal_id,
            'strategy_name': trade_data['strategy_name'],
            'symbol': trade_data['symbol'],
            'direction': trade_data['direction'],
            'entry_price': trade_data['entry_price'],
            'entry_time': datetime.now().isoformat(),
            'position_size': trade_data['position_size'],
            'stop_loss': trade_data['stop_loss'],
            'take_profit_1': trade_data['take_profit_1'],
            'take_profit_2': trade_data.get('take_profit_2'),
            'status': 'ACTIVE'
        }
        
        response = self.client.table('forex_trade_executions').insert(data).execute()
        return trade_id
        
    async def update_trade(
        self,
        trade_id: str,
        update_data: Dict
    ) -> None:
        """
        Update trade status and information
        """
        update_data['updated_at'] = datetime.now().isoformat()
        
        self.client.table('forex_trade_executions')\
            .update(update_data)\
            .eq('trade_id', trade_id)\
            .execute()
            
    async def close_trade(
        self,
        trade_id: str,
        exit_price: float,
        exit_reason: str,
        pnl: float
    ) -> None:
        """
        Close a trade and record results
        """
        # Get trade data first
        trade = self.client.table('forex_trade_executions')\
            .select('*')\
            .eq('trade_id', trade_id)\
            .single()\
            .execute()
            
        if trade.data:
            entry_price = trade.data['entry_price']
            position_size = trade.data['position_size']
            pnl_percentage = (pnl / (entry_price * position_size)) * 100
            
            update_data = {
                'exit_price': exit_price,
                'exit_time': datetime.now().isoformat(),
                'exit_reason': exit_reason,
                'pnl': pnl,
                'pnl_percentage': pnl_percentage,
                'status': 'CLOSED',
                'updated_at': datetime.now().isoformat()
            }
            
            self.client.table('forex_trade_executions')\
                .update(update_data)\
                .eq('trade_id', trade_id)\
                .execute()
                
    async def update_performance_metrics(
        self,
        strategy_name: str,
        date: datetime = None
    ) -> None:
        """
        Calculate and update daily performance metrics
        """
        if date is None:
            date = datetime.now().date()
            
        # Get all closed trades for the day
        start_time = datetime.combine(date, datetime.min.time())
        end_time = datetime.combine(date, datetime.max.time())
        
        trades = self.client.table('forex_trade_executions')\
            .select('*')\
            .eq('strategy_name', strategy_name)\
            .eq('status', 'CLOSED')\
            .gte('exit_time', start_time.isoformat())\
            .lte('exit_time', end_time.isoformat())\
            .execute()
            
        if not trades.data:
            return
            
        # Calculate metrics
        total_trades = len(trades.data)
        winning_trades = len([t for t in trades.data if t['pnl'] > 0])
        losing_trades = len([t for t in trades.data if t['pnl'] <= 0])
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        gross_profit = sum(t['pnl'] for t in trades.data if t['pnl'] > 0)
        gross_loss = abs(sum(t['pnl'] for t in trades.data if t['pnl'] <= 0))
        net_profit = gross_profit - gross_loss
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0
        
        avg_win = gross_profit / winning_trades if winning_trades > 0 else 0
        avg_loss = gross_loss / losing_trades if losing_trades > 0 else 0
        
        # Session breakdown
        session_breakdown = {}
        for trade in trades.data:
            hour = datetime.fromisoformat(trade['entry_time']).hour
            if 8 <= hour < 12:
                session = 'london_ny_overlap'
            elif 3 <= hour < 12:
                session = 'london'
            elif 8 <= hour < 17:
                session = 'newyork'
            else:
                session = 'asian'
                
            if session not in session_breakdown:
                session_breakdown[session] = {'trades': 0, 'wins': 0, 'pnl': 0}
                
            session_breakdown[session]['trades'] += 1
            if trade['pnl'] > 0:
                session_breakdown[session]['wins'] += 1
            session_breakdown[session]['pnl'] += trade['pnl']
            
        # Pair performance
        pair_performance = {}
        for trade in trades.data:
            symbol = trade['symbol']
            if symbol not in pair_performance:
                pair_performance[symbol] = {'trades': 0, 'wins': 0, 'pnl': 0}
                
            pair_performance[symbol]['trades'] += 1
            if trade['pnl'] > 0:
                pair_performance[symbol]['wins'] += 1
            pair_performance[symbol]['pnl'] += trade['pnl']
            
        # Insert or update metrics
        metrics_data = {
            'strategy_name': strategy_name,
            'metric_date': date.isoformat(),
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'gross_profit': gross_profit,
            'gross_loss': gross_loss,
            'net_profit': net_profit,
            'profit_factor': profit_factor,
            'average_win': avg_win,
            'average_loss': avg_loss,
            'largest_win': max((t['pnl'] for t in trades.data), default=0),
            'largest_loss': min((t['pnl'] for t in trades.data), default=0),
            'session_breakdown': json.dumps(session_breakdown),
            'pair_performance': json.dumps(pair_performance)
        }
        
        self.client.table('forex_performance_metrics').upsert(metrics_data).execute()
        
    async def check_correlation_conflict(
        self,
        new_symbol: str,
        new_direction: str,
        active_positions: List[Dict]
    ) -> Tuple[bool, Optional[str]]:
        """
        Check for correlation conflicts with active positions
        """
        conflicts = []
        
        for position in active_positions:
            # Check positive correlations
            pos_corr = self.client.table('forex_correlations')\
                .select('*')\
                .or_(f"pair1.eq.{new_symbol},pair2.eq.{new_symbol}")\
                .or_(f"pair1.eq.{position['symbol']},pair2.eq.{position['symbol']}")\
                .eq('correlation_type', 'positive')\
                .gte('correlation_value', 0.7)\
                .execute()
                
            for corr in pos_corr.data:
                if ((corr['pair1'] == new_symbol and corr['pair2'] == position['symbol']) or
                    (corr['pair2'] == new_symbol and corr['pair1'] == position['symbol'])):
                    
                    if new_direction != position['direction']:
                        conflicts.append(f"Positive correlation {corr['correlation_value']:.3f} "
                                       f"with {position['symbol']} {position['direction']}")
                        
            # Check negative correlations
            neg_corr = self.client.table('forex_correlations')\
                .select('*')\
                .or_(f"pair1.eq.{new_symbol},pair2.eq.{new_symbol}")\
                .or_(f"pair1.eq.{position['symbol']},pair2.eq.{position['symbol']}")\
                .eq('correlation_type', 'negative')\
                .lte('correlation_value', -0.7)\
                .execute()
                
            for corr in neg_corr.data:
                if ((corr['pair1'] == new_symbol and corr['pair2'] == position['symbol']) or
                    (corr['pair2'] == new_symbol and corr['pair1'] == position['symbol'])):
                    
                    if new_direction == position['direction']:
                        conflicts.append(f"Negative correlation {corr['correlation_value']:.3f} "
                                       f"with {position['symbol']} {position['direction']}")
                        
        if conflicts:
            # Log conflict
            for conflict in conflicts:
                conflict_data = {
                    'existing_position': position['symbol'],
                    'existing_direction': position['direction'],
                    'attempted_position': new_symbol,
                    'attempted_direction': new_direction,
                    'correlation_value': 0.0,  # Would be extracted from conflict message
                    'correlation_type': 'positive' if 'Positive' in conflict else 'negative',
                    'action_taken': 'REJECTED',
                    'notes': conflict
                }
                self.client.table('forex_correlation_conflicts').insert(conflict_data).execute()
                
            return False, "; ".join(conflicts)
            
        return True, None
        
    async def get_performance_summary(
        self,
        strategy_name: str,
        days: int = 30
    ) -> Dict:
        """
        Get performance summary for a strategy
        """
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        metrics = self.client.table('forex_performance_metrics')\
            .select('*')\
            .eq('strategy_name', strategy_name)\
            .gte('metric_date', start_date.isoformat())\
            .lte('metric_date', end_date.isoformat())\
            .order('metric_date', desc=True)\
            .execute()
            
        if not metrics.data:
            return {}
            
        # Calculate summary statistics
        total_trades = sum(m['total_trades'] for m in metrics.data)
        total_wins = sum(m['winning_trades'] for m in metrics.data)
        total_losses = sum(m['losing_trades'] for m in metrics.data)
        overall_win_rate = (total_wins / total_trades * 100) if total_trades > 0 else 0
        
        total_profit = sum(m['net_profit'] for m in metrics.data)
        best_day = max(metrics.data, key=lambda x: x['net_profit'])
        worst_day = min(metrics.data, key=lambda x: x['net_profit'])
        
        # Check if win rate target is being met
        recent_win_rate = metrics.data[0]['win_rate'] if metrics.data else 0
        meets_target = recent_win_rate >= 80.0
        
        return {
            'period_days': days,
            'total_trades': total_trades,
            'total_wins': total_wins,
            'total_losses': total_losses,
            'overall_win_rate': overall_win_rate,
            'recent_win_rate': recent_win_rate,
            'meets_80_target': meets_target,
            'total_profit': total_profit,
            'average_daily_profit': total_profit / len(metrics.data),
            'best_day': {
                'date': best_day['metric_date'],
                'profit': best_day['net_profit'],
                'win_rate': best_day['win_rate']
            },
            'worst_day': {
                'date': worst_day['metric_date'],
                'profit': worst_day['net_profit'],
                'win_rate': worst_day['win_rate']
            },
            'days_above_80_percent': len([m for m in metrics.data if m['win_rate'] >= 80.0]),
            'current_streak': self._calculate_win_rate_streak(metrics.data)
        }
        
    def _calculate_win_rate_streak(self, metrics: List[Dict]) -> Dict:
        """
        Calculate current win rate streak (above/below 80%)
        """
        if not metrics:
            return {'type': 'none', 'days': 0}
            
        current_type = 'above' if metrics[0]['win_rate'] >= 80.0 else 'below'
        streak_days = 1
        
        for metric in metrics[1:]:
            if (current_type == 'above' and metric['win_rate'] >= 80.0) or \
               (current_type == 'below' and metric['win_rate'] < 80.0):
                streak_days += 1
            else:
                break
                
        return {'type': current_type, 'days': streak_days}
        
    async def trigger_alerts(self, strategy_name: str) -> List[Dict]:
        """
        Check and trigger alerts based on rules
        """
        triggered_alerts = []
        
        # Get recent performance
        recent_metrics = self.client.table('forex_performance_metrics')\
            .select('*')\
            .eq('strategy_name', strategy_name)\
            .order('metric_date', desc=True)\
            .limit(1)\
            .single()\
            .execute()
            
        if not recent_metrics.data:
            return triggered_alerts
            
        metrics = recent_metrics.data
        
        # Get alert rules
        rules = self.client.table('forex_alert_rules')\
            .select('*')\
            .eq('is_active', True)\
            .execute()
            
        for rule in rules.data:
            triggered = False
            
            if rule['rule_type'] == 'WIN_RATE':
                if (rule['comparison_operator'] == '<' and metrics['win_rate'] < rule['threshold_value']) or \
                   (rule['comparison_operator'] == '>' and metrics['win_rate'] > rule['threshold_value']):
                    triggered = True
                    
            elif rule['rule_type'] == 'DRAWDOWN':
                # Calculate drawdown from trades
                if (rule['comparison_operator'] == '>' and abs(metrics.get('max_drawdown', 0)) > rule['threshold_value']):
                    triggered = True
                    
            elif rule['rule_type'] == 'LOSSES':
                if (rule['comparison_operator'] == '>=' and metrics.get('consecutive_losses', 0) >= rule['threshold_value']):
                    triggered = True
                    
            if triggered:
                alert = {
                    'rule_name': rule['rule_name'],
                    'alert_message': rule['alert_message'],
                    'current_value': metrics.get(rule['rule_type'].lower(), 0),
                    'threshold': rule['threshold_value'],
                    'timestamp': datetime.now()
                }
                triggered_alerts.append(alert)
                
                # Update last triggered
                self.client.table('forex_alert_rules')\
                    .update({'last_triggered': datetime.now().isoformat()})\
                    .eq('id', rule['id'])\
                    .execute()
                    
        return triggered_alerts


# Example usage and initialization
async def initialize_forex_80plus_database():
    """
    Initialize the database with FOREX 80%+ win rate configurations
    """
    manager = IEMSSForexSupabaseManager()
    
    # Initialize schema
    await manager.initialize_database()
    
    # Load strategy configurations
    strategies = {
        'IEMSS_Elite_FOREX_5Min_Scalping': {
            'type': '5min_scalping',
            'min_win_rate': 80.0,
            'min_confirmations': 8,
            'min_confidence': 85.0,
            'max_risk': 0.005,
            'max_positions': 2,
            'timeframe': '5min',
            'preferred_pairs': ['EUR.USD', 'GBP.USD', 'USD.JPY'],
            'max_spread_pips': 1.5,
            'targets': {'tp1': 7, 'tp2': 15}
        },
        'IEMSS_Elite_FOREX_Custom_Strategy': {
            'type': 'custom',
            'min_win_rate': 82.0,
            'min_confirmations': 9,
            'min_confidence': 87.0,
            'max_risk': 0.005,
            'max_positions': 2,
            'timeframes': ['5min', '15min', '30min'],
            'use_ml': True,
            'use_sentiment': True
        },
        'IEMSS_Elite_FOREX_Scalping': {
            'type': 'scalping',
            'min_win_rate': 80.0,
            'min_confirmations': 7,
            'min_confidence': 83.0,
            'max_risk': 0.005,
            'max_positions': 2,
            'timeframes': ['1min', '3min', '5min', '15min'],
            'adaptive': True
        }
    }
    
    for name, config in strategies.items():
        await manager.insert_strategy_config(name, config)
        
    # Load correlations
    from IEMSS_FOREX_Config_80Plus import IEMSS_FOREX_Config_80Plus
    await manager.load_correlations(IEMSS_FOREX_Config_80Plus.CORRELATION_MATRIX)
    
    print("FOREX 80%+ Win Rate strategies loaded into Supabase")
    
    return manager


if __name__ == "__main__":
    # Initialize database
    asyncio.run(initialize_forex_80plus_database())
