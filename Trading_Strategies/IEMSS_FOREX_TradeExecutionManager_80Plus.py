"""
IEMSS Elite FOREX 80%+ Win Rate Trade Execution Manager
======================================================
Ensures all trades meet strict criteria for 80%+ win rate

Author: IEMSS Trading Desk
Version: 3.0.0
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import pandas as pd
import numpy as np
from dataclasses import dataclass, field
from enum import Enum

from IEMSS_FOREX_Config_80Plus import IEMSS_FOREX_Config_80Plus as Config

logger = logging.getLogger(__name__)


class TradeStatus(Enum):
    """Trade status enumeration"""
    ANALYZING = "ANALYZING"
    QUALIFIED = "QUALIFIED"
    REJECTED = "REJECTED"
    PENDING_ENTRY = "PENDING_ENTRY"
    ACTIVE = "ACTIVE"
    PARTIAL_CLOSE = "PARTIAL_CLOSE"
    CLOSED = "CLOSED"
    

@dataclass
class TradeSetupValidation:
    """Detailed validation results for trade setup"""
    symbol: str
    timestamp: datetime
    is_valid: bool
    confidence_score: float
    confirmations: int
    rejection_reasons: List[str] = field(default_factory=list)
    validation_scores: Dict[str, float] = field(default_factory=dict)
    risk_reward_ratio: float = 0.0
    correlation_check_passed: bool = True
    session_quality: float = 0.0
    

class IEMSS_FOREX_TradeExecutionManager_80Plus:
    """
    Trade execution manager ensuring 80%+ win rate through strict validation
    """
    
    def __init__(self, strategy_type: str):
        """
        Initialize trade execution manager
        
        Args:
            strategy_type: '5min_scalping', 'custom', or 'scalping'
        """
        self.strategy_type = strategy_type
        self.config = Config()
        self.strategy_config = Config.get_strategy_config(strategy_type)
        
        # Trade tracking
        self.active_trades = {}
        self.pending_trades = {}
        self.closed_trades = []
        
        # Performance tracking
        self.daily_stats = {
            'trades': 0,
            'wins': 0,
            'losses': 0,
            'win_rate': 0.0,
            'pnl': 0.0
        }
        
        # Session tracking
        self.current_session = None
        self.session_trades = 0
        
        # Correlation tracking
        self.position_correlations = {}
        
    async def validate_trade_setup(
        self,
        signal: Dict,
        market_data: pd.DataFrame,
        active_positions: Dict[str, Dict]
    ) -> TradeSetupValidation:
        """
        Comprehensive validation of trade setup for 80%+ win rate
        
        Args:
            signal: Trade signal with entry, stop, targets
            market_data: Recent market data
            active_positions: Currently active positions
            
        Returns:
            TradeSetupValidation object
        """
        validation = TradeSetupValidation(
            symbol=signal['symbol'],
            timestamp=datetime.now(),
            is_valid=True,
            confidence_score=0.0,
            confirmations=0
        )
        
        # 1. Check Trading Session
        session_score = self._validate_trading_session()
        validation.validation_scores['session'] = session_score
        if session_score < 0.6:
            validation.is_valid = False
            validation.rejection_reasons.append(f"Poor trading session (score: {session_score:.2f})")
            
        # 2. Check Position Limits
        if len(active_positions) >= Config.MAX_CONCURRENT_POSITIONS:
            validation.is_valid = False
            validation.rejection_reasons.append(
                f"Maximum positions reached ({Config.MAX_CONCURRENT_POSITIONS})"
            )
            
        # 3. Check Correlations
        correlation_result = self._validate_correlations(
            signal['symbol'], 
            signal['direction'],
            active_positions
        )
        validation.correlation_check_passed = correlation_result['passed']
        validation.validation_scores['correlation'] = correlation_result['score']
        
        if not correlation_result['passed']:
            validation.is_valid = False
            validation.rejection_reasons.append(correlation_result['reason'])
            
        # 4. Validate Technical Confirmations
        tech_validation = self._validate_technical_confirmations(signal, market_data)
        validation.confirmations = tech_validation['confirmations']
        validation.validation_scores.update(tech_validation['scores'])
        
        if validation.confirmations < Config.MIN_CONFIRMATIONS_REQUIRED:
            validation.is_valid = False
            validation.rejection_reasons.append(
                f"Insufficient confirmations ({validation.confirmations}/{Config.MIN_CONFIRMATIONS_REQUIRED})"
            )
            
        # 5. Check Risk/Reward Ratio
        risk_reward = self._calculate_risk_reward(signal)
        validation.risk_reward_ratio = risk_reward
        validation.validation_scores['risk_reward'] = min(risk_reward / 2, 1.0)  # Score caps at 1.0
        
        if risk_reward < Config.MIN_RISK_REWARD_RATIO:
            validation.is_valid = False
            validation.rejection_reasons.append(
                f"Poor risk/reward ratio ({risk_reward:.2f} < {Config.MIN_RISK_REWARD_RATIO})"
            )
            
        # 6. Check News Events
        news_clear = await self._check_news_calendar(signal['symbol'])
        if not news_clear:
            validation.is_valid = False
            validation.rejection_reasons.append("High-impact news event nearby")
            
        # 7. Check Spread
        if self.strategy_type == '5min_scalping':
            spread = await self._get_current_spread(signal['symbol'])
            max_spread = self.strategy_config.get('max_spread_pips', 2.0)
            if spread > max_spread:
                validation.is_valid = False
                validation.rejection_reasons.append(f"Spread too wide ({spread:.1f} pips)")
                
        # 8. Calculate Overall Confidence Score
        validation.confidence_score = self._calculate_confidence_score(validation.validation_scores)
        
        if validation.confidence_score < Config.MIN_CONFIDENCE_SCORE:
            validation.is_valid = False
            validation.rejection_reasons.append(
                f"Low confidence score ({validation.confidence_score:.2%} < {Config.MIN_CONFIDENCE_SCORE:.2%})"
            )
            
        # 9. Check Daily Performance
        if self.daily_stats['trades'] >= 5:  # After 5 trades
            current_win_rate = (self.daily_stats['wins'] / self.daily_stats['trades'] 
                              if self.daily_stats['trades'] > 0 else 0)
            if current_win_rate < 0.60:  # If win rate drops below 60%
                validation.is_valid = False
                validation.rejection_reasons.append(
                    f"Daily win rate too low ({current_win_rate:.1%})"
                )
                
        # 10. Final Validation Summary
        validation.session_quality = session_score
        
        if validation.is_valid:
            logger.info(f"Trade setup VALIDATED for {signal['symbol']}: "
                       f"Confidence {validation.confidence_score:.1%}, "
                       f"Confirmations {validation.confirmations}, "
                       f"R:R {validation.risk_reward_ratio:.2f}")
        else:
            logger.info(f"Trade setup REJECTED for {signal['symbol']}: "
                       f"{', '.join(validation.rejection_reasons)}")
            
        return validation
        
    def _validate_trading_session(self) -> float:
        """
        Validate current trading session quality
        
        Returns:
            Session quality score (0-1)
        """
        current_time = datetime.now().time()
        current_hour = current_time.hour
        
        # Determine current session
        for session_name, session_info in Config.OPTIMAL_SESSIONS.items():
            start_hour = session_info['start'].hour
            end_hour = session_info['end'].hour
            
            # Handle sessions that cross midnight
            if start_hour > end_hour:
                if current_hour >= start_hour or current_hour < end_hour:
                    self.current_session = session_name
                    return session_info['score']
            else:
                if start_hour <= current_hour < end_hour:
                    self.current_session = session_name
                    return session_info['score']
                    
        # Outside optimal sessions
        return 0.3
        
    def _validate_correlations(
        self,
        symbol: str,
        direction: str,
        active_positions: Dict[str, Dict]
    ) -> Dict:
        """
        Validate correlations with existing positions
        
        Returns:
            Dictionary with 'passed', 'score', and 'reason'
        """
        if not active_positions:
            return {'passed': True, 'score': 1.0, 'reason': 'No active positions'}
            
        conflicts = []
        correlation_scores = []
        
        for pos_symbol, pos_data in active_positions.items():
            pos_direction = pos_data['direction']
            
            # Check positive correlations
            for (pair1, pair2), corr_value in Config.CORRELATION_MATRIX['positive'].items():
                if (symbol == pair1 and pos_symbol == pair2) or \
                   (symbol == pair2 and pos_symbol == pair1):
                    
                    if corr_value > Config.CORRELATION_THRESHOLD:
                        if direction == pos_direction:
                            # Same direction with positive correlation - OK but reduces size
                            correlation_scores.append(0.7)
                        else:
                            # Opposite direction with positive correlation - CONFLICT
                            conflicts.append(f"Conflicts with {pos_symbol} (corr: {corr_value:.3f})")
                            correlation_scores.append(0.2)
                            
            # Check negative correlations
            for (pair1, pair2), corr_value in Config.CORRELATION_MATRIX['negative'].items():
                if (symbol == pair1 and pos_symbol == pair2) or \
                   (symbol == pair2 and pos_symbol == pair1):
                    
                    if abs(corr_value) > Config.CORRELATION_THRESHOLD:
                        if direction != pos_direction:
                            # Opposite direction with negative correlation - GOOD (hedge)
                            correlation_scores.append(1.0)
                        else:
                            # Same direction with negative correlation - CONFLICT
                            conflicts.append(f"Conflicts with {pos_symbol} (corr: {corr_value:.3f})")
                            correlation_scores.append(0.2)
                            
        if conflicts:
            return {
                'passed': False,
                'score': np.mean(correlation_scores) if correlation_scores else 0.0,
                'reason': f"Correlation conflicts: {'; '.join(conflicts)}"
            }
            
        avg_score = np.mean(correlation_scores) if correlation_scores else 0.8
        return {
            'passed': avg_score >= 0.6,
            'score': avg_score,
            'reason': 'Correlation check passed'
        }
        
    def _validate_technical_confirmations(
        self,
        signal: Dict,
        market_data: pd.DataFrame
    ) -> Dict:
        """
        Validate technical analysis confirmations
        
        Returns:
            Dictionary with 'confirmations' count and individual 'scores'
        """
        confirmations = 0
        scores = {}
        
        # Extract indicator values from signal
        indicators = signal.get('indicators', {})
        
        # 1. Trend Alignment
        if indicators.get('trend_aligned', False):
            confirmations += 2 if indicators.get('trend_strength', 0) > 0.8 else 1
            scores['trend'] = indicators.get('trend_strength', 0)
            
        # 2. Momentum Confirmation
        momentum_score = indicators.get('momentum_score', 0)
        if momentum_score > 0.7:
            confirmations += 2 if momentum_score > 0.85 else 1
            scores['momentum'] = momentum_score
            
        # 3. Pattern Quality
        pattern_score = indicators.get('pattern_score', 0)
        min_pattern = self.strategy_config.get('min_pattern_score', 0.8)
        if pattern_score >= min_pattern:
            confirmations += 2
            scores['pattern'] = pattern_score
        elif pattern_score > 0.7:
            confirmations += 1
            scores['pattern'] = pattern_score
            
        # 4. Support/Resistance
        sr_score = indicators.get('sr_score', 0)
        if sr_score > 0.8:
            confirmations += 1
            scores['support_resistance'] = sr_score
            
        # 5. Volume Confirmation
        volume_score = indicators.get('volume_score', 0)
        if volume_score > 0.7:
            confirmations += 1
            scores['volume'] = volume_score
            
        # 6. Market Structure
        structure_score = indicators.get('structure_score', 0)
        if structure_score > 0.8:
            confirmations += 1
            scores['structure'] = structure_score
            
        # 7. Additional Strategy-Specific Confirmations
        if self.strategy_type == 'custom':
            # Machine learning confirmation
            ml_score = indicators.get('ml_prediction', 0)
            if ml_score > 0.75:
                confirmations += 2
                scores['ml_prediction'] = ml_score
                
            # Sentiment confirmation
            sentiment_score = indicators.get('sentiment_score', 0)
            if abs(sentiment_score) < 0.3:  # Not extreme
                confirmations += 1
                scores['sentiment'] = 1 - abs(sentiment_score)
                
        return {
            'confirmations': confirmations,
            'scores': scores
        }
        
    def _calculate_risk_reward(self, signal: Dict) -> float:
        """
        Calculate risk/reward ratio
        """
        entry = signal['entry_price']
        stop = signal['stop_loss']
        
        # Use the second target for risk/reward calculation
        target = signal.get('take_profit_2', signal.get('take_profit_1', 0))
        
        if target == 0 or stop == 0:
            return 0.0
            
        risk = abs(entry - stop)
        reward = abs(target - entry)
        
        return reward / risk if risk > 0 else 0.0
        
    async def _check_news_calendar(self, symbol: str) -> bool:
        """
        Check for upcoming news events
        
        Returns:
            True if clear to trade, False if news event nearby
        """
        # In production, this would check an economic calendar API
        # For now, simulate with time-based logic
        
        current_time = datetime.now()
        
        # Avoid common news times (simplified)
        if current_time.weekday() == 4 and current_time.hour >= 15:  # Friday afternoon
            return False
            
        # NFP first Friday of month at 8:30 AM ET
        if (current_time.weekday() == 4 and 
            current_time.day <= 7 and 
            8 <= current_time.hour <= 9):
            return False
            
        # FOMC typically Wednesday at 2 PM ET
        if (current_time.weekday() == 2 and 
            13 <= current_time.hour <= 15):
            return False
            
        return True
        
    async def _get_current_spread(self, symbol: str) -> float:
        """
        Get current spread in pips
        
        Returns:
            Spread in pips
        """
        # In production, this would get real-time spread from broker
        # Simulate based on symbol and time
        
        base_spreads = {
            'EUR.USD': 0.8,
            'GBP.USD': 1.2,
            'USD.JPY': 0.9,
            'USD.CHF': 1.1,
            'AUD.USD': 1.3,
            'NZD.USD': 1.5,
            'USD.CAD': 1.4,
        }
        
        base = base_spreads.get(symbol, 1.5)
        
        # Adjust for session
        session_multipliers = {
            'london_ny_overlap': 1.0,
            'london': 1.1,
            'newyork': 1.1,
            'asian': 1.5,
        }
        
        multiplier = session_multipliers.get(self.current_session, 1.3)
        
        return base * multiplier
        
    def _calculate_confidence_score(self, scores: Dict[str, float]) -> float:
        """
        Calculate overall confidence score using weighted average
        """
        if not scores:
            return 0.0
            
        # Get weights from config
        weights = Config.CONFIRMATION_WEIGHTS
        
        weighted_sum = 0.0
        total_weight = 0.0
        
        for key, score in scores.items():
            weight = weights.get(key, 0.05)  # Default 5% weight
            weighted_sum += score * weight
            total_weight += weight
            
        # Add risk/reward bonus
        rr_score = scores.get('risk_reward', 0)
        if rr_score > 0:
            rr_bonus = min(rr_score / 3, 0.15)  # Max 15% bonus
            weighted_sum += rr_bonus
            total_weight += 0.15
            
        return weighted_sum / total_weight if total_weight > 0 else 0.0
        
    async def execute_trade(
        self,
        signal: Dict,
        validation: TradeSetupValidation,
        account_balance: float
    ) -> Dict:
        """
        Execute validated trade with proper position sizing
        
        Returns:
            Trade execution result
        """
        if not validation.is_valid:
            return {
                'status': 'rejected',
                'reason': validation.rejection_reasons
            }
            
        # Calculate position size
        position_size = self._calculate_position_size(
            signal,
            account_balance,
            validation.confidence_score
        )
        
        # Adjust for correlations
        if validation.correlation_check_passed and validation.validation_scores.get('correlation', 1.0) < 0.8:
            position_size *= 0.5  # Reduce size for correlated positions
            
        # Create trade record
        trade = {
            'id': f"{signal['symbol']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'symbol': signal['symbol'],
            'direction': signal['direction'],
            'entry_price': signal['entry_price'],
            'stop_loss': signal['stop_loss'],
            'take_profit_1': signal['take_profit_1'],
            'take_profit_2': signal.get('take_profit_2', signal['take_profit_1']),
            'position_size': position_size,
            'confidence': validation.confidence_score,
            'risk_reward': validation.risk_reward_ratio,
            'entry_time': datetime.now(),
            'status': TradeStatus.PENDING_ENTRY
        }
        
        # Add to pending trades
        self.pending_trades[trade['id']] = trade
        
        logger.info(f"Trade queued for execution: {trade['id']}")
        
        return {
            'status': 'pending',
            'trade_id': trade['id'],
            'position_size': position_size
        }
        
    def _calculate_position_size(
        self,
        signal: Dict,
        account_balance: float,
        confidence_score: float
    ) -> float:
        """
        Calculate position size based on risk management rules
        """
        # Base risk amount
        risk_percent = Config.MAX_RISK_PER_TRADE
        
        # Adjust based on confidence (higher confidence = closer to max risk)
        confidence_multiplier = 0.5 + (confidence_score - 0.8) * 2.5  # 0.5x to 1.0x
        confidence_multiplier = max(0.5, min(1.0, confidence_multiplier))
        
        # Adjust based on daily performance
        if self.daily_stats['losses'] >= 2:
            confidence_multiplier *= 0.5  # Reduce size after losses
            
        # Calculate risk amount
        risk_amount = account_balance * risk_percent * confidence_multiplier
        
        # Calculate position size based on stop loss
        entry = signal['entry_price']
        stop = signal['stop_loss']
        pips_risk = abs(entry - stop) * 10000  # Convert to pips
        
        # Position size in units
        position_size = risk_amount / pips_risk * 10000
        
        # Apply minimum and maximum limits
        min_size = 1000  # Minimum 1 micro lot
        max_size = account_balance * 0.02 / entry  # Max 2% of account
        
        return max(min_size, min(position_size, max_size))
        
    async def manage_active_trades(self, current_prices: Dict[str, float]):
        """
        Manage active trades - trailing stops, partial closes, etc.
        """
        for trade_id, trade in list(self.active_trades.items()):
            symbol = trade['symbol']
            current_price = current_prices.get(symbol)
            
            if not current_price:
                continue
                
            # Calculate unrealized P&L
            if trade['direction'] == 'BUY':
                pnl_pips = (current_price - trade['entry_price']) * 10000
            else:
                pnl_pips = (trade['entry_price'] - current_price) * 10000
                
            # Check for partial close at TP1
            if trade['status'] == TradeStatus.ACTIVE and pnl_pips >= 5:  # 5 pips profit
                if self.strategy_type == '5min_scalping':
                    # Close 50% at TP1
                    await self._partial_close_trade(trade_id, 0.5)
                    trade['status'] = TradeStatus.PARTIAL_CLOSE
                    
                    # Move stop to breakeven
                    trade['stop_loss'] = trade['entry_price']
                    logger.info(f"Trade {trade_id}: Partial close at TP1, stop moved to breakeven")
                    
            # Check for trailing stop
            if trade['status'] == TradeStatus.PARTIAL_CLOSE and pnl_pips >= 10:
                # Trail the stop
                if trade['direction'] == 'BUY':
                    new_stop = current_price - 0.0003  # 3 pips trailing
                    if new_stop > trade['stop_loss']:
                        trade['stop_loss'] = new_stop
                else:
                    new_stop = current_price + 0.0003
                    if new_stop < trade['stop_loss']:
                        trade['stop_loss'] = new_stop
                        
            # Check for stop loss hit
            if trade['direction'] == 'BUY' and current_price <= trade['stop_loss']:
                await self._close_trade(trade_id, current_price, 'STOP_LOSS')
            elif trade['direction'] == 'SELL' and current_price >= trade['stop_loss']:
                await self._close_trade(trade_id, current_price, 'STOP_LOSS')
                
            # Check for take profit hit
            if trade['direction'] == 'BUY' and current_price >= trade['take_profit_2']:
                await self._close_trade(trade_id, current_price, 'TAKE_PROFIT')
            elif trade['direction'] == 'SELL' and current_price <= trade['take_profit_2']:
                await self._close_trade(trade_id, current_price, 'TAKE_PROFIT')
                
    async def _partial_close_trade(self, trade_id: str, close_percent: float):
        """
        Partially close a trade
        """
        trade = self.active_trades.get(trade_id)
        if not trade:
            return
            
        closed_size = trade['position_size'] * close_percent
        trade['position_size'] -= closed_size
        trade['closed_size'] = trade.get('closed_size', 0) + closed_size
        
        logger.info(f"Partially closed {close_percent*100}% of trade {trade_id}")
        
    async def _close_trade(self, trade_id: str, exit_price: float, reason: str):
        """
        Close a trade completely
        """
        trade = self.active_trades.get(trade_id)
        if not trade:
            return
            
        # Calculate final P&L
        if trade['direction'] == 'BUY':
            pnl = (exit_price - trade['entry_price']) * trade['position_size']
        else:
            pnl = (trade['entry_price'] - exit_price) * trade['position_size']
            
        # Update stats
        self.daily_stats['trades'] += 1
        if pnl > 0:
            self.daily_stats['wins'] += 1
        else:
            self.daily_stats['losses'] += 1
            
        self.daily_stats['pnl'] += pnl
        self.daily_stats['win_rate'] = (self.daily_stats['wins'] / 
                                        self.daily_stats['trades'] * 100)
        
        # Move to closed trades
        trade['exit_price'] = exit_price
        trade['exit_time'] = datetime.now()
        trade['pnl'] = pnl
        trade['exit_reason'] = reason
        trade['status'] = TradeStatus.CLOSED
        
        self.closed_trades.append(trade)
        del self.active_trades[trade_id]
        
        logger.info(f"Trade {trade_id} closed: {reason}, P&L: ${pnl:.2f}")
        
        # Log daily performance
        logger.info(f"Daily Stats - Trades: {self.daily_stats['trades']}, "
                   f"Win Rate: {self.daily_stats['win_rate']:.1f}%, "
                   f"P&L: ${self.daily_stats['pnl']:.2f}")
        
    def get_performance_report(self) -> Dict:
        """
        Generate performance report
        """
        total_trades = len(self.closed_trades)
        if total_trades == 0:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'avg_win': 0,
                'avg_loss': 0,
                'profit_factor': 0,
                'total_pnl': 0
            }
            
        wins = [t for t in self.closed_trades if t['pnl'] > 0]
        losses = [t for t in self.closed_trades if t['pnl'] <= 0]
        
        avg_win = np.mean([t['pnl'] for t in wins]) if wins else 0
        avg_loss = abs(np.mean([t['pnl'] for t in losses])) if losses else 0
        
        gross_profit = sum(t['pnl'] for t in wins)
        gross_loss = abs(sum(t['pnl'] for t in losses))
        
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0
        
        return {
            'total_trades': total_trades,
            'win_rate': len(wins) / total_trades * 100,
            'wins': len(wins),
            'losses': len(losses),
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'total_pnl': sum(t['pnl'] for t in self.closed_trades),
            'best_trade': max(self.closed_trades, key=lambda t: t['pnl'])['pnl'] if self.closed_trades else 0,
            'worst_trade': min(self.closed_trades, key=lambda t: t['pnl'])['pnl'] if self.closed_trades else 0,
            'avg_confidence': np.mean([t['confidence'] for t in self.closed_trades]),
            'avg_risk_reward': np.mean([t['risk_reward'] for t in self.closed_trades])
        }


# Example usage
if __name__ == "__main__":
    # Initialize execution manager
    manager = IEMSS_FOREX_TradeExecutionManager_80Plus('5min_scalping')
    
    # Example signal
    signal = {
        'symbol': 'EUR.USD',
        'direction': 'BUY',
        'entry_price': 1.0850,
        'stop_loss': 1.0843,
        'take_profit_1': 1.0857,
        'take_profit_2': 1.0865,
        'indicators': {
            'trend_aligned': True,
            'trend_strength': 0.85,
            'momentum_score': 0.82,
            'pattern_score': 0.88,
            'volume_score': 0.75,
            'sr_score': 0.90,
            'structure_score': 0.83
        }
    }
    
    # Validate trade setup
    async def test_validation():
        validation = await manager.validate_trade_setup(
            signal,
            pd.DataFrame(),  # Would have real market data
            {}  # No active positions
        )
        
        print(f"Validation Result: {validation.is_valid}")
        print(f"Confidence Score: {validation.confidence_score:.1%}")
        print(f"Confirmations: {validation.confirmations}")
        print(f"Risk/Reward: {validation.risk_reward_ratio:.2f}")
        
        if not validation.is_valid:
            print(f"Rejection Reasons: {validation.rejection_reasons}")
            
    # Run test
    asyncio.run(test_validation())
