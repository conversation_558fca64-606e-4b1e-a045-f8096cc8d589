# Core Framework
fastapi>=0.95.0
uvicorn>=0.21.1
requests>=2.31.0
python-dotenv>=1.0.0
pydantic>=1.10.13
httpx>=0.18.2
mcp==1.8.0

# Interactive Brokers
ib_async>=1.0.3
ibapi>=9.81.1

# Data Processing & Analysis
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.11.0
matplotlib>=3.7.0
nest_asyncio>=1.5.6
tenacity>=8.2.2

# Technical Analysis & Trading
# TA-Lib>=0.4.25  # Requires system ta-lib library
pandas-ta>=0.3.14b
backtrader>=1.9.78

# Options & Greeks Calculations
# py_vollib>=1.0.1  # Requires lets_be_rational which needs swig
# vollib>=0.1.5     # Requires lets_be_rational which needs swig
mibian>=0.1.3

# Machine Learning & Analytics
scikit-learn>=1.3.0
lightgbm>=4.0.0
xgboost>=1.7.0
statsmodels>=0.14.0

# News Sentiment & Alternative Data
newsapi-python>=0.2.6
textblob>=0.17.1
vaderSentiment>=3.3.2
yfinance>=0.2.18

# High-Frequency & Real-time Processing
websockets>=10.3,<12.0
aioredis>=2.0.1
psutil>=5.9.0

# Financial Data APIs
finnhub-python>=2.4.17
alpha-vantage>=2.3.1
polygon-api-client>=1.12.0,<1.13.0

# Database & Storage
sqlalchemy>=2.0.0
aiosqlite>=0.19.0
supabase>=1.0.0

# Google Cloud Services
google-cloud-pubsub>=2.18.0
google-cloud-monitoring>=2.15.0
google-cloud-monitoring-dashboards>=1.8.0
google-cloud-error-reporting>=1.9.0
google-auth>=2.23.0
google-api-core>=2.11.0
google-cloud-core>=2.3.0

# Prometheus Monitoring
prometheus-client>=0.17.0

# Utilities
colorama>=0.4.6
tqdm>=4.65.0
rich>=13.4.0
loguru>=0.7.0
jsonschema>=4.17.0
