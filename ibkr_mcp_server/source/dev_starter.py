#!/Users/<USER>/IBKR/b-team/ibkr_mcp_server/source/.venv/bin/python
"""
Development Starter Script
Quick test to verify IBKR and MCP components work in the source environment
"""
import sys
import os
from pathlib import Path

def test_ibkr_imports():
    """Test IBKR-related imports"""
    print("🔧 Testing IBKR imports...")
    
    try:
        import ib_async
        print(f"✅ ib_async {ib_async.__version__}")
    except ImportError as e:
        print(f"❌ ib_async: {e}")
    
    try:
        import ibapi
        print(f"✅ ibapi available")
    except ImportError as e:
        print(f"❌ ibapi: {e}")
    
    try:
        from ib_async import IB, Stock, MarketOrder
        print("✅ ib_async core classes imported")
    except ImportError as e:
        print(f"❌ ib_async classes: {e}")

def test_mcp_imports():
    """Test MCP-related imports"""
    print("\n🔧 Testing MCP imports...")
    
    try:
        import mcp
        print(f"✅ mcp available")
    except ImportError as e:
        print(f"❌ mcp: {e}")
    
    try:
        from mcp.server.fastmcp import FastMCP
        print("✅ FastMCP imported")
    except ImportError as e:
        print(f"❌ FastMCP: {e}")

def test_data_analysis():
    """Test data analysis imports"""
    print("\n🔧 Testing data analysis imports...")
    
    try:
        import pandas as pd
        import numpy as np
        import matplotlib.pyplot as plt
        print("✅ pandas, numpy, matplotlib imported")
        
        # Quick test
        data = pd.DataFrame({'x': [1, 2, 3], 'y': [4, 5, 6]})
        print(f"✅ pandas DataFrame created: {data.shape}")
        
    except ImportError as e:
        print(f"❌ Data analysis libraries: {e}")

def test_environment_info():
    """Display environment information"""
    print("\n📋 Environment Information:")
    print(f"🐍 Python: {sys.executable}")
    print(f"🐍 Version: {sys.version}")
    print(f"📁 Working Directory: {os.getcwd()}")
    print(f"📁 Script Location: {Path(__file__).parent}")

def create_simple_mcp_test():
    """Create a simple MCP server test"""
    print("\n🧪 Creating simple MCP server test...")
    
    try:
        from mcp.server.fastmcp import FastMCP
        
        # Create a simple MCP server
        mcp = FastMCP("Source Environment Test Server")
        
        @mcp.tool()
        def test_tool() -> str:
            """Test tool for source environment"""
            return "✅ Source environment MCP tool working!"
        
        @mcp.tool()
        def environment_info() -> dict:
            """Get environment information"""
            return {
                "python_executable": sys.executable,
                "working_directory": os.getcwd(),
                "python_version": sys.version,
                "status": "Source environment ready"
            }
        
        print("✅ MCP server created with test tools")
        print("📋 Available tools:")
        print("   - test_tool()")
        print("   - environment_info()")
        
        return mcp
        
    except Exception as e:
        print(f"❌ MCP server creation failed: {e}")
        return None

def main():
    """Main test function"""
    print("🚀 Source Environment Development Starter")
    print("=" * 50)
    
    test_environment_info()
    test_ibkr_imports()
    test_mcp_imports()
    test_data_analysis()
    
    mcp_server = create_simple_mcp_test()
    
    print("\n🎉 Source environment testing complete!")
    print("\n📋 Next Steps:")
    print("1. Start developing your IBKR trading applications")
    print("2. Use the MCP server for Claude Desktop integration")
    print("3. Leverage the installed data analysis libraries")
    print("4. Check SOURCE_VENV_SETUP_GUIDE.md for detailed info")
    
    if mcp_server:
        print("\n🔧 To run the MCP server:")
        print("   mcp_server.run()")

if __name__ == "__main__":
    main()
