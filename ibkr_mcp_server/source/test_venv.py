#!/usr/bin/env python3
import sys
import subprocess

def test_environment():
    print("🧪 Testing Source Virtual Environment")
    print("=" * 40)
    print(f"🐍 Python executable: {sys.executable}")
    print(f"🐍 Python version: {sys.version}")
    
    # Test imports
    modules = ['mcp', 'fastapi', 'matplotlib', 'pandas', 'numpy']
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module} - OK")
        except ImportError:
            print(f"❌ {module} - FAILED")
    
    print("\n🎉 Environment test complete!")

if __name__ == "__main__":
    test_environment()
