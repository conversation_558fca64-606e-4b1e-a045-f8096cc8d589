# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ExecutionFilter.proto
# Protobuf Python Version: 5.29.3
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    3,
    '',
    'ExecutionFilter.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15\x45xecutionFilter.proto\x12\x08protobuf\"\xb4\x02\n\x0f\x45xecutionFilter\x12\x15\n\x08\x63lientId\x18\x01 \x01(\x05H\x00\x88\x01\x01\x12\x15\n\x08\x61\x63\x63tCode\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\x11\n\x04time\x18\x03 \x01(\tH\x02\x88\x01\x01\x12\x13\n\x06symbol\x18\x04 \x01(\tH\x03\x88\x01\x01\x12\x14\n\x07secType\x18\x05 \x01(\tH\x04\x88\x01\x01\x12\x15\n\x08\x65xchange\x18\x06 \x01(\tH\x05\x88\x01\x01\x12\x11\n\x04side\x18\x07 \x01(\tH\x06\x88\x01\x01\x12\x16\n\tlastNDays\x18\x08 \x01(\x05H\x07\x88\x01\x01\x12\x15\n\rspecificDates\x18\t \x03(\x05\x42\x0b\n\t_clientIdB\x0b\n\t_acctCodeB\x07\n\x05_timeB\t\n\x07_symbolB\n\n\x08_secTypeB\x0b\n\t_exchangeB\x07\n\x05_sideB\x0c\n\n_lastNDaysB?\n\x16\x63om.ib.client.protobufB\x14\x45xecutionFilterProto\xaa\x02\x0eIBApi.protobufb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ExecutionFilter_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\026com.ib.client.protobufB\024ExecutionFilterProto\252\002\016IBApi.protobuf'
  _globals['_EXECUTIONFILTER']._serialized_start=36
  _globals['_EXECUTIONFILTER']._serialized_end=344
# @@protoc_insertion_point(module_scope)
