# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: Contract.proto
# Protobuf Python Version: 5.29.3
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    3,
    '',
    'Contract.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import ibapi.protobuf.ComboLeg_pb2 as ComboLeg__pb2
import ibapi.protobuf.DeltaNeutralContract_pb2 as DeltaNeutralContract__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0e\x43ontract.proto\x12\x08protobuf\x1a\x0e\x43omboLeg.proto\x1a\x1a\x44\x65ltaNeutralContract.proto\"\xdd\x06\n\x08\x43ontract\x12\x12\n\x05\x63onId\x18\x01 \x01(\x05H\x00\x88\x01\x01\x12\x13\n\x06symbol\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\x14\n\x07secType\x18\x03 \x01(\tH\x02\x88\x01\x01\x12)\n\x1clastTradeDateOrContractMonth\x18\x04 \x01(\tH\x03\x88\x01\x01\x12\x13\n\x06strike\x18\x05 \x01(\x01H\x04\x88\x01\x01\x12\x12\n\x05right\x18\x06 \x01(\tH\x05\x88\x01\x01\x12\x17\n\nmultiplier\x18\x07 \x01(\x01H\x06\x88\x01\x01\x12\x15\n\x08\x65xchange\x18\x08 \x01(\tH\x07\x88\x01\x01\x12\x18\n\x0bprimaryExch\x18\t \x01(\tH\x08\x88\x01\x01\x12\x15\n\x08\x63urrency\x18\n \x01(\tH\t\x88\x01\x01\x12\x18\n\x0blocalSymbol\x18\x0b \x01(\tH\n\x88\x01\x01\x12\x19\n\x0ctradingClass\x18\x0c \x01(\tH\x0b\x88\x01\x01\x12\x16\n\tsecIdType\x18\r \x01(\tH\x0c\x88\x01\x01\x12\x12\n\x05secId\x18\x0e \x01(\tH\r\x88\x01\x01\x12\x18\n\x0b\x64\x65scription\x18\x0f \x01(\tH\x0e\x88\x01\x01\x12\x15\n\x08issuerId\x18\x10 \x01(\tH\x0f\x88\x01\x01\x12\x41\n\x14\x64\x65ltaNeutralContract\x18\x11 \x01(\x0b\x32\x1e.protobuf.DeltaNeutralContractH\x10\x88\x01\x01\x12\x1b\n\x0eincludeExpired\x18\x12 \x01(\x08H\x11\x88\x01\x01\x12\x1d\n\x10\x63omboLegsDescrip\x18\x13 \x01(\tH\x12\x88\x01\x01\x12%\n\tcomboLegs\x18\x14 \x03(\x0b\x32\x12.protobuf.ComboLegB\x08\n\x06_conIdB\t\n\x07_symbolB\n\n\x08_secTypeB\x1f\n\x1d_lastTradeDateOrContractMonthB\t\n\x07_strikeB\x08\n\x06_rightB\r\n\x0b_multiplierB\x0b\n\t_exchangeB\x0e\n\x0c_primaryExchB\x0b\n\t_currencyB\x0e\n\x0c_localSymbolB\x0f\n\r_tradingClassB\x0c\n\n_secIdTypeB\x08\n\x06_secIdB\x0e\n\x0c_descriptionB\x0b\n\t_issuerIdB\x17\n\x15_deltaNeutralContractB\x11\n\x0f_includeExpiredB\x13\n\x11_comboLegsDescripB8\n\x16\x63om.ib.client.protobufB\rContractProto\xaa\x02\x0eIBApi.protobufb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'Contract_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\026com.ib.client.protobufB\rContractProto\252\002\016IBApi.protobuf'
  _globals['_CONTRACT']._serialized_start=73
  _globals['_CONTRACT']._serialized_end=934
# @@protoc_insertion_point(module_scope)
