# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: DeltaNeutralContract.proto
# Protobuf Python Version: 5.29.3
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    3,
    '',
    'DeltaNeutralContract.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1a\x44\x65ltaNeutralContract.proto\x12\x08protobuf\"p\n\x14\x44\x65ltaNeutralContract\x12\x12\n\x05\x63onId\x18\x01 \x01(\x05H\x00\x88\x01\x01\x12\x12\n\x05\x64\x65lta\x18\x02 \x01(\x01H\x01\x88\x01\x01\x12\x12\n\x05price\x18\x03 \x01(\x01H\x02\x88\x01\x01\x42\x08\n\x06_conIdB\x08\n\x06_deltaB\x08\n\x06_priceBD\n\x16\x63om.ib.client.protobufB\x19\x44\x65ltaNeutralContractProto\xaa\x02\x0eIBApi.protobufb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'DeltaNeutralContract_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\026com.ib.client.protobufB\031DeltaNeutralContractProto\252\002\016IBApi.protobuf'
  _globals['_DELTANEUTRALCONTRACT']._serialized_start=40
  _globals['_DELTANEUTRALCONTRACT']._serialized_end=152
# @@protoc_insertion_point(module_scope)
