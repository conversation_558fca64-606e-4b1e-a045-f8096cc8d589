"""
Phase 2 Configuration Management

Centralized configuration for database webhooks, Edge Functions,
and real-time processing components.
"""

import os
from typing import List, Optional
from dataclasses import dataclass, field
from enum import Enum


class NotificationChannel(Enum):
    """Supported notification channels"""
    SLACK = "slack"
    DISCORD = "discord"
    TELEGRAM = "telegram"
    EMAIL = "email"
    WEBHOOK = "webhook"


@dataclass
class EdgeFunctionsConfig:
    """Configuration for Supabase Edge Functions"""
    base_url: str = field(default_factory=lambda: os.getenv('SUPABASE_URL', ''))
    supabase_url: str = field(default_factory=lambda: os.getenv('SUPABASE_URL', ''))
    service_role_key: str = field(default_factory=lambda: os.getenv('SUPABASE_SERVICE_ROLE_KEY', ''))
    supabase_anon_key: str = field(default_factory=lambda: os.getenv('SUPABASE_ANON_KEY', ''))
    timeout_seconds: int = 30
    timeout: int = 30
    retry_attempts: int = 3
    retry_delay: float = 1.0

    # Function-specific settings
    market_data_aggregator_enabled: bool = True
    signal_executor_enabled: bool = True
    batch_processing_enabled: bool = True
    max_batch_size: int = 100

    # Processing intervals
    indicator_calculation_interval: int = 60  # seconds
    signal_processing_interval: int = 30  # seconds


@dataclass
class NotificationConfig:
    """Configuration for notification channels"""
    enabled_channels: List[NotificationChannel] = field(default_factory=list)
    
    # Slack configuration
    slack_webhook_url: Optional[str] = field(default_factory=lambda: os.getenv('SLACK_WEBHOOK_URL'))
    slack_channel: str = "#trading-alerts"
    
    # Discord configuration
    discord_webhook_url: Optional[str] = field(default_factory=lambda: os.getenv('DISCORD_WEBHOOK_URL'))
    
    # Telegram configuration
    telegram_bot_token: Optional[str] = field(default_factory=lambda: os.getenv('TELEGRAM_BOT_TOKEN'))
    telegram_chat_id: Optional[str] = field(default_factory=lambda: os.getenv('TELEGRAM_CHAT_ID'))
    
    # Email configuration
    smtp_host: Optional[str] = field(default_factory=lambda: os.getenv('SMTP_HOST'))
    smtp_port: int = field(default_factory=lambda: int(os.getenv('SMTP_PORT', '587')))
    smtp_username: Optional[str] = field(default_factory=lambda: os.getenv('SMTP_USERNAME'))
    smtp_password: Optional[str] = field(default_factory=lambda: os.getenv('SMTP_PASSWORD'))
    email_from: Optional[str] = field(default_factory=lambda: os.getenv('EMAIL_FROM'))
    email_to: List[str] = field(default_factory=list)
    
    # Generic webhook configuration
    webhook_urls: List[str] = field(default_factory=list)
    generic_webhook_url: Optional[str] = field(default_factory=lambda: os.getenv('GENERIC_WEBHOOK_URL'))
    webhook_secret: Optional[str] = field(default_factory=lambda: os.getenv('WEBHOOK_SECRET'))


@dataclass
class WebhookConfig:
    """Configuration for webhook processing"""
    enabled: bool = True
    processing_interval: int = 5  # seconds
    poll_interval: int = 5  # seconds
    batch_size: int = 50
    max_retries: int = 3
    retry_delay: float = 2.0
    cleanup_interval: int = 3600  # 1 hour
    retention_days: int = 7
    cleanup_days: int = 7

    # Concurrency settings
    max_concurrent_processors: int = 5
    max_concurrent: int = 5
    worker_timeout: int = 30


@dataclass
class RiskManagementConfig:
    """Risk management parameters"""
    max_total_exposure: float = field(default_factory=lambda: float(os.getenv('RISK_MAX_TOTAL_EXPOSURE', '100000.0')))
    max_position_size: float = field(default_factory=lambda: float(os.getenv('RISK_MAX_POSITION_SIZE', '10000.0')))
    max_drawdown_pct: float = field(default_factory=lambda: float(os.getenv('RISK_MAX_DRAWDOWN_PCT', '5.0')))
    min_signal_strength: float = field(default_factory=lambda: float(os.getenv('RISK_MIN_SIGNAL_STRENGTH', '0.3')))
    
    # Position limits
    max_positions_per_symbol: int = 3
    max_total_positions: int = 20
    
    # Correlation limits
    max_correlation_exposure: float = 0.7
    correlation_lookback_periods: int = 50

    # Monitoring intervals
    position_monitoring_interval: int = 30  # seconds


@dataclass
class Phase2Config:
    """Complete Phase 2 configuration"""
    edge_functions: EdgeFunctionsConfig = field(default_factory=EdgeFunctionsConfig)
    notifications: NotificationConfig = field(default_factory=NotificationConfig)
    webhooks: WebhookConfig = field(default_factory=WebhookConfig)
    risk_management: RiskManagementConfig = field(default_factory=RiskManagementConfig)
    
    # Service settings
    service_name: str = "IBKR-Phase2-Service"
    log_level: str = field(default_factory=lambda: os.getenv('LOG_LEVEL', 'INFO'))
    debug_mode: bool = field(default_factory=lambda: os.getenv('DEBUG', 'false').lower() == 'true')
    
    # Database settings
    supabase_url: str = field(default_factory=lambda: os.getenv('SUPABASE_URL', ''))
    supabase_service_key: str = field(default_factory=lambda: os.getenv('SUPABASE_SERVICE_ROLE_KEY', ''))
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors"""
        errors = []
        
        if not self.supabase_url:
            errors.append("SUPABASE_URL is required")
        
        if not self.supabase_service_key:
            errors.append("SUPABASE_SERVICE_ROLE_KEY is required")
        
        if not self.edge_functions.base_url:
            errors.append("Edge Functions base URL is required")
        
        # Validate notification channels
        for channel in self.notifications.enabled_channels:
            if channel == NotificationChannel.SLACK and not self.notifications.slack_webhook_url:
                errors.append("Slack webhook URL is required when Slack notifications are enabled")
            elif channel == NotificationChannel.DISCORD and not self.notifications.discord_webhook_url:
                errors.append("Discord webhook URL is required when Discord notifications are enabled")
            elif channel == NotificationChannel.TELEGRAM:
                if not self.notifications.telegram_bot_token:
                    errors.append("Telegram bot token is required when Telegram notifications are enabled")
                if not self.notifications.telegram_chat_id:
                    errors.append("Telegram chat ID is required when Telegram notifications are enabled")
        
        return errors


# Default configuration instance
default_config = Phase2Config()

# Auto-enable notification channels based on available credentials
if default_config.notifications.slack_webhook_url:
    default_config.notifications.enabled_channels.append(NotificationChannel.SLACK)

if default_config.notifications.discord_webhook_url:
    default_config.notifications.enabled_channels.append(NotificationChannel.DISCORD)

if default_config.notifications.telegram_bot_token and default_config.notifications.telegram_chat_id:
    default_config.notifications.enabled_channels.append(NotificationChannel.TELEGRAM)
