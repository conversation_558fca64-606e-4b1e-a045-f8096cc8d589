"""
High-Frequency Trading API Endpoints

Ultra-low latency trading endpoints for market making,
arbitrage, and microstructure analysis.
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime
from decimal import Decimal
import asyncio

from ...services.hft.high_frequency_engine import HighFrequencyEngine, HFTSignalType, LatencyCategory
from ...services.hft.market_making import MarketMakingStrategy, MarketMakingMode
from ...services.ibkr_service import IBKRService

router = APIRouter(prefix="/hft", tags=["high-frequency-trading"])

# Request/Response Models

class HFTEngineConfigRequest(BaseModel):
    latency_target_us: float = Field(default=100, description="Target latency in microseconds")
    max_positions: int = Field(default=10, description="Maximum concurrent positions")
    risk_limits: Optional[Dict[str, Any]] = Field(default=None, description="Risk management limits")

class MarketMakingStartRequest(BaseModel):
    symbols: List[str] = Field(..., description="Symbols to make markets in")
    mode: str = Field(default="neutral", description="Market making mode")
    parameters: Optional[Dict[str, Any]] = Field(default={}, description="Strategy parameters")

class MarketMakingConfigRequest(BaseModel):
    base_spread_bps: Optional[float] = Field(default=None, description="Base spread in basis points")
    base_quote_size: Optional[int] = Field(default=None, description="Base quote size")
    max_position_value: Optional[float] = Field(default=None, description="Maximum position value")
    order_refresh_interval: Optional[float] = Field(default=None, description="Quote refresh interval")

class TickDataRequest(BaseModel):
    symbol: str = Field(..., description="Symbol")
    price: float = Field(..., description="Price")
    size: int = Field(..., description="Size")
    bid: Optional[float] = Field(default=None, description="Bid price")
    ask: Optional[float] = Field(default=None, description="Ask price")
    bid_size: Optional[int] = Field(default=None, description="Bid size")
    ask_size: Optional[int] = Field(default=None, description="Ask size")

class OrderBookRequest(BaseModel):
    symbol: str = Field(..., description="Symbol")
    bids: List[Dict[str, Any]] = Field(..., description="Bid levels")
    asks: List[Dict[str, Any]] = Field(..., description="Ask levels")

# Initialize services (these would normally be dependency injected)
hft_engine = None  # Will be injected
market_making_strategy = None  # Will be injected
ibkr_service = None  # Will be injected

def get_hft_services():
    """Dependency to get HFT services"""
    global hft_engine, market_making_strategy, ibkr_service
    if not hft_engine:
        # This would be properly initialized in main app
        pass
    return hft_engine, market_making_strategy, ibkr_service

@router.post("/engine/start")
async def start_hft_engine(
    config: HFTEngineConfigRequest,
    background_tasks: BackgroundTasks,
    services = Depends(get_hft_services)
) -> Dict[str, Any]:
    """
    Start the high-frequency trading engine
    
    Initializes ultra-low latency processing with specified configuration
    and performance targets.
    """
    try:
        hft_engine, _, _ = services
        
        # Update configuration
        if config.risk_limits:
            await hft_engine.update_risk_limits(config.risk_limits)
        
        await hft_engine.set_latency_target(config.latency_target_us)
        
        # Start engine in background
        background_tasks.add_task(hft_engine.start_engine)
        
        return {
            "success": True,
            "message": "HFT engine started successfully",
            "config": {
                "latency_target_us": config.latency_target_us,
                "max_positions": config.max_positions,
                "risk_limits": config.risk_limits
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/engine/performance")
async def get_hft_performance(
    services = Depends(get_hft_services)
) -> Dict[str, Any]:
    """
    Get HFT engine performance statistics
    
    Returns detailed latency metrics, throughput stats,
    and performance distribution analysis.
    """
    try:
        hft_engine, _, _ = services
        
        performance_stats = await hft_engine.get_performance_stats()
        
        return {
            "success": True,
            "performance": performance_stats
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/engine/signals")
async def get_active_hft_signals(
    services = Depends(get_hft_services)
) -> Dict[str, Any]:
    """
    Get currently active HFT signals
    
    Returns all active high-frequency trading signals
    with timing and confidence information.
    """
    try:
        hft_engine, _, _ = services
        
        signals = await hft_engine.get_active_signals()
        
        return {
            "success": True,
            "signals": signals,
            "total_signals": len(signals)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/engine/tick")
async def process_tick_data(
    tick: TickDataRequest,
    services = Depends(get_hft_services)
) -> Dict[str, Any]:
    """
    Process incoming tick data
    
    Processes high-frequency market data tick for signal generation
    with microsecond-precision timing.
    """
    try:
        hft_engine, _, _ = services
        
        from ...services.hft.high_frequency_engine import TickData
        
        # Convert request to TickData object
        tick_data = TickData(
            symbol=tick.symbol,
            price=Decimal(str(tick.price)),
            size=tick.size,
            timestamp=datetime.now().timestamp() * 1e9,  # nanoseconds
            bid=Decimal(str(tick.bid)) if tick.bid else None,
            ask=Decimal(str(tick.ask)) if tick.ask else None,
            bid_size=tick.bid_size,
            ask_size=tick.ask_size
        )
        
        # Process tick (this is ultra-fast)
        await hft_engine.process_tick(tick_data)
        
        return {
            "success": True,
            "message": "Tick processed successfully",
            "processing_time_target_us": hft_engine.latency_target_us
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/engine/orderbook")
async def process_order_book(
    order_book: OrderBookRequest,
    services = Depends(get_hft_services)
) -> Dict[str, Any]:
    """
    Process order book update
    
    Analyzes order book for microstructure signals
    and imbalance detection.
    """
    try:
        hft_engine, _, _ = services
        
        from ...services.hft.high_frequency_engine import OrderBook, OrderBookLevel
        
        # Convert request to OrderBook object
        bids = [
            OrderBookLevel(
                price=Decimal(str(level['price'])),
                size=level['size'],
                orders=level.get('orders', 1)
            )
            for level in order_book.bids
        ]
        
        asks = [
            OrderBookLevel(
                price=Decimal(str(level['price'])),
                size=level['size'],
                orders=level.get('orders', 1)
            )
            for level in order_book.asks
        ]
        
        book = OrderBook(
            symbol=order_book.symbol,
            timestamp=datetime.now().timestamp() * 1e9,
            bids=bids,
            asks=asks
        )
        
        # Process order book
        await hft_engine.process_order_book(book)
        
        # Calculate imbalance
        imbalance = book.calculate_imbalance()
        
        return {
            "success": True,
            "message": "Order book processed successfully",
            "analysis": {
                "imbalance": imbalance,
                "spread": float(book.spread) if book.spread else None,
                "best_bid": float(book.best_bid.price) if book.best_bid else None,
                "best_ask": float(book.best_ask.price) if book.best_ask else None
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/market-making/start")
async def start_market_making(
    request: MarketMakingStartRequest,
    services = Depends(get_hft_services)
) -> Dict[str, Any]:
    """
    Start market making strategy
    
    Begins providing liquidity for specified symbols
    with continuous bid/ask quote management.
    """
    try:
        _, market_making_strategy, _ = services
        
        # Validate mode
        try:
            mode = MarketMakingMode(request.mode)
            market_making_strategy.mode = mode
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid market making mode: {request.mode}"
            )
        
        # Update parameters if provided
        if request.parameters:
            await market_making_strategy.update_strategy_parameters(request.parameters)
        
        # Start market making
        result = await market_making_strategy.start_market_making(request.symbols)
        
        return {
            "success": result['status'] == 'success',
            "result": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/market-making/stop")
async def stop_market_making(
    services = Depends(get_hft_services)
) -> Dict[str, Any]:
    """
    Stop market making strategy
    
    Cancels all quotes and stops providing liquidity.
    """
    try:
        _, market_making_strategy, _ = services
        
        result = await market_making_strategy.stop_market_making()
        
        return {
            "success": result['status'] == 'success',
            "result": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/market-making/status")
async def get_market_making_status(
    services = Depends(get_hft_services)
) -> Dict[str, Any]:
    """
    Get market making status
    
    Returns current status, positions, quotes, and performance metrics
    for the market making strategy.
    """
    try:
        _, market_making_strategy, _ = services
        
        status = await market_making_strategy.get_market_making_status()
        
        return {
            "success": status['status'] == 'success',
            "status": status.get('data', {})
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/market-making/config")
async def update_market_making_config(
    config: MarketMakingConfigRequest,
    services = Depends(get_hft_services)
) -> Dict[str, Any]:
    """
    Update market making configuration
    
    Updates strategy parameters for spread management,
    position sizing, and risk controls.
    """
    try:
        _, market_making_strategy, _ = services
        
        # Build parameters dictionary
        parameters = {}
        if config.base_spread_bps is not None:
            parameters['base_spread_bps'] = config.base_spread_bps
        if config.base_quote_size is not None:
            parameters['base_quote_size'] = config.base_quote_size
        if config.max_position_value is not None:
            parameters['max_position_value'] = config.max_position_value
        if config.order_refresh_interval is not None:
            parameters['order_refresh_interval'] = config.order_refresh_interval
        
        result = await market_making_strategy.update_strategy_parameters(parameters)
        
        return {
            "success": result['status'] == 'success',
            "result": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/market-making/flatten")
async def flatten_market_making_inventory(
    symbol: Optional[str] = None,
    services = Depends(get_hft_services)
) -> Dict[str, Any]:
    """
    Force flatten market making inventory
    
    Immediately closes positions to reduce inventory risk.
    """
    try:
        _, market_making_strategy, _ = services
        
        result = await market_making_strategy.force_inventory_flatten(symbol)
        
        return {
            "success": result['status'] == 'success',
            "result": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/microstructure/analysis/{symbol}")
async def get_microstructure_analysis(
    symbol: str,
    lookback_minutes: int = 5
) -> Dict[str, Any]:
    """
    Get market microstructure analysis
    
    Analyzes order flow, trade patterns, and liquidity
    for the specified symbol.
    """
    try:
        from ...services.hft.high_frequency_engine import MarketMicrostructureAnalyzer
        
        analyzer = MarketMicrostructureAnalyzer()
        
        # This would analyze recent trade flow and order book data
        # Simplified implementation for demo
        analysis = {
            "symbol": symbol,
            "lookback_minutes": lookback_minutes,
            "trade_flow": {
                "total_volume": 50000,
                "buy_volume": 28000,
                "sell_volume": 22000,
                "flow_imbalance": 0.12,
                "trade_velocity": 15.2,
                "vwap": 150.25
            },
            "order_book": {
                "spread_bps": 2.5,
                "depth_imbalance": 0.08,
                "quote_stability": 0.85
            },
            "detected_patterns": {
                "iceberg_orders": 1,
                "momentum_bursts": 3,
                "mean_reversion_signals": 2
            },
            "liquidity_metrics": {
                "market_impact": "LOW",
                "resilience_score": 0.78,
                "depth_score": 0.82
            }
        }
        
        return {
            "success": True,
            "analysis": analysis
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/arbitrage/detect")
async def detect_arbitrage_opportunities(
    symbols: List[str],
    services = Depends(get_hft_services)
) -> Dict[str, Any]:
    """
    Detect arbitrage opportunities
    
    Scans for price discrepancies and execution opportunities
    across multiple symbols or venues.
    """
    try:
        opportunities = []
        
        # This would implement real arbitrage detection
        # Simplified example
        for symbol in symbols:
            # Mock arbitrage opportunity
            opportunity = {
                "symbol": symbol,
                "type": "STATISTICAL_ARBITRAGE",
                "confidence": 0.85,
                "expected_profit_bps": 5.2,
                "execution_window_ms": 250,
                "risk_level": "LOW",
                "strategy": "PAIR_TRADE",
                "correlated_symbol": "SPY" if symbol != "SPY" else "QQQ"
            }
            opportunities.append(opportunity)
        
        return {
            "success": True,
            "opportunities": opportunities,
            "scan_timestamp": datetime.now().isoformat(),
            "total_opportunities": len(opportunities)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/latency/benchmark")
async def run_latency_benchmark(
    test_duration_seconds: int = 10,
    services = Depends(get_hft_services)
) -> Dict[str, Any]:
    """
    Run latency benchmark test
    
    Performs comprehensive latency testing to measure
    system performance under load.
    """
    try:
        import time
        import statistics
        
        latencies = []
        start_time = time.time()
        
        # Simulate latency testing
        while time.time() - start_time < test_duration_seconds:
            test_start = time.perf_counter_ns()
            
            # Simulate signal processing
            await asyncio.sleep(0.0001)  # 100 microseconds
            
            test_latency = (time.perf_counter_ns() - test_start) / 1000  # microseconds
            latencies.append(test_latency)
        
        if latencies:
            benchmark_results = {
                "test_duration_seconds": test_duration_seconds,
                "samples": len(latencies),
                "avg_latency_us": statistics.mean(latencies),
                "min_latency_us": min(latencies),
                "max_latency_us": max(latencies),
                "p50_latency_us": statistics.median(latencies),
                "p95_latency_us": sorted(latencies)[int(0.95 * len(latencies))],
                "p99_latency_us": sorted(latencies)[int(0.99 * len(latencies))],
                "std_dev_us": statistics.stdev(latencies) if len(latencies) > 1 else 0,
                "performance_grade": "A" if statistics.mean(latencies) < 100 else "B" if statistics.mean(latencies) < 500 else "C"
            }
        else:
            benchmark_results = {"error": "No samples collected"}
        
        return {
            "success": True,
            "benchmark": benchmark_results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
