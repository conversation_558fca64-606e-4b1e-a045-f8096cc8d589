# This file exports commonly used models
# Import are done at runtime to avoid circular import issues

__all__ = [
    "Signal",
    "Order", 
    "Position",
    "OrderStatus",
    "OrderInfo",
    "Portfolio",
    "PortfolioItem"
]

def __getattr__(name):
    """Dynamic import to avoid circular dependencies"""
    if name == "Signal":
        from .domain import Signal
        return Signal
    elif name == "Order":
        from .domain import Order
        return Order
    elif name == "Position":
        from .domain import Position
        return Position
    elif name == "OrderStatus":
        from .order_models import OrderStatus
        return OrderStatus
    elif name == "OrderInfo":
        from .order_models import OrderInfo
        return OrderInfo
    elif name == "Portfolio":
        from .portfolio import Portfolio
        return Portfolio
    elif name == "PortfolioItem":
        from .portfolio import PortfolioItem
        return PortfolioItem
    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")