"""
Database Models for IBKR MCP Server

Provides SQLAlchemy-style base models and common database patterns
for use across the application.
"""

from typing import Any, Dict
from datetime import datetime, timezone
from uuid import UUID, uuid4
from enum import Enum
import json


class Base:
    """
    Base class for all database models.
    
    Provides common functionality and patterns used across
    all database operations in the IBKR MCP Server.
    """
    
    def __init__(self, **kwargs):
        """Initialize base model with provided attributes"""
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary representation"""
        result = {}
        for key, value in self.__dict__.items():
            if not key.startswith('_'):
                if isinstance(value, datetime):
                    result[key] = value.isoformat()
                elif isinstance(value, UUID):
                    result[key] = str(value)
                elif isinstance(value, Enum):
                    result[key] = value.value
                else:
                    result[key] = value
        return result
    
    def from_dict(self, data: Dict[str, Any]) -> 'Base':
        """Create model instance from dictionary"""
        for key, value in data.items():
            setattr(self, key, value)
        return self
    
    def to_json(self) -> str:
        """Convert model to JSON string"""
        return json.dumps(self.to_dict(), default=str)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Base':
        """Create model instance from JSON string"""
        data = json.loads(json_str)
        return cls().from_dict(data)


class TimestampMixin:
    """Mixin for models that need timestamp tracking"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.created_at = kwargs.get('created_at', datetime.now(timezone.utc))
        self.updated_at = kwargs.get('updated_at', datetime.now(timezone.utc))
    
    def touch(self):
        """Update the updated_at timestamp"""
        self.updated_at = datetime.now(timezone.utc)


class UUIDMixin:
    """Mixin for models that use UUID primary keys"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.id = kwargs.get('id', uuid4())


class MarketDataModel(Base, TimestampMixin, UUIDMixin):
    """Model for market data records"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.symbol = kwargs.get('symbol')
        self.price = kwargs.get('price')
        self.bid = kwargs.get('bid')
        self.ask = kwargs.get('ask')
        self.volume = kwargs.get('volume')
        self.source = kwargs.get('source', 'IBKR')


class TradingSignalModel(Base, TimestampMixin, UUIDMixin):
    """Model for trading signals"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.symbol = kwargs.get('symbol')
        self.signal_type = kwargs.get('signal_type')  # BUY, SELL
        self.signal_strength = kwargs.get('signal_strength')
        self.target_price = kwargs.get('target_price')
        self.suggested_quantity = kwargs.get('suggested_quantity')
        self.timeframe = kwargs.get('timeframe')
        self.metadata = kwargs.get('metadata', {})
        self.is_active = kwargs.get('is_active', True)


class OrderModel(Base, TimestampMixin, UUIDMixin):
    """Model for trading orders"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.symbol = kwargs.get('symbol')
        self.order_type = kwargs.get('order_type')  # MARKET, LIMIT, STOP
        self.side = kwargs.get('side')  # BUY, SELL
        self.quantity = kwargs.get('quantity')
        self.price = kwargs.get('price')
        self.status = kwargs.get('status', 'PENDING')
        self.filled_quantity = kwargs.get('filled_quantity', 0)
        self.avg_fill_price = kwargs.get('avg_fill_price')
        self.ibkr_order_id = kwargs.get('ibkr_order_id')


class PositionModel(Base, TimestampMixin, UUIDMixin):
    """Model for trading positions"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.symbol = kwargs.get('symbol')
        self.quantity = kwargs.get('quantity')
        self.avg_cost = kwargs.get('avg_cost')
        self.market_value = kwargs.get('market_value')
        self.unrealized_pnl = kwargs.get('unrealized_pnl')
        self.realized_pnl = kwargs.get('realized_pnl')


class WebhookEventModel(Base, TimestampMixin, UUIDMixin):
    """Model for webhook events"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.event_type = kwargs.get('event_type')
        self.payload = kwargs.get('payload', {})
        self.status = kwargs.get('status', 'PENDING')
        self.retry_count = kwargs.get('retry_count', 0)
        self.processed_at = kwargs.get('processed_at')
        self.error_message = kwargs.get('error_message')


class RiskParametersModel(Base, TimestampMixin, UUIDMixin):
    """Model for risk management parameters"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.max_total_exposure = kwargs.get('max_total_exposure')
        self.max_position_size = kwargs.get('max_position_size')
        self.max_drawdown_pct = kwargs.get('max_drawdown_pct')
        self.min_signal_strength = kwargs.get('min_signal_strength')
        self.is_active = kwargs.get('is_active', True)


class AuditLogModel(Base, TimestampMixin, UUIDMixin):
    """Model for audit log entries"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.action = kwargs.get('action')
        self.entity_type = kwargs.get('entity_type')
        self.entity_id = kwargs.get('entity_id')
        self.old_values = kwargs.get('old_values', {})
        self.new_values = kwargs.get('new_values', {})
        self.user_id = kwargs.get('user_id')
        self.session_id = kwargs.get('session_id')


# Export commonly used models
__all__ = [
    'Base',
    'TimestampMixin',
    'UUIDMixin',
    'MarketDataModel',
    'TradingSignalModel',
    'OrderModel',
    'PositionModel',
    'WebhookEventModel',
    'RiskParametersModel',
    'AuditLogModel'
]
