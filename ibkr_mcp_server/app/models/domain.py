"""
Domain Models

Basic domain models for signals, orders, and positions.
These are simplified models for use in event streaming and general operations.
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from uuid import UUID


@dataclass
class Signal:
    """Trading signal model"""
    id: str
    symbol: str
    signal_type: str  # "BUY", "SELL", "HOLD"
    strength: float
    timestamp: datetime
    source: str
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "symbol": self.symbol,
            "signal_type": self.signal_type,
            "strength": self.strength,
            "timestamp": self.timestamp.isoformat(),
            "source": self.source,
            "metadata": self.metadata or {}
        }


@dataclass 
class Order:
    """Order model"""
    id: str
    symbol: str
    action: str  # "BUY", "SELL"
    quantity: int
    order_type: str  # "LMT", "MKT", etc.
    price: Optional[Decimal] = None
    status: str = "PENDING"
    timestamp: Optional[datetime] = None
    account: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "symbol": self.symbol,
            "action": self.action,
            "quantity": self.quantity,
            "order_type": self.order_type,
            "price": float(self.price) if self.price else None,
            "status": self.status,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "account": self.account,
            "metadata": self.metadata or {}
        }


@dataclass
class Position:
    """Position model"""
    id: str
    symbol: str
    quantity: Decimal
    avg_cost: Decimal
    market_price: Optional[Decimal] = None
    market_value: Optional[Decimal] = None
    unrealized_pnl: Optional[Decimal] = None
    account: Optional[str] = None
    timestamp: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.market_value is None and self.market_price is not None:
            self.market_value = self.quantity * self.market_price
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "symbol": self.symbol,
            "quantity": float(self.quantity),
            "avg_cost": float(self.avg_cost),
            "market_price": float(self.market_price) if self.market_price else None,
            "market_value": float(self.market_value) if self.market_value else None,
            "unrealized_pnl": float(self.unrealized_pnl) if self.unrealized_pnl else None,
            "account": self.account,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "metadata": self.metadata or {}
        }
