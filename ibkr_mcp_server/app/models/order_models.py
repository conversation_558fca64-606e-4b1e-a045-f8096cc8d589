
# /Users/<USER>/projects/b-team/ibkr_mcp_server/app/models/order_models.py
from enum import Enum
from dataclasses import dataclass, field
from typing import Optional, Dict, Any
from datetime import datetime
import logging # Added for logger if used directly, or remove logger call

class OrderStatus(Enum):
    PENDING_SUBMIT = "PendingSubmit"
    PENDING_CANCEL = "PendingCancel"
    PRE_SUBMITTED = "PreSubmitted"
    SUBMITTED = "Submitted"
    API_CANCELLED = "ApiCancelled" # TWS often sends ApiCancelled for successful cancellations
    CANCELLED = "Cancelled"
    FILLED = "Filled"
    INACTIVE = "Inactive"
    API_PENDING = "ApiPending" # For orders not yet acknowledged by TWS
    UNKNOWN = "Unknown" # Default or error state

    @classmethod
    def _missing_(cls, value):
        # Handle cases where TWS might send a status string not exactly matching an enum member
        for member in cls:
            if member.value.lower() == str(value).lower():
                return member
        # Consider logging this in the service layer instead of directly in the model
        # logging.getLogger(__name__).warning(f"Unknown OrderStatus value: {value}. Defaulting to UNKNOWN.")
        return cls.UNKNOWN

@dataclass
class OrderInfo: # Renamed for broader use beyond just status
    order_id: int
    status: OrderStatus = field(default=OrderStatus.UNKNOWN)
    filled: float = 0.0
    remaining: float = 0.0
    avg_fill_price: float = 0.0
    perm_id: int = 0
    parent_id: int = 0
    last_fill_price: float = 0.0
    client_id: int = 0
    why_held: str = ""
    mkt_cap_price: Optional[float] = None # Make optional as it's not always present
    timestamp: datetime = field(default_factory=datetime.now)
    # For active orders list:
    contract: Optional[Any] = None # from ib_async.contract import Contract
    order: Optional[Any] = None    # from ib_async.order import Order
    order_state: Optional[Any] = None # from ib_async.order import OrderState

    # Helper to update from OrderStatusInfo if needed, or directly from TWS params
    def update_status(self, status_str: str, filled: float, remaining: float, avg_fill_price: float,
                      perm_id: int, parent_id: int, last_fill_price: float, client_id: int,
                      why_held: str, mkt_cap_price: Optional[float]):
        self.status = OrderStatus(status_str) # Use the enum's _missing_ to handle variations
        self.filled = filled
        self.remaining = remaining
        self.avg_fill_price = avg_fill_price
        self.perm_id = perm_id
        self.parent_id = parent_id
        self.last_fill_price = last_fill_price
        self.client_id = client_id
        self.why_held = why_held
        self.mkt_cap_price = mkt_cap_price
        self.timestamp = datetime.now()
