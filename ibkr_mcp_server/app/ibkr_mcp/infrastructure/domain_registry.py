"""
Domain Registry for Multi-Layer Architecture
Central registration and management of all domain managers.
"""

from typing import Dict, List, Type, Any, Optional
import logging
from .base_domain import BaseDomain<PERSON>anager, DomainConfig

logger = logging.getLogger(__name__)

class DomainRegistry:
    """Central registry for all domain managers"""
    
    def __init__(self):
        self.domains: Dict[str, BaseDomainManager] = {}
        self.services: Dict[str, Any] = {}
    
    def register_services(self, services: Dict[str, Any]):
        """Register common services available to all domains"""
        self.services.update(services)
        logger.info(f"Registered {len(services)} services: {list(services.keys())}")
    
    def register_domain(self, domain_name: str, domain_class: Type[BaseDomainManager], 
                       implementations: Dict[str, Type], custom_services: Optional[Dict[str, Any]] = None):
        """Register a new domain manager"""
        try:
            # Merge common services with domain-specific services
            all_services = {**self.services}
            if custom_services:
                all_services.update(custom_services)
            
            config = DomainConfig(
                name=domain_name,
                implementations=implementations,
                services=all_services
            )
            
            domain_manager = domain_class(config)
            self.domains[domain_name] = domain_manager
            
            logger.info(f"Registered domain: {domain_name}")
            return domain_manager
            
        except Exception as e:
            logger.error(f"Failed to register domain {domain_name}: {e}")
            raise
    
    def get_domain(self, domain_name: str) -> BaseDomainManager:
        """Get domain manager by name"""
        if domain_name not in self.domains:
            available = list(self.domains.keys())
            raise ValueError(f"Domain '{domain_name}' not registered. Available: {available}")
        
        return self.domains[domain_name]
    
    def list_domains(self) -> List[str]:
        """List all registered domain names"""
        return list(self.domains.keys())
    
    def get_all_tools(self) -> Dict[str, List[str]]:
        """Get all available tools across all domains"""
        all_tools = {}
        for domain_name, domain_manager in self.domains.items():
            all_tools[domain_name] = domain_manager.get_available_tools()
        return all_tools
    
    def health_check(self) -> Dict[str, Any]:
        """Check health of all registered domains"""
        health_status = {
            "overall_status": "healthy",
            "domains": {},
            "total_domains": len(self.domains),
            "total_services": len(self.services)
        }
        
        for domain_name, domain_manager in self.domains.items():
            try:
                domain_manager.check_initialization()
                health_status["domains"][domain_name] = {
                    "status": "healthy",
                    "initialized": True,
                    "tools_count": len(domain_manager.get_available_tools())
                }
            except Exception as e:
                health_status["domains"][domain_name] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "initialized": False
                }
                health_status["overall_status"] = "degraded"
        
        return health_status

# Global registry instance
registry = DomainRegistry()
