"""
Integration Coordinator for Multi-Domain Operations
Coordinates operations that span multiple domains.
"""

from typing import Any, Dict, List, Optional
import logging
from .domain_registry import registry

logger = logging.getLogger(__name__)

class IntegrationCoordinator:
    """Coordinates multi-domain operations and cross-cutting concerns"""
    
    def __init__(self):
        self.registry = registry
    
    def execute_cross_domain_operation(self, operation_config: Dict[str, Any]) -> Dict[str, Any]:
        """Execute operation that spans multiple domains"""
        try:
            domain_name = operation_config.get('domain')
            tool_name = operation_config.get('tool')
            params = operation_config.get('params', {})
            
            # Validate required parameters
            if not domain_name:
                return {
                    "success": False,
                    "error": "Domain name is required",
                    "operation_config": operation_config
                }
            
            if not tool_name:
                return {
                    "success": False,
                    "error": "Tool name is required",
                    "operation_config": operation_config
                }
            
            # Get domain manager
            domain_manager = self.registry.get_domain(domain_name)
            
            # Execute operation through domain
            result = self._execute_domain_operation(domain_manager, tool_name, params)
            
            # Add coordination metadata
            result['coordination'] = {
                'domain': domain_name,
                'tool': tool_name,
                'coordinated_by': 'IntegrationCoordinator'
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Cross-domain operation failed: {e}")
            return {
                "success": False,
                "error": f"Cross-domain operation failed: {str(e)}",
                "operation_config": operation_config
            }
    
    def _execute_domain_operation(self, domain_manager, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute operation within a specific domain"""
        # This will be implemented by each domain manager
        # For now, return a placeholder
        return {
            "success": True,
            "message": f"Operation {tool_name} executed in domain {domain_manager.config.name}",
            "params": params
        }
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status across all domains"""
        try:
            health_check = self.registry.health_check()
            all_tools = self.registry.get_all_tools()
            
            return {
                "timestamp": self._get_timestamp(),
                "system_health": health_check,
                "available_tools": all_tools,
                "coordination_status": "active"
            }
            
        except Exception as e:
            logger.error(f"System status check failed: {e}")
            return {
                "timestamp": self._get_timestamp(),
                "system_health": {"overall_status": "error", "error": str(e)},
                "coordination_status": "error"
            }
    
    def validate_operation_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate operation configuration before execution"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Check required fields
        required_fields = ['domain', 'tool']
        for field in required_fields:
            if field not in config:
                validation_result["valid"] = False
                validation_result["errors"].append(f"Missing required field: {field}")
        
        # Check domain exists
        if 'domain' in config:
            domain_name = config['domain']
            if domain_name not in self.registry.list_domains():
                validation_result["valid"] = False
                validation_result["errors"].append(f"Unknown domain: {domain_name}")
        
        return validation_result
    
    def _get_timestamp(self) -> str:
        """Get current timestamp"""
        from datetime import datetime
        return datetime.now().isoformat()

# Global coordinator instance
coordinator = IntegrationCoordinator()
