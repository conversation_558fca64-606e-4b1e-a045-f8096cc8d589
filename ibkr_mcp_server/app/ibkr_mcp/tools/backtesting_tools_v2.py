"""
Backtesting Tools - Layer 1: MCP Interface
Pure MCP interface with single-line delegations to domain manager.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, List, Optional
from ibkr_mcp.domains.backtesting_domain import BacktestingDomain

class BacktestingToolsV2:
    """MCP Tools interface for Backtesting domain"""
    
    def __init__(self):
        self.domain = BacktestingDomain()
    
    def get_available_tools(self) -> list:
        """Return list of available tools for this domain"""
        return [
            "run_backtest",
            "optimize_strategy",
            "walk_forward_analysis",
            "monte_carlo_simulation",
            "calculate_performance_metrics",
            "compare_strategies",
            "generate_backtest_report",
            "validate_strategy"
        ]

    async def run_backtest(self, strategy_config: Dict[str, Any], start_date: str, end_date: str, initial_capital: float = 100000) -> Dict[str, Any]:
        """Run a comprehensive backtest on historical data with strategy configuration"""
        return await self.domain.run_backtest(strategy_config=strategy_config, start_date=start_date, end_date=end_date, initial_capital=initial_capital)

    async def optimize_strategy(self, strategy_config: Dict[str, Any], parameter_ranges: Dict[str, Any], optimization_metric: str = "sharpe_ratio") -> Dict[str, Any]:
        """Optimize strategy parameters using grid search or genetic algorithms"""
        return await self.domain.optimize_strategy(strategy_config=strategy_config, parameter_ranges=parameter_ranges, optimization_metric=optimization_metric)

    async def walk_forward_analysis(self, strategy_config: Dict[str, Any], window_size: int = 252, step_size: int = 63) -> Dict[str, Any]:
        """Perform walk-forward analysis to test strategy robustness over time"""
        return await self.domain.walk_forward_analysis(strategy_config=strategy_config, window_size=window_size, step_size=step_size)

    async def monte_carlo_simulation(self, strategy_config: Dict[str, Any], num_simulations: int = 1000, confidence_level: float = 0.95) -> Dict[str, Any]:
        """Run Monte Carlo simulation to assess strategy risk and return distributions"""
        return await self.domain.monte_carlo_simulation(strategy_config=strategy_config, num_simulations=num_simulations, confidence_level=confidence_level)

    async def calculate_performance_metrics(self, returns: List[float], benchmark_returns: Optional[List[float]] = None) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics including Sharpe, Sortino, Calmar ratios"""
        return await self.domain.calculate_performance_metrics(returns=returns, benchmark_returns=benchmark_returns)

    async def compare_strategies(self, strategy_results: List[Dict[str, Any]], comparison_metrics: Optional[List[str]] = None) -> Dict[str, Any]:
        """Compare multiple strategy results across various performance metrics"""
        return await self.domain.compare_strategies(strategy_results=strategy_results, comparison_metrics=comparison_metrics)

    async def generate_backtest_report(self, backtest_results: Dict[str, Any], include_charts: bool = True) -> Dict[str, Any]:
        """Generate comprehensive backtest report with charts and analysis"""
        return await self.domain.generate_backtest_report(backtest_results=backtest_results, include_charts=include_charts)

    async def validate_strategy(self, strategy_config: Dict[str, Any], validation_period: str = "1Y") -> Dict[str, Any]:
        """Validate strategy performance on out-of-sample data"""
        return await self.domain.validate_strategy(strategy_config=strategy_config, validation_period=validation_period)
