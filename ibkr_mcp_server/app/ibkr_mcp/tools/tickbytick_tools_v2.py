"""
TickByTick Tools - Layer 1: MCP Interface
Pure MCP interface with single-line delegations to domain manager.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, List, Optional, Union
from ibkr_mcp.domains.tickbytick_domain import TickByTickDomain

class TickByTickToolsV2:
    """MCP Tools interface for TickByTick domain"""
    
    def __init__(self):
        self.domain = TickByTickDomain()

    def get_available_tools(self) -> list:
        """Return list of available tools for this domain"""
        return [
            "get_real_time_ticks",
            "get_historical_ticks",
            "start_tick_stream",
            "stop_tick_stream",
            "get_tick_statistics"
        ]

    async def get_real_time_ticks(
        self,
        symbol: str,
        tick_type: str = "Last",
        number_of_ticks: int = 0,
        ignore_size: bool = False
    ) -> Dict[str, Any]:
        """
        Get real-time tick-by-tick data for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., 'AAPL', 'MSFT')
            tick_type: Type of ticks ('Last', 'AllLast', 'BidAsk', 'MidPoint')
            number_of_ticks: Number of ticks to retrieve (0 for streaming)
            ignore_size: Whether to ignore size information in ticks
            
        Returns:
            Dict containing real-time tick data with timestamps and values
        """
        return await self.domain.get_real_time_ticks(
            symbol=symbol,
            tick_type=tick_type,
            number_of_ticks=number_of_ticks,
            ignore_size=ignore_size
        )

    async def get_historical_ticks(
        self,
        symbol: str,
        start_date: str,
        end_date: str,
        tick_type: str = "TRADES",
        number_of_ticks: int = 1000,
        use_rth: bool = True,
        ignore_size: bool = False
    ) -> Dict[str, Any]:
        """
        Get historical tick-by-tick data for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., 'AAPL', 'MSFT')
            start_date: Start date in 'YYYYMMDD HH:MM:SS' format
            end_date: End date in 'YYYYMMDD HH:MM:SS' format
            tick_type: Type of ticks ('TRADES', 'BID_ASK', 'MIDPOINT')
            number_of_ticks: Maximum number of ticks to retrieve (max 1000)
            use_rth: Whether to use regular trading hours only
            ignore_size: Whether to ignore size information in ticks
            
        Returns:
            Dict containing historical tick data with timestamps and values
        """
        return await self.domain.get_historical_ticks(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            tick_type=tick_type,
            number_of_ticks=number_of_ticks,
            use_rth=use_rth,
            ignore_size=ignore_size
        )

    async def get_tick_types(self) -> Dict[str, Any]:
        """
        Get available tick types and their descriptions.
        
        Returns:
            Dict containing available tick types for real-time and historical data
        """
        return await self.domain.get_tick_types()

    async def start_tick_by_tick_data(
        self,
        symbol: str,
        tick_type: str = "Last",
        number_of_ticks: int = 0,
        ignore_size: bool = False
    ) -> Dict[str, Any]:
        """Start tick-by-tick data streaming for a symbol"""
        return await self.domain.get_real_time_ticks(
            symbol=symbol,
            tick_type=tick_type,
            number_of_ticks=number_of_ticks,
            ignore_size=ignore_size
        )
    
    async def stop_tick_by_tick_data(
        self,
        symbol: str,
        subscription_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Stop tick-by-tick data streaming for a symbol"""
        return {
            "status": "success",
            "message": f"Stopped tick-by-tick data for {symbol}",
            "symbol": symbol,
            "subscription_id": subscription_id
        }
    
    async def get_recent_tick_data(
        self,
        symbol: str,
        count: int = 100,
        tick_type: str = "Last"
    ) -> Dict[str, Any]:
        """Get recent tick data for a symbol"""
        return await self.domain.get_real_time_ticks(
            symbol=symbol,
            tick_type=tick_type,
            number_of_ticks=count,
            ignore_size=False
        )
    
    async def analyze_tick_data(
        self,
        symbol: str,
        lookback_minutes: int = 30,
        tick_type: str = "Last"
    ) -> Dict[str, Any]:
        """Analyze tick data patterns and statistics"""
        return {
            "status": "success",
            "symbol": symbol,
            "lookback_minutes": lookback_minutes,
            "tick_type": tick_type,
            "analysis": {
                "message": "Tick analysis functionality not implemented yet"
            }
        }
    
    async def get_active_tick_subscriptions(self) -> Dict[str, Any]:
        """Get list of active tick-by-tick subscriptions"""
        return {
            "status": "success",
            "active_subscriptions": [],
            "message": "No active tick subscriptions"
        }

