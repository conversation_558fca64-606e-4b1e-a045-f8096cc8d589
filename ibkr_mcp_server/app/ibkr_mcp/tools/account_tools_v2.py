"""
Account Tools - Layer 1: MCP Interface
Pure MCP interface with single-line delegations to domain manager.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional
from ibkr_mcp.domains.account_domain import AccountDomain

class AccountToolsV2:
    """MCP Tools interface for Account domain"""
    
    def __init__(self):
        self.domain = AccountDomain()
    
    def get_available_tools(self) -> list:
        """Return list of available tools for this domain"""
        return [
            "get_account_summary",
            "get_portfolio_positions",
            "get_account_balance",
            "get_buying_power",
            "get_pnl",
            "get_account_info",
            "get_margin_info",
            "get_cash_balance"
        ]

    async def get_account_summary(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get comprehensive account summary including balance, positions, P&L"""
        return await self.domain.get_account_summary(account=account)

    async def get_portfolio_positions(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get current portfolio positions with market values"""
        return await self.domain.get_portfolio_positions(account=account)

    async def get_account_balance(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get account cash balance and available funds"""
        return await self.domain.get_account_balance(account=account)

    async def get_buying_power(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get available buying power for trading"""
        return await self.domain.get_buying_power(account=account)

    async def get_pnl(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get profit and loss information for account"""
        return await self.domain.get_pnl(account=account)

    async def get_account_info(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get detailed account information and settings"""
        return await self.domain.get_account_info(account=account)

    async def get_margin_info(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get margin requirements and maintenance information"""
        return await self.domain.get_margin_info(account=account)

    async def get_cash_balance(self, account: Optional[str] = None, currency: str = "USD") -> Dict[str, Any]:
        """Get cash balance for specific currency"""
        return await self.domain.get_cash_balance(account=account, currency=currency)
