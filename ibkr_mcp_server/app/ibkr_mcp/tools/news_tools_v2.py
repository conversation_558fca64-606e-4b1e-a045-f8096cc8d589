"""
News Tools - Layer 1: MCP Interface
Pure MCP interface with single-line delegations to domain manager.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional, Callable
from ibkr_mcp.domains.news_domain import NewsDomain

class NewsToolsV2:
    """MCP Tools interface for News domain"""
    
    def __init__(self):
        self.domain = NewsDomain()
    
    def get_available_tools(self) -> list:
        """Return list of available tools for this domain"""
        return [
            "request_news_bulletins",
            "cancel_news_bulletins", 
            "request_historical_news",
            "get_news_providers",
            "analyze_news_sentiment"
        ]
    
    async def request_news_bulletins(
        self,
        all_msgs: bool = True,
        provider: Optional[str] = None,
        callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Request real-time news bulletins streaming"""
        return await self.domain.request_news_bulletins(all_msgs=all_msgs, provider=provider, callback=callback)
    
    async def cancel_news_bulletins(self, subscription_id: str) -> Dict[str, Any]:
        """Cancel news bulletins subscription"""
        return await self.domain.cancel_news_bulletins(subscription_id=subscription_id)
    
    async def request_historical_news(
        self,
        conid: int,
        provider_codes: str = "BRFG+DJNL",
        start_date_time: str = "",
        end_date_time: str = "",
        total_results: int = 10
    ) -> Dict[str, Any]:
        """Request historical news articles for a contract"""
        return await self.domain.request_historical_news(conid=conid, provider_codes=provider_codes, start_date_time=start_date_time, end_date_time=end_date_time, total_results=total_results)
    
    async def request_news_article(self, article_id: str, provider_code: str) -> Dict[str, Any]:
        """Request full content of a specific news article"""
        return await self.domain.request_news_article(article_id=article_id, provider_code=provider_code)

    async def get_news_providers(self) -> Dict[str, Any]:
        """Get list of available news providers and their details"""
        return await self.domain.get_news_providers()

    async def get_active_subscriptions(self) -> Dict[str, Any]:
        """Get all active news subscriptions"""
        return await self.domain.get_active_subscriptions()
    
    async def get_news_headlines(self, provider: Optional[str] = None, limit: int = 50) -> Dict[str, Any]:
        """Get news headlines from specified provider"""
        return await self.domain.get_news_headlines(provider=provider, limit=limit)
    
    async def get_news_article(self, article_id: str, provider_code: str) -> Dict[str, Any]:
        """Get full news article content - alias for request_news_article"""
        return await self.request_news_article(article_id, provider_code)
    
    async def stream_news(self, provider: Optional[str] = None, callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Stream real-time news updates - alias for request_news_bulletins"""
        return await self.request_news_bulletins(all_msgs=True, provider=provider, callback=callback)
    
    async def stop_news_stream(self, subscription_id: str) -> Dict[str, Any]:
        """Stop news streaming - alias for cancel_news_bulletins"""
        return await self.cancel_news_bulletins(subscription_id)
    
    async def analyze_news_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment of news text"""
        return await self.domain.analyze_news_sentiment(text=text)
    
    async def get_news_sources(self) -> Dict[str, Any]:
        """Get available news sources - alias for get_news_providers"""
        return await self.get_news_providers()

