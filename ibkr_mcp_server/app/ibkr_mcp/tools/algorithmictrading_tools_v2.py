"""
AlgorithmicTrading Tools - Layer 1: MCP Interface
Pure MCP interface with single-line delegations to domain manager.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, List, Optional
from ibkr_mcp.domains.algorithmictrading_domain import AlgorithmicTradingDomain

class AlgorithmicTradingToolsV2:
    """MCP Tools interface for AlgorithmicTrading domain"""
    
    def __init__(self):
        self.domain = AlgorithmicTradingDomain()
    
    def get_available_tools(self) -> list:
        """Return list of available tools for this domain"""
        return [
            "execute_algo_strategy",
            "stop_algo_strategy", 
            "subscribe_streaming_data",
            "unsubscribe_streaming_data",
            "get_market_depth",
            "rebalance_portfolio",
            "optimize_portfolio",
            "route_smart_order",
            "analyze_market_microstructure",
            "start_trading_strategy",
            "stop_trading_strategy",
            "get_strategy_status",
            "get_active_algo_strategies"
        ]
    
    async def execute_algo_strategy(
        self,
        strategy_type: str,
        symbols: List[str],
        max_position_size: float,
        risk_limit: float,
        account: str,
        custom_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute algorithmic trading strategy"""
        return await self.domain.execute_algo_strategy(strategy_type, symbols, max_position_size, risk_limit, account, custom_params)
    
    async def stop_algo_strategy(self, strategy_id: str) -> Dict[str, Any]:
        """Stop running algorithmic strategy"""
        return await self.domain.stop_algo_strategy(strategy_id)
    
    async def subscribe_streaming_data(
        self,
        symbol: str,
        data_types: List[str],
        generic_tick_list: str = ""
    ) -> Dict[str, Any]:
        """Subscribe to real-time streaming market data"""
        return await self.domain.subscribe_streaming_data(symbol, data_types, generic_tick_list)
    
    async def unsubscribe_streaming_data(self, subscription_id: str) -> Dict[str, Any]:
        """Unsubscribe from streaming data"""
        return await self.domain.unsubscribe_streaming_data(subscription_id)
    
    async def rebalance_portfolio(
        self,
        strategy: str,
        target_allocations: List[Dict[str, Any]],
        account: str,
        rebalance_threshold: float = 0.05,
        force: bool = False
    ) -> Dict[str, Any]:
        """Rebalance portfolio to target allocations"""
        return await self.domain.rebalance_portfolio(strategy, target_allocations, account, rebalance_threshold, force)
    
    async def optimize_portfolio(
        self,
        symbols: List[str],
        optimization_method: str = "maximum_sharpe",
        constraints: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Optimize portfolio allocation"""
        return await self.domain.optimize_portfolio(symbols, optimization_method, constraints)
    
    async def route_smart_order(
        self,
        algorithm: str,
        symbol: str,
        action: str,
        quantity: float,
        account: str,
        urgency: str = "Normal",
        custom_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Route order using smart order routing algorithms"""
        return await self.domain.route_smart_order(algorithm, symbol, action, quantity, account, urgency, custom_params)
    
    async def analyze_market_microstructure(
        self,
        symbol: str,
        size: int
    ) -> Dict[str, Any]:
        """Analyze market microstructure"""
        return await self.domain.analyze_market_microstructure(symbol, size)
    
    async def start_trading_strategy(
        self,
        strategy_name: str,
        strategy_id: str,
        account: str,
        symbols: List[str],
        parameters: Dict[str, Any],
        risk_limits: Dict[str, float]
    ) -> Dict[str, Any]:
        """Start custom trading strategy"""
        return await self.domain.start_trading_strategy(strategy_name, strategy_id, account, symbols, parameters, risk_limits)
    
    async def stop_trading_strategy(self, strategy_id: str) -> Dict[str, Any]:
        """Stop trading strategy"""
        return await self.domain.stop_trading_strategy(strategy_id)
    
    async def get_strategy_status(self, strategy_id: str) -> Dict[str, Any]:
        """Get strategy status"""
        return await self.domain.get_strategy_status(strategy_id)
    
    async def get_active_algo_strategies(self) -> Dict[str, Any]:
        """Get active algorithmic strategies"""
        return await self.domain.get_active_algo_strategies()

