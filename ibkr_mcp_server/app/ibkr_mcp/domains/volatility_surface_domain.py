"""
Volatility Surface Domain Manager - Layer 2: Coordination
Coordinates between MCP interface and focused implementations.
Generated systematically for bloat reduction.
"""

from typing import Dict, Any, List, Optional
from ibkr_mcp.infrastructure.base_domain import BaseDomainManager, DomainConfig
from implementations.volatility.surface_calculation_impl import SurfaceCalculationImpl
from implementations.volatility.smile_analysis_impl import SmileAnalysisImpl
from implementations.volatility.term_structure_impl import TermStructureImplementation
from implementations.volatility.arbitrage_detection_impl import ArbitrageDetectionImplementation

class VolatilitySurfaceDomain(BaseDomainManager):
    """Domain manager for Volatility Surface operations"""
    
    def __init__(self):
        implementations = {
            'surface_calc': SurfaceCalculationImpl,
            'smile_analysis': SmileAnalysisImpl,
            'term_structure': TermStructureImplementation,
            'arbitrage_detection': ArbitrageDetectionImplementation
        }
        
        # Services will be injected by the registry
        services = {}
        
        config = DomainConfig(
            name='VolatilitySurface',
            implementations=implementations,
            services=services
        )
        
        super().__init__(config)
    
    def get_available_tools(self) -> List[str]:
        """Return list of available tool names"""
        return [
            "build_volatility_surface",
            "analyze_volatility_smile", 
            "analyze_term_structure",
            "detect_volatility_arbitrage"
        ]
    
    async def call_method(self, method_name: str, **kwargs) -> Dict[str, Any]:
        """Generic method calling interface"""
        if hasattr(self, method_name):
            method = getattr(self, method_name)
            return await method(**kwargs)
        else:
            return {"success": False, "error": f"Method {method_name} not found"}

    async def build_volatility_surface(
        self, 
        underlying_symbol: str,
        strikes: Optional[List[float]] = None,
        expiries: Optional[List[str]] = None,
        surface_type: str = "implied_volatility"
    ) -> Dict[str, Any]:
        """Delegate build_volatility_surface to implementation"""
        try:
            self.check_initialization()
            impl = self.get_implementation('surface_calc')
            return await impl.build_volatility_surface(underlying_symbol, strikes, expiries, surface_type)
        except Exception as e:
            return self.handle_error(e, 'build_volatility_surface')

    async def analyze_volatility_smile(
        self,
        underlying_symbol: str,
        expiry: str,
        strikes: Optional[List[float]] = None
    ) -> Dict[str, Any]:
        """Delegate analyze_volatility_smile to implementation"""
        try:
            self.check_initialization()
            impl = self.get_implementation('smile_analysis')
            return await impl.analyze_volatility_smile(underlying_symbol, expiry, strikes)
        except Exception as e:
            return self.handle_error(e, 'analyze_volatility_smile')

    async def analyze_term_structure(
        self,
        underlying_symbol: str,
        strike: float,
        expiries: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Delegate analyze_term_structure to implementation"""
        try:
            self.check_initialization()
            impl = self.get_implementation('term_structure')
            # Get option chain data first (simplified for this example)
            option_chain_data = await self._get_option_chain_data(underlying_symbol)
            return await impl.analyze_term_structure(underlying_symbol, option_chain_data)
        except Exception as e:
            return self.handle_error(e, 'analyze_term_structure')

    async def detect_volatility_arbitrage(
        self,
        underlying_symbol: str,
        min_profit_threshold: float = 0.01,
        include_calendar_spreads: bool = True
    ) -> Dict[str, Any]:
        """Delegate detect_volatility_arbitrage to implementation"""
        try:
            self.check_initialization()
            impl = self.get_implementation('arbitrage_detection')
            # Get option chain and surface data
            option_chain_data = await self._get_option_chain_data(underlying_symbol)
            surface_data = await self._get_surface_data(underlying_symbol)
            return await impl.detect_arbitrage_opportunities(underlying_symbol, option_chain_data, surface_data)
        except Exception as e:
            return self.handle_error(e, 'detect_volatility_arbitrage')
    
    async def _get_option_chain_data(self, symbol: str) -> Dict:
        """Helper method to get option chain data"""
        # This would use the service to get option chain data
        # For now, return empty dict as placeholder
        return {}
    
    async def _get_surface_data(self, symbol: str) -> Dict:
        """Helper method to get surface data"""
        # This would use the surface calculation implementation
        # For now, return empty dict as placeholder
        return {}
