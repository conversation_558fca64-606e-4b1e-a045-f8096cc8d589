"""
Account Domain - Layer 2: Domain Manager
Manages account and portfolio operations with clean delegation to implementations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, List, Optional
import logging
from ibkr_mcp.infrastructure.base_domain import BaseDomainManager, DomainConfig
from implementations.account.account_impl import AccountImplementation

logger = logging.getLogger(__name__)

class AccountDomain(BaseDomainManager):
    """Domain manager for Account and Portfolio operations"""
    
    def __init__(self):
        implementations = {
            'main_impl': AccountImplementation
        }
        
        # CRITICAL FIX: Get services from registry or use emergency injection
        from ibkr_mcp.infrastructure.domain_registry import registry
        services = registry.services.copy() if hasattr(registry, 'services') and registry.services else {}
        
        # Emergency injection if no services
        if not services or 'ibkr_service' not in services:
            try:
                from services.ibkr_service import ibkr_service as global_service
                services['ibkr_service'] = global_service
                logger.warning(f"Account domain: Using emergency service injection")
            except Exception as e:
                logger.error(f"Account domain: Failed to inject service: {e}")
        
        config = DomainConfig(
            name='Account',
            implementations=implementations,
            services=services
        )
        
        super().__init__(config)
    
    def get_available_tools(self) -> List[str]:
        """Return list of available tool names"""
        return [
            "get_account_summary",
            "get_portfolio_positions",
            "get_account_balance",
            "get_buying_power",
            "get_pnl",
            "get_account_info",
            "get_margin_info",
            "get_cash_balance"
        ]
    
    async def get_account_summary(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get comprehensive account summary"""
        impl = self.get_implementation('main_impl')
        return await impl.get_account_summary(account=account)
    
    async def get_portfolio_positions(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get current portfolio positions"""
        impl = self.get_implementation('main_impl')
        return await impl.get_portfolio_positions(account=account)
    
    async def get_account_balance(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get account cash balance"""
        impl = self.get_implementation('main_impl')
        return await impl.get_account_balance(account=account)
    
    async def get_buying_power(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get available buying power"""
        impl = self.get_implementation('main_impl')
        return await impl.get_buying_power(account=account)
    
    async def get_pnl(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get profit and loss information"""
        impl = self.get_implementation('main_impl')
        return await impl.get_pnl(account=account)
    
    async def get_account_info(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get detailed account information"""
        impl = self.get_implementation('main_impl')
        return await impl.get_account_info(account=account)
    
    async def get_margin_info(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get margin requirements and information"""
        impl = self.get_implementation('main_impl')
        return await impl.get_margin_info(account=account)
    
    async def get_cash_balance(self, account: Optional[str] = None, currency: str = "USD") -> Dict[str, Any]:
        """Get cash balance for specific currency"""
        impl = self.get_implementation('main_impl')
        return await impl.get_cash_balance(account=account, currency=currency)
