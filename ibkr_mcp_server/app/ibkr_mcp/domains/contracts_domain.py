"""
Contracts Domain - Layer 2: Domain Manager
Manages contract research and lookup operations with clean delegation to implementations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, List, Optional
import logging
from ibkr_mcp.infrastructure.base_domain import BaseDomainManager, DomainConfig
from implementations.contracts.contracts_impl import ContractsImplementation

logger = logging.getLogger(__name__)

class ContractsDomain(BaseDomainManager):
    """Domain manager for Contract Research operations"""
    
    def __init__(self):
        implementations = {
            'main_impl': ContractsImplementation
        }
        
        # CRITICAL FIX: Get services from registry or use emergency injection
        from ibkr_mcp.infrastructure.domain_registry import registry
        services = registry.services.copy() if hasattr(registry, 'services') and registry.services else {}
        
        # Emergency injection if no services
        if not services or 'ibkr_service' not in services:
            try:
                from services.ibkr_service import ibkr_service as global_service
                services['ibkr_service'] = global_service
                logger.warning(f"Contracts domain: Using emergency service injection")
            except Exception as e:
                logger.error(f"Contracts domain: Failed to inject service: {e}")
        
        config = DomainConfig(
            name='Contracts',
            implementations=implementations,
            services=services
        )
        
        super().__init__(config)
    
    def get_available_tools(self) -> List[str]:
        """Return list of available tool names"""
        return [
            "search_contracts",
            "get_contract_details",
            "search_forex_pairs",
            "search_futures",
            "search_options",
            "search_stocks",
            "get_contract_specifications",
            "validate_contract"
        ]
    
    async def search_contracts(self, pattern: str, sec_type: str = "STK", exchange: str = "SMART", currency: str = "USD") -> Dict[str, Any]:
        """Search for contracts matching pattern"""
        impl = self.get_implementation('main_impl')
        return await impl.search_contracts(pattern=pattern, sec_type=sec_type, exchange=exchange, currency=currency)
    
    async def get_contract_details(self, symbol: str, sec_type: str = "STK", exchange: str = "SMART", currency: str = "USD") -> Dict[str, Any]:
        """Get detailed contract information"""
        impl = self.get_implementation('main_impl')
        return await impl.get_contract_details(symbol=symbol, sec_type=sec_type, exchange=exchange, currency=currency)
    
    async def search_forex_pairs(self, base_currency: str = "USD", quote_currency: str = "EUR") -> Dict[str, Any]:
        """Search for forex currency pairs"""
        impl = self.get_implementation('main_impl')
        return await impl.search_forex_pairs(base_currency=base_currency, quote_currency=quote_currency)
    
    async def search_futures(self, symbol: str, exchange: str = "CME", currency: str = "USD", include_expired: bool = False) -> Dict[str, Any]:
        """Search for futures contracts"""
        impl = self.get_implementation('main_impl')
        return await impl.search_futures(symbol=symbol, exchange=exchange, currency=currency, include_expired=include_expired)
    
    async def search_options(self, underlying_symbol: str, expiry: Optional[str] = None, strike: Optional[float] = None, right: str = "C") -> Dict[str, Any]:
        """Search for options contracts"""
        impl = self.get_implementation('main_impl')
        return await impl.search_options(underlying_symbol=underlying_symbol, expiry=expiry, strike=strike, right=right)
    
    async def search_stocks(self, pattern: str, exchange: str = "SMART", currency: str = "USD") -> Dict[str, Any]:
        """Search for stock contracts"""
        impl = self.get_implementation('main_impl')
        return await impl.search_stocks(pattern=pattern, exchange=exchange, currency=currency)
    
    async def get_contract_specifications(self, symbol: str, sec_type: str = "STK", exchange: str = "SMART") -> Dict[str, Any]:
        """Get contract specifications and trading details"""
        impl = self.get_implementation('main_impl')
        return await impl.get_contract_specifications(symbol=symbol, sec_type=sec_type, exchange=exchange)
    
    async def validate_contract(self, symbol: str, sec_type: str = "STK", exchange: str = "SMART", currency: str = "USD") -> Dict[str, Any]:
        """Validate if a contract exists and is tradeable"""
        impl = self.get_implementation('main_impl')
        return await impl.validate_contract(symbol=symbol, sec_type=sec_type, exchange=exchange, currency=currency)
