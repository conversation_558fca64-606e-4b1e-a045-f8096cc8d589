"""
RiskManagement Domain Manager - Layer 2: Coordination
Coordinates between MCP interface and focused implementations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional
from ibkr_mcp.infrastructure.base_domain import BaseDomainManager, DomainConfig
from implementations.riskmanagement.riskmanagement_impl import RiskManagementImplementation

class RiskManagementDomain(BaseDomainManager):
    """Domain manager for RiskManagement operations"""
    
    def __init__(self):
        implementations = {
            'main_impl': RiskManagementImplementation
        }
        
        # This will be injected by the registry
        services = {}  # Services injected during registration
        
        config = DomainConfig(
            name='RiskManagement',
            implementations=implementations,
            services=services
        )
        
        super().__init__(config)
    
    def get_available_tools(self) -> list:
        """Return list of available tool names"""
        return [
            "calculate_portfolio_var",
            "analyze_position_risk", 
            "monitor_risk_limits",
            "calculate_correlation_matrix",
            "perform_stress_test"
        ]
    
    # Implementation delegation methods
    
    async def call_method(self, method_name: str, **kwargs) -> Dict[str, Any]:
        """Generic method calling interface"""
        impl = self.get_implementation('main_impl')
        if hasattr(impl, method_name):
            method = getattr(impl, method_name)
            return await method(**kwargs)
        else:
            return {"success": False, "error": f"Method {method_name} not found"}
    
    async def calculate_portfolio_var(self, portfolio_positions: list, 
                                    confidence_level: float = 0.95,
                                    time_horizon: int = 1) -> Dict[str, Any]:
        """Calculate portfolio Value at Risk"""
        impl = self.get_implementation('main_impl')
        return await impl.calculate_portfolio_var(portfolio_positions=portfolio_positions,
                                                confidence_level=confidence_level,
                                                time_horizon=time_horizon)
    
    async def analyze_position_risk(self, position: Dict[str, Any],
                                  stress_test: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Analyze risk metrics for a single position"""
        impl = self.get_implementation('main_impl')
        return await impl.analyze_position_risk(position=position, stress_test=stress_test)
    
    async def monitor_risk_limits(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Monitor current risk limits and exposures"""
        impl = self.get_implementation('main_impl')
        return await impl.monitor_risk_limits(account=account)
    
    async def calculate_correlation_matrix(self, symbols: list, 
                                         period: str = "1Y") -> Dict[str, Any]:
        """Calculate correlation matrix for given symbols"""
        impl = self.get_implementation('main_impl')
        return await impl.calculate_correlation_matrix(symbols=symbols, period=period)
    
    async def perform_stress_test(self, scenarios: Dict[str, Any],
                                 portfolio: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Perform stress testing on portfolio"""
        impl = self.get_implementation('main_impl')
        return await impl.perform_stress_test(scenarios=scenarios, portfolio=portfolio)
