"""
OptionsTrading Domain Manager - Layer 2: Coordination
Coordinates between MCP interface and focused implementations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional
from ibkr_mcp.infrastructure.base_domain import BaseDomainManager, DomainConfig
from implementations.optionstrading.optionstrading_impl import OptionsTradingImplementation

class OptionsTradingDomain(BaseDomainManager):
    """Domain manager for OptionsTrading operations"""
    
    def __init__(self):
        implementations = {
            'main_impl': OptionsTradingImplementation
        }
        
        # This will be injected by the registry
        services = {}  # Services injected during registration
        
        config = DomainConfig(
            name='OptionsTrading',
            implementations=implementations,
            services=services
        )
        
        super().__init__(config)
    
    def get_available_tools(self) -> list:
        """Return list of available tool names"""
        return [
            "get_options_chain",
            "calculate_greeks", 
            "analyze_option_strategy",
            "get_implied_volatility_surface",
            "create_options_strategy"
        ]
    
    # Implementation delegation methods
    
    async def delegate_async(self, method_name: str, **kwargs) -> Dict[str, Any]:
        """Async delegation method for options trading operations"""
        impl = self.get_implementation('main_impl')
        if hasattr(impl, method_name):
            method = getattr(impl, method_name)
            return await method(**kwargs)
        else:
            return {"success": False, "error": f"Method {method_name} not found"}
    
    async def get_options_chain(self, symbol: str, expiration_date: Optional[str] = None,
                               strike_range: Optional[tuple] = None) -> Dict[str, Any]:
        """Get options chain for a symbol"""
        impl = self.get_implementation('main_impl')
        return await impl.get_options_chain(symbol=symbol, expiration_date=expiration_date,
                                          strike_range=strike_range)
    
    async def calculate_greeks(self, symbol: str, option_type: str, strike: float,
                             expiration: str, risk_free_rate: float = 0.05) -> Dict[str, Any]:
        """Calculate option Greeks"""
        impl = self.get_implementation('main_impl')
        return await impl.calculate_greeks(symbol=symbol, option_type=option_type,
                                         strike=strike, expiration=expiration,
                                         risk_free_rate=risk_free_rate)
    
    async def analyze_option_strategy(self, strategy_name: str, legs: list,
                                    underlying_price: float) -> Dict[str, Any]:
        """Analyze an options strategy"""
        impl = self.get_implementation('main_impl')
        return await impl.analyze_option_strategy(strategy_name=strategy_name, legs=legs,
                                                 underlying_price=underlying_price)
    
    async def get_implied_volatility_surface(self, symbol: str) -> Dict[str, Any]:
        """Get implied volatility surface for a symbol"""
        impl = self.get_implementation('main_impl')
        return await impl.get_implied_volatility_surface(symbol=symbol)
    
    async def create_options_strategy(self, strategy_type: str, symbol: str,
                                    parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Create an options trading strategy"""
        impl = self.get_implementation('main_impl')
        return await impl.create_options_strategy(strategy_type=strategy_type, symbol=symbol,
                                                 parameters=parameters)
