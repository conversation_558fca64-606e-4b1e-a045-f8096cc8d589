"""
MarketDepth Domain Manager - Layer 2: Coordination
Coordinates between MCP interface and focused implementations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any
from ibkr_mcp.infrastructure.base_domain import BaseDomainManager, DomainConfig
from implementations.marketdepth.marketdepth_impl import MarketDepthImplementation

class MarketDepthDomain(BaseDomainManager):
    """Domain manager for MarketDepth operations"""
    
    def __init__(self):
        implementations = {
            'main_impl': MarketDepthImplementation
        }
        
        # This will be injected by the registry
        services = {}  # Services injected during registration
        
        config = DomainConfig(
            name='MarketDepth',
            implementations=implementations,
            services=services
        )
        
        super().__init__(config)
    
    def get_available_tools(self) -> list:
        """Return list of available tool names"""
        return [
            "get_market_depth",
            "stream_market_depth", 
            "cancel_market_depth",
            "get_order_book_analysis",
            "get_active_subscriptions"
        ]
    
    # Implementation delegation methods
    
    async def execute(self, method_name: str, **kwargs) -> Dict[str, Any]:
        """Generic execution method for backward compatibility"""
        impl = self.get_implementation('main_impl')
        if hasattr(impl, method_name):
            method = getattr(impl, method_name)
            return await method(**kwargs)
        else:
            return {"success": False, "error": f"Method {method_name} not found"}
    
    async def get_market_depth(self, symbol: str, exchange: str = "SMART", 
                              num_rows: int = 5) -> Dict[str, Any]:
        """Get current market depth (Level II) data"""
        impl = self.get_implementation('main_impl')
        return await impl.get_market_depth(symbol=symbol, exchange=exchange, num_rows=num_rows)
    
    async def stream_market_depth(self, symbol: str, exchange: str = "SMART",
                                 num_rows: int = 5, callback=None) -> Dict[str, Any]:
        """Start streaming market depth data"""
        impl = self.get_implementation('main_impl')
        return await impl.stream_market_depth(symbol=symbol, exchange=exchange, 
                                            num_rows=num_rows, callback=callback)
    
    async def cancel_market_depth(self, subscription_id: str) -> Dict[str, Any]:
        """Cancel market depth subscription"""
        impl = self.get_implementation('main_impl')
        return await impl.cancel_market_depth(subscription_id=subscription_id)
    
    async def get_order_book_analysis(self, symbol: str) -> Dict[str, Any]:
        """Analyze order book for insights"""
        impl = self.get_implementation('main_impl')
        return await impl.get_order_book_analysis(symbol=symbol)
    
    async def get_active_subscriptions(self) -> Dict[str, Any]:
        """Get all active market depth subscriptions"""
        impl = self.get_implementation('main_impl')
        return await impl.get_active_subscriptions()
