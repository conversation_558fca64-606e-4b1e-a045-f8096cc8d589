"""
HistoricalData Domain Manager - Layer 2: Coordination
Coordinates between MCP interface and focused implementations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional
import logging
from ibkr_mcp.infrastructure.base_domain import BaseDomainManager, DomainConfig
from implementations.historicaldata.historicaldata_impl import HistoricalDataImplementation

logger = logging.getLogger(__name__)

class HistoricalDataDomain(BaseDomainManager):
    """Domain manager for HistoricalData operations"""
    
    def __init__(self):
        implementations = {
            'main_impl': HistoricalDataImplementation
        }
        
        # CRITICAL FIX: Get services from registry or use emergency injection
        from ibkr_mcp.infrastructure.domain_registry import registry
        services = registry.services.copy() if hasattr(registry, 'services') and registry.services else {}
        
        # Emergency injection if no services
        if not services or 'ibkr_service' not in services:
            try:
                from services.ibkr_service import ibkr_service as global_service
                services['ibkr_service'] = global_service
                logger.warning(f"HistoricalData domain: Using emergency service injection")
            except Exception as e:
                logger.error(f"HistoricalData domain: Failed to inject service: {e}")
        
        config = DomainConfig(
            name='HistoricalData',
            implementations=implementations,
            services=services
        )
        
        super().__init__(config)
    
    def get_available_tools(self) -> list:
        """Return list of available tool names"""
        return [
            'get_historical_data',
            'get_historical_ticks', 
            'get_head_timestamp'
        ]
    
    # Implementation delegation methods with enhanced signatures
    
    async def get_historical_data(self, symbol: str, timeframe: str = "1d",
                                 end_date: Optional[str] = None,
                                 what_to_show: str = "TRADES",
                                 use_rth: bool = True,
                                 duration: Optional[str] = None) -> Dict[str, Any]:
        """Get historical price data for a symbol"""
        return await self.get_implementation('main_impl').get_historical_data(
            symbol=symbol,
            timeframe=timeframe,
            end_date=end_date,
            what_to_show=what_to_show,
            use_rth=use_rth,
            duration=duration
        )
    
    async def get_historical_ticks(self, symbol: str, start_date: str, end_date: str,
                                  number_of_ticks: int = 1000, what_to_show: str = "TRADES",
                                  use_rth: bool = True, ignore_size: bool = False) -> Dict[str, Any]:
        """Get historical tick data for a symbol"""
        return await self.get_implementation('main_impl').get_historical_ticks(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            number_of_ticks=number_of_ticks,
            what_to_show=what_to_show,
            use_rth=use_rth,
            ignore_size=ignore_size
        )
    
    async def get_head_timestamp(self, symbol: str, what_to_show: str = "TRADES",
                               use_rth: bool = True) -> Dict[str, Any]:
        """Get the earliest available data timestamp for a contract"""
        return await self.get_implementation('main_impl').get_head_timestamp(
            symbol=symbol,
            what_to_show=what_to_show,
            use_rth=use_rth
        )

