"""
Event processor for coordinating real-time event handling

This module provides the main event processing engine that coordinates
various event handlers and manages the event stream lifecycle.

Note: Currently using polling-based approach instead of Supabase real-time
subscriptions due to API limitations. This provides reliable event processing
while maintaining the same interface for future real-time implementation.
"""

from typing import Dict, Any, List, Optional, Callable
import asyncio
from datetime import datetime
from app.database.connection import supabase_conn
from .handlers import (
    MarketDataHandler,
    SignalHandler,
    OrderExecutionHandler,
    PositionUpdateHandler,
    RiskAlertHandler
)
import logging

logger = logging.getLogger(__name__)


class EventProcessor:
    """
    Main event processor that manages real-time subscriptions
    and routes events to appropriate handlers.
    """
    
    def __init__(self):
        """Initialize event processor with handlers"""
        self.db = supabase_conn
        self.channels = {}
        self.handlers = {
            'market_data': MarketDataHandler(),
            'signals': SignalHandler(),
            'orders': OrderExecutionHandler(),
            'positions': PositionUpdateHandler(),
            'risk_alerts': RiskAlertHandler()
        }
        self._running = False
        self._tasks = []
    
    async def start(self, config: Optional[Dict[str, Any]] = None):
        """
        Start the event processor with specified configuration.
        
        Args:
            config: Configuration dictionary with subscription settings
        """
        if self._running:
            logger.warning("Event processor already running")
            return
        
        self._running = True
        config = config or {}
        
        # Start handlers that need initialization
        if hasattr(self.handlers['market_data'], 'start'):
            await self.handlers['market_data'].start()
        
        # Set up subscriptions based on config
        if config.get('enable_market_data', True):
            await self._subscribe_market_data(config.get('instrument_ids', []))
        
        if config.get('enable_signals', True):
            await self._subscribe_signals(config.get('strategy_ids'))
        
        if config.get('enable_orders', True):
            await self._subscribe_orders()
        
        if config.get('enable_positions', True):
            await self._subscribe_positions()
        
        logger.info("Event processor started successfully")
    
    async def stop(self):
        """Stop the event processor and clean up subscriptions"""
        if not self._running:
            return
        
        self._running = False
        
        # Stop handlers
        for handler in self.handlers.values():
            if hasattr(handler, 'stop'):
                await handler.stop()
        
        # Stop all channels and tasks
        for channel_name, channel_info in self.channels.items():
            try:
                if isinstance(channel_info, dict) and "task" in channel_info:
                    # This is a polling task
                    channel_info["task"].cancel()
                    logger.info(f"Cancelled polling task for channel: {channel_name}")
                # Note: Real-time channels would be handled here when properly implemented
            except Exception as e:
                logger.error(f"Error stopping channel {channel_name}: {e}")
        
        self.channels.clear()
        
        # Cancel any running tasks
        for task in self._tasks:
            task.cancel()
        
        if self._tasks:
            await asyncio.gather(*self._tasks, return_exceptions=True)
            self._tasks.clear()
        
        logger.info("Event processor stopped")
    
    async def _subscribe_market_data(self, instrument_ids: List[str]):
        """Subscribe to market data updates using polling approach"""
        channel_name = "market_data_stream"
        
        # Instead of using realtime subscriptions which aren't working properly,
        # implement a polling-based approach for now
        async def poll_market_data():
            last_timestamp = datetime.utcnow()
            while self._running:
                try:
                    # Query for new market data since last check
                    query = self.db.client.table("market_data").select("*")
                    query = query.gte("created_at", last_timestamp.isoformat())
                    
                    if instrument_ids:
                        query = query.in_("instrument_id", instrument_ids)
                    
                    result = query.execute()
                    
                    if result.data:
                        for record in result.data:
                            await self.handlers['market_data'].handle({
                                'eventType': 'INSERT',
                                'new': record,
                                'old': None
                            })
                        last_timestamp = datetime.utcnow()
                    
                    await asyncio.sleep(0.1)  # Poll every 100ms
                    
                except Exception as e:
                    logger.error(f"Error polling market data: {e}")
                    await asyncio.sleep(1)  # Wait longer on error
        
        # Start polling task
        task = asyncio.create_task(poll_market_data())
        self._tasks.append(task)
        self.channels[channel_name] = {"task": task, "type": "polling"}
        logger.info(f"Started polling for market data stream for {len(instrument_ids)} instruments")
    
    async def _subscribe_signals(self, strategy_ids: Optional[List[str]] = None):
        """Subscribe to trading signal updates using polling approach"""
        channel_name = "trading_signals_stream"
        
        async def poll_signals():
            last_timestamp = datetime.utcnow()
            while self._running:
                try:
                    # Query for new signals since last check
                    query = self.db.client.table("trading_signals").select("*")
                    query = query.gte("created_at", last_timestamp.isoformat())
                    
                    if strategy_ids:
                        query = query.in_("strategy_id", strategy_ids)
                    
                    result = query.execute()
                    
                    if result.data:
                        for record in result.data:
                            await self.handlers['signals'].handle({
                                'eventType': 'INSERT',
                                'new': record,
                                'old': None
                            })
                        last_timestamp = datetime.utcnow()
                    
                    await asyncio.sleep(1)  # Poll every second for signals
                    
                except Exception as e:
                    logger.error(f"Error polling signals: {e}")
                    await asyncio.sleep(5)  # Wait longer on error
        
        # Start polling task
        task = asyncio.create_task(poll_signals())
        self._tasks.append(task)
        self.channels[channel_name] = {"task": task, "type": "polling"}
        logger.info("Started polling for trading signals stream")
    
    async def _subscribe_orders(self):
        """Subscribe to order updates using polling approach"""
        channel_name = "orders_stream"
        
        async def poll_orders():
            last_updated = datetime.utcnow()
            while self._running:
                try:
                    # Query for recently updated orders
                    query = self.db.client.table("orders").select("*")
                    query = query.gte("updated_at", last_updated.isoformat())
                    
                    result = query.execute()
                    
                    if result.data:
                        for record in result.data:
                            await self.handlers['orders'].handle({
                                'eventType': 'UPDATE',
                                'new': record,
                                'old': None
                            })
                        last_updated = datetime.utcnow()
                    
                    await asyncio.sleep(0.5)  # Poll every 500ms for orders
                    
                except Exception as e:
                    logger.error(f"Error polling orders: {e}")
                    await asyncio.sleep(2)  # Wait longer on error
        
        # Start polling task
        task = asyncio.create_task(poll_orders())
        self._tasks.append(task)
        self.channels[channel_name] = {"task": task, "type": "polling"}
        logger.info("Started polling for orders stream")
    
    async def _subscribe_positions(self):
        """Subscribe to position updates using polling approach"""
        channel_name = "positions_stream"
        
        async def poll_positions():
            last_updated = datetime.utcnow()
            while self._running:
                try:
                    # Query for recently updated open positions
                    query = self.db.client.table("positions").select("*")
                    query = query.eq("is_open", True)
                    query = query.gte("updated_at", last_updated.isoformat())
                    
                    result = query.execute()
                    
                    if result.data:
                        for record in result.data:
                            await self.handlers['positions'].handle({
                                'eventType': 'UPDATE',
                                'new': record,
                                'old': None
                            })
                        last_updated = datetime.utcnow()
                    
                    await asyncio.sleep(1)  # Poll every second for positions
                    
                except Exception as e:
                    logger.error(f"Error polling positions: {e}")
                    await asyncio.sleep(3)  # Wait longer on error
        
        # Start polling task
        task = asyncio.create_task(poll_positions())
        self._tasks.append(task)
        self.channels[channel_name] = {"task": task, "type": "polling"}
        logger.info("Started polling for positions stream")
    
    def register_callback(self, event_type: str, callback_type: str, callback: Callable):
        """
        Register a callback for specific event types.
        
        Args:
            event_type: Type of event ('market_data', 'signals', etc.)
            callback_type: Specific callback type for the handler
            callback: Async callable to execute
        """
        if event_type in self.handlers:
            handler = self.handlers[event_type]
            
            # Different handlers have different callback mechanisms
            if hasattr(handler, 'callbacks'):
                if callback_type not in handler.callbacks:
                    handler.callbacks[callback_type] = []
                handler.callbacks[callback_type].append(callback)
            elif hasattr(handler, f'{callback_type}_callback'):
                setattr(handler, f'{callback_type}_callback', callback)
            elif hasattr(handler, 'signal_processor'):
                handler.signal_processor = callback
            
            logger.info(f"Registered callback for {event_type}.{callback_type}")
        else:
            logger.error(f"Unknown event type: {event_type}")
    
    def get_handler(self, event_type: str):
        """Get specific event handler"""
        return self.handlers.get(event_type)
    
    async def emit_custom_event(self, table: str, event_type: str, data: Dict[str, Any]):
        """
        Emit a custom event through Supabase.
        
        Args:
            table: Table name to emit event for
            event_type: Type of event ('INSERT', 'UPDATE', 'DELETE')
            data: Event data
        """
        try:
            # This would typically be done through a stored procedure
            # that triggers the appropriate PostgreSQL notify
            await self.db.execute_rpc('emit_custom_event', {
                'table_name': table,
                'event_type': event_type,
                'event_data': data
            })
            
            logger.info(f"Emitted custom event: {table}.{event_type}")
            
        except Exception as e:
            logger.error(f"Error emitting custom event: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of event processor"""
        return {
            'running': self._running,
            'active_channels': list(self.channels.keys()),
            'handlers': {
                name: {
                    'type': type(handler).__name__,
                    'active': hasattr(handler, '_running') and handler._running
                }
                for name, handler in self.handlers.items()
            },
            'subscriptions': len(self.channels)
        }


class EventAggregator:
    """
    Aggregates events over time windows for batch processing
    and analytics.
    """
    
    def __init__(self, window_size: int = 60):  # seconds
        self.window_size = window_size
        self.buffers = {}
        self._aggregation_task = None
    
    async def start(self):
        """Start the aggregation process"""
        self._aggregation_task = asyncio.create_task(self._aggregate_loop())
    
    async def stop(self):
        """Stop the aggregation process"""
        if self._aggregation_task:
            self._aggregation_task.cancel()
            try:
                await self._aggregation_task
            except asyncio.CancelledError:
                pass
    
    async def add_event(self, event_type: str, event_data: Dict[str, Any]):
        """Add event to aggregation buffer"""
        if event_type not in self.buffers:
            self.buffers[event_type] = []
        
        self.buffers[event_type].append({
            'timestamp': datetime.utcnow(),
            'data': event_data
        })
    
    async def _aggregate_loop(self):
        """Main aggregation loop"""
        while True:
            try:
                await asyncio.sleep(self.window_size)
                await self._process_buffers()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in aggregation loop: {e}")
    
    async def _process_buffers(self):
        """Process and clear buffers"""
        cutoff_time = datetime.utcnow()
        
        for event_type, events in self.buffers.items():
            if events:
                # Process events
                aggregated = self._aggregate_events(event_type, events)
                
                # Clear old events
                self.buffers[event_type] = [
                    e for e in events 
                    if (cutoff_time - e['timestamp']).total_seconds() < self.window_size
                ]
                
                # Log aggregated results
                if aggregated:
                    logger.info(f"Aggregated {event_type}: {aggregated}")
    
    def _aggregate_events(self, event_type: str, events: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Aggregate events based on type"""
        if not events:
            return {}
        
        if event_type == 'market_data':
            # Aggregate market data into OHLCV
            prices = [e['data'].get('mid', 0) for e in events if e['data'].get('mid')]
            if prices:
                return {
                    'open': prices[0],
                    'high': max(prices),
                    'low': min(prices),
                    'close': prices[-1],
                    'count': len(prices)
                }
        
        elif event_type == 'orders':
            # Aggregate order statistics
            return {
                'total_orders': len(events),
                'buy_orders': sum(1 for e in events if e['data'].get('side') == 'BUY'),
                'sell_orders': sum(1 for e in events if e['data'].get('side') == 'SELL'),
                'filled': sum(1 for e in events if e['data'].get('status') == 'FILLED')
            }
        
        return {'count': len(events)}
