"""
Event handlers for real-time Supabase events

Implements handlers for various real-time events from Supabase
including market data updates, trading signals, and order executions.
"""

from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
from abc import ABC, abstractmethod
import logging
import asyncio

logger = logging.getLogger(__name__)


class BaseEventHandler(ABC):
    """Base class for all event handlers"""
    
    @abstractmethod
    async def handle(self, payload: Dict[str, Any]) -> None:
        """Handle incoming event"""
        pass
    
    def validate_payload(self, payload: Dict[str, Any], required_fields: List[str]) -> bool:
        """Validate that payload contains required fields"""
        for field in required_fields:
            if field not in payload:
                logger.error(f"Missing required field '{field}' in payload")
                return False
        return True


class MarketDataHandler(BaseEventHandler):
    """Handles real-time market data updates"""
    
    def __init__(self, callback_registry: Optional[Dict[str, List[Callable]]] = None):
        self.callbacks = callback_registry or {}
        self.buffer = []
        self.buffer_size = 100
        self.flush_interval = 0.1  # seconds
        self._flush_task = None
    
    async def start(self):
        """Start the handler with periodic buffer flushing"""
        self._flush_task = asyncio.create_task(self._periodic_flush())
    
    async def stop(self):
        """Stop the handler and flush remaining data"""
        if self._flush_task:
            self._flush_task.cancel()
            try:
                await self._flush_task
            except asyncio.CancelledError:
                pass
        await self._flush_buffer()
    
    async def handle(self, payload: Dict[str, Any]) -> None:
        """Handle incoming market data event"""
        try:
            # Validate payload
            if not self.validate_payload(payload, ['new']):
                return
            
            data = payload['new']
            
            # Add to buffer for batch processing
            self.buffer.append({
                'timestamp': data.get('timestamp', datetime.utcnow().isoformat()),
                'instrument_id': data.get('instrument_id'),
                'bid': float(data.get('bid', 0)),
                'ask': float(data.get('ask', 0)),
                'mid': float(data.get('mid', 0)),
                'spread': float(data.get('spread', 0)),
                'bid_volume': data.get('bid_volume'),
                'ask_volume': data.get('ask_volume')
            })
            
            # Flush if buffer is full
            if len(self.buffer) >= self.buffer_size:
                await self._flush_buffer()
            
            # Execute callbacks
            for callback in self.callbacks.get('market_data', []):
                await callback(data)
                
        except Exception as e:
            logger.error(f"Error handling market data event: {e}")
    
    async def _flush_buffer(self):
        """Flush buffered data to registered processors"""
        if not self.buffer:
            return
        
        try:
            # Process buffered data
            for callback in self.callbacks.get('batch_market_data', []):
                await callback(self.buffer.copy())
            
            # Clear buffer
            self.buffer.clear()
            
        except Exception as e:
            logger.error(f"Error flushing market data buffer: {e}")
    
    async def _periodic_flush(self):
        """Periodically flush the buffer"""
        while True:
            try:
                await asyncio.sleep(self.flush_interval)
                await self._flush_buffer()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic flush: {e}")


class SignalHandler(BaseEventHandler):
    """Handles trading signal events"""
    
    def __init__(self, signal_processor: Optional[Callable] = None):
        self.signal_processor = signal_processor
        self.active_signals = {}
    
    async def handle(self, payload: Dict[str, Any]) -> None:
        """Handle incoming trading signal event"""
        try:
            if not self.validate_payload(payload, ['new']):
                return
            
            signal = payload['new']
            
            # Extract signal details
            signal_data = {
                'id': signal.get('id'),
                'strategy_id': signal.get('strategy_id'),
                'pair_id': signal.get('pair_id'),
                'signal_type': signal.get('signal_type'),
                'signal_strength': float(signal.get('signal_strength', 0)),
                'target_price': float(signal.get('target_price', 0)) if signal.get('target_price') else None,
                'stop_loss': float(signal.get('stop_loss', 0)) if signal.get('stop_loss') else None,
                'take_profit': float(signal.get('take_profit', 0)) if signal.get('take_profit') else None,
                'expires_at': signal.get('expires_at'),
                'metadata': signal.get('metadata', {}),
                'created_at': signal.get('created_at', datetime.utcnow().isoformat())
            }
            
            # Store active signal
            self.active_signals[signal_data['id']] = signal_data
            
            # Process signal if processor is available
            if self.signal_processor:
                await self.signal_processor(signal_data)
            
            logger.info(f"Processed trading signal: {signal_data['id']} - {signal_data['signal_type']}")
            
        except Exception as e:
            logger.error(f"Error handling trading signal event: {e}")
    
    def get_active_signals(self) -> Dict[str, Dict[str, Any]]:
        """Get currently active signals"""
        # Filter out expired signals
        now = datetime.utcnow()
        active = {}
        
        for signal_id, signal in self.active_signals.items():
            if signal.get('expires_at'):
                expires = datetime.fromisoformat(signal['expires_at'].replace('Z', '+00:00'))
                if expires > now:
                    active[signal_id] = signal
            else:
                active[signal_id] = signal
        
        return active


class OrderExecutionHandler(BaseEventHandler):
    """Handles order execution events"""
    
    def __init__(self, execution_callback: Optional[Callable] = None):
        self.execution_callback = execution_callback
        self.execution_history = []
        self.max_history = 1000
    
    async def handle(self, payload: Dict[str, Any]) -> None:
        """Handle order execution event"""
        try:
            if not self.validate_payload(payload, ['new', 'old']):
                return
            
            new_state = payload['new']
            old_state = payload['old']
            
            # Check if this is a fill event
            if new_state.get('status') == 'FILLED' and old_state.get('status') != 'FILLED':
                execution_data = {
                    'order_id': new_state.get('id'),
                    'pair_id': new_state.get('pair_id'),
                    'side': new_state.get('side'),
                    'order_type': new_state.get('order_type'),
                    'filled_quantity': float(new_state.get('filled_quantity', 0)),
                    'average_fill_price': float(new_state.get('average_fill_price', 0)),
                    'commission': float(new_state.get('commission', 0)) if new_state.get('commission') else None,
                    'filled_at': new_state.get('filled_at', datetime.utcnow().isoformat()),
                    'metadata': new_state.get('metadata', {})
                }
                
                # Add to history
                self.execution_history.append(execution_data)
                if len(self.execution_history) > self.max_history:
                    self.execution_history.pop(0)
                
                # Execute callback
                if self.execution_callback:
                    await self.execution_callback(execution_data)
                
                logger.info(f"Order executed: {execution_data['order_id']} - "
                          f"{execution_data['side']} {execution_data['filled_quantity']} "
                          f"@ {execution_data['average_fill_price']}")
            
        except Exception as e:
            logger.error(f"Error handling order execution event: {e}")
    
    def get_recent_executions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent execution history"""
        return self.execution_history[-limit:]


class PositionUpdateHandler(BaseEventHandler):
    """Handles position update events"""
    
    def __init__(self, risk_manager: Optional[Callable] = None):
        self.risk_manager = risk_manager
        self.position_cache = {}
    
    async def handle(self, payload: Dict[str, Any]) -> None:
        """Handle position update event"""
        try:
            if not self.validate_payload(payload, ['new']):
                return
            
            position = payload['new']
            
            # Extract position data
            position_data = {
                'id': position.get('id'),
                'pair_id': position.get('pair_id'),
                'strategy_id': position.get('strategy_id'),
                'side': position.get('side'),
                'quantity': float(position.get('quantity', 0)),
                'entry_price': float(position.get('entry_price', 0)),
                'current_price': float(position.get('current_price', 0)),
                'unrealized_pnl': float(position.get('unrealized_pnl', 0)),
                'stop_loss': float(position.get('stop_loss', 0)) if position.get('stop_loss') else None,
                'take_profit': float(position.get('take_profit', 0)) if position.get('take_profit') else None,
                'is_open': position.get('is_open', True)
            }
            
            # Update cache
            self.position_cache[position_data['id']] = position_data
            
            # Check risk limits if risk manager is available
            if self.risk_manager:
                await self.risk_manager(position_data)
            
            # Log significant P&L changes
            if abs(position_data['unrealized_pnl']) > 1000:
                logger.warning(f"Significant P&L on position {position_data['id']}: "
                             f"${position_data['unrealized_pnl']:.2f}")
            
        except Exception as e:
            logger.error(f"Error handling position update event: {e}")
    
    def get_open_positions(self) -> Dict[str, Dict[str, Any]]:
        """Get all open positions from cache"""
        return {
            pos_id: pos for pos_id, pos in self.position_cache.items()
            if pos.get('is_open', True)
        }


class RiskAlertHandler(BaseEventHandler):
    """Handles risk alert events"""
    
    def __init__(self, alert_callback: Optional[Callable] = None):
        self.alert_callback = alert_callback
        self.alert_history = []
        self.alert_thresholds = {
            'position_size': 0.1,  # 10% of portfolio
            'daily_loss': 0.02,    # 2% daily loss
            'correlation_breakdown': 0.3  # 30% correlation change
        }
    
    async def handle(self, payload: Dict[str, Any]) -> None:
        """Handle risk alert event"""
        try:
            alert_data = payload.get('new', payload)
            
            alert = {
                'timestamp': datetime.utcnow().isoformat(),
                'type': alert_data.get('alert_type', 'unknown'),
                'severity': alert_data.get('severity', 'medium'),
                'message': alert_data.get('message', ''),
                'metadata': alert_data.get('metadata', {}),
                'action_required': alert_data.get('action_required', False)
            }
            
            # Add to history
            self.alert_history.append(alert)
            
            # Execute callback for high severity alerts
            if alert['severity'] == 'high' and self.alert_callback:
                await self.alert_callback(alert)
            
            logger.warning(f"Risk alert: {alert['type']} - {alert['message']}")
            
        except Exception as e:
            logger.error(f"Error handling risk alert event: {e}")
