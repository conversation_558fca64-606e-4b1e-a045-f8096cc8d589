"""
Supabase Connection Manager

Provides singleton connection management for Supabase integration,
handling both standard database operations and real-time subscriptions.
"""

import os
from typing import Optional, Dict, Any
from supabase import create_client, Client
from contextlib import asynccontextmanager
import logging
# from functools import lru_cache  # Not used in current implementation

logger = logging.getLogger(__name__)

class SupabaseConnection:
    """
    Singleton Supabase connection manager.
    
    This class ensures a single connection instance is used throughout
    the application, providing methods for database operations and
    real-time subscriptions.
    """
    
    _instance: Optional['SupabaseConnection'] = None
    _client: Optional[Client] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize connection manager (client created lazily)"""
        # Don't initialize client during import - use lazy initialization
        pass

    def _ensure_client(self):
        """Ensure Supabase client is initialized (lazy initialization)"""
        if self._client is None:
            url = os.getenv('SUPABASE_URL')
            key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')

            if not url or not key:
                raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set")

            try:
                self._client = create_client(url, key)
                logger.info("Supabase client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Supabase client: {e}")
                raise
    
    @property
    def client(self) -> Client:
        """Get Supabase client instance (lazy initialization)"""
        self._ensure_client()
        assert self._client is not None  # Should be initialized by _ensure_client
        return self._client

    @property
    def realtime(self):
        """Get realtime client for subscriptions"""
        return self.client.realtime
    
    @asynccontextmanager
    async def transaction(self):
        """
        Context manager for database transactions.
        
        Note: Supabase doesn't have native transaction support in the client.
        This is a placeholder for future RPC-based transaction implementation.
        """
        # TODO: Implement RPC-based transaction handling
        yield self.client
    
    def get_channel(self, name: str):
        """Create a realtime channel for subscriptions"""
        return self.realtime.channel(name)
    
    async def execute_rpc(self, function_name: str, params: Optional[Dict[str, Any]] = None):
        """Execute a stored procedure or edge function"""
        try:
            result = self.client.rpc(function_name, params or {}).execute()
            return result.data
        except Exception as e:
            logger.error(f"RPC execution failed for {function_name}: {e}")
            raise
    
    def table(self, table_name: str):
        """
        Get a table reference for database operations.

        Args:
            table_name: Name of the database table

        Returns:
            Supabase table reference
        """
        return self.client.table(table_name)

    def select(self, table_name: str, columns: str = "*"):
        """
        Create a select query for a table.

        Args:
            table_name: Name of the database table
            columns: Columns to select (default: "*")

        Returns:
            Supabase query builder
        """
        return self.table(table_name).select(columns)

    def insert(self, table_name: str, data: Dict[str, Any]):
        """
        Insert data into a table.

        Args:
            table_name: Name of the database table
            data: Data to insert

        Returns:
            Supabase query builder
        """
        return self.table(table_name).insert(data)

    def update(self, table_name: str, data: Dict[str, Any]):
        """
        Update data in a table.

        Args:
            table_name: Name of the database table
            data: Data to update

        Returns:
            Supabase query builder
        """
        return self.table(table_name).update(data)

    def delete(self, table_name: str):
        """
        Delete data from a table.

        Args:
            table_name: Name of the database table

        Returns:
            Supabase query builder
        """
        return self.table(table_name).delete()

    def health_check(self) -> bool:
        """Check if Supabase connection is healthy"""
        try:
            # Simple query to test connection
            self.table('instruments').select("id").limit(1).execute()
            return True
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False

# Singleton instance
supabase_conn = SupabaseConnection()
