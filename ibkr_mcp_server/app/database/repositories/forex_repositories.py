"""
Forex Trading Repositories

Specialized repositories for forex trading data access patterns.
"""

from typing import List, Optional, Dict, Any, Tu<PERSON>
from datetime import datetime, timedelta
from uuid import UUID
import numpy as np
from .base import BaseRepository
from app.database.connection import supabase_conn
import logging

logger = logging.getLogger(__name__)


class CurrencyPairRepository(BaseRepository):
    """Repository for managing currency pairs"""
    
    def __init__(self):
        super().__init__('currency_pairs')
    
    async def get_by_symbol(self, base: str, quote: str) -> Optional[Dict[str, Any]]:
        """Get currency pair by base and quote currencies"""
        try:
            result = self.db.table(self.table_name)\
                .select("*")\
                .eq("base_currency", base)\
                .eq("quote_currency", quote)\
                .execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Failed to get currency pair {base}/{quote}: {e}")
            raise
    
    async def get_active_pairs(self) -> List[Dict[str, Any]]:
        """Get all active currency pairs"""
        return await self.find_all(filters={"is_active": True})
    


class MarketDataRepository(BaseRepository):
    """Repository for high-frequency market data operations"""
    
    def __init__(self):
        super().__init__('market_data')
    
    async def insert_tick(self, pair_id: UUID, bid: float, ask: float, 
                         bid_volume: Optional[int] = None, 
                         ask_volume: Optional[int] = None) -> Dict[str, Any]:
        """Insert single tick data"""
        tick_data = {
            "pair_id": str(pair_id),
            "bid": bid,
            "ask": ask,
            "bid_volume": bid_volume,
            "ask_volume": ask_volume,
            "timestamp": datetime.utcnow().isoformat()
        }
        result = await self.create(tick_data)
        if result is None:
            raise ValueError(f"Failed to create tick data for pair: {pair_id}")
        return result
    
    async def bulk_insert_ticks(self, ticks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Bulk insert multiple ticks for performance"""
        # Ensure timestamps are properly formatted
        for tick in ticks:
            if 'timestamp' not in tick:
                tick['timestamp'] = datetime.utcnow().isoformat()
            tick['pair_id'] = str(tick['pair_id'])
        
        return await self.create_many(ticks)
    
    async def get_latest_prices(self, pair_ids: List[UUID]) -> Dict[UUID, Dict[str, Any]]:
        """Get latest prices for multiple pairs"""
        try:
            # Convert UUIDs to strings for query
            pair_id_strings = [str(pid) for pid in pair_ids]
            
            # Use raw SQL for better performance with window functions
            query = """
                SELECT DISTINCT ON (pair_id) 
                    pair_id, timestamp, bid, ask, mid, spread
                FROM market_data
                WHERE pair_id = ANY($1)
                ORDER BY pair_id, timestamp DESC
            """
            
            result = await supabase_conn.execute_rpc('get_latest_prices', {
                'pair_ids': pair_id_strings
            })
            
            # Convert to dict keyed by pair_id
            return {UUID(row['pair_id']): row for row in result} if result else {}
        except Exception as e:
            logger.error(f"Failed to get latest prices: {e}")
            raise
    
    async def get_price_history(self, pair_id: UUID, 
                               start_time: datetime, 
                               end_time: datetime,
                               interval: str = '1min') -> List[Dict[str, Any]]:
        """Get aggregated price history with OHLC data"""
        try:
            # Map interval to PostgreSQL interval format
            interval_map = {
                '1min': '1 minute',
                '5min': '5 minutes',
                '15min': '15 minutes',
                '1hour': '1 hour',
                '1day': '1 day'
            }
            
            pg_interval = interval_map.get(interval, '1 minute')
            
            # Call stored procedure for OHLC aggregation
            result = await supabase_conn.execute_rpc('get_ohlc_data', {
                'p_pair_id': str(pair_id),
                'p_start_time': start_time.isoformat(),
                'p_end_time': end_time.isoformat(),
                'p_interval': pg_interval
            })
            
            return result if result else []
        except Exception as e:
            logger.error(f"Failed to get price history: {e}")
            raise


class CorrelationMatrixRepository(BaseRepository):
    """Repository for correlation analysis data"""
    
    def __init__(self):
        super().__init__('correlation_matrix')
    
    async def save_correlation(self, pair1_id: UUID, pair2_id: UUID,
                             correlation: float, lookback_period: int,
                             method: str = 'pearson',
                             confidence_interval: Optional[float] = None,
                             p_value: Optional[float] = None) -> Dict[str, Any]:
        """Save correlation calculation result"""
        data = {
            "pair1_id": str(pair1_id),
            "pair2_id": str(pair2_id),
            "correlation_coefficient": correlation,
            "lookback_period": lookback_period,
            "calculation_method": method,
            "confidence_interval": confidence_interval,
            "p_value": p_value
        }
        result = await self.create(data)
        if result is None:
            raise ValueError(f"Failed to save correlation between pairs: {pair1_id} and {pair2_id}")
        return result
    
    async def get_latest_correlations(self, pair_ids: List[UUID], 
                                    lookback_period: int = 50) -> Dict[Tuple[UUID, UUID], float]:
        """Get latest correlations for a set of pairs"""
        try:
            pair_id_strings = [str(pid) for pid in pair_ids]
            
            result = self.db.table(self.table_name)\
                .select("*")\
                .in_("pair1_id", pair_id_strings)\
                .in_("pair2_id", pair_id_strings)\
                .eq("lookback_period", lookback_period)\
                .order("timestamp", desc=True)\
                .limit(len(pair_ids) * len(pair_ids))\
                .execute()
            
            # Convert to dict keyed by pair tuple
            correlations = {}
            for row in result.data:
                key = (UUID(row['pair1_id']), UUID(row['pair2_id']))
                correlations[key] = row['correlation_coefficient']
            
            return correlations
        except Exception as e:
            logger.error(f"Failed to get latest correlations: {e}")
            raise
    
    async def get_correlation_history(self, pair1_id: UUID, pair2_id: UUID,
                                    start_time: datetime,
                                    lookback_period: int = 50) -> List[Dict[str, Any]]:
        """Get correlation history between two pairs"""
        try:
            result = self.db.table(self.table_name)\
                .select("*")\
                .eq("pair1_id", str(pair1_id))\
                .eq("pair2_id", str(pair2_id))\
                .eq("lookback_period", lookback_period)\
                .gte("timestamp", start_time.isoformat())\
                .order("timestamp", desc=False)\
                .execute()
            
            return result.data if result.data else []
        except Exception as e:
            logger.error(f"Failed to get correlation history: {e}")
            raise


class TradingSignalRepository(BaseRepository):
    """Repository for trading signals"""
    
    def __init__(self):
        super().__init__('trading_signals')
    
    async def create_signal(self, strategy_id: UUID, pair_id: UUID,
                          signal_type: str, strength: float,
                          target_price: Optional[float] = None,
                          stop_loss: Optional[float] = None,
                          take_profit: Optional[float] = None,
                          expires_in_minutes: int = 60,
                          metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Create a new trading signal"""
        signal_data = {
            "strategy_id": str(strategy_id),
            "pair_id": str(pair_id),
            "signal_type": signal_type,
            "signal_strength": strength,
            "target_price": target_price,
            "stop_loss": stop_loss,
            "take_profit": take_profit,
            "expires_at": (datetime.utcnow() + timedelta(minutes=expires_in_minutes)).isoformat(),
            "metadata": metadata or {}
        }
        result = await self.create(signal_data)
        if result is None:
            raise ValueError(f"Failed to create trading signal for pair: {pair_id}")
        return result
    
    async def get_active_signals(self, strategy_id: Optional[UUID] = None) -> List[Dict[str, Any]]:
        """Get all active signals, optionally filtered by strategy"""
        filters: Dict[str, Any] = {"is_active": True}
        if strategy_id:
            filters["strategy_id"] = str(strategy_id)
        
        # Also check expiration
        result = self.db.table(self.table_name)\
            .select("*")\
            .eq("is_active", True)\
            .gt("expires_at", datetime.utcnow().isoformat())
        
        if strategy_id:
            result = result.eq("strategy_id", str(strategy_id))
        
        result = result.execute()
        return result.data if result.data else []
    
    async def deactivate_signal(self, signal_id: UUID) -> bool:
        """Deactivate a signal"""
        result = await self.update(signal_id, {"is_active": False})
        return result is not None


class OrderRepository(BaseRepository):
    """Repository for order management"""
    
    def __init__(self):
        super().__init__('orders')
    
    async def create_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new order"""
        # Ensure required fields
        required_fields = ['pair_id', 'order_type', 'side', 'quantity']
        for field in required_fields:
            if field not in order_data:
                raise ValueError(f"Missing required field: {field}")
        
        # Convert UUIDs to strings
        if 'pair_id' in order_data:
            order_data['pair_id'] = str(order_data['pair_id'])
        if 'signal_id' in order_data:
            order_data['signal_id'] = str(order_data['signal_id'])
        
        result = await self.create(order_data)
        if result is None:
            raise ValueError("Failed to create order")
        return result
    
    async def update_order_status(self, order_id: UUID, status: str,
                                filled_quantity: Optional[float] = None,
                                average_fill_price: Optional[float] = None,
                                commission: Optional[float] = None) -> Dict[str, Any]:
        """Update order status and fill information"""
        updates: Dict[str, Any] = {"status": status}
        
        if filled_quantity is not None:
            updates["filled_quantity"] = filled_quantity
        if average_fill_price is not None:
            updates["average_fill_price"] = average_fill_price
        if commission is not None:
            updates["commission"] = commission
        
        if status == "FILLED":
            updates["filled_at"] = datetime.utcnow().isoformat()
        elif status == "CANCELLED":
            updates["cancelled_at"] = datetime.utcnow().isoformat()
        
        result = await self.update(order_id, updates)
        if result is None:
            raise ValueError(f"Failed to update order status for order: {order_id}")
        return result
    
    async def get_pending_orders(self) -> List[Dict[str, Any]]:
        """Get all pending orders"""
        return await self.find_all(
            filters={"status": "PENDING"},
            order_by="created_at",
            order_desc=False
        )


class PositionRepository(BaseRepository):
    """Repository for position management"""
    
    def __init__(self):
        super().__init__('positions')
    
    async def open_position(self, position_data: Dict[str, Any]) -> Dict[str, Any]:
        """Open a new position"""
        # Ensure required fields
        required_fields = ['pair_id', 'side', 'quantity', 'entry_price']
        for field in required_fields:
            if field not in position_data:
                raise ValueError(f"Missing required field: {field}")
        
        # Convert UUIDs to strings
        position_data['pair_id'] = str(position_data['pair_id'])
        if 'strategy_id' in position_data:
            position_data['strategy_id'] = str(position_data['strategy_id'])
        
        position_data['is_open'] = True
        position_data['opened_at'] = datetime.utcnow().isoformat()
        
        result = await self.create(position_data)
        if result is None:
            raise ValueError("Failed to create position")
        return result
    
    async def update_position_price(self, position_id: UUID, current_price: float) -> Dict[str, Any]:
        """Update current price for P&L calculation"""
        result = await self.update(position_id, {"current_price": current_price})
        if result is None:
            raise ValueError(f"Failed to update position price for position: {position_id}")
        return result
    
    async def close_position(self, position_id: UUID, exit_price: float, 
                           realized_pnl: float) -> Dict[str, Any]:
        """Close a position"""
        updates = {
            "is_open": False,
            "closed_at": datetime.utcnow().isoformat(),
            "current_price": exit_price,
            "realized_pnl": realized_pnl
        }
        result = await self.update(position_id, updates)
        if result is None:
            raise ValueError(f"Failed to close position: {position_id}")
        return result
    
    async def get_open_positions(self, pair_id: Optional[UUID] = None,
                               strategy_id: Optional[UUID] = None) -> List[Dict[str, Any]]:
        """Get open positions with optional filters"""
        filters: Dict[str, Any] = {"is_open": True}
        
        if pair_id:
            filters["pair_id"] = str(pair_id)
        if strategy_id:
            filters["strategy_id"] = str(strategy_id)
        
        return await self.find_all(filters=filters, order_by="opened_at")
    
    async def calculate_total_exposure(self) -> Dict[str, float]:
        """Calculate total exposure by currency"""
        try:
            # This would be better as a stored procedure
            result = await supabase_conn.execute_rpc('calculate_total_exposure', {})
            return result if result else {}
        except Exception as e:
            logger.error(f"Failed to calculate total exposure: {e}")
            # Fallback to client-side calculation
            positions = await self.get_open_positions()
            exposure = {}
            for pos in positions:
                pair_id = pos['pair_id']
                side = pos['side']
                quantity = pos['quantity']
                # This would need currency pair info to properly calculate
                # For now, just aggregate by pair
                if pair_id not in exposure:
                    exposure[pair_id] = 0
                exposure[pair_id] += quantity if side == 'LONG' else -quantity
            return exposure


class PerformanceMetricsRepository(BaseRepository):
    """Repository for performance tracking"""
    
    def __init__(self):
        super().__init__('performance_metrics')
    
    async def update_daily_metrics(self, strategy_id: Optional[UUID], 
                                 metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Update or create daily performance metrics"""
        metric_date = metrics.get('metric_date', datetime.utcnow().date())
        
        # Check if metrics exist for this date
        filters: Dict[str, Any] = {"metric_date": metric_date}
        if strategy_id:
            filters["strategy_id"] = str(strategy_id)
        
        existing = await self.find_all(filters=filters, limit=1)
        
        if existing:
            # Update existing metrics
            result = await self.update(UUID(existing[0]['id']), metrics)
            if result is None:
                raise ValueError("Failed to update daily metrics")
            return result
        else:
            # Create new metrics
            metrics['metric_date'] = metric_date
            if strategy_id:
                metrics['strategy_id'] = str(strategy_id)
            result = await self.create(metrics)
            if result is None:
                raise ValueError("Failed to create daily metrics")
            return result
    
    async def get_performance_history(self, strategy_id: Optional[UUID],
                                    days: int = 30) -> List[Dict[str, Any]]:
        """Get performance history for the last N days"""
        start_date = datetime.utcnow().date() - timedelta(days=days)
        
        query = self.db.table(self.table_name)\
            .select("*")\
            .gte("metric_date", start_date.isoformat())
        
        if strategy_id:
            query = query.eq("strategy_id", str(strategy_id))
        
        query = query.order("metric_date", desc=False)
        result = query.execute()
        
        return result.data if result.data else []
