"""
Base Repository Pattern Implementation

Provides common CRUD operations for all repositories.
"""

from typing import Generic, TypeVar, List, Optional, Dict, Any, cast
from abc import ABC, abstractmethod
from uuid import UUID
from datetime import datetime
import logging
from app.database.connection import supabase_conn

T = TypeVar('T', bound=Dict[str, Any])

logger = logging.getLogger(__name__)

class BaseRepository(Generic[T], ABC):
    """
    Base repository with common CRUD operations.
    
    All repository implementations should inherit from this class
    to maintain consistency across the data access layer.
    """
    
    def __init__(self, table_name: str):
        """
        Initialize repository with table name.
        
        Args:
            table_name: Name of the Supabase table
        """
        self.table_name = table_name
        self.db = supabase_conn.client
        self.logger = logger
    
    async def create(self, entity: Dict[str, Any]) -> Optional[T]:
        """
        Create a new entity.
        
        Args:
            entity: Entity data to create
            
        Returns:
            Created entity or None if failed
        """
        try:
            result = self.db.table(self.table_name).insert(entity).execute()
            return cast(T, result.data[0]) if result.data else None
        except Exception as e:
            self.logger.error(f"Failed to create entity in {self.table_name}: {e}")
            raise
    
    async def create_many(self, entities: List[Dict[str, Any]]) -> List[T]:
        """
        Create multiple entities in a single operation.
        
        Args:
            entities: List of entity data to create
            
        Returns:
            List of created entities
        """
        try:
            result = self.db.table(self.table_name).insert(entities).execute()
            return cast(List[T], result.data) if result.data else []
        except Exception as e:
            self.logger.error(f"Failed to create entities in {self.table_name}: {e}")
            raise
    
    async def get_by_id(self, id: UUID) -> Optional[T]:
        """
        Get entity by ID.
        
        Args:
            id: Entity UUID
            
        Returns:
            Entity or None if not found
        """
        try:
            result = self.db.table(self.table_name).select("*").eq("id", str(id)).execute()
            return cast(T, result.data[0]) if result.data else None
        except Exception as e:
            self.logger.error(f"Failed to get entity {id} from {self.table_name}: {e}")
            raise
    
    async def update(self, id: UUID, updates: Dict[str, Any]) -> Optional[T]:
        """
        Update entity by ID.
        
        Args:
            id: Entity UUID
            updates: Fields to update
            
        Returns:
            Updated entity or None if not found
        """
        try:
            # Add updated_at timestamp if table has this field
            updates['updated_at'] = datetime.utcnow().isoformat()
            
            result = self.db.table(self.table_name).update(updates).eq("id", str(id)).execute()
            return cast(T, result.data[0]) if result.data else None
        except Exception as e:
            self.logger.error(f"Failed to update entity {id} in {self.table_name}: {e}")
            raise
    
    async def delete(self, id: UUID) -> bool:
        """
        Delete entity by ID.
        
        Args:
            id: Entity UUID
            
        Returns:
            True if deleted, False otherwise
        """
        try:
            result = self.db.table(self.table_name).delete().eq("id", str(id)).execute()
            return len(result.data) > 0 if result.data else False
        except Exception as e:
            self.logger.error(f"Failed to delete entity {id} from {self.table_name}: {e}")
            raise
    
    async def find_all(self, 
                      filters: Optional[Dict[str, Any]] = None, 
                      order_by: Optional[str] = None,
                      order_desc: bool = False,
                      limit: int = 100, 
                      offset: int = 0) -> List[T]:
        """
        Find all entities with optional filters.
        
        Args:
            filters: Dictionary of field:value filters
            order_by: Field to order by
            order_desc: Whether to order descending
            limit: Maximum number of results
            offset: Number of results to skip
            
        Returns:
            List of entities matching criteria
        """
        try:
            query = self.db.table(self.table_name).select("*")
            
            # Apply filters
            if filters:
                for key, value in filters.items():
                    if isinstance(value, list):
                        query = query.in_(key, value)
                    elif value is None:
                        query = query.is_(key, 'null')
                    else:
                        query = query.eq(key, value)
            
            # Apply ordering
            if order_by:
                query = query.order(order_by, desc=order_desc)
            
            # Apply pagination
            query = query.limit(limit).offset(offset)
            
            result = query.execute()
            return cast(List[T], result.data) if result.data else []
        except Exception as e:
            self.logger.error(f"Failed to find entities in {self.table_name}: {e}")
            raise
    
    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Count entities matching filters.
        
        Args:
            filters: Dictionary of field:value filters
            
        Returns:
            Count of matching entities
        """
        try:
            query = self.db.table(self.table_name).select("*")
            
            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)
            
            result = query.execute()
            return len(result.data) if result.data else 0
        except Exception as e:
            self.logger.error(f"Failed to count entities in {self.table_name}: {e}")
            raise
    
    async def exists(self, filters: Dict[str, Any]) -> bool:
        """
        Check if entity exists matching filters.
        
        Args:
            filters: Dictionary of field:value filters
            
        Returns:
            True if exists, False otherwise
        """
        count = await self.count(filters)
        return count > 0
