"""
Multi-Asset Repositories

Specialized repositories for multi-asset trading across stocks, options, futures, and crypto.
"""

from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date
from uuid import UUID
from .base import BaseRepository
from app.database.connection import supabase_conn
import logging

logger = logging.getLogger(__name__)


class InstrumentRepository(BaseRepository):
    """Repository for managing instruments across all asset classes"""
    
    def __init__(self):
        super().__init__('instruments')
    
    async def get_by_symbol_and_class(self, symbol: str, asset_class: str, 
                                      exchange: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get instrument by symbol and asset class"""
        filters = {
            "symbol": symbol,
            "asset_class": asset_class,
            "is_active": True
        }
        if exchange:
            filters["exchange"] = exchange
        
        results = await self.find_all(filters=filters, limit=1)
        return results[0] if results else None
    
    async def create_instrument(self, symbol: str, asset_class: str,
                              exchange: Optional[str] = None,
                              currency: str = "USD",
                              contract_details: Optional[Dict[str, Any]] = None,
                              description: Optional[str] = None) -> Dict[str, Any]:
        """Create new instrument entry"""
        instrument_data = {
            "symbol": symbol,
            "asset_class": asset_class,
            "exchange": exchange,
            "currency": currency,
            "contract_details": contract_details or {},
            "description": description,
            "is_active": True,
            "is_tradeable": True
        }
        result = await self.create(instrument_data)
        if result is None:
            raise ValueError(f"Failed to create instrument: {symbol}")
        return result
    
    async def get_instruments_by_class(self, asset_class: str, 
                                     is_tradeable: bool = True) -> List[Dict[str, Any]]:
        """Get all instruments of a specific asset class"""
        filters = {"asset_class": asset_class, "is_active": True}
        if is_tradeable is not None:
            filters["is_tradeable"] = is_tradeable
        
        return await self.find_all(
            filters=filters,
            order_by="symbol"
        )
    
    async def search_instruments(self, query: str, 
                               asset_classes: Optional[List[str]] = None,
                               limit: int = 50) -> List[Dict[str, Any]]:
        """Search instruments by symbol or description"""
        try:
            # Build query
            db_query = self.db.table(self.table_name).select("*")
            
            # Search in symbol and description
            db_query = db_query.or_(f"symbol.ilike.%{query}%,description.ilike.%{query}%")
            
            # Filter by asset classes if provided
            if asset_classes:
                db_query = db_query.in_("asset_class", asset_classes)
            
            # Active instruments only
            db_query = db_query.eq("is_active", True)
            
            # Order and limit
            db_query = db_query.order("symbol").limit(limit)
            
            result = db_query.execute()
            return result.data if result.data else []
            
        except Exception as e:
            logger.error(f"Failed to search instruments: {e}")
            raise


class StockDetailsRepository(BaseRepository):
    """Repository for stock-specific details"""
    
    def __init__(self):
        super().__init__('stock_details')
    
    async def upsert_stock_details(self, instrument_id: UUID, details: Dict[str, Any]) -> Dict[str, Any]:
        """Update or insert stock details"""
        details["instrument_id"] = str(instrument_id)
        details["updated_at"] = datetime.utcnow().isoformat()
        
        try:
            # Try update first
            existing = await self.get_by_id(instrument_id)
            if existing:
                result = await self.update(instrument_id, details)
                if result is None:
                    raise ValueError(f"Failed to update stock details for instrument: {instrument_id}")
                return result
            else:
                result = await self.create(details)
                if result is None:
                    raise ValueError(f"Failed to create stock details for instrument: {instrument_id}")
                return result
        except Exception as e:
            logger.error(f"Failed to upsert stock details: {e}")
            raise
    
    async def get_stocks_by_sector(self, sector: str) -> List[Dict[str, Any]]:
        """Get all stocks in a specific sector"""
        return await self.find_all(
            filters={"sector": sector},
            order_by="market_cap",
            order_desc=True
        )
    
    async def get_high_volume_stocks(self, min_market_cap: int = 1000000000) -> List[Dict[str, Any]]:
        """Get stocks with high market cap"""
        try:
            result = self.db.table(self.table_name)\
                .select("*, instruments!inner(*)")\
                .gte("market_cap", min_market_cap)\
                .order("market_cap", desc=True)\
                .execute()
            
            return result.data if result.data else []
        except Exception as e:
            logger.error(f"Failed to get high volume stocks: {e}")
            raise


class OptionDetailsRepository(BaseRepository):
    """Repository for option-specific details"""
    
    def __init__(self):
        super().__init__('option_details')
    
    async def create_option(self, instrument_id: UUID, underlying_id: UUID,
                          option_type: str, strike_price: float,
                          expiration_date: date,
                          exercise_style: str = "AMERICAN") -> Dict[str, Any]:
        """Create new option contract"""
        option_data = {
            "instrument_id": str(instrument_id),
            "underlying_id": str(underlying_id),
            "option_type": option_type,
            "strike_price": strike_price,
            "expiration_date": expiration_date.isoformat(),
            "exercise_style": exercise_style
        }
        result = await self.create(option_data)
        if result is None:
            raise ValueError(f"Failed to create option contract for instrument: {instrument_id}")
        return result
    
    async def update_greeks(self, instrument_id: UUID, greeks: Dict[str, float]) -> Dict[str, Any]:
        """Update option Greeks"""
        updates = {
            "delta": greeks.get("delta"),
            "gamma": greeks.get("gamma"),
            "theta": greeks.get("theta"),
            "vega": greeks.get("vega"),
            "rho": greeks.get("rho"),
            "implied_volatility": greeks.get("implied_volatility"),
            "updated_at": datetime.utcnow().isoformat()
        }
        result = await self.update(instrument_id, updates)
        if result is None:
            raise ValueError(f"Failed to update Greeks for instrument: {instrument_id}")
        return result
    
    async def get_option_chain(self, underlying_id: UUID,
                             expiration_date: Optional[date] = None,
                             option_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get option chain for underlying"""
        try:
            query = self.db.table(self.table_name)\
                .select("*, instruments!inner(*)")\
                .eq("underlying_id", str(underlying_id))
            
            if expiration_date:
                query = query.eq("expiration_date", expiration_date.isoformat())
            
            if option_type:
                query = query.eq("option_type", option_type)
            
            query = query.order("expiration_date").order("strike_price")
            
            result = query.execute()
            return result.data if result.data else []
            
        except Exception as e:
            logger.error(f"Failed to get option chain: {e}")
            raise
    
    async def get_options_by_expiration(self, expiration_date: date) -> List[Dict[str, Any]]:
        """Get all options expiring on a specific date"""
        return await self.find_all(
            filters={"expiration_date": expiration_date.isoformat()},
            order_by="strike_price"
        )


class FutureDetailsRepository(BaseRepository):
    """Repository for futures contract details"""
    
    def __init__(self):
        super().__init__('future_details')
    
    async def create_future(self, instrument_id: UUID,
                          underlying_asset: str,
                          contract_size: float,
                          contract_month: str,
                          last_trading_date: date,
                          tick_size: float,
                          tick_value: float,
                          **kwargs) -> Dict[str, Any]:
        """Create new futures contract"""
        future_data = {
            "instrument_id": str(instrument_id),
            "underlying_asset": underlying_asset,
            "contract_size": contract_size,
            "contract_month": contract_month,
            "last_trading_date": last_trading_date.isoformat(),
            "tick_size": tick_size,
            "tick_value": tick_value
        }
        
        # Add optional fields
        for key in ["first_notice_date", "delivery_date", "initial_margin", 
                   "maintenance_margin", "daily_limit"]:
            if key in kwargs and kwargs[key] is not None:
                if isinstance(kwargs[key], date):
                    future_data[key] = kwargs[key].isoformat()
                else:
                    future_data[key] = kwargs[key]
        
        result = await self.create(future_data)
        if result is None:
            raise ValueError(f"Failed to create future contract for instrument: {instrument_id}")
        return result
    
    async def get_active_contracts(self, underlying_asset: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get active futures contracts"""
        try:
            query = self.db.table(self.table_name)\
                .select("*, instruments!inner(*)")\
                .gt("last_trading_date", datetime.utcnow().date().isoformat())
            
            if underlying_asset:
                query = query.eq("underlying_asset", underlying_asset)
            
            query = query.order("contract_month")
            
            result = query.execute()
            return result.data if result.data else []
            
        except Exception as e:
            logger.error(f"Failed to get active contracts: {e}")
            raise
    
    async def get_front_month_contract(self, underlying_asset: str) -> Optional[Dict[str, Any]]:
        """Get the front month (nearest expiration) contract"""
        contracts = await self.get_active_contracts(underlying_asset)
        return contracts[0] if contracts else None


class CryptoDetailsRepository(BaseRepository):
    """Repository for cryptocurrency details"""
    
    def __init__(self):
        super().__init__('crypto_details')
    
    async def upsert_crypto_details(self, instrument_id: UUID, 
                                  details: Dict[str, Any]) -> Dict[str, Any]:
        """Update or insert crypto details"""
        details["instrument_id"] = str(instrument_id)
        details["updated_at"] = datetime.utcnow().isoformat()
        
        try:
            # Check if exists
            existing = await self.find_all(
                filters={"instrument_id": str(instrument_id)},
                limit=1
            )
            
            if existing:
                result = await self.update(UUID(existing[0]["id"]), details)
                if result is None:
                    raise ValueError(f"Failed to update crypto details for instrument: {instrument_id}")
                return result
            else:
                result = await self.create(details)
                if result is None:
                    raise ValueError(f"Failed to create crypto details for instrument: {instrument_id}")
                return result
                
        except Exception as e:
            logger.error(f"Failed to upsert crypto details: {e}")
            raise
    
    async def get_top_cryptos_by_market_cap(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get top cryptocurrencies by market cap"""
        try:
            result = self.db.table(self.table_name)\
                .select("*, instruments!inner(*)")\
                .order("market_cap", desc=True)\
                .limit(limit)\
                .execute()
            
            return result.data if result.data else []
            
        except Exception as e:
            logger.error(f"Failed to get top cryptos: {e}")
            raise
    
    async def get_crypto_by_blockchain(self, blockchain: str) -> List[Dict[str, Any]]:
        """Get all cryptocurrencies on a specific blockchain"""
        return await self.find_all(
            filters={"blockchain": blockchain},
            order_by="market_cap",
            order_desc=True
        )


class CrossAssetCorrelationRepository(BaseRepository):
    """Repository for cross-asset correlations"""
    
    def __init__(self):
        super().__init__('cross_asset_correlations')
    
    async def save_correlation(self, instrument1_id: UUID, instrument2_id: UUID,
                             correlation: float, lookback_period: int,
                             timeframe: str = "1day") -> Dict[str, Any]:
        """Save cross-asset correlation"""
        # Ensure consistent ordering (smaller UUID first)
        if str(instrument1_id) > str(instrument2_id):
            instrument1_id, instrument2_id = instrument2_id, instrument1_id
        
        data = {
            "instrument1_id": str(instrument1_id),
            "instrument2_id": str(instrument2_id),
            "correlation_coefficient": correlation,
            "lookback_period": lookback_period,
            "timeframe": timeframe
        }
        result = await self.create(data)
        if result is None:
            raise ValueError(f"Failed to save correlation between instruments: {instrument1_id} and {instrument2_id}")
        return result
    
    async def get_correlations_for_instrument(self, instrument_id: UUID,
                                            min_correlation: float = 0.5,
                                            lookback_period: int = 50,
                                            timeframe: str = "1day") -> List[Dict[str, Any]]:
        """Get all correlations for a specific instrument"""
        try:
            # Need to check both instrument1_id and instrument2_id
            result = self.db.table(self.table_name)\
                .select("*, instruments!instrument1_id(*), instruments!instrument2_id(*)")\
                .or_(f"instrument1_id.eq.{instrument_id},instrument2_id.eq.{instrument_id}")\
                .gte("correlation_coefficient", min_correlation)\
                .eq("lookback_period", lookback_period)\
                .eq("timeframe", timeframe)\
                .order("correlation_coefficient", desc=True)\
                .execute()
            
            return result.data if result.data else []
            
        except Exception as e:
            logger.error(f"Failed to get correlations: {e}")
            raise
    
    async def get_correlation_matrix(self, instrument_ids: List[UUID],
                                   lookback_period: int = 50,
                                   timeframe: str = "1day") -> Dict[Tuple[UUID, UUID], float]:
        """Get correlation matrix for multiple instruments"""
        try:
            # Convert UUIDs to strings
            id_strings = [str(id) for id in instrument_ids]
            
            result = self.db.table(self.table_name)\
                .select("*")\
                .in_("instrument1_id", id_strings)\
                .in_("instrument2_id", id_strings)\
                .eq("lookback_period", lookback_period)\
                .eq("timeframe", timeframe)\
                .execute()
            
            # Build correlation matrix
            matrix = {}
            for row in result.data:
                key = (UUID(row['instrument1_id']), UUID(row['instrument2_id']))
                matrix[key] = row['correlation_coefficient']
                # Add reverse mapping for symmetry
                reverse_key = (UUID(row['instrument2_id']), UUID(row['instrument1_id']))
                matrix[reverse_key] = row['correlation_coefficient']
            
            # Add self-correlations
            for inst_id in instrument_ids:
                matrix[(inst_id, inst_id)] = 1.0
            
            return matrix
            
        except Exception as e:
            logger.error(f"Failed to get correlation matrix: {e}")
            raise


class AssetAllocationRepository(BaseRepository):
    """Repository for asset allocation management"""
    
    def __init__(self):
        super().__init__('asset_allocations')
    
    async def set_target_allocation(self, strategy_id: UUID, 
                                  allocations: Dict[str, float]) -> List[Dict[str, Any]]:
        """Set target allocations for a strategy"""
        # Validate allocations sum to 1.0 (or close)
        total = sum(allocations.values())
        if not 0.99 <= total <= 1.01:
            raise ValueError(f"Allocations must sum to 1.0, got {total}")
        
        results = []
        for asset_class, target in allocations.items():
            # Check if allocation exists
            existing = await self.find_all(
                filters={
                    "strategy_id": str(strategy_id),
                    "asset_class": asset_class
                },
                limit=1
            )
            
            if existing:
                # Update existing
                result = await self.update(
                    UUID(existing[0]['id']),
                    {"target_allocation": target}
                )
                if result is None:
                    raise ValueError(f"Failed to update allocation for asset class: {asset_class}")
            else:
                # Create new
                result = await self.create({
                    "strategy_id": str(strategy_id),
                    "asset_class": asset_class,
                    "target_allocation": target
                })
                if result is None:
                    raise ValueError(f"Failed to create allocation for asset class: {asset_class}")
            
            results.append(result)
        
        return results
    
    async def get_allocations(self, strategy_id: UUID) -> Dict[str, Dict[str, float]]:
        """Get current and target allocations for a strategy"""
        allocations = await self.find_all(
            filters={"strategy_id": str(strategy_id)}
        )
        
        result = {}
        for alloc in allocations:
            result[alloc['asset_class']] = {
                'target': alloc['target_allocation'],
                'current': alloc.get('current_allocation', 0),
                'min': alloc.get('min_allocation', 0),
                'max': alloc.get('max_allocation', 1),
                'rebalance_threshold': alloc.get('rebalance_threshold', 0.05)
            }
        
        return result
    
    async def update_current_allocations(self, strategy_id: UUID,
                                       current_values: Dict[str, float]) -> None:
        """Update current allocation percentages"""
        total_value = sum(current_values.values())
        if total_value == 0:
            return
        
        # Calculate percentages
        for asset_class, value in current_values.items():
            percentage = value / total_value
            
            # Find and update
            existing = await self.find_all(
                filters={
                    "strategy_id": str(strategy_id),
                    "asset_class": asset_class
                },
                limit=1
            )
            
            if existing:
                await self.update(
                    UUID(existing[0]['id']),
                    {"current_allocation": percentage}
                )
    
    async def get_rebalance_candidates(self, strategy_id: UUID) -> List[Dict[str, Any]]:
        """Get asset classes that need rebalancing"""
        allocations = await self.find_all(
            filters={"strategy_id": str(strategy_id)}
        )
        
        candidates = []
        for alloc in allocations:
            if alloc.get('current_allocation') is None:
                continue
            
            deviation = abs(alloc['current_allocation'] - alloc['target_allocation'])
            if deviation > alloc.get('rebalance_threshold', 0.05):
                candidates.append({
                    'asset_class': alloc['asset_class'],
                    'target': alloc['target_allocation'],
                    'current': alloc['current_allocation'],
                    'deviation': deviation,
                    'action': 'BUY' if alloc['current_allocation'] < alloc['target_allocation'] else 'SELL'
                })
        
        return sorted(candidates, key=lambda x: x['deviation'], reverse=True)
