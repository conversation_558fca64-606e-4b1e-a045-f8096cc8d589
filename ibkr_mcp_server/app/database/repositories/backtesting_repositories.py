"""
Backtesting Database Repositories

Provides data access layer for backtesting results storage and retrieval.
"""

from typing import Dict, List, Optional, Any
from datetime import date
from uuid import UUID
import logging

from .base import BaseRepository
from app.database.connection import supabase_conn

logger = logging.getLogger(__name__)


class BacktestRunRepository(BaseRepository):
    """Repository for backtest run summary data"""
    
    def __init__(self):
        super().__init__("backtest_runs")
    
    async def create_run(self, run_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new backtest run record"""
        try:
            result = supabase_conn.table(self.table_name).insert(run_data).execute()
            if result.data:
                logger.info(f"Created backtest run: {result.data[0]['id']}")
                return result.data[0]
            else:
                raise Exception("Failed to create backtest run")
        except Exception as e:
            logger.error(f"Error creating backtest run: {e}")
            raise

    async def get_all_runs(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """Get all backtest runs with pagination"""
        try:
            result = supabase_conn.table(self.table_name)\
                .select("*")\
                .order("created_at", desc=True)\
                .range(offset, offset + limit - 1)\
                .execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error fetching backtest runs: {e}")
            raise

    async def get_run_by_id(self, run_id: UUID) -> Optional[Dict[str, Any]]:
        """Get a specific backtest run by ID"""
        try:
            result = supabase_conn.table(self.table_name)\
                .select("*")\
                .eq("id", str(run_id))\
                .single()\
                .execute()
            return result.data
        except Exception as e:
            logger.error(f"Error fetching backtest run {run_id}: {e}")
            return None

    async def get_runs_by_strategy(self, strategy_name: str) -> List[Dict[str, Any]]:
        """Get all runs for a specific strategy"""
        try:
            result = supabase_conn.table(self.table_name)\
                .select("*")\
                .eq("strategy_name", strategy_name)\
                .order("created_at", desc=True)\
                .execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error fetching runs for strategy {strategy_name}: {e}")
            raise


class BacktestTradeRepository(BaseRepository):
    """Repository for individual backtest trade data"""
    
    def __init__(self):
        super().__init__("backtest_trades")
    
    async def create_trades(self, trades_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create multiple trade records"""
        try:
            result = supabase_conn.table(self.table_name).insert(trades_data).execute()
            if result.data:
                logger.info(f"Created {len(result.data)} trade records")
                return result.data
            else:
                raise Exception("Failed to create trade records")
        except Exception as e:
            logger.error(f"Error creating trade records: {e}")
            raise

    async def get_trades_by_run_id(self, run_id: UUID) -> List[Dict[str, Any]]:
        """Get all trades for a specific backtest run"""
        try:
            result = supabase_conn.table(self.table_name)\
                .select("*")\
                .eq("backtest_run_id", str(run_id))\
                .order("entry_date")\
                .execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error fetching trades for run {run_id}: {e}")
            raise

    async def get_trades_by_symbol(self, run_id: UUID, symbol: str) -> List[Dict[str, Any]]:
        """Get trades for a specific symbol in a backtest run"""
        try:
            result = supabase_conn.table(self.table_name)\
                .select("*")\
                .eq("backtest_run_id", str(run_id))\
                .eq("symbol", symbol)\
                .order("entry_date")\
                .execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error fetching trades for {symbol} in run {run_id}: {e}")
            raise


class BacktestEquityCurveRepository(BaseRepository):
    """Repository for backtest equity curve time series data"""
    
    def __init__(self):
        super().__init__("backtest_equity_curves")
    
    async def create_equity_points(self, equity_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create multiple equity curve data points"""
        try:
            result = supabase_conn.table(self.table_name).insert(equity_data).execute()
            if result.data:
                logger.info(f"Created {len(result.data)} equity curve points")
                return result.data
            else:
                raise Exception("Failed to create equity curve points")
        except Exception as e:
            logger.error(f"Error creating equity curve points: {e}")
            raise

    async def get_equity_curve_by_run_id(self, run_id: UUID) -> List[Dict[str, Any]]:
        """Get equity curve data for a specific backtest run"""
        try:
            result = supabase_conn.table(self.table_name)\
                .select("*")\
                .eq("backtest_run_id", str(run_id))\
                .order("date")\
                .execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error fetching equity curve for run {run_id}: {e}")
            raise

    async def get_equity_curve_range(self, run_id: UUID, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """Get equity curve data for a specific date range"""
        try:
            result = supabase_conn.table(self.table_name)\
                .select("*")\
                .eq("backtest_run_id", str(run_id))\
                .gte("date", start_date.isoformat())\
                .lte("date", end_date.isoformat())\
                .order("date")\
                .execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error fetching equity curve range for run {run_id}: {e}")
            raise
