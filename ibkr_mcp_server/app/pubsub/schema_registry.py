"""
Message Schema Registry for Google Cloud Pub/Sub

This module implements a schema registry supporting both JSON Schema and Protocol Buffers
for message validation, versioning, and evolution.

Features:
- JSON Schema and Protobuf support
- Schema versioning and evolution
- Backward/forward compatibility checking
- Schema caching and lazy loading
- Integration with Google Cloud Schema Registry

Author: IBKR MCP Server Team
Version: 2.0.0
"""

# App-specific imports
from ibkr_mcp_server.app.core.config import Config

# Standard library imports
import json
import hashlib
import logging
import threading
from typing import Dict, List, Optional, Any, Union, Type
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path

# Import jsonschema with proper fallback handling
has_jsonschema = False
jsonschema = None

try:
    import jsonschema as _jsonschema  # type: ignore[import]
    has_jsonschema = True
    jsonschema = _jsonschema
    
    # Ensure exceptions namespace is available for older versions
    if not hasattr(jsonschema, 'exceptions'):
        # Create a minimal exceptions namespace
        class JsonSchemaValidationError(Exception):
            def __init__(self, message):
                self.message = message
                super().__init__(message)
        
        # Create namespace with ValidationError
        class JsonSchemaExceptionsNamespace:
            ValidationError = JsonSchemaValidationError
        
        # Try to attach it to the module, but don't fail if we can't
        try:
            setattr(jsonschema, 'exceptions', JsonSchemaExceptionsNamespace())
        except (AttributeError, TypeError):
            # If we can't modify the module, we'll handle it differently
            pass
        
except ImportError:
    # Define minimal jsonschema implementation for fallback
    class FallbackValidationError(Exception):
        def __init__(self, message):
            self.message = message
            super().__init__(message)
    
    class FallbackExceptionsNamespace:
        ValidationError = FallbackValidationError
    
    class FallbackDraft7Validator:
        def __init__(self, schema):
            self.schema = schema
            
        def validate(self, data):
            # Simple validation - in production, install jsonschema package
            pass
            
        def iter_errors(self, data):
            return []
    
    class JsonSchemaFallback:
        def __init__(self):
            self.exceptions = FallbackExceptionsNamespace()
            self.Draft7Validator = FallbackDraft7Validator
    
    # Use the fallback implementation
    jsonschema = JsonSchemaFallback()  # type: ignore[assignment]
# Google Cloud imports
from google.cloud import pubsub_v1
from google.api_core import exceptions as gcp_exceptions
from google.protobuf import descriptor_pb2, message
from google.protobuf.json_format import MessageToJson, Parse

logger = logging.getLogger(__name__)


class SchemaType(Enum):
    """Supported schema types"""
    JSON_SCHEMA = "JSON_SCHEMA"
    PROTOBUF = "PROTOBUF"
    AVRO = "AVRO"  # Future support


class CompatibilityMode(Enum):
    """Schema compatibility modes"""
    BACKWARD = "BACKWARD"  # New schema can read old data
    FORWARD = "FORWARD"   # Old schema can read new data
    FULL = "FULL"        # Both backward and forward compatible
    NONE = "NONE"        # No compatibility checks


@dataclass
class ValidationResult:
    """Result of schema validation"""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


@dataclass
class SchemaMetadata:
    """Metadata for a schema"""
    schema_id: str
    name: str
    type: SchemaType
    version: int
    created_at: datetime
    updated_at: datetime
    compatibility_mode: CompatibilityMode = CompatibilityMode.BACKWARD
    tags: Dict[str, str] = field(default_factory=dict)
    description: str = ""


class MessageSchema:
    """Base class for message schemas"""
    
    def __init__(
        self,
        schema_id: str,
        name: str,
        schema_type: SchemaType,
        version: int = 1
    ):
        self.schema_id = schema_id
        self.name = name
        self.schema_type = schema_type
        self.version = version
        # Add empty attributes for type checking
        # These will be overridden in subclasses
        self.json_schema = None
        self.proto_descriptor = None
        self.metadata = SchemaMetadata(
            schema_id=schema_id,
            name=name,
            type=schema_type,
            version=version,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    def validate(self, data: Any) -> ValidationResult:
        """Validate data against schema"""
        raise NotImplementedError("Subclasses must implement validate")
    
    def serialize(self, data: Any) -> bytes:
        """Serialize data according to schema"""
        raise NotImplementedError("Subclasses must implement serialize")
    
    def deserialize(self, data: bytes) -> Any:
        """Deserialize data according to schema"""
        raise NotImplementedError("Subclasses must implement deserialize")
    
    def check_compatibility(self, other: 'MessageSchema') -> ValidationResult:
        """Check compatibility with another schema"""
        raise NotImplementedError


class JsonMessageSchema(MessageSchema):
    """JSON Schema implementation"""
    
    def __init__(
        self,
        schema_id: str,
        name: str,
        json_schema: Dict[str, Any],
        version: int = 1
    ):
        super().__init__(schema_id, name, SchemaType.JSON_SCHEMA, version)
        self.json_schema = json_schema
        
        # Initialize validator with proper error handling
        if has_jsonschema and jsonschema is not None:
            self._validator = jsonschema.Draft7Validator(json_schema)  # type: ignore[attr-defined]
        else:
            # Use fallback validator
            self._validator = None
    
    def validate(self, data: Any) -> ValidationResult:
        """Validate data against JSON schema"""
        try:
            # Check if we have real jsonschema and a validator
            if has_jsonschema and self._validator is not None:
                try:
                    self._validator.validate(data)
                    return ValidationResult(is_valid=True)
                except Exception as e:
                    errors = []
                    # Handle validation errors safely
                    error_msg = str(e)
                    errors.append(f"Validation error: {error_msg}")
                    
                    # Collect all validation errors if possible
                    try:
                        if hasattr(self._validator, 'iter_errors'):
                            for error in self._validator.iter_errors(data):
                                error_path = " -> ".join(str(p) for p in error.absolute_path)
                                if hasattr(error, 'message'):
                                    errors.append(f"{error_path}: {error.message}")
                                else:
                                    errors.append(f"{error_path}: {str(error)}")
                    except Exception:
                        pass  # Ignore errors in error collection
                    
                    return ValidationResult(is_valid=False, errors=errors)
            else:
                # With fallback, we can't really validate
                logger.warning("JSON schema validation skipped - jsonschema package not available")
                return ValidationResult(is_valid=True, warnings=["JSON schema validation skipped"])
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"Unexpected validation error: {e}"]
            )
    
    def serialize(self, data: Any) -> bytes:
        """Serialize to JSON"""
        validation_result = self.validate(data)
        if not validation_result.is_valid:
            raise ValueError(f"Data validation failed: {validation_result.errors}")
        
        return json.dumps(data).encode('utf-8')
    
    def deserialize(self, data: bytes) -> Any:
        """Deserialize from JSON"""
        try:
            parsed = json.loads(data.decode('utf-8'))
            validation_result = self.validate(parsed)
            if not validation_result.is_valid:
                raise ValueError(f"Deserialized data validation failed: {validation_result.errors}")
            return parsed
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON: {str(e)}")
    
    def check_compatibility(self, other: 'MessageSchema') -> ValidationResult:
        """Check JSON schema compatibility"""
        if not isinstance(other, JsonMessageSchema):
            return ValidationResult(
                is_valid=False,
                errors=["Cannot check compatibility between different schema types"]
            )
        
        # For JSON schemas, we'll do basic structural comparison
        # In production, use a proper JSON Schema compatibility library
        warnings = []
        errors = []
        
        # Check required fields
        old_required = set(self.json_schema.get('required', []))
        new_required = set(other.json_schema.get('required', []))
        
        # Backward compatibility: new schema shouldn't require fields that old didn't
        new_requirements = new_required - old_required
        if new_requirements:
            errors.append(
                f"Backward incompatible: new schema requires additional fields: {new_requirements}"
            )
        
        # Forward compatibility: old schema's required fields should exist in new
        removed_requirements = old_required - new_required
        if removed_requirements:
            warnings.append(
                f"Forward compatibility warning: fields no longer required: {removed_requirements}"
            )
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )


class ProtobufMessageSchema(MessageSchema):
    """Protocol Buffer schema implementation"""
    
    def __init__(
        self,
        schema_id: str,
        name: str,
        proto_descriptor: descriptor_pb2.FileDescriptorProto,
        message_type: Type[message.Message],
        version: int = 1
    ):
        super().__init__(schema_id, name, SchemaType.PROTOBUF, version)
        self.proto_descriptor = proto_descriptor
        self.message_type = message_type
    
    def validate(self, data: Union[Dict[str, Any], message.Message]) -> ValidationResult:
        """Validate protobuf data"""
        try:
            if isinstance(data, dict):
                # Try to parse JSON representation
                msg = Parse(json.dumps(data), self.message_type())
            elif isinstance(data, self.message_type):
                # Already a protobuf message
                msg = data
            else:
                return ValidationResult(
                    is_valid=False,
                    errors=[f"Invalid data type: expected dict or {self.message_type.__name__}"]
                )
            
            # Check required fields
            errors = []
            for field, value in msg.ListFields():
                # Check if field is required - has_presence is not available in all protobuf versions
                # Use a more compatible approach
                if field.label == field.LABEL_REQUIRED and not value:
                    errors.append(f"Required field '{field.name}' is not set")
            
            return ValidationResult(is_valid=len(errors) == 0, errors=errors)
            
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"Protobuf validation error: {str(e)}"]
            )
    
    def serialize(self, data: Union[Dict[str, Any], message.Message]) -> bytes:
        """Serialize to protobuf binary format"""
        if isinstance(data, dict):
            msg = Parse(json.dumps(data), self.message_type())
        elif isinstance(data, self.message_type):
            msg = data
        else:
            raise ValueError(f"Invalid data type for protobuf serialization")
        
        return msg.SerializeToString()
    
    def deserialize(self, data: bytes) -> message.Message:
        """Deserialize from protobuf binary format"""
        msg = self.message_type()
        msg.ParseFromString(data)
        return msg
    
    def check_compatibility(self, other: 'MessageSchema') -> ValidationResult:
        """Check protobuf compatibility"""
        if not isinstance(other, ProtobufMessageSchema):
            return ValidationResult(
                is_valid=False,
                errors=["Cannot check compatibility between different schema types"]
            )
        
        # Protobuf has built-in backward/forward compatibility
        # New fields with default values are backward compatible
        # Removed fields are forward compatible if they weren't required
        
        # This is a simplified check - in production, use protobuf reflection
        return ValidationResult(
            is_valid=True,
            warnings=["Protobuf compatibility check simplified - verify manually"]
        )


class SchemaRegistry:
    """
    Central registry for message schemas with caching and versioning.
    Integrates with Google Cloud Pub/Sub Schema Registry when available.
    """
    
    def __init__(
        self,
        project_id: Optional[str] = None,
        cache_ttl: int = 3600,
        enable_cloud_registry: bool = False  # Default to False for safety
    ):
        """
        Initialize schema registry.
        
        Args:
            project_id: GCP project ID for cloud schema registry
            cache_ttl: Cache TTL in seconds
            enable_cloud_registry: Enable Google Cloud Schema Registry integration
        """
        self.project_id = project_id
        self.cache_ttl = cache_ttl
        self.enable_cloud_registry = enable_cloud_registry and project_id is not None
        
        # Local schema cache
        self._schemas: Dict[str, MessageSchema] = {}
        self._schema_versions: Dict[str, Dict[int, MessageSchema]] = {}
        self._cache_lock = threading.RLock()
        
        # Cloud schema client
        self._schema_client = None
        if self.enable_cloud_registry and project_id:
            try:
                # Initialize the schema client with proper client library
                try:
                    from google.cloud.pubsub_v1 import SchemaServiceClient
                    self._schema_client = SchemaServiceClient()
                    self._project_path = f"projects/{project_id}"
                    logger.info(f"Connected to Google Cloud Schema Registry for project: {project_id}")
                except (ImportError, AttributeError) as e:
                    logger.warning(f"Could not import SchemaServiceClient: {e}")
                    self.enable_cloud_registry = False
            except Exception as e:
                logger.warning(f"Failed to connect to Cloud Schema Registry: {e}")
                self.enable_cloud_registry = False
        
        # Load built-in schemas
        self._load_builtin_schemas()
    
    def _load_builtin_schemas(self):
        """Load built-in IBKR schemas"""
        # Market data schema
        market_data_schema = JsonMessageSchema(
            schema_id="ibkr.market_data.v1",
            name="MarketData",
            json_schema={
                "$schema": "http://json-schema.org/draft-07/schema#",
                "type": "object",
                "properties": {
                    "symbol": {"type": "string"},
                    "timestamp": {"type": "integer"},
                    "bid": {"type": "number"},
                    "ask": {"type": "number"},
                    "last": {"type": "number"},
                    "volume": {"type": "integer"},
                    "high": {"type": "number"},
                    "low": {"type": "number"},
                    "open": {"type": "number"},
                    "close": {"type": "number"}
                },
                "required": ["symbol", "timestamp"]
            }
        )
        
        # Order schema
        order_schema = JsonMessageSchema(
            schema_id="ibkr.order.v1",
            name="Order",
            json_schema={
                "$schema": "http://json-schema.org/draft-07/schema#",
                "type": "object",
                "properties": {
                    "orderId": {"type": "string"},
                    "symbol": {"type": "string"},
                    "action": {"type": "string", "enum": ["BUY", "SELL"]},
                    "quantity": {"type": "number"},
                    "orderType": {"type": "string", "enum": ["MKT", "LMT", "STP", "STP_LMT"]},
                    "price": {"type": "number"},
                    "timeInForce": {"type": "string", "enum": ["DAY", "GTC", "IOC", "FOK"]},
                    "status": {"type": "string"},
                    "timestamp": {"type": "integer"}
                },
                "required": ["orderId", "symbol", "action", "quantity", "orderType"]
            }
        )
        
        # Position schema
        position_schema = JsonMessageSchema(
            schema_id="ibkr.position.v1",
            name="Position",
            json_schema={
                "$schema": "http://json-schema.org/draft-07/schema#",
                "type": "object",
                "properties": {
                    "account": {"type": "string"},
                    "symbol": {"type": "string"},
                    "position": {"type": "number"},
                    "marketPrice": {"type": "number"},
                    "marketValue": {"type": "number"},
                    "averageCost": {"type": "number"},
                    "unrealizedPnL": {"type": "number"},
                    "realizedPnL": {"type": "number"},
                    "timestamp": {"type": "integer"}
                },
                "required": ["account", "symbol", "position"]
            }
        )
        
        # Register built-in schemas
        for schema in [market_data_schema, order_schema, position_schema]:
            self.register_schema(schema)
    
    def register_schema(
        self,
        schema: MessageSchema,
        check_compatibility: bool = True
    ) -> ValidationResult:
        """
        Register a new schema or version.
        
        Args:
            schema: Schema to register
            check_compatibility: Check compatibility with previous versions
            
        Returns:
            ValidationResult indicating success or compatibility issues
        """
        with self._cache_lock:
            schema_id = schema.schema_id
            # Ensure version is an integer
            try:
                version = int(schema.version)
            except (ValueError, TypeError):
                version = 1  # Default to version 1 if conversion fails
            
            # Check compatibility if required
            if check_compatibility and schema_id in self._schema_versions:
                latest_version = max(self._schema_versions[schema_id].keys())
                latest_schema = self._schema_versions[schema_id][latest_version]
                
                compatibility_result = latest_schema.check_compatibility(schema)
                if not compatibility_result.is_valid:
                    return compatibility_result
            
            # Register in local cache
            self._schemas[schema_id] = schema
            
            if schema_id not in self._schema_versions:
                self._schema_versions[schema_id] = {}
            self._schema_versions[schema_id][version] = schema
            
            # Register in cloud registry if enabled
            if self.enable_cloud_registry and self._schema_client is not None:
                # Use a separate try-except block to avoid impacting local registration
                try:
                    self._register_cloud_schema(schema)
                except Exception as e:
                    logger.error(f"Failed to register schema in cloud: {e}")
                    # Continue with local registration
            
            logger.info(f"Registered schema: {schema_id} v{version}")
            return ValidationResult(is_valid=True)
    
    def _register_cloud_schema(self, schema: MessageSchema):
        """Register schema in Google Cloud Schema Registry"""
        # Skip if schema client is not available
        if not self._schema_client:
            logger.warning("Schema client not available, skipping cloud registration")
            return
            
        try:
            # Extract schema definition based on type
            if schema.schema_type == SchemaType.JSON_SCHEMA:
                # For JSON_SCHEMA type, we need the json_schema attribute
                # First check if the schema has the expected attribute
                if isinstance(schema, JsonMessageSchema) and hasattr(schema, 'json_schema'):
                    definition = json.dumps(schema.json_schema)
                else:
                    # Try to access it anyway as a fallback
                    try:
                        definition = json.dumps(schema.json_schema)
                    except (AttributeError, TypeError) as e:
                        logger.warning(f"Schema {schema.schema_id} missing json_schema attribute: {e}")
                        return
                type_enum = "JSON"
            elif schema.schema_type == SchemaType.PROTOBUF:
                # For PROTOBUF type, we need the proto_descriptor attribute
                # First check if the schema has the expected attribute
                if isinstance(schema, ProtobufMessageSchema) and hasattr(schema, 'proto_descriptor'):
                    definition = str(schema.proto_descriptor)
                else:
                    # Try to access it anyway as a fallback
                    try:
                        definition = str(schema.proto_descriptor)
                    except (AttributeError, TypeError) as e:
                        logger.warning(f"Schema {schema.schema_id} missing proto_descriptor attribute: {e}")
                        return
                type_enum = "PROTOCOL_BUFFER"
            else:
                logger.warning(f"Unsupported schema type for cloud registry: {schema.schema_type}")
                return
            
            # Create schema using the client API - simplified approach with type safety
            try:
                # Skip cloud registration if client is not properly configured
                if not hasattr(self._schema_client, 'create_schema'):
                    logger.warning("Schema client does not have create_schema method")
                    return
                
                # Try to create schema with a very simple approach
                # This may fail due to type mismatches, but we'll catch and log
                try:
                    # Many GCP client libraries accept dict objects
                    schema_data = {
                        "name": f"{self._project_path}/schemas/{schema.schema_id}",
                        "type": type_enum,
                        "definition": definition
                    }
                    
                    # Call the API with basic parameters - suppress type warnings
                    self._schema_client.create_schema(  # type: ignore[misc]
                        parent=self._project_path,
                        schema_id=schema.schema_id,
                        schema=schema_data  # type: ignore[arg-type]
                    )
                    logger.info(f"Successfully registered schema {schema.schema_id} in cloud")
                except Exception as api_error:
                    logger.warning(f"Could not register schema {schema.schema_id} in cloud: {api_error}")
                    # Continue execution - cloud registration is optional
            except Exception as e:
                logger.error(f"Failed to register schema in cloud: {e}")
        except Exception as e:
            logger.error(f"Error in cloud schema registration: {e}")
    
    def get_schema(
        self,
        schema_id: str,
        version: Optional[int] = None
    ) -> Optional[MessageSchema]:
        """
        Get schema by ID and optional version.
        
        Args:
            schema_id: Schema identifier
            version: Specific version (latest if None)
            
        Returns:
            MessageSchema or None if not found
        """
        with self._cache_lock:
            if version is None:
                # Get latest version
                return self._schemas.get(schema_id)
            else:
                # Get specific version
                versions = self._schema_versions.get(schema_id, {})
                return versions.get(version)
    
    def list_schemas(self) -> List[SchemaMetadata]:
        """List all registered schemas"""
        with self._cache_lock:
            return [schema.metadata for schema in self._schemas.values()]
    
    def get_schema_versions(self, schema_id: str) -> List[int]:
        """Get all versions of a schema"""
        with self._cache_lock:
            versions = self._schema_versions.get(schema_id, {})
            return sorted(versions.keys())
    
    def validate_message(
        self,
        schema_id: str,
        message_data: Any,
        version: Optional[int] = None
    ) -> ValidationResult:
        """
        Validate a message against a schema.
        
        Args:
            schema_id: Schema identifier
            message_data: Message data to validate
            version: Schema version (latest if None)
            
        Returns:
            ValidationResult
        """
        schema = self.get_schema(schema_id, version)
        if not schema:
            return ValidationResult(
                is_valid=False,
                errors=[f"Schema not found: {schema_id}"]
            )
        
        return schema.validate(message_data)
    
    def create_schema_from_sample(
        self,
        schema_id: str,
        name: str,
        sample_data: Any,
        schema_type: SchemaType = SchemaType.JSON_SCHEMA
    ) -> MessageSchema:
        """
        Create a schema from sample data (useful for development).
        
        Args:
            schema_id: Schema identifier
            name: Schema name
            sample_data: Sample data to infer schema from
            schema_type: Type of schema to create
            
        Returns:
            Created MessageSchema
        """
        if schema_type == SchemaType.JSON_SCHEMA:
            # Infer JSON schema from sample
            json_schema = self._infer_json_schema(sample_data)
            return JsonMessageSchema(
                schema_id=schema_id,
                name=name,
                json_schema=json_schema
            )
        else:
            raise NotImplementedError(f"Schema inference not implemented for {schema_type}")
    
    def get_schema_json(self, schema_id: str, version: Optional[int] = None) -> Dict[str, Any]:
        """Get schema as JSON"""
        try:
            schema = self.get_schema(schema_id, version)
            # Check if schema is None
            if schema is None:
                return {"error": f"Schema not found: {schema_id}"}
                
            # Check if schema_type exists
            if not hasattr(schema, 'schema_type'):
                return {"error": f"Invalid schema format: missing schema_type"}
                
            # Now we can safely check schema type
            if schema.schema_type == SchemaType.JSON_SCHEMA:
                # Access json_schema safely
                if isinstance(schema, JsonMessageSchema) and hasattr(schema, 'json_schema'):
                    return schema.json_schema or {"error": "Empty json_schema"}
                else:
                    # Try to access it anyway as a fallback
                    try:
                        return schema.json_schema or {"error": "Empty json_schema"}  # type: ignore
                    except (AttributeError, TypeError):
                        return {"error": "Schema does not have json_schema attribute"}
            elif schema.schema_type == SchemaType.PROTOBUF:
                # Convert protobuf schema to JSON representation
                # Access proto_descriptor safely
                if isinstance(schema, ProtobufMessageSchema) and hasattr(schema, 'proto_descriptor'):
                    return {"type": "protobuf", "definition": str(schema.proto_descriptor or "")} 
                else:
                    # Try to access it anyway as a fallback
                    try:
                        return {"type": "protobuf", "definition": str(schema.proto_descriptor or "")}  # type: ignore
                    except (AttributeError, TypeError):
                        return {"error": "Schema does not have proto_descriptor attribute"}
            else:
                return {"error": f"Unsupported schema type: {schema.schema_type}"}
        except Exception as e:
            return {"error": f"Error retrieving schema: {str(e)}"}
    
    def _infer_json_schema(self, data: Any) -> Dict[str, Any]:
        """Infer JSON schema from sample data"""
        if isinstance(data, dict):
            properties = {}
            required = []
            
            for key, value in data.items():
                properties[key] = self._infer_json_type(value)
                required.append(key)
            
            return {
                "$schema": "http://json-schema.org/draft-07/schema#",
                "type": "object",
                "properties": properties,
                "required": required
            }
        elif isinstance(data, list):
            if data:
                item_schema = self._infer_json_type(data[0])
                return {
                    "type": "array",
                    "items": item_schema
                }
            else:
                return {"type": "array"}
        else:
            return self._infer_json_type(data)
    
    def _infer_json_type(self, value: Any) -> Dict[str, str]:
        """Infer JSON type from Python value"""
        if isinstance(value, bool):
            return {"type": "boolean"}
        elif isinstance(value, int):
            return {"type": "integer"}
        elif isinstance(value, float):
            return {"type": "number"}
        elif isinstance(value, str):
            return {"type": "string"}
        elif isinstance(value, dict):
            return self._infer_json_schema(value)
        elif isinstance(value, list):
            return self._infer_json_schema(value)
        elif value is None:
            return {"type": "null"}
        else:
            return {"type": "string"}  # Default fallback
    
    def export_schemas(self, output_dir: Path):
        """Export all schemas to files"""
        output_dir.mkdir(parents=True, exist_ok=True)
        
        with self._cache_lock:
            for schema_id, schema in self._schemas.items():
                safe_id = schema_id.replace('.', '_')
                
                if schema.schema_type == SchemaType.JSON_SCHEMA:
                    file_path = output_dir / f"{safe_id}.json"
                    with open(file_path, 'w') as f:
                        json.dump(schema.json_schema, f, indent=2)
                
                # Export metadata
                meta_path = output_dir / f"{safe_id}.meta.json"
                with open(meta_path, 'w') as f:
                    json.dump({
                        'schema_id': schema.schema_id,
                        'name': schema.name,
                        'type': schema.schema_type.value,
                        'version': schema.version,
                        'created_at': schema.metadata.created_at.isoformat(),
                        'updated_at': schema.metadata.updated_at.isoformat()
                    }, f, indent=2)
        
        logger.info(f"Exported {len(self._schemas)} schemas to {output_dir}")
