"""
IBKR to Google Cloud Pub/Sub Bridge

Bridges IBKR real-time data streams to Google Cloud Pub/Sub for
enterprise-grade message distribution and processing.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime, timezone

from .pubsub_manager import PubSubManager, TradingMessage
from app.services.ibkr_service import IBKRService
from app.websocket.fastapi_websocket import broadcaster, connection_manager

logger = logging.getLogger(__name__)


class IBKRPubSubBridge:
    """
    Bridges IBKR real-time data to Google Cloud Pub/Sub
    
    Converts IBKR callbacks into Pub/Sub messages for:
    - Market data (ticks, bars, depth)
    - Order updates and executions
    - Position changes
    - News and alerts
    - Risk events
    """
    
    def __init__(self, ibkr_service: IBKRService, pubsub_manager: PubSubManager):
        self.ibkr_service = ibkr_service
        self.pubsub_manager = pubsub_manager
        
        # Processing statistics
        self.stats = {
            'market_data_published': 0,
            'order_updates_published': 0,
            'position_updates_published': 0,
            'news_published': 0,
            'risk_alerts_published': 0,
            'errors': 0,
            'last_activity': None
        }
        
        # Message filtering and throttling
        self.last_market_data: Dict[str, Dict] = {}
        self.market_data_throttle_ms = 100  # Minimum 100ms between updates per symbol
        
        # Register IBKR event handlers
        self._register_ibkr_handlers()
        
        # Register Pub/Sub message handlers for WebSocket broadcasting
        self._register_pubsub_handlers()
    
    def _register_ibkr_handlers(self):
        """Register handlers for IBKR real-time events"""
        if hasattr(self.ibkr_service, 'ib') and self.ibkr_service.ib:
            # Market data events
            self.ibkr_service.ib.pendingTickersEvent += self._handle_pending_tickers
            self.ibkr_service.ib.barUpdateEvent += self._handle_bar_update
            self.ibkr_service.ib.newsBulletinEvent += self._handle_news_bulletin

            # Order and position events
            self.ibkr_service.ib.orderStatusEvent += self._handle_order_status
            self.ibkr_service.ib.openOrderEvent += self._handle_open_order
            self.ibkr_service.ib.positionEvent += self._handle_position_update

            # Error events
            self.ibkr_service.ib.errorEvent += self._handle_error

            logger.info("IBKR event handlers registered for Pub/Sub bridge")
    
    def _register_pubsub_handlers(self):
        """Register Pub/Sub message handlers for WebSocket broadcasting"""
        self.pubsub_manager.register_message_handler(
            'market_data_update', self._broadcast_market_data
        )
        self.pubsub_manager.register_message_handler(
            'order_update', self._broadcast_order_update
        )
        self.pubsub_manager.register_message_handler(
            'position_update', self._broadcast_position_update
        )
        self.pubsub_manager.register_message_handler(
            'trading_signal', self._broadcast_trading_signal
        )
        self.pubsub_manager.register_message_handler(
            'risk_alert', self._broadcast_risk_alert
        )
    
    async def _handle_pending_tickers(self, tickers):
        """Handle IBKR pending tickers updates and publish to Pub/Sub"""
        try:
            for ticker in tickers:
                symbol = ticker.contract.symbol
                current_time = datetime.now(timezone.utc)

                # Throttle updates to prevent spam
                last_update = self.last_market_data.get(symbol, {}).get('timestamp')
                if last_update:
                    time_diff = (current_time - last_update).total_seconds() * 1000
                    if time_diff < self.market_data_throttle_ms:
                        continue

                # Extract market data
                market_data = {
                    'symbol': symbol,
                    'last': float(ticker.last) if ticker.last and ticker.last > 0 else None,
                    'bid': float(ticker.bid) if ticker.bid and ticker.bid > 0 else None,
                    'ask': float(ticker.ask) if ticker.ask and ticker.ask > 0 else None,
                    'bid_size': int(ticker.bidSize) if ticker.bidSize else None,
                    'ask_size': int(ticker.askSize) if ticker.askSize else None,
                    'volume': int(ticker.volume) if ticker.volume else None,
                    'high': float(ticker.high) if ticker.high and ticker.high > 0 else None,
                    'low': float(ticker.low) if ticker.low and ticker.low > 0 else None,
                    'close': float(ticker.close) if ticker.close and ticker.close > 0 else None,
                    'timestamp': current_time.isoformat()
                }

                # Calculate derived fields
                if market_data['last'] and market_data['close']:
                    market_data['change'] = market_data['last'] - market_data['close']
                    market_data['change_percent'] = (market_data['change'] / market_data['close']) * 100

                # Publish to Pub/Sub
                await self.pubsub_manager.publish_market_data(symbol, market_data)

                # Update tracking
                self.last_market_data[symbol] = {
                    'data': market_data,
                    'timestamp': current_time
                }

                # Update statistics
                self.stats['market_data_published'] += 1

            self.stats['last_activity'] = datetime.now(timezone.utc).isoformat()

        except Exception as e:
            logger.error(f"Error handling pending tickers: {e}")
            self.stats['errors'] += 1

    async def _handle_bar_update(self, bars, has_new_bar):
        """Handle IBKR real-time bar updates"""
        if not has_new_bar or not bars:
            return

        try:
            # Get the latest bar
            latest_bar = bars[-1]
            symbol = bars.contract.symbol

            bar_data = {
                'symbol': symbol,
                'timestamp': latest_bar.time,
                'open': float(latest_bar.open),
                'high': float(latest_bar.high),
                'low': float(latest_bar.low),
                'close': float(latest_bar.close),
                'volume': int(latest_bar.volume),
                'wap': float(latest_bar.wap) if latest_bar.wap else None,
                'count': int(latest_bar.count) if latest_bar.count else None
            }

            # Create bar update message
            message = TradingMessage(
                message_type='bar_update',
                symbol=symbol,
                data=bar_data
            )

            # Publish to market data topic
            await self.pubsub_manager._publish_message('market-data', message, ordering_key=symbol)

            self.stats['market_data_published'] += 1
            self.stats['last_activity'] = datetime.now(timezone.utc).isoformat()

        except Exception as e:
            logger.error(f"Error handling bar update: {e}")
            self.stats['errors'] += 1

    async def _handle_open_order(self, trade):
        """Handle IBKR open order updates"""
        try:
            # Similar to order status but for open orders
            await self._handle_order_status(trade)

        except Exception as e:
            logger.error(f"Error handling open order: {e}")
            self.stats['errors'] += 1

    async def _handle_order_status(self, trade):
        """Handle IBKR order status updates and publish to Pub/Sub"""
        try:
            order_data = {
                'order_id': trade.order.orderId,
                'client_id': trade.order.clientId,
                'perm_id': trade.order.permId,
                'status': trade.orderStatus.status,
                'filled': float(trade.orderStatus.filled),
                'remaining': float(trade.orderStatus.remaining),
                'avg_fill_price': float(trade.orderStatus.avgFillPrice) if trade.orderStatus.avgFillPrice > 0 else None,
                'last_fill_price': float(trade.orderStatus.lastFillPrice) if trade.orderStatus.lastFillPrice > 0 else None,
                'symbol': trade.contract.symbol,
                'action': trade.order.action,
                'total_quantity': float(trade.order.totalQuantity),
                'order_type': trade.order.orderType,
                'limit_price': float(trade.order.lmtPrice) if trade.order.lmtPrice > 0 else None,
                'stop_price': float(trade.order.auxPrice) if trade.order.auxPrice > 0 else None,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

            # Publish to Pub/Sub
            account = getattr(trade.order, 'account', 'default')
            await self.pubsub_manager.publish_order_update(account, order_data)

            # Update statistics
            self.stats['order_updates_published'] += 1
            self.stats['last_activity'] = datetime.now(timezone.utc).isoformat()
            
        except Exception as e:
            logger.error(f"Error handling order status: {e}")
            self.stats['errors'] += 1
    
    async def _handle_position_update(self, position):
        """Handle IBKR position updates and publish to Pub/Sub"""
        try:
            position_data = {
                'account': position.account,
                'symbol': position.contract.symbol,
                'position': float(position.position),
                'market_price': float(position.marketPrice) if position.marketPrice else None,
                'market_value': float(position.marketValue) if position.marketValue else None,
                'average_cost': float(position.avgCost) if position.avgCost else None,
                'unrealized_pnl': float(position.unrealizedPNL) if position.unrealizedPNL else None,
                'realized_pnl': float(position.realizedPNL) if position.realizedPNL else None,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

            # Create position update message
            message = TradingMessage(
                message_type='position_update',
                symbol=position.contract.symbol,
                data=position_data,
                account=position.account
            )

            # Publish to positions topic
            await self.pubsub_manager._publish_message('positions', message, ordering_key=position.account)

            # Update statistics
            self.stats['position_updates_published'] += 1
            self.stats['last_activity'] = datetime.now(timezone.utc).isoformat()
            
        except Exception as e:
            logger.error(f"Error handling position update: {e}")
            self.stats['errors'] += 1
    
    async def _handle_news_bulletin(self, msg_id, msg_type, message, orig_exchange):
        """Handle IBKR news bulletins and publish to Pub/Sub"""
        try:
            news_data = {
                'message_id': msg_id,
                'message_type': msg_type,
                'message': message,
                'exchange': orig_exchange,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

            # Create news message
            news_message = TradingMessage(
                message_type='news_bulletin',
                symbol='',  # News may not be symbol-specific
                data=news_data
            )

            # Publish to news topic
            await self.pubsub_manager._publish_message('news', news_message)

            # Update statistics
            self.stats['news_published'] += 1
            self.stats['last_activity'] = datetime.now(timezone.utc).isoformat()
            
        except Exception as e:
            logger.error(f"Error handling news bulletin: {e}")
            self.stats['errors'] += 1
    
    async def _handle_error(self, req_id, error_code, error_string, contract):
        """Handle IBKR errors and publish as risk alerts"""
        try:
            # Determine severity based on error code
            severity = 'high' if error_code < 1000 else 'medium'
            
            error_data = {
                'request_id': req_id,
                'error_code': error_code,
                'error_message': error_string,
                'contract': contract.symbol if contract else None,
                'severity': severity,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            # Publish as risk alert
            await self.pubsub_manager.publish_risk_alert(error_data)
            
            # Update statistics
            self.stats['risk_alerts_published'] += 1
            self.stats['errors'] += 1
            
        except Exception as e:
            logger.error(f"Error handling IBKR error: {e}")
    
    # Pub/Sub to WebSocket broadcasting handlers
    async def _broadcast_market_data(self, message: TradingMessage):
        """Broadcast market data from Pub/Sub to WebSocket clients"""
        try:
            await broadcaster.broadcast_market_data(message.symbol, message.data)
        except Exception as e:
            logger.error(f"Error broadcasting market data: {e}")
    
    async def _broadcast_order_update(self, message: TradingMessage):
        """Broadcast order update from Pub/Sub to WebSocket clients"""
        try:
            # Only broadcast if account is specified
            if message.account:
                await broadcaster.broadcast_order_update(message.account, message.data)
            else:
                logger.warning("Order update message missing account - skipping broadcast")
        except Exception as e:
            logger.error(f"Error broadcasting order update: {e}")
    
    async def _broadcast_position_update(self, message: TradingMessage):
        """Broadcast position update from Pub/Sub to WebSocket clients"""
        try:
            # Only broadcast if account is specified
            if message.account:
                # Broadcast to position-specific channel
                channel = f"positions:{message.account}"
                await connection_manager.broadcast_to_channel(channel, {
                    'type': 'position_update',
                    'channel': channel,
                    'data': message.data
                })
            else:
                logger.warning("Position update message missing account - skipping broadcast")
        except Exception as e:
            logger.error(f"Error broadcasting position update: {e}")
    
    async def _broadcast_trading_signal(self, message: TradingMessage):
        """Broadcast trading signal from Pub/Sub to WebSocket clients"""
        try:
            # Only broadcast if strategy_id is specified
            if message.strategy_id:
                await broadcaster.broadcast_trading_signal(message.strategy_id, message.data)
            else:
                logger.warning("Trading signal message missing strategy_id - skipping broadcast")
        except Exception as e:
            logger.error(f"Error broadcasting trading signal: {e}")
    
    async def _broadcast_risk_alert(self, message: TradingMessage):
        """Broadcast risk alert from Pub/Sub to WebSocket clients"""
        try:
            await broadcaster.broadcast_risk_alert(message.data)
        except Exception as e:
            logger.error(f"Error broadcasting risk alert: {e}")
    
    async def publish_trading_signal(self, strategy_id: str, signal_data: Dict[str, Any]):
        """Manually publish trading signal (for algorithmic strategies)"""
        try:
            await self.pubsub_manager.publish_trading_signal(strategy_id, signal_data)
            logger.info(f"Published trading signal for strategy {strategy_id}")
        except Exception as e:
            logger.error(f"Failed to publish trading signal: {e}")
    
    async def publish_risk_alert(self, alert_data: Dict[str, Any]):
        """Manually publish risk alert (for risk management systems)"""
        try:
            await self.pubsub_manager.publish_risk_alert(alert_data)
            logger.info("Published risk alert")
        except Exception as e:
            logger.error(f"Failed to publish risk alert: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get bridge statistics"""
        return {
            **self.stats,
            'pubsub_stats': self.pubsub_manager.get_statistics(),
            'throttle_settings': {
                'market_data_throttle_ms': self.market_data_throttle_ms
            },
            'active_symbols': list(self.last_market_data.keys())
        }


# Global bridge instance
ibkr_pubsub_bridge: Optional[IBKRPubSubBridge] = None
