#!/usr/bin/env python3
"""
<PERSON>ript to verify and fix imports in all pub/sub Python files
"""

import os
import re
import sys
from pathlib import Path

def check_imports(file_path):
    """Check imports in a Python file and return issues found."""
    issues = []
    fixes = []
    
    with open(file_path, 'r') as f:
        content = f.read()
        lines = content.split('\n')
    
    for i, line in enumerate(lines):
        # Check for absolute imports that should be relative
        if re.match(r'^from app\.pubsub\.\w+ import', line):
            match = re.search(r'from app\.pubsub\.(\w+) import', line)
            if match:
                module = match.group(1)
                fix = line.replace(f'from app.pubsub.{module}', f'from .{module}')
                issues.append({
                    'line': i + 1,
                    'issue': f'Absolute import should be relative: {line}',
                    'fix': fix
                })
                fixes.append((i, fix))
        
        # Check for other app imports that need handling
        elif re.match(r'^from app\.\w+', line) and 'pubsub' not in line:
            issues.append({
                'line': i + 1,
                'issue': f'External app import needs error handling: {line}',
                'fix': None  # These need manual review
            })
    
    return issues, fixes, lines


def fix_imports(file_path, fixes, lines):
    """Apply fixes to the file."""
    for line_num, fix in fixes:
        lines[line_num] = fix
    
    with open(file_path, 'w') as f:
        f.write('\n'.join(lines))


def main():
    """Main function to check all Python files."""
    pubsub_dir = Path(__file__).parent.parent
    python_files = list(pubsub_dir.glob('*.py'))
    
    print(f"Checking {len(python_files)} Python files in {pubsub_dir}")
    print("=" * 60)
    
    total_issues = 0
    files_with_issues = []
    
    for file_path in sorted(python_files):
        if file_path.name.startswith('__'):
            continue
            
        issues, fixes, lines = check_imports(file_path)
        
        if issues:
            total_issues += len(issues)
            files_with_issues.append(file_path.name)
            print(f"\n{file_path.name}:")
            
            for issue in issues:
                print(f"  Line {issue['line']}: {issue['issue']}")
                if issue['fix']:
                    print(f"    Fix: {issue['fix']}")
            
            # Auto-fix relative imports
            if fixes and '--fix' in sys.argv:
                fix_imports(file_path, fixes, lines)
                print(f"  ✓ Applied {len(fixes)} automatic fixes")
    
    print("\n" + "=" * 60)
    print(f"Total issues found: {total_issues}")
    print(f"Files with issues: {len(files_with_issues)}")
    
    if files_with_issues:
        print("\nFiles needing attention:")
        for file in files_with_issues:
            print(f"  - {file}")
    
    if total_issues > 0 and '--fix' not in sys.argv:
        print("\nRun with --fix to automatically fix relative imports")
    
    return 0 if total_issues == 0 else 1


if __name__ == "__main__":
    sys.exit(main())
