#!/usr/bin/env python3
"""
Simple test script to verify pub/sub imports work correctly
"""

import sys
import os

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def test_imports():
    """Test that all core imports work correctly."""
    print("Testing pub/sub imports...\n")
    
    results = []
    
    # Test core imports
    try:
        from app.pubsub import PubSubManager, PubSubConfig, TradingMessage
        results.append(("Core imports (PubSubManager, etc.)", "✅ Success"))
    except Exception as e:
        results.append(("Core imports", f"❌ Failed: {e}"))
    
    # Test enhanced manager
    try:
        from app.pubsub import EnhancedPubSubManager, CircuitBreakerConfig
        results.append(("Enhanced Manager imports", "✅ Success"))
    except Exception as e:
        results.append(("Enhanced Manager imports", f"❌ Failed: {e}"))
    
    # Test bridge
    try:
        from app.pubsub import IBKRPubSubBridge
        results.append(("IBKR Bridge import", "✅ Success"))
    except Exception as e:
        results.append(("IBKR Bridge import", f"❌ Failed: {e}"))
    
    # Test integration adapter
    try:
        from app.pubsub import PubSubIntegrationAdapter, get_pubsub_adapter
        results.append(("Integration Adapter imports", "✅ Success"))
    except Exception as e:
        results.append(("Integration Adapter imports", f"❌ Failed: {e}"))
    
    # Print results
    print("Import Test Results:")
    print("-" * 50)
    for test, result in results:
        print(f"{test:<30} {result}")
    
    # Test instantiation
    print("\nTesting instantiation...")
    try:
        config = PubSubConfig()
        print(f"✅ Created PubSubConfig with project_id: {config.project_id}")
    except Exception as e:
        print(f"❌ Failed to create PubSubConfig: {e}")
    
    try:
        from app.pubsub.enhanced_manager import CircuitBreakerState
        print(f"✅ Imported CircuitBreakerState (CLOSED={CircuitBreakerState.CLOSED})")
    except Exception as e:
        print(f"❌ Failed to import CircuitBreakerState: {e}")
    
    print("\n✅ Basic import test complete!")


if __name__ == "__main__":
    test_imports()
