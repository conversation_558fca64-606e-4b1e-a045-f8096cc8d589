"""
Enhanced Pub/Sub Infrastructure for IBKR MCP Server

This package provides comprehensive Google Cloud Pub/Sub functionality
with advanced features for the IBKR MCP Server.

Core Components:
- PubSubManager: Basic Pub/Sub functionality
- EnhancedPubSubManager: Advanced features (circuit breaker, deduplication)
- IBKRPubSubBridge: Integration with IBKR services
- PubSubIntegrationAdapter: Unified interface for all components

Optional Components (loaded based on configuration):
- MessageSchemaRegistry: Schema validation and evolution
- BatchPublisher: High-throughput batch publishing
- StreamingAggregator: Real-time data aggregation
- DistributedProcessor: Scalable message processing
- DeadLetterQueueManager: Failed message handling
- MonitoringService: Observability and metrics
- MultiRegionManager: Global deployment support
"""

# Core components - always available
from .pubsub_manager import (
    PubSubManager,
    PubSubConfig,
    TradingMessage,
    pubsub_manager
)

from .ibkr_pubsub_bridge import IBKRPubSubBridge

# Enhanced components
from .enhanced_manager import (
    EnhancedPubSubManager,
    CircuitBreakerConfig,
    get_enhanced_pubsub_manager
)

from .integration_adapter import (
    PubSubIntegrationAdapter,
    get_pubsub_adapter,
    setup_pubsub_adapter
)

# Optional components - may not be available
_optional_components = []

try:
    from .schema_registry import SchemaRegistry as MessageSchemaRegistry
    _optional_components.append('MessageSchemaRegistry')
except ImportError:
    MessageSchemaRegistry = None

try:
    from .batch_publisher import BatchPublisher
    _optional_components.append('BatchPublisher')
except ImportError:
    BatchPublisher = None

try:
    from .streaming_aggregator import StreamingAggregator
    _optional_components.append('StreamingAggregator')
except ImportError:
    StreamingAggregator = None

try:
    from .distributed_processor import DistributedProcessor
    _optional_components.append('DistributedProcessor')
except ImportError:
    DistributedProcessor = None

try:
    from .dlq_manager import DeadLetterQueueManager
    _optional_components.append('DeadLetterQueueManager')
except ImportError:
    DeadLetterQueueManager = None

try:
    from .monitoring_service import MonitoringService
    _optional_components.append('MonitoringService')
except ImportError:
    MonitoringService = None

try:
    from .multi_region_manager import MultiRegionManager
    _optional_components.append('MultiRegionManager')
except ImportError:
    MultiRegionManager = None

# Export all available components
__all__ = [
    # Core components
    'PubSubManager',
    'PubSubConfig',
    'TradingMessage',
    'pubsub_manager',
    'IBKRPubSubBridge',
    
    # Enhanced components
    'EnhancedPubSubManager',
    'CircuitBreakerConfig',
    'get_enhanced_pubsub_manager',
    
    # Integration
    'PubSubIntegrationAdapter',
    'get_pubsub_adapter',
    'setup_pubsub_adapter',
    
    # Optional components (if available)
    *_optional_components
]

# Version info
__version__ = '2.0.0'
__author__ = 'IBKR Integration Team'

# Log available components
import logging
logger = logging.getLogger(__name__)
logger.info(f"Pub/Sub package initialized with components: {', '.join(_optional_components)}")
