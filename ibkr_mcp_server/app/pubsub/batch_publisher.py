"""
Batch Publisher for Google Cloud Pub/Sub

This module implements an optimized batch publishing system with:
- Automatic batching based on size, count, and time
- Future-based asynchronous publishing
- Adaptive batch sizing based on throughput
- Memory-efficient buffering
- Comprehensive error handling and retries

Author: IBKR MCP Server Team
Version: 2.0.0
"""

# App-specific imports (added during migration)
# from app.core.config import settings  # Not used in this module
# from app.models.database_models import Base  # Not used in this module
# from app.services.base_service import BaseService  # Not used in this module

# import asyncio  # Not used in this module
import time
import logging
import threading
from typing import Dict, List, Optional, Callable, Tuple, Union, Any
from dataclasses import dataclass, field
from collections import defaultdict
from queue import Queue, Empty
from concurrent.futures import Future, ThreadPoolExecutor
# import json  # Not used in this module

from google.cloud import pubsub_v1
from google.api_core import retry
import numpy as np

logger = logging.getLogger(__name__)


@dataclass
class BatchConfig:
    """Configuration for batch publishing"""
    max_messages: int = 1000
    max_bytes: int = 10 * 1024 * 1024  # 10MB
    max_latency_ms: int = 10  # 10ms
    adaptive_sizing: bool = True
    compression_enabled: bool = True
    retry_config: retry.Retry = field(default_factory=lambda: retry.Retry(deadline=30))


@dataclass
class MessageWrapper:
    """Wrapper for a message with metadata"""
    data: bytes
    attributes: Dict[str, str]
    ordering_key: str
    future: Future
    enqueued_at: float = field(default_factory=time.time)
    size_bytes: int = 0
    
    def __post_init__(self):
        if self.size_bytes == 0:
            self.size_bytes = len(self.data) + sum(
                len(k) + len(v) for k, v in self.attributes.items()
            )


@dataclass
class BatchStats:
    """Statistics for batch operations"""
    messages_sent: int = 0
    bytes_sent: int = 0
    batches_sent: int = 0
    errors: int = 0
    avg_batch_size: float = 0.0
    avg_latency_ms: float = 0.0
    throughput_mbps: float = 0.0


class BatchPublisher:
    """
    High-performance batch publisher for Google Cloud Pub/Sub.
    
    This publisher automatically batches messages for optimal throughput
    while maintaining low latency through adaptive sizing and time-based flushing.
    """
    
    def __init__(
        self,
        project_id: str,
        batch_config: Optional[BatchConfig] = None,
        num_threads: int = 4,
        enable_monitoring: bool = True,
        max_batch_size: Optional[int] = None  # For backward compatibility
    ):
        """
        Initialize batch publisher.

        Args:
            project_id: GCP project ID or publisher client
            batch_config: Batch configuration
            num_threads: Number of publisher threads
            enable_monitoring: Enable performance monitoring
            max_batch_size: Maximum batch size (for backward compatibility)
        """
        # Handle backward compatibility - project_id might be a publisher client
        if hasattr(project_id, 'publish'):
            # It's a publisher client, extract project_id
            self.project_id = getattr(project_id, 'project', 'default-project')
            self._external_publisher = project_id
        else:
            self.project_id = project_id
            self._external_publisher = None

        # Apply max_batch_size if provided
        if max_batch_size and batch_config is None:
            batch_config = BatchConfig(max_messages=max_batch_size)
        elif max_batch_size and batch_config:
            batch_config.max_messages = max_batch_size

        self.batch_config = batch_config or BatchConfig()
        self.num_threads = num_threads
        self.enable_monitoring = enable_monitoring
        
        # Message queues by topic
        self._queues: Dict[str, Queue[MessageWrapper]] = defaultdict(lambda: Queue())
        self._queue_locks: Dict[str, threading.Lock] = defaultdict(threading.Lock)
        
        # Batch buffers
        self._batches: Dict[str, List[MessageWrapper]] = defaultdict(list)
        self._batch_sizes: Dict[str, int] = defaultdict(int)
        self._batch_timers: Dict[str, threading.Timer] = {}
        
        # Publishers
        self._publishers: Dict[str, pubsub_v1.PublisherClient] = {}
        
        # Thread pool for publishing
        self._executor = ThreadPoolExecutor(max_workers=num_threads)
        
        # Statistics
        self._stats: Dict[str, BatchStats] = defaultdict(BatchStats)
        self._stats_lock = threading.Lock()
        
        # Adaptive sizing
        self._adaptive_sizes: Dict[str, int] = defaultdict(
            lambda: self.batch_config.max_messages
        )
        self._throughput_history: Dict[str, List[float]] = defaultdict(list)
        
        # Control flags
        self._running = True
        self._worker_threads: List[threading.Thread] = []
        
        # Don't auto-start workers - wait for explicit start() call
        self._started = False

        logger.info(f"Batch Publisher initialized with {num_threads} threads")

    async def start(self):
        """Start the batch publisher"""
        if not self._started:
            self._start_workers()
            self._started = True
            logger.info("Batch Publisher started")

    async def stop(self):
        """Stop the batch publisher"""
        if self._started:
            self.shutdown()
            self._started = False
            logger.info("Batch Publisher stopped")

    async def publish_async(
        self,
        data: Union[Dict[str, Any], bytes],
        topic: str,
        ordering_key: Optional[str] = None,
        attributes: Optional[Dict[str, str]] = None
    ) -> str:
        """
        Publish a message asynchronously (compatibility method).

        Args:
            data: Message data (dict will be JSON encoded)
            topic: Target topic
            ordering_key: Optional ordering key
            attributes: Optional attributes

        Returns:
            Message ID
        """
        # Convert data to bytes if needed
        if isinstance(data, dict):
            import json
            message_data = json.dumps(data).encode('utf-8')
        else:
            message_data = data

        # Use the existing publish method
        future = await self.publish(
            topic_name=topic,
            data=message_data,
            ordering_key=ordering_key,
            attributes=attributes
        )

        # Wait for the result
        return future.result()

    def _start_workers(self):
        """Start worker threads for batch processing"""
        for i in range(self.num_threads):
            thread = threading.Thread(
                target=self._worker_loop,
                name=f"BatchPublisher-Worker-{i}",
                daemon=True
            )
            thread.start()
            self._worker_threads.append(thread)
    
    def _worker_loop(self):
        """Main worker loop for processing batches"""
        while self._running:
            # Process all topics in round-robin fashion
            for topic in list(self._queues.keys()):
                if not self._running:
                    break
                
                try:
                    # Check if we should flush this topic
                    if self._should_flush_batch(topic):
                        self._flush_batch(topic)
                    
                    # Process messages from queue
                    queue = self._queues[topic]
                    try:
                        message = queue.get(timeout=0.001)  # 1ms timeout
                        self._add_to_batch(topic, message)
                    except Empty:
                        pass
                    
                except Exception as e:
                    logger.error(f"Worker error for topic {topic}: {e}")
            
            # Brief sleep to prevent CPU spinning
            time.sleep(0.001)
    
    def _get_publisher(self, topic: str) -> pubsub_v1.PublisherClient:
        """Get or create publisher for topic"""
        if topic not in self._publishers:
            batch_settings = pubsub_v1.types.BatchSettings(
                max_messages=self.batch_config.max_messages,
                max_bytes=self.batch_config.max_bytes,
                max_latency=self.batch_config.max_latency_ms / 1000.0
            )
            
            self._publishers[topic] = pubsub_v1.PublisherClient(
                batch_settings=batch_settings
            )
        
        return self._publishers[topic]
    
    async def publish(
        self,
        topic_name: str,
        data: bytes,
        ordering_key: Optional[str] = None,
        attributes: Optional[Dict[str, str]] = None,
        callback: Optional[Callable[[Future], None]] = None
    ) -> Future:
        """
        Publish a message asynchronously.
        
        Args:
            topic_name: Target topic
            data: Message data
            ordering_key: Optional ordering key
            attributes: Optional attributes
            callback: Optional callback for completion
            
        Returns:
            Future that will contain the message ID
        """
        # Create future for this message
        future = Future()
        if callback:
            future.add_done_callback(callback)
        
        # Wrap message
        message = MessageWrapper(
            data=data,
            attributes=attributes or {},
            ordering_key=ordering_key or '',
            future=future
        )
        
        # Add to queue
        self._queues[topic_name].put(message)
        
        # Start timer if this is the first message in a batch
        with self._queue_locks[topic_name]:
            if topic_name not in self._batch_timers:
                self._start_batch_timer(topic_name)
        
        return future
    
    async def publish_messages(
        self,
        topic_name: str,
        messages: List[Tuple[bytes, Optional[Dict[str, str]]]],
        ordering_key: Optional[str] = None
    ) -> List[Future]:
        """
        Publish multiple messages.
        
        Args:
            topic_name: Target topic
            messages: List of (data, attributes) tuples
            ordering_key: Common ordering key
            
        Returns:
            List of futures
        """
        futures = []
        
        for data, attributes in messages:
            future = await self.publish(
                topic_name,
                data,
                ordering_key=ordering_key,
                attributes=attributes
            )
            futures.append(future)
        
        return futures
    
    def _add_to_batch(self, topic: str, message: MessageWrapper):
        """Add message to batch"""
        with self._queue_locks[topic]:
            self._batches[topic].append(message)
            self._batch_sizes[topic] += message.size_bytes
            
            # Check if batch should be flushed
            if self._should_flush_batch(topic):
                self._flush_batch(topic)
    
    def _should_flush_batch(self, topic: str) -> bool:
        """Check if batch should be flushed"""
        batch = self._batches[topic]
        batch_size = self._batch_sizes[topic]
        
        if not batch:
            return False
        
        # Check size limits
        if len(batch) >= self._adaptive_sizes[topic]:
            return True
        
        if batch_size >= self.batch_config.max_bytes:
            return True
        
        # Check time limit
        oldest_message = batch[0]
        age_ms = (time.time() - oldest_message.enqueued_at) * 1000
        if age_ms >= self.batch_config.max_latency_ms:
            return True
        
        return False
    
    def _flush_batch(self, topic: str):
        """Flush a batch of messages"""
        with self._queue_locks[topic]:
            batch = self._batches[topic]
            if not batch:
                return
            
            # Clear batch
            self._batches[topic] = []
            self._batch_sizes[topic] = 0
            
            # Cancel timer
            if topic in self._batch_timers:
                self._batch_timers[topic].cancel()
                del self._batch_timers[topic]
        
        # Publish batch asynchronously
        self._executor.submit(self._publish_batch, topic, batch)
    
    def _publish_batch(self, topic: str, batch: List[MessageWrapper]):
        """Publish a batch of messages"""
        start_time = time.time()
        publisher = self._get_publisher(topic)
        topic_path = f"projects/{self.project_id}/topics/{topic}"
        
        try:
            # Group messages by ordering key for proper sequencing
            ordered_batches = defaultdict(list)
            for msg in batch:
                ordered_batches[msg.ordering_key].append(msg)
            
            # Publish each ordered batch
            for ordering_key, ordered_messages in ordered_batches.items():
                publish_futures = []
                
                for msg in ordered_messages:
                    # Compress if enabled
                    data = msg.data
                    attributes = msg.attributes.copy()
                    
                    if self.batch_config.compression_enabled and len(data) > 1024:
                        import zlib
                        compressed = zlib.compress(data)
                        if len(compressed) < len(data) * 0.9:  # Only use if >10% savings
                            data = compressed
                            attributes['compression'] = 'zlib'
                    
                    # Publish with retry
                    pub_future = publisher.publish(
                        topic_path,
                        data,
                        ordering_key=ordering_key,
                        retry=self.batch_config.retry_config,
                        **attributes
                    )
                    publish_futures.append((msg, pub_future))
                
                # Wait for all futures and set results
                for msg, pub_future in publish_futures:
                    try:
                        message_id = pub_future.result()
                        msg.future.set_result(message_id)
                    except Exception as e:
                        msg.future.set_exception(e)
                        self._record_error(topic)
            
            # Update statistics
            elapsed_ms = (time.time() - start_time) * 1000
            self._update_stats(topic, len(batch), sum(m.size_bytes for m in batch), elapsed_ms)
            
            # Adaptive sizing
            if self.batch_config.adaptive_sizing:
                self._update_adaptive_size(topic, len(batch), elapsed_ms)
            
        except Exception as e:
            logger.error(f"Batch publish error for {topic}: {e}")
            # Set exception on all futures
            for msg in batch:
                if not msg.future.done():
                    msg.future.set_exception(e)
            self._record_error(topic, len(batch))
    
    def _start_batch_timer(self, topic: str):
        """Start timer for batch timeout"""
        def timeout_callback():
            if self._should_flush_batch(topic):
                self._flush_batch(topic)
        
        timer = threading.Timer(
            self.batch_config.max_latency_ms / 1000.0,
            timeout_callback
        )
        timer.daemon = True
        timer.start()
        self._batch_timers[topic] = timer
    
    def _update_stats(self, topic: str, messages: int, bytes_sent: int, latency_ms: float):
        """Update publishing statistics"""
        with self._stats_lock:
            stats = self._stats[topic]
            stats.messages_sent += messages
            stats.bytes_sent += bytes_sent
            stats.batches_sent += 1
            
            # Update moving averages
            alpha = 0.1  # Exponential smoothing factor
            stats.avg_batch_size = (1 - alpha) * stats.avg_batch_size + alpha * messages
            stats.avg_latency_ms = (1 - alpha) * stats.avg_latency_ms + alpha * latency_ms
            
            # Calculate throughput
            if latency_ms > 0:
                mbps = (bytes_sent / 1024 / 1024) / (latency_ms / 1000)
                stats.throughput_mbps = (1 - alpha) * stats.throughput_mbps + alpha * mbps
                self._throughput_history[topic].append(mbps)
                
                # Keep only recent history
                if len(self._throughput_history[topic]) > 100:
                    self._throughput_history[topic].pop(0)
    
    def _record_error(self, topic: str, count: int = 1):
        """Record publishing error"""
        with self._stats_lock:
            self._stats[topic].errors += count
    
    def _update_adaptive_size(self, topic: str, batch_size: int, latency_ms: float):
        """Update adaptive batch size based on performance"""
        # Acknowledge parameters for future implementation
        _ = batch_size  # Will be used in adaptive sizing algorithm

        if len(self._throughput_history[topic]) < 10:
            return  # Not enough data
        
        # Calculate optimal batch size based on throughput history
        throughputs = self._throughput_history[topic][-10:]  # Last 10 samples
        avg_throughput = np.mean(throughputs)
        std_throughput = np.std(throughputs)
        
        current_size = self._adaptive_sizes[topic]
        
        # If throughput is stable and latency is low, increase batch size
        if std_throughput / avg_throughput < 0.2 and latency_ms < self.batch_config.max_latency_ms * 0.8:
            new_size = min(
                int(current_size * 1.1),
                self.batch_config.max_messages
            )
        # If latency is high or throughput is unstable, decrease batch size
        elif latency_ms > self.batch_config.max_latency_ms or std_throughput / avg_throughput > 0.5:
            new_size = max(
                int(current_size * 0.9),
                10  # Minimum batch size
            )
        else:
            new_size = current_size
        
        if new_size != current_size:
            self._adaptive_sizes[topic] = new_size
            logger.debug(f"Adjusted batch size for {topic}: {current_size} -> {new_size}")
    
    def get_stats(self, topic: Optional[str] = None) -> Dict[str, BatchStats]:
        """Get publishing statistics"""
        with self._stats_lock:
            if topic:
                return {topic: self._stats[topic]}
            else:
                return dict(self._stats)
    
    def flush_all(self, timeout: float = 5.0):
        """
        Flush all pending batches.
        
        Args:
            timeout: Maximum time to wait for flush completion
        """
        start_time = time.time()
        topics = list(self._batches.keys())
        
        for topic in topics:
            if time.time() - start_time > timeout:
                logger.warning("Flush timeout reached")
                break
            
            self._flush_batch(topic)
        
        # Wait for queues to empty
        while time.time() - start_time < timeout:
            all_empty = all(q.empty() for q in self._queues.values())
            all_flushed = all(len(b) == 0 for b in self._batches.values())
            
            if all_empty and all_flushed:
                break
            
            time.sleep(0.01)
    
    def optimize_settings(self, target_latency_ms: Optional[float] = None):
        """
        Optimize batch settings based on observed performance.
        
        Args:
            target_latency_ms: Target latency (uses config if not specified)
        """
        target_latency_ms = target_latency_ms or self.batch_config.max_latency_ms
        
        with self._stats_lock:
            for topic, stats in self._stats.items():
                if stats.batches_sent < 10:
                    continue  # Not enough data
                
                # Analyze performance
                if stats.avg_latency_ms > target_latency_ms * 1.2:
                    # Latency too high, reduce batch size
                    self._adaptive_sizes[topic] = int(self._adaptive_sizes[topic] * 0.8)
                    logger.info(
                        f"Reducing batch size for {topic} due to high latency: "
                        f"{stats.avg_latency_ms:.1f}ms"
                    )
                elif stats.avg_latency_ms < target_latency_ms * 0.5:
                    # Latency very low, can increase batch size
                    self._adaptive_sizes[topic] = min(
                        int(self._adaptive_sizes[topic] * 1.2),
                        self.batch_config.max_messages
                    )
                    logger.info(
                        f"Increasing batch size for {topic} due to low latency: "
                        f"{stats.avg_latency_ms:.1f}ms"
                    )
    
    def shutdown(self, timeout: float = 10.0):
        """
        Gracefully shutdown the publisher.
        
        Args:
            timeout: Maximum time to wait for shutdown
        """
        logger.info("Shutting down Batch Publisher...")
        
        # Stop accepting new messages
        self._running = False
        
        # Flush all pending batches
        self.flush_all(timeout=timeout / 2)
        
        # Stop worker threads
        for thread in self._worker_threads:
            thread.join(timeout=1.0)
        
        # Cancel all timers
        for timer in self._batch_timers.values():
            timer.cancel()
        
        # Shutdown executor
        self._executor.shutdown(wait=True)
        
        # Log final statistics
        if self.enable_monitoring:
            for topic, stats in self._stats.items():
                logger.info(
                    f"Final stats for {topic}: "
                    f"messages={stats.messages_sent}, "
                    f"errors={stats.errors}, "
                    f"avg_batch={stats.avg_batch_size:.1f}, "
                    f"avg_latency={stats.avg_latency_ms:.1f}ms, "
                    f"throughput={stats.throughput_mbps:.1f}Mbps"
                )
        
        logger.info("Batch Publisher shutdown complete")
