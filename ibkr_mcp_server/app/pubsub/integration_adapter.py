"""
Integration Adapter for Enhanced Pub/Sub Components

This adapter bridges the enhanced Pub/Sub components with the existing
IBKR MCP Server infrastructure, providing seamless integration and
backward compatibility.
"""

import logging
from typing import Any, Dict, Optional, List
from datetime import datetime

from .pubsub_manager import PubSubManager, PubSubConfig
from .enhanced_manager import EnhancedPubSubManager
from .ibkr_pubsub_bridge import IBKRPubSubBridge

# Import external dependencies with fallbacks
try:
    from ..core.settings import ApplicationSettings
    settings = ApplicationSettings()
except ImportError:
    try:
        from ..core.config import Config
        settings = Config()
    except ImportError:
        # Fallback settings for testing
        class MockSettings:
            GCP_PROJECT_ID = "gen-lang-client-0397821884"
            ENABLE_SCHEMA_REGISTRY = False
            ENABLE_BATCH_PUBLISHER = False
            ENABLE_STREAMING_AGGREGATOR = False
            ENABLE_DISTRIBUTED_PROCESSOR = False
            ENABLE_DLQ = False
            ENABLE_MONITORING = False
            ENABLE_MULTI_REGION = False
            USE_ENHANCED_PUBSUB = True
            PUBSUB_BATCH_MAX_MESSAGES = 1000
            STRICT_SCHEMA_VALIDATION = False
            PROMETHEUS_GATEWAY = "http://localhost:9091"
            PRIMARY_REGION = "us-central1"
            ENABLED_REGIONS = ["us-central1"]

        settings = MockSettings()

try:
    from ..services.ibkr_service import IBKRService
except ImportError:
    IBKRService = None  # Will be handled as optional

try:
    from ..models.database_models import TradingSignalModel
except ImportError:
    TradingSignalModel = None

# Create a fallback TradingSignal type for type annotations
class TradingSignal:
    """TradingSignal class for type annotations"""
    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.symbol = kwargs.get('symbol')
        self.action = kwargs.get('action')
        self.quantity = kwargs.get('quantity')
        self.entry_price = kwargs.get('entry_price')
        self.stop_loss = kwargs.get('stop_loss')
        self.take_profit = kwargs.get('take_profit')
        self.confidence = kwargs.get('confidence')
        self.timestamp = kwargs.get('timestamp', datetime.now())

# Database session import removed - not used in this module


class PubSubIntegrationAdapter:
    """
    Adapter that integrates enhanced Pub/Sub components with existing infrastructure
    
    Features:
    - Seamless switching between basic and enhanced managers
    - Component lifecycle management
    - Unified interface for all Pub/Sub operations
    - Integration with IBKR services
    """
    
    def __init__(
        self,
        ibkr_service: Optional[Any] = None,
        use_enhanced: bool = True,
        enable_components: Optional[Dict[str, bool]] = None
    ):
        """
        Initialize the integration adapter
        
        Args:
            ibkr_service: IBKR service instance
            use_enhanced: Whether to use enhanced Pub/Sub manager
            enable_components: Override component enablement settings
        """
        self.ibkr_service = ibkr_service
        self.use_enhanced = use_enhanced
        self.logger = logging.getLogger(__name__)
        
        # Component enablement
        self.enable_components = enable_components or {
            'schema_registry': getattr(settings, 'ENABLE_SCHEMA_REGISTRY', False),
            'batch_publisher': getattr(settings, 'ENABLE_BATCH_PUBLISHER', False),
            'streaming_aggregator': getattr(settings, 'ENABLE_STREAMING_AGGREGATOR', False),
            'distributed_processor': getattr(settings, 'ENABLE_DISTRIBUTED_PROCESSOR', False),
            'dlq_manager': getattr(settings, 'ENABLE_DLQ', False),
            'monitoring_service': getattr(settings, 'ENABLE_MONITORING', False),
            'multi_region': getattr(settings, 'ENABLE_MULTI_REGION', False)
        }
        
        # Initialize manager
        self.manager: Optional[PubSubManager] = None
        
        # Component registry
        self._components: Dict[str, Any] = {}
        
        # Bridge for IBKR integration
        self.bridge: Optional[IBKRPubSubBridge] = None
        
        # Statistics
        self.stats = {
            'initialized': False,
            'components_loaded': 0,
            'messages_processed': 0
        }
    
    async def initialize(self):
        """Initialize all components and establish connections"""
        try:
            # Initialize Pub/Sub manager
            if self.use_enhanced:
                self.manager = EnhancedPubSubManager()
                self.logger.info("Using Enhanced Pub/Sub Manager")
            else:
                config = PubSubConfig()
                self.manager = PubSubManager(config)
                self.logger.info("Using Basic Pub/Sub Manager")
            
            await self.manager.initialize()
            
            # Initialize IBKR bridge if service available
            if self.ibkr_service and self.manager:
                self.bridge = IBKRPubSubBridge(self.ibkr_service, self.manager)
                self.logger.info("IBKR Bridge initialized")
            
            # Initialize optional components
            await self._initialize_components()
            
            self.stats['initialized'] = True
            self.logger.info(
                f"Pub/Sub Integration Adapter initialized with "
                f"{self.stats['components_loaded']} components"
            )
            
        except Exception as e:
            self.logger.error(f"Failed to initialize adapter: {e}")
            raise
    
    async def _initialize_components(self):
        """Initialize optional components based on configuration"""
        # Schema Registry
        if self.enable_components.get('schema_registry'):
            try:
                from .schema_registry import SchemaRegistry as MessageSchemaRegistry
                self._components['schema_registry'] = MessageSchemaRegistry(
                    getattr(settings, 'GCP_PROJECT_ID', 'gen-lang-client-0397821884')
                )
                self.stats['components_loaded'] += 1
                self.logger.info("Schema Registry component loaded")
            except Exception as e:
                self.logger.warning(f"Failed to load Schema Registry: {e}")
        
        # Batch Publisher
        if self.enable_components.get('batch_publisher'):
            try:
                from .batch_publisher import BatchPublisher
                if self.manager and hasattr(self.manager, 'publisher'):
                    self._components['batch_publisher'] = BatchPublisher(
                        project_id=self.manager.config.project_id,
                        max_batch_size=getattr(settings, 'PUBSUB_BATCH_MAX_MESSAGES', 1000)
                    )
                    await self._components['batch_publisher'].start()
                    self.stats['components_loaded'] += 1
                    self.logger.info("Batch Publisher component loaded")
            except Exception as e:
                self.logger.warning(f"Failed to load Batch Publisher: {e}")
        
        # Streaming Aggregator - disabled due to constructor issues
        if self.enable_components.get('streaming_aggregator'):
            try:
                # TODO: Fix StreamingAggregator constructor parameters
                self.logger.warning("Streaming Aggregator component disabled - constructor needs fixing")
            except Exception as e:
                self.logger.warning(f"Failed to load Streaming Aggregator: {e}")
        
        # DLQ Manager
        if self.enable_components.get('dlq_manager'):
            try:
                from .dlq_manager import DeadLetterQueueManager
                self._components['dlq_manager'] = DeadLetterQueueManager(
                    getattr(settings, 'GCP_PROJECT_ID', 'gen-lang-client-0397821884'),
                    dlq_suffix="_dlq",
                    default_retention_days=7
                )
                await self._components['dlq_manager'].initialize()
                self.stats['components_loaded'] += 1
                self.logger.info("DLQ Manager component loaded")
            except Exception as e:
                self.logger.warning(f"Failed to load DLQ Manager: {e}")
        
        # Monitoring Service
        if self.enable_components.get('monitoring_service'):
            try:
                from .monitoring_service import MonitoringService
                self._components['monitoring_service'] = MonitoringService(
                    getattr(settings, 'GCP_PROJECT_ID', 'gen-lang-client-0397821884'),
                    prometheus_gateway=getattr(settings, 'PROMETHEUS_GATEWAY', 'http://localhost:9091')
                )
                await self._components['monitoring_service'].initialize()
                self.stats['components_loaded'] += 1
                self.logger.info("Monitoring Service component loaded")
            except Exception as e:
                self.logger.warning(f"Failed to load Monitoring Service: {e}")
        
        # Multi-Region Manager
        if self.enable_components.get('multi_region'):
            try:
                from .multi_region_manager import MultiRegionManager, Region
                primary_region_name = getattr(settings, 'PRIMARY_REGION', 'us-central1')
                primary_region = Region[primary_region_name.upper().replace('-', '_')]
                self._components['multi_region'] = MultiRegionManager(
                    primary_region=primary_region,
                    project_id=getattr(settings, 'GCP_PROJECT_ID', 'gen-lang-client-0397821884'),
                    topic_prefix="ibkr"
                )
                # Initialize with configured regions
                enabled_regions = getattr(settings, 'ENABLED_REGIONS', ['us-central1'])
                regions = [Region[r.upper().replace('-', '_')] for r in enabled_regions]
                await self._components['multi_region'].initialize(regions)
                self.stats['components_loaded'] += 1
                self.logger.info("Multi-Region Manager component loaded")
            except Exception as e:
                self.logger.warning(f"Failed to load Multi-Region Manager: {e}")
    
    async def publish_market_data(
        self,
        symbol: str,
        data: Dict[str, Any],
        validate_schema: bool = True
    ) -> str:
        """
        Publish market data with optional schema validation
        
        Args:
            symbol: Trading symbol
            data: Market data dictionary
            validate_schema: Whether to validate against schema
            
        Returns:
            Published message ID
        """
        if not self.manager:
            raise RuntimeError("Manager not initialized")
        
        # Schema validation if requested and available
        if validate_schema and 'schema_registry' in self._components:
            try:
                registry = self._components['schema_registry']
                await registry.validate_message(data, 'market_data', '1.0')
            except Exception as e:
                self.logger.error(f"Schema validation failed: {e}")
                if getattr(settings, 'STRICT_SCHEMA_VALIDATION', False):
                    raise
        
        # Use batch publisher if available for high-throughput data
        if 'batch_publisher' in self._components and len(data) > 10:
            publisher = self._components['batch_publisher']
            return await publisher.publish_async(
                data,
                topic='market-data',
                attributes={'symbol': symbol, 'type': 'market_data'}
            )
        
        # Use regular publishing
        return await self.manager.publish_market_data(symbol, data)
    
    async def publish_trading_signal(
        self,
        signal: "TradingSignal",
        strategy_id: str
    ) -> str:
        """
        Publish trading signal with persistence
        
        Args:
            signal: Trading signal object
            strategy_id: Strategy identifier
            
        Returns:
            Published message ID
        """
        if not self.manager:
            raise RuntimeError("Manager not initialized")
        
        # Convert signal to dictionary
        signal_data = {
            'signal_id': str(signal.id),
            'symbol': signal.symbol,
            'action': signal.action,
            'quantity': signal.quantity,
            'entry_price': float(signal.entry_price) if signal.entry_price else None,
            'stop_loss': float(signal.stop_loss) if signal.stop_loss else None,
            'take_profit': float(signal.take_profit) if signal.take_profit else None,
            'confidence': signal.confidence,
            'timestamp': signal.timestamp.isoformat()
        }
        
        # Publish signal
        message_id = await self.manager.publish_trading_signal(strategy_id, signal_data)
        
        # Track in monitoring if available
        if 'monitoring_service' in self._components:
            monitoring = self._components['monitoring_service']
            monitoring.record_metric(
                'trading_signals_published',
                1,
                labels={'strategy': strategy_id, 'symbol': signal.symbol}
            )
        
        return message_id
    
    async def process_failed_message(
        self,
        message: Any,
        error: Exception,
        topic: str,
        subscription: str
    ) -> bool:
        """
        Process failed message using DLQ if available
        
        Args:
            message: Failed message
            error: Exception that caused failure
            topic: Source topic
            subscription: Source subscription
            
        Returns:
            True if handled, False otherwise
        """
        if 'dlq_manager' not in self._components:
            self.logger.error(f"No DLQ manager available to handle failed message: {error}")
            return False
        
        try:
            dlq_manager = self._components['dlq_manager']
            await dlq_manager.send_to_dlq(
                message,
                topic,
                subscription,
                error
            )
            
            self.logger.info(f"Failed message sent to DLQ: {message.message_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send message to DLQ: {e}")
            return False
    
    async def aggregate_market_data(
        self,
        symbol: str,
        data_points: List[Dict[str, Any]],
        window_type: str = 'time_based'
    ) -> Dict[str, Any]:
        """
        Aggregate market data using streaming aggregator
        
        Args:
            symbol: Trading symbol
            data_points: List of market data points
            window_type: Type of aggregation window
            
        Returns:
            Aggregated results
        """
        if 'streaming_aggregator' not in self._components:
            # Fallback to simple aggregation
            return self._simple_aggregation(data_points, window_type)
        
        aggregator = self._components['streaming_aggregator']
        
        # Feed data points to aggregator
        for point in data_points:
            await aggregator.process(point)
        
        # Get aggregated results
        return await aggregator.get_aggregates(symbol)
    
    def _simple_aggregation(self, data_points: List[Dict[str, Any]], window_type: str = 'time_based') -> Dict[str, Any]:
        """Simple fallback aggregation when streaming aggregator not available"""
        if not data_points:
            return {}
        
        prices = [p.get('price', 0) for p in data_points if p.get('price')]
        volumes = [p.get('volume', 0) for p in data_points if p.get('volume')]
        
        return {
            'count': len(data_points),
            'avg_price': sum(prices) / len(prices) if prices else 0,
            'total_volume': sum(volumes),
            'min_price': min(prices) if prices else 0,
            'max_price': max(prices) if prices else 0,
            'window_type': window_type
        }
    
    def get_component(self, name: str) -> Optional[Any]:
        """Get a specific component by name"""
        return self._components.get(name)
    
    async def get_status(self) -> Dict[str, Any]:
        """Get comprehensive status of the adapter and all components"""
        status = {
            'initialized': self.stats['initialized'],
            'use_enhanced': self.use_enhanced,
            'components_loaded': self.stats['components_loaded'],
            'components': {},
            'manager_stats': {}
        }
        
        # Get manager statistics
        if self.manager:
            try:
                # Try enhanced statistics first
                status['manager_stats'] = getattr(self.manager, 'get_enhanced_statistics', lambda: {})()
            except (AttributeError, TypeError):
                try:
                    # Fall back to basic statistics
                    status['manager_stats'] = getattr(self.manager, 'get_statistics', lambda: {})()
                except (AttributeError, TypeError):
                    status['manager_stats'] = {'error': 'No statistics method available'}
        
        # Get component status
        for name, component in self._components.items():
            status['components'][name] = {
                'loaded': True,
                'type': type(component).__name__
            }
            
            # Add component-specific status if available
            if hasattr(component, 'get_status'):
                try:
                    component_status = await component.get_status()
                    status['components'][name].update(component_status)
                except:
                    pass
        
        return status
    
    async def shutdown(self):
        """Shutdown all components gracefully"""
        self.logger.info("Shutting down Pub/Sub Integration Adapter")
        
        # Shutdown components in reverse order
        for name in reversed(list(self._components.keys())):
            component = self._components[name]
            if hasattr(component, 'shutdown'):
                try:
                    await component.shutdown()
                    self.logger.info(f"Shutdown component: {name}")
                except Exception as e:
                    self.logger.error(f"Error shutting down {name}: {e}")
        
        # Shutdown manager
        if self.manager:
            await self.manager.shutdown()
        
        self.stats['initialized'] = False
        self.logger.info("Pub/Sub Integration Adapter shutdown complete")


# Singleton instance for the application
_adapter_instance: Optional[PubSubIntegrationAdapter] = None


async def get_pubsub_adapter(
    ibkr_service: Optional[Any] = None
) -> PubSubIntegrationAdapter:
    """
    Get or create the Pub/Sub adapter instance
    
    This function ensures a single adapter instance is used throughout
    the application lifecycle.
    """
    global _adapter_instance
    
    if _adapter_instance is None:
        _adapter_instance = PubSubIntegrationAdapter(
            ibkr_service=ibkr_service,
            use_enhanced=getattr(settings, 'USE_ENHANCED_PUBSUB', True)
        )
        await _adapter_instance.initialize()
    
    return _adapter_instance


# FastAPI integration example
def setup_pubsub_adapter(app):
    """
    Setup Pub/Sub adapter for FastAPI application
    
    Usage:
        app = FastAPI()
        setup_pubsub_adapter(app)
    """
    @app.on_event("startup")
    async def startup_pubsub():
        # Get IBKR service if available
        ibkr_service = getattr(app.state, 'ibkr_service', None)
        
        # Initialize adapter
        app.state.pubsub_adapter = await get_pubsub_adapter(ibkr_service)
        
        # Register message handlers
        if app.state.pubsub_adapter.manager:
            # Example: Register market data handler
            async def handle_market_data(_message):
                # Process market data (placeholder implementation)
                # _message parameter intentionally unused in example
                pass
            
            app.state.pubsub_adapter.manager.register_message_handler(
                'market_data_update',
                handle_market_data
            )
    
    @app.on_event("shutdown")
    async def shutdown_pubsub():
        if hasattr(app.state, 'pubsub_adapter'):
            await app.state.pubsub_adapter.shutdown()
