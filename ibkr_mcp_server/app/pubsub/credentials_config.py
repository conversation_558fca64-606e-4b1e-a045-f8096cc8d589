"""
Google Cloud Pub/Sub Credentials Configuration

Manages Google Cloud service account credentials and project configuration
for the IBKR MCP server Pub/Sub integration.
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from google.oauth2 import service_account
from google.auth import default

logger = logging.getLogger(__name__)


class GCPCredentialsManager:
    """Manages Google Cloud Platform credentials for Pub/Sub integration"""
    
    def __init__(self):
        # Service Account Information
        self.service_account_name = "IBKR"
        self.service_account_id = "ibkr-684"
        self.service_account_email = "<EMAIL>"
        self.api_key = "AIzaSyAG6lxGOgliAxjvDsXV8xCs11rhUZbXemY"
        self.project_id = "gen-lang-client-**********"
        
        # Credentials
        self.credentials = None
        self.credentials_path = None
        
    def create_service_account_key_file(self, output_path: Optional[str] = None) -> str:
        """
        Create a service account key file for authentication.
        
        Note: This creates a template. You'll need to download the actual
        private key from Google Cloud Console.
        
        Args:
            output_path: Path where to save the key file
            
        Returns:
            Path to the created key file
        """
        if output_path is None:
            output_path = os.path.join(os.path.dirname(__file__), "gcp-service-account-key.json")
        
        # Template service account key structure
        # Note: You need to replace this with the actual private key from GCP Console
        service_account_key = {
            "type": "service_account",
            "project_id": self.project_id,
            "private_key_id": "REPLACE_WITH_ACTUAL_PRIVATE_KEY_ID",
            "private_key": "-----BEGIN PRIVATE KEY-----\nREPLACE_WITH_ACTUAL_PRIVATE_KEY\n-----END PRIVATE KEY-----\n",
            "client_email": self.service_account_email,
            "client_id": "REPLACE_WITH_ACTUAL_CLIENT_ID",
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
            "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
            "client_x509_cert_url": f"https://www.googleapis.com/robot/v1/metadata/x509/{self.service_account_email}",
            "universe_domain": "googleapis.com"
        }
        
        try:
            with open(output_path, 'w') as f:
                json.dump(service_account_key, f, indent=2)
            
            logger.info(f"Service account key template created at: {output_path}")
            logger.warning("⚠️  IMPORTANT: Replace template values with actual private key from GCP Console!")
            
            return output_path
            
        except Exception as e:
            logger.error(f"Failed to create service account key file: {e}")
            raise
    
    def load_credentials_from_file(self, key_file_path: str) -> bool:
        """
        Load credentials from service account key file.
        
        Args:
            key_file_path: Path to the service account key JSON file
            
        Returns:
            True if credentials loaded successfully
        """
        try:
            if not os.path.exists(key_file_path):
                logger.error(f"Service account key file not found: {key_file_path}")
                return False
            
            self.credentials = service_account.Credentials.from_service_account_file(
                key_file_path,
                scopes=['https://www.googleapis.com/auth/pubsub']
            )
            
            self.credentials_path = key_file_path
            logger.info("✅ Google Cloud credentials loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load credentials: {e}")
            return False
    
    def load_credentials_from_environment(self) -> bool:
        """
        Load credentials from environment variables or default credentials.
        
        Returns:
            True if credentials loaded successfully
        """
        try:
            # Try to use Application Default Credentials (ADC)
            self.credentials, project = default(
                scopes=['https://www.googleapis.com/auth/pubsub']
            )
            
            if project:
                self.project_id = project
            
            logger.info("✅ Google Cloud credentials loaded from environment")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load credentials from environment: {e}")
            return False
    
    def setup_environment_variables(self) -> Dict[str, str]:
        """
        Set up environment variables for Google Cloud authentication.
        
        Returns:
            Dictionary of environment variables to set
        """
        env_vars = {
            'GOOGLE_CLOUD_PROJECT': self.project_id,
            'GOOGLE_APPLICATION_CREDENTIALS': self.credentials_path or '',
            'GCP_SERVICE_ACCOUNT_EMAIL': self.service_account_email,
            'GCP_API_KEY': self.api_key
        }
        
        # Set environment variables
        for key, value in env_vars.items():
            if value:
                os.environ[key] = value
        
        logger.info("✅ Google Cloud environment variables configured")
        return env_vars
    
    def validate_credentials(self) -> bool:
        """
        Validate that credentials are properly configured.
        
        Returns:
            True if credentials are valid
        """
        try:
            if not self.credentials:
                logger.error("❌ No credentials loaded")
                return False
            
            # Try to refresh credentials to validate them
            try:
                if hasattr(self.credentials, 'refresh') and callable(getattr(self.credentials, 'refresh', None)):
                    from google.auth.transport.requests import Request
                    self.credentials.refresh(Request())  # type: ignore
                else:
                    # For credentials that don't support refresh, just check if they exist
                    logger.debug("Credentials don't support refresh, skipping validation")
            except Exception as refresh_error:
                logger.warning(f"Credential refresh failed, but credentials may still be valid: {refresh_error}")
            
            logger.info("✅ Credentials validation successful")
            return True
            
        except Exception as e:
            logger.error(f"❌ Credentials validation failed: {e}")
            return False
    
    def get_project_info(self) -> Dict[str, Any]:
        """Get project and service account information"""
        return {
            'project_id': self.project_id,
            'service_account_name': self.service_account_name,
            'service_account_id': self.service_account_id,
            'service_account_email': self.service_account_email,
            'credentials_loaded': self.credentials is not None,
            'credentials_path': self.credentials_path
        }
    
    def initialize(self, key_file_path: Optional[str] = None) -> bool:
        """
        Initialize credentials with automatic fallback.
        
        Args:
            key_file_path: Optional path to service account key file
            
        Returns:
            True if initialization successful
        """
        logger.info("🔐 Initializing Google Cloud credentials...")
        
        # Try to load from file first
        if key_file_path and self.load_credentials_from_file(key_file_path):
            self.setup_environment_variables()
            return self.validate_credentials()
        
        # Fallback to environment credentials
        if self.load_credentials_from_environment():
            self.setup_environment_variables()
            return self.validate_credentials()
        
        logger.error("❌ Failed to initialize Google Cloud credentials")
        logger.info("💡 Please ensure you have:")
        logger.info("   1. Downloaded service account key from GCP Console")
        logger.info("   2. Set GOOGLE_APPLICATION_CREDENTIALS environment variable")
        logger.info("   3. Or configured Application Default Credentials")
        
        return False


# Global credentials manager instance
gcp_credentials = GCPCredentialsManager()
