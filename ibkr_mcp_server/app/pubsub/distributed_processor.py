"""
Distributed Processor for Google Cloud Pub/Sub

This module implements a distributed processing system with worker pool management,
task distribution, load balancing, fault tolerance, and horizontal scaling support.

Features:
- Dynamic worker pool management with auto-scaling
- Task distribution with load balancing algorithms
- Fault tolerance with retry logic and circuit breakers
- Horizontal scaling with coordination through Pub/Sub
- Processing guarantees (at-least-once, at-most-once, exactly-once)
- Metrics and monitoring integration

Author: IBKR Integration Team
Date: 2024
"""

import asyncio
import concurrent.futures
import json
import logging
import multiprocessing
import threading
import time
import uuid
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Callable, Dict, Optional, Set

from google.cloud import pubsub_v1
from google.cloud.pubsub_v1.subscriber import message as subscriber_message
from prometheus_client import Counter, Gauge, Histogram

logger = logging.getLogger(__name__)


# Metrics
MESSAGES_PROCESSED = Counter(
    'distributed_processor_messages_processed_total',
    'Total messages processed',
    ['processor_id', 'status', 'worker_id']
)

PROCESSING_TIME = Histogram(
    'distributed_processor_processing_duration_seconds',
    'Time spent processing messages',
    ['processor_id', 'worker_id'],
    buckets=[0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0]
)

WORKER_POOL_SIZE = Gauge(
    'distributed_processor_worker_pool_size',
    'Current worker pool size',
    ['processor_id', 'pool_type']
)

QUEUE_SIZE = Gauge(
    'distributed_processor_queue_size',
    'Current queue size',
    ['processor_id', 'queue_name']
)

WORKER_UTILIZATION = Gauge(
    'distributed_processor_worker_utilization_percent',
    'Worker utilization percentage',
    ['processor_id', 'worker_id']
)

TASK_RETRY_COUNT = Counter(
    'distributed_processor_task_retry_total',
    'Total task retries',
    ['processor_id', 'worker_id', 'reason']
)

COORDINATOR_HEARTBEAT = Gauge(
    'distributed_processor_coordinator_heartbeat_timestamp',
    'Last heartbeat timestamp from coordinator',
    ['processor_id', 'instance_id']
)


class ProcessingGuarantee(Enum):
    """Processing guarantee levels"""
    AT_LEAST_ONCE = "at_least_once"
    AT_MOST_ONCE = "at_most_once"
    EXACTLY_ONCE = "exactly_once"


class LoadBalancingStrategy(Enum):
    """Load balancing strategies for task distribution"""
    ROUND_ROBIN = "round_robin"
    LEAST_LOADED = "least_loaded"
    HASH_BASED = "hash_based"
    RANDOM = "random"
    WEIGHTED = "weighted"


class WorkerState(Enum):
    """Worker state enumeration"""
    IDLE = "idle"
    PROCESSING = "processing"
    PAUSED = "paused"
    TERMINATING = "terminating"
    FAILED = "failed"


@dataclass
class WorkerStats:
    """Statistics for a worker"""
    worker_id: str
    messages_processed: int = 0
    messages_failed: int = 0
    total_processing_time: float = 0.0
    last_message_time: Optional[datetime] = None
    current_load: int = 0
    max_load: int = 10
    state: WorkerState = WorkerState.IDLE
    error_count: int = 0
    consecutive_errors: int = 0


@dataclass
class ProcessingTask:
    """Represents a processing task"""
    task_id: str
    message: subscriber_message.Message
    processor_func: Callable
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    attempts: int = 0
    max_attempts: int = 3
    priority: int = 0
    partition_key: Optional[str] = None
    deadline: Optional[datetime] = None


@dataclass
class WorkerConfig:
    """Configuration for worker pools"""
    min_workers: int = 2
    max_workers: int = 10
    scale_up_threshold: float = 0.8  # 80% utilization
    scale_down_threshold: float = 0.2  # 20% utilization
    scale_check_interval: int = 30  # seconds
    worker_timeout: int = 300  # 5 minutes
    max_concurrent_tasks: int = 10
    enable_cpu_pool: bool = True
    cpu_pool_size: Optional[int] = None  # None = CPU count


class Worker:
    """Individual worker that processes tasks"""
    
    def __init__(
        self,
        worker_id: str,
        processor_id: str,
        task_queue: asyncio.Queue,
        result_queue: asyncio.Queue,
        stats: WorkerStats,
        max_concurrent_tasks: int = 10
    ):
        self.worker_id = worker_id
        self.processor_id = processor_id
        self.task_queue = task_queue
        self.result_queue = result_queue
        self.stats = stats
        self.max_concurrent_tasks = max_concurrent_tasks
        self._running = False
        self._tasks: Set[asyncio.Task] = set()
        self._semaphore = asyncio.Semaphore(max_concurrent_tasks)
        
    async def start(self):
        """Start the worker"""
        self._running = True
        self.stats.state = WorkerState.IDLE
        logger.info(f"Worker {self.worker_id} started")
        
        try:
            while self._running:
                try:
                    # Wait for task with timeout
                    task = await asyncio.wait_for(
                        self.task_queue.get(),
                        timeout=1.0
                    )
                    
                    # Create processing task
                    asyncio.create_task(self._process_task(task))
                    
                except asyncio.TimeoutError:
                    # Check if we should continue
                    continue
                    
        except Exception as e:
            logger.error(f"Worker {self.worker_id} error: {e}")
            self.stats.state = WorkerState.FAILED
            self.stats.error_count += 1
            
    async def stop(self):
        """Stop the worker gracefully"""
        logger.info(f"Stopping worker {self.worker_id}")
        self._running = False
        self.stats.state = WorkerState.TERMINATING
        
        # Wait for current tasks to complete
        if self._tasks:
            await asyncio.gather(*self._tasks, return_exceptions=True)
            
        self.stats.state = WorkerState.IDLE
        
    async def _process_task(self, task: ProcessingTask):
        """Process a single task"""
        async with self._semaphore:
            self.stats.current_load += 1
            self.stats.state = WorkerState.PROCESSING
            
            # Update metrics
            WORKER_UTILIZATION.labels(
                processor_id=self.processor_id,
                worker_id=self.worker_id
            ).set(self.stats.current_load / self.max_concurrent_tasks * 100)
            
            start_time = time.time()
            success = False
            
            try:
                # Check deadline
                if task.deadline and datetime.now(timezone.utc) > task.deadline:
                    raise TimeoutError(f"Task {task.task_id} exceeded deadline")
                
                # Process the message
                result = await self._execute_processor(task)
                
                # Acknowledge message
                task.message.ack()
                
                # Update stats
                self.stats.messages_processed += 1
                self.stats.consecutive_errors = 0
                success = True
                
                # Record result
                await self.result_queue.put({
                    'task_id': task.task_id,
                    'worker_id': self.worker_id,
                    'status': 'success',
                    'result': result,
                    'processing_time': time.time() - start_time
                })
                
            except Exception as e:
                logger.error(f"Worker {self.worker_id} failed to process task {task.task_id}: {e}")
                
                # Update error stats
                self.stats.messages_failed += 1
                self.stats.consecutive_errors += 1
                self.stats.error_count += 1
                
                # Handle retry
                await self._handle_task_failure(task, e)
                
            finally:
                # Update metrics
                processing_time = time.time() - start_time
                self.stats.total_processing_time += processing_time
                self.stats.last_message_time = datetime.now(timezone.utc)
                self.stats.current_load -= 1
                
                if self.stats.current_load == 0:
                    self.stats.state = WorkerState.IDLE
                
                MESSAGES_PROCESSED.labels(
                    processor_id=self.processor_id,
                    status='success' if success else 'failed',
                    worker_id=self.worker_id
                ).inc()
                
                PROCESSING_TIME.labels(
                    processor_id=self.processor_id,
                    worker_id=self.worker_id
                ).observe(processing_time)
                
                WORKER_UTILIZATION.labels(
                    processor_id=self.processor_id,
                    worker_id=self.worker_id
                ).set(self.stats.current_load / self.max_concurrent_tasks * 100)
                
    async def _execute_processor(self, task: ProcessingTask) -> Any:
        """Execute the processor function"""
        if asyncio.iscoroutinefunction(task.processor_func):
            return await task.processor_func(task.message)
        else:
            # Run in thread pool for blocking functions
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                None,
                task.processor_func,
                task.message
            )
            
    async def _handle_task_failure(self, task: ProcessingTask, error: Exception):
        """Handle task failure with retry logic"""
        task.attempts += 1
        
        if task.attempts < task.max_attempts:
            # Requeue for retry
            TASK_RETRY_COUNT.labels(
                processor_id=self.processor_id,
                worker_id=self.worker_id,
                reason=type(error).__name__
            ).inc()
            
            # Add exponential backoff
            await asyncio.sleep(2 ** task.attempts)
            
            # Requeue task
            await self.task_queue.put(task)
            
        else:
            # Max retries exceeded, nack the message
            task.message.nack()
            
            # Record failure
            await self.result_queue.put({
                'task_id': task.task_id,
                'worker_id': self.worker_id,
                'status': 'failed',
                'error': str(error),
                'attempts': task.attempts,
                'processing_time': 0
            })


class WorkerPool:
    """Manages a pool of workers"""
    
    def __init__(
        self,
        pool_id: str,
        processor_id: str,
        config: WorkerConfig,
        task_queue: asyncio.Queue,
        result_queue: asyncio.Queue
    ):
        self.pool_id = pool_id
        self.processor_id = processor_id
        self.config = config
        self.task_queue = task_queue
        self.result_queue = result_queue
        self.workers: Dict[str, Worker] = {}
        self.worker_stats: Dict[str, WorkerStats] = {}
        self._running = False
        self._scale_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
        
    async def start(self):
        """Start the worker pool"""
        self._running = True
        
        # Start initial workers
        for i in range(self.config.min_workers):
            await self._add_worker()
            
        # Start scaling monitor
        self._scale_task = asyncio.create_task(self._monitor_and_scale())
        
        logger.info(f"Worker pool {self.pool_id} started with {len(self.workers)} workers")
        
    async def stop(self):
        """Stop the worker pool"""
        self._running = False
        
        # Cancel scaling task
        if self._scale_task:
            self._scale_task.cancel()
            
        # Stop all workers
        tasks = []
        for worker in self.workers.values():
            tasks.append(worker.stop())
            
        await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info(f"Worker pool {self.pool_id} stopped")
        
    async def _add_worker(self) -> Optional[str]:
        """Add a new worker to the pool"""
        async with self._lock:
            if len(self.workers) >= self.config.max_workers:
                return None
                
            worker_id = f"{self.pool_id}-worker-{uuid.uuid4().hex[:8]}"
            stats = WorkerStats(worker_id=worker_id, max_load=self.config.max_concurrent_tasks)
            self.worker_stats[worker_id] = stats
            
            worker = Worker(
                worker_id=worker_id,
                processor_id=self.processor_id,
                task_queue=self.task_queue,
                result_queue=self.result_queue,
                stats=stats,
                max_concurrent_tasks=self.config.max_concurrent_tasks
            )
            
            self.workers[worker_id] = worker
            asyncio.create_task(worker.start())
            
            # Update metrics
            WORKER_POOL_SIZE.labels(
                processor_id=self.processor_id,
                pool_type=self.pool_id
            ).set(len(self.workers))
            
            logger.info(f"Added worker {worker_id} to pool {self.pool_id}")
            return worker_id
            
    async def _remove_worker(self, worker_id: str):
        """Remove a worker from the pool"""
        async with self._lock:
            if worker_id not in self.workers:
                return
                
            if len(self.workers) <= self.config.min_workers:
                return
                
            worker = self.workers[worker_id]
            await worker.stop()
            
            del self.workers[worker_id]
            del self.worker_stats[worker_id]
            
            # Update metrics
            WORKER_POOL_SIZE.labels(
                processor_id=self.processor_id,
                pool_type=self.pool_id
            ).set(len(self.workers))
            
            logger.info(f"Removed worker {worker_id} from pool {self.pool_id}")
            
    async def _monitor_and_scale(self):
        """Monitor pool utilization and scale workers"""
        while self._running:
            try:
                await asyncio.sleep(self.config.scale_check_interval)
                
                # Calculate pool utilization
                total_capacity = sum(s.max_load for s in self.worker_stats.values())
                current_load = sum(s.current_load for s in self.worker_stats.values())
                
                if total_capacity > 0:
                    utilization = current_load / total_capacity
                    
                    # Scale up if needed
                    if utilization > self.config.scale_up_threshold:
                        workers_to_add = min(
                            2,  # Add up to 2 workers at a time
                            self.config.max_workers - len(self.workers)
                        )
                        for _ in range(workers_to_add):
                            await self._add_worker()
                            
                    # Scale down if needed
                    elif utilization < self.config.scale_down_threshold:
                        # Find least loaded workers
                        idle_workers = [
                            worker_id for worker_id, stats in self.worker_stats.items()
                            if stats.state == WorkerState.IDLE and stats.current_load == 0
                        ]
                        
                        # Remove one idle worker
                        if idle_workers and len(self.workers) > self.config.min_workers:
                            await self._remove_worker(idle_workers[0])
                            
                # Check for failed workers
                failed_workers = [
                    worker_id for worker_id, stats in self.worker_stats.items()
                    if stats.state == WorkerState.FAILED or stats.consecutive_errors > 5
                ]
                
                for worker_id in failed_workers:
                    logger.warning(f"Replacing failed worker {worker_id}")
                    await self._remove_worker(worker_id)
                    await self._add_worker()
                    
            except Exception as e:
                logger.error(f"Error in scaling monitor: {e}")


class DistributedProcessor:
    """
    Distributed processor for handling Pub/Sub messages with worker pools,
    load balancing, fault tolerance, and horizontal scaling.
    
    Example usage:
    ```python
    async def process_market_data(message):
        # Process market data message
        data = json.loads(message.data.decode('utf-8'))
        # Perform processing...
        return data
        
    # Create processor
    processor = DistributedProcessor(
        processor_id="market-data-processor",
        subscription_name="projects/my-project/subscriptions/market-data-sub",
        processor_func=process_market_data,
        guarantee=ProcessingGuarantee.EXACTLY_ONCE,
        worker_config=WorkerConfig(min_workers=4, max_workers=20)
    )
    
    # Start processing
    await processor.start()
    
    # Stop when done
    await processor.stop()
    ```
    """
    
    def __init__(
        self,
        processor_id: str,
        subscription_name: str,
        processor_func: Callable,
        guarantee: ProcessingGuarantee = ProcessingGuarantee.AT_LEAST_ONCE,
        load_balancing: LoadBalancingStrategy = LoadBalancingStrategy.LEAST_LOADED,
        worker_config: Optional[WorkerConfig] = None,
        max_messages: int = 100,
        enable_coordinator: bool = True,
        coordinator_topic: Optional[str] = None
    ):
        self.processor_id = processor_id
        self.instance_id = f"{processor_id}-{uuid.uuid4().hex[:8]}"
        self.subscription_name = subscription_name
        self.processor_func = processor_func
        self.guarantee = guarantee
        self.load_balancing = load_balancing
        self.worker_config = worker_config or WorkerConfig()
        self.max_messages = max_messages
        self.enable_coordinator = enable_coordinator
        self.coordinator_topic = coordinator_topic
        
        # Queues
        self.task_queue = asyncio.Queue(maxsize=max_messages * 2)
        self.result_queue = asyncio.Queue()
        
        # Worker pools
        self.async_pool: Optional[WorkerPool] = None
        self.cpu_pool: Optional[concurrent.futures.ProcessPoolExecutor] = None
        
        # Subscriber
        self.subscriber_client = pubsub_v1.SubscriberClient()
        self.flow_control = pubsub_v1.types.FlowControl(max_messages=max_messages)
        
        # State
        self._running = False
        self._subscriber_future = None
        self._coordinator_task: Optional[asyncio.Task] = None
        self._result_processor_task: Optional[asyncio.Task] = None
        self._round_robin_counter = 0
        self._processed_ids: Set[str] = set()  # For exactly-once
        self._processed_ids_lock = threading.Lock()
        
        # Initialize CPU pool if enabled
        if self.worker_config.enable_cpu_pool:
            cpu_count = self.worker_config.cpu_pool_size or multiprocessing.cpu_count()
            self.cpu_pool = concurrent.futures.ProcessPoolExecutor(max_workers=cpu_count)
            
    async def start(self):
        """Start the distributed processor"""
        if self._running:
            return
            
        self._running = True
        logger.info(f"Starting distributed processor {self.processor_id}")
        
        # Start worker pool
        self.async_pool = WorkerPool(
            pool_id="async",
            processor_id=self.processor_id,
            config=self.worker_config,
            task_queue=self.task_queue,
            result_queue=self.result_queue
        )
        await self.async_pool.start()
        
        # Start result processor
        self._result_processor_task = asyncio.create_task(self._process_results())
        
        # Start coordinator if enabled
        if self.enable_coordinator:
            self._coordinator_task = asyncio.create_task(self._coordinator_loop())
            
        # Start subscriber
        self._start_subscriber()
        
        logger.info(f"Distributed processor {self.processor_id} started")
        
    async def stop(self):
        """Stop the distributed processor"""
        if not self._running:
            return
            
        self._running = False
        logger.info(f"Stopping distributed processor {self.processor_id}")
        
        # Stop subscriber
        if self._subscriber_future:
            self._subscriber_future.cancel()
            
        # Stop coordinator
        if self._coordinator_task:
            self._coordinator_task.cancel()
            
        # Stop result processor
        if self._result_processor_task:
            self._result_processor_task.cancel()
            
        # Stop worker pool
        if self.async_pool:
            await self.async_pool.stop()
            
        # Shutdown CPU pool
        if self.cpu_pool:
            self.cpu_pool.shutdown(wait=True)
            
        logger.info(f"Distributed processor {self.processor_id} stopped")
        
    def _start_subscriber(self):
        """Start the Pub/Sub subscriber"""
        def callback(message: subscriber_message.Message):
            """Handle incoming messages"""
            try:
                # Create task
                task = ProcessingTask(
                    task_id=message.message_id,
                    message=message,
                    processor_func=self.processor_func,
                    partition_key=message.attributes.get('partition_key')
                )
                
                # Check for exactly-once processing
                if self.guarantee == ProcessingGuarantee.EXACTLY_ONCE:
                    with self._processed_ids_lock:
                        if task.task_id in self._processed_ids:
                            logger.debug(f"Skipping duplicate message {task.task_id}")
                            message.ack()
                            return
                        self._processed_ids.add(task.task_id)
                        
                # Handle at-most-once by acking immediately
                if self.guarantee == ProcessingGuarantee.AT_MOST_ONCE:
                    message.ack()
                    
                # Queue task for processing
                asyncio.run_coroutine_threadsafe(
                    self._queue_task(task),
                    asyncio.get_event_loop()
                )
                
                # Update metrics
                QUEUE_SIZE.labels(
                    processor_id=self.processor_id,
                    queue_name='task_queue'
                ).set(self.task_queue.qsize())
                
            except Exception as e:
                logger.error(f"Error handling message: {e}")
                message.nack()
                
        # Start subscriber
        self._subscriber_future = self.subscriber_client.subscribe(
            self.subscription_name,
            callback=callback,
            flow_control=self.flow_control
        )
        
    async def _queue_task(self, task: ProcessingTask):
        """Queue task based on load balancing strategy"""
        try:
            # For hash-based routing, we could implement partition-specific queues
            # For now, using a single queue with different selection strategies
            await self.task_queue.put(task)
            
        except asyncio.QueueFull:
            logger.warning(f"Task queue full, rejecting task {task.task_id}")
            if self.guarantee != ProcessingGuarantee.AT_MOST_ONCE:
                task.message.nack()
                
    async def _process_results(self):
        """Process results from workers"""
        while self._running:
            try:
                result = await asyncio.wait_for(
                    self.result_queue.get(),
                    timeout=1.0
                )
                
                # Log result
                if result['status'] == 'success':
                    logger.debug(
                        f"Task {result['task_id']} completed successfully "
                        f"by worker {result['worker_id']} in {result['processing_time']:.3f}s"
                    )
                else:
                    logger.error(
                        f"Task {result['task_id']} failed on worker {result['worker_id']}: "
                        f"{result.get('error')}"
                    )
                    
                # Clean up exactly-once tracking
                if self.guarantee == ProcessingGuarantee.EXACTLY_ONCE:
                    with self._processed_ids_lock:
                        self._processed_ids.discard(result['task_id'])
                        
                        # Periodic cleanup of old IDs
                        if len(self._processed_ids) > 10000:
                            # Keep only recent 5000 IDs
                            self._processed_ids = set(list(self._processed_ids)[-5000:])
                            
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error processing results: {e}")
                
    async def _coordinator_loop(self):
        """Coordinator loop for horizontal scaling coordination"""
        if not self.coordinator_topic:
            return
            
        publisher = pubsub_v1.PublisherClient()
        
        while self._running:
            try:
                # Send heartbeat
                heartbeat_data = {
                    'processor_id': self.processor_id,
                    'instance_id': self.instance_id,
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'stats': {
                        'workers': len(self.async_pool.workers) if self.async_pool else 0,
                        'queue_size': self.task_queue.qsize(),
                        'processed': sum(s.messages_processed for s in self.async_pool.worker_stats.values()) if self.async_pool else 0
                    }
                }
                
                publisher.publish(
                    self.coordinator_topic,
                    json.dumps(heartbeat_data).encode('utf-8'),
                    processor_id=self.processor_id,
                    instance_id=self.instance_id,
                    type='heartbeat'
                )
                
                # Update metric
                COORDINATOR_HEARTBEAT.labels(
                    processor_id=self.processor_id,
                    instance_id=self.instance_id
                ).set(time.time())
                
                # Wait for next heartbeat
                await asyncio.sleep(10)
                
            except Exception as e:
                logger.error(f"Coordinator error: {e}")
                await asyncio.sleep(30)
                
    def get_stats(self) -> Dict[str, Any]:
        """Get processor statistics"""
        stats = {
            'processor_id': self.processor_id,
            'instance_id': self.instance_id,
            'running': self._running,
            'guarantee': self.guarantee.value,
            'load_balancing': self.load_balancing.value,
            'queue_size': self.task_queue.qsize(),
            'workers': {}
        }
        
        if self.async_pool:
            for worker_id, worker_stats in self.async_pool.worker_stats.items():
                stats['workers'][worker_id] = {
                    'state': worker_stats.state.value,
                    'messages_processed': worker_stats.messages_processed,
                    'messages_failed': worker_stats.messages_failed,
                    'current_load': worker_stats.current_load,
                    'error_count': worker_stats.error_count,
                    'utilization': worker_stats.current_load / worker_stats.max_load * 100
                }
                
        return stats


# Preset configurations for common use cases
class ProcessorPresets:
    """Pre-configured processor settings for common scenarios"""
    
    @staticmethod
    def high_throughput() -> Dict[str, Any]:
        """Configuration for high throughput processing"""
        return {
            'worker_config': WorkerConfig(
                min_workers=8,
                max_workers=32,
                max_concurrent_tasks=20,
                scale_up_threshold=0.7,
                scale_down_threshold=0.3
            ),
            'max_messages': 1000,
            'guarantee': ProcessingGuarantee.AT_LEAST_ONCE,
            'load_balancing': LoadBalancingStrategy.LEAST_LOADED
        }
        
    @staticmethod
    def low_latency() -> Dict[str, Any]:
        """Configuration for low latency processing"""
        return {
            'worker_config': WorkerConfig(
                min_workers=4,
                max_workers=16,
                max_concurrent_tasks=5,
                scale_up_threshold=0.6,
                scale_down_threshold=0.2,
                enable_cpu_pool=True
            ),
            'max_messages': 100,
            'guarantee': ProcessingGuarantee.AT_MOST_ONCE,
            'load_balancing': LoadBalancingStrategy.ROUND_ROBIN
        }
        
    @staticmethod
    def exactly_once() -> Dict[str, Any]:
        """Configuration for exactly-once processing"""
        return {
            'worker_config': WorkerConfig(
                min_workers=2,
                max_workers=8,
                max_concurrent_tasks=10,
                worker_timeout=600  # 10 minutes for complex processing
            ),
            'max_messages': 50,
            'guarantee': ProcessingGuarantee.EXACTLY_ONCE,
            'load_balancing': LoadBalancingStrategy.HASH_BASED
        }
        
    @staticmethod
    def cpu_intensive() -> Dict[str, Any]:
        """Configuration for CPU-intensive processing"""
        return {
            'worker_config': WorkerConfig(
                min_workers=1,
                max_workers=4,
                max_concurrent_tasks=2,
                enable_cpu_pool=True,
                cpu_pool_size=multiprocessing.cpu_count() - 1
            ),
            'max_messages': 20,
            'guarantee': ProcessingGuarantee.AT_LEAST_ONCE,
            'load_balancing': LoadBalancingStrategy.LEAST_LOADED
        }


# Example usage and testing
if __name__ == "__main__":
    """
    Example usage and testing of the Distributed Processor
    
    Test scenarios:
    1. Basic message processing
    2. Worker scaling
    3. Fault tolerance
    4. Different processing guarantees
    5. Load balancing strategies
    """
    
    async def sample_processor(message):
        """Sample message processor"""
        data = json.loads(message.data.decode('utf-8'))
        
        # Simulate processing
        await asyncio.sleep(0.1)
        
        # Simulate occasional failures
        import random
        if random.random() < 0.1:
            raise Exception("Simulated processing error")
            
        return {'processed': data, 'timestamp': datetime.now(timezone.utc).isoformat()}
        
    async def main():
        # Create processor with high throughput preset
        config = ProcessorPresets.high_throughput()
        
        processor = DistributedProcessor(
            processor_id="test-processor",
            subscription_name="projects/test-project/subscriptions/test-sub",
            processor_func=sample_processor,
            **config
        )
        
        # Start processor
        await processor.start()
        
        # Run for 60 seconds
        await asyncio.sleep(60)
        
        # Print stats
        stats = processor.get_stats()
        print(json.dumps(stats, indent=2))
        
        # Stop processor
        await processor.stop()
        
    # Run example
    asyncio.run(main())
