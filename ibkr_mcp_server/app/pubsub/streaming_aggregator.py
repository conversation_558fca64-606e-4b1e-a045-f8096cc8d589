"""
Streaming Aggregator for Google Cloud Pub/Sub

This module implements real-time streaming aggregation with:
- Multiple time windows (1s, 5s, 1m, 5m)
- Configurable aggregation functions
- Watermark handling for late data
- State management and recovery
- Memory-efficient sliding windows

Author: IBKR MCP Server Team
Version: 2.0.0
"""

# App-specific imports
from ibkr_mcp_server.app.core.config import Config

import asyncio
import time
import logging
import json
from typing import Dict, List, Optional, Any, Callable, Set, Union
from dataclasses import dataclass, field
from collections import defaultdict, deque
from datetime import datetime, timedelta
from enum import Enum
import threading
import heapq

import numpy as np
from google.cloud import pubsub_v1

logger = logging.getLogger(__name__)


class AggregationType(Enum):
    """Types of aggregation functions"""
    SUM = "sum"
    AVG = "avg"
    MIN = "min"
    MAX = "max"
    COUNT = "count"
    STDDEV = "stddev"
    PERCENTILE = "percentile"
    CUSTOM = "custom"


class WindowType(Enum):
    """Types of time windows"""
    TUMBLING = "tumbling"  # Non-overlapping fixed windows
    SLIDING = "sliding"    # Overlapping windows
    SESSION = "session"    # Gap-based dynamic windows


@dataclass
class TimeWindow:
    """Represents a time window"""
    start_time: float
    end_time: float
    window_type: WindowType
    duration_seconds: float
    
    # Add comparison methods for heap operations
    def __lt__(self, other):
        if isinstance(other, TimeWindow):
            return self.end_time < other.end_time
        return NotImplemented
        
    # Add __iter__ method to make TimeWindow iterable
    def __iter__(self):
        yield self.start_time
        yield self.end_time
        
    # Add __getitem__ method to support indexing
    def __getitem__(self, key):
        if key == 0:
            return self.start_time
        elif key == 1:
            return self.end_time
        else:
            raise IndexError("TimeWindow index out of range")
    
    def contains(self, timestamp: float) -> bool:
        """Check if timestamp falls within window"""
        return self.start_time <= timestamp < self.end_time
    
    def is_expired(self, current_time: float, grace_period: float = 0) -> bool:
        """Check if window has expired"""
        return current_time > self.end_time + grace_period


@dataclass
class AggregationConfig:
    """Configuration for aggregation"""
    aggregation_type: AggregationType
    field_path: str  # JSON path to field to aggregate
    window_durations: List[str] = field(default_factory=lambda: ["1s", "5s", "1m", "5m"])
    window_type: WindowType = WindowType.TUMBLING
    watermark_delay_seconds: float = 5.0  # Allow 5s for late data
    custom_function: Optional[Callable] = None
    percentile_value: float = 95.0


@dataclass
class AggregationResult:
    """Result of an aggregation operation"""
    window: TimeWindow
    aggregation_type: AggregationType
    field_path: str
    value: Any
    message_count: int
    metadata: Dict[str, Any] = field(default_factory=dict)
    

class StreamingAggregator:
    """
    Real-time streaming aggregator for Pub/Sub messages.
    
    Supports multiple time windows and aggregation functions with
    efficient memory usage and late data handling.
    """
    
    def __init__(
        self,
        project_id: str,
        source_subscription: str,
        aggregation_configs: List[AggregationConfig],
        output_topic_pattern: str = "{source_topic}_aggregated_{window}",
        enable_state_persistence: bool = True
    ):
        """
        Initialize streaming aggregator.
        
        Args:
            project_id: GCP project ID
            source_subscription: Source subscription to consume from
            aggregation_configs: List of aggregation configurations
            output_topic_pattern: Pattern for output topic names
            enable_state_persistence: Enable state persistence for recovery
        """
        self.project_id = project_id
        self.source_subscription = source_subscription
        self.aggregation_configs = aggregation_configs
        self.output_topic_pattern = output_topic_pattern
        self.enable_state_persistence = enable_state_persistence
        
        # Window management
        self._windows: Dict[str, Dict[str, TimeWindow]] = defaultdict(dict)
        self._window_data: Dict[str, Dict[str, List[Any]]] = defaultdict(lambda: defaultdict(list))
        self._window_locks: Dict[str, threading.Lock] = defaultdict(threading.Lock)
        
        # Watermarks for late data handling
        self._watermarks: Dict[str, float] = {}
        self._pending_windows: Dict[str, List[tuple[float, TimeWindow]]] = defaultdict(list)
        
        # State management
        self._state_lock = threading.Lock()
        self._checkpoint_interval = 60  # seconds
        self._last_checkpoint = time.time()
        
        # Publishers for aggregated data
        self._publishers: Dict[str, pubsub_v1.PublisherClient] = {}
        
        # Subscriber
        self._subscriber = pubsub_v1.SubscriberClient()
        self._subscription_future = None
        
        # Control flags
        self._running = False
        self._aggregation_thread = None
        
        # Statistics
        self._messages_processed = 0
        self._windows_triggered = 0
        self._late_messages = 0
        
        # Parse window durations
        self._window_durations_seconds = self._parse_window_durations()
        
        logger.info(
            f"Streaming Aggregator initialized for {source_subscription} "
            f"with {len(aggregation_configs)} configurations"
        )
    
    def _parse_window_durations(self) -> Dict[str, float]:
        """Parse window duration strings to seconds"""
        durations = {}
        
        for config in self.aggregation_configs:
            for duration_str in config.window_durations:
                if duration_str not in durations:
                    durations[duration_str] = self._parse_duration(duration_str)
        
        return durations
    
    def _parse_duration(self, duration_str: str) -> float:
        """Parse duration string like '1s', '5m' to seconds"""
        unit_map = {
            's': 1,
            'm': 60,
            'h': 3600,
            'd': 86400
        }
        
        if not duration_str or not duration_str[-1] in unit_map:
            raise ValueError(f"Invalid duration format: {duration_str}")
        
        value = float(duration_str[:-1])
        unit = duration_str[-1]
        
        return value * unit_map[unit]
    
    async def start(self):
        """Start the aggregator"""
        if self._running:
            logger.warning("Aggregator already running")
            return
        
        self._running = True
        
        # Start aggregation thread
        self._aggregation_thread = threading.Thread(
            target=self._aggregation_loop,
            daemon=True
        )
        self._aggregation_thread.start()
        
        # Start consuming messages
        await self._start_consumer()
        
        logger.info("Streaming Aggregator started")
    
    async def _start_consumer(self):
        """Start consuming from subscription"""
        subscription_path = f"projects/{self.project_id}/subscriptions/{self.source_subscription}"
        
        flow_control = pubsub_v1.types.FlowControl(max_messages=1000)
        
        def callback(message):
            try:
                # Parse message
                data = json.loads(message.data.decode('utf-8'))
                timestamp = float(message.attributes.get(
                    'timestamp',
                    str(int(time.time() * 1000))
                )) / 1000
                
                # Process message
                self._process_message(data, timestamp, message.attributes)
                
                # Acknowledge
                message.ack()
                
                self._messages_processed += 1
                
            except Exception as e:
                logger.error(f"Error processing message: {e}")
                message.nack()
        
        self._subscription_future = self._subscriber.subscribe(
            subscription_path,
            callback=callback,
            flow_control=flow_control
        )
    
    def _process_message(self, data: Dict[str, Any], timestamp: float, attributes: Dict[str, str]):
        """Process a single message"""
        # Update watermark
        topic = attributes.get('source_topic', 'unknown')
        self._update_watermark(topic, timestamp)
        
        # Process each aggregation config
        for config in self.aggregation_configs:
            self._add_to_windows(data, timestamp, config, topic)
    
    def _update_watermark(self, topic: str, timestamp: float):
        """Update watermark for topic"""
        current_watermark = self._watermarks.get(topic, 0)
        
        # Watermark advances based on event time minus delay
        new_watermark = timestamp - max(
            config.watermark_delay_seconds 
            for config in self.aggregation_configs
        )
        
        if new_watermark > current_watermark:
            self._watermarks[topic] = new_watermark
            # Trigger expired windows
            self._trigger_expired_windows(topic, new_watermark)
    
    def _add_to_windows(self, data: Dict[str, Any], timestamp: float, 
                       config: AggregationConfig, topic: str):
        """Add data to appropriate windows"""
        # Extract field value
        try:
            value = self._extract_field_value(data, config.field_path)
            if value is None:
                return
        except Exception as e:
            logger.debug(f"Failed to extract field {config.field_path}: {e}")
            return
        
        # Add to each window duration
        for duration_str in config.window_durations:
            duration_seconds = self._window_durations_seconds[duration_str]
            window_key = f"{topic}_{config.field_path}_{duration_str}"
            
            with self._window_locks[window_key]:
                # Get or create window
                window = self._get_window_for_timestamp(
                    timestamp, duration_seconds, config.window_type, window_key
                )
                
                # Check if message is late
                watermark = self._watermarks.get(topic, 0)
                if timestamp < watermark:
                    self._late_messages += 1
                    logger.debug(f"Late message: timestamp={timestamp}, watermark={watermark}")
                
                # Add value to window
                window_id = f"{window.start_time}_{window.end_time}"
                self._window_data[window_key][window_id].append(value)
    
    def _get_window_for_timestamp(self, timestamp: float, duration: float, 
                                 window_type: WindowType, window_key: str) -> TimeWindow:
        """Get or create window for timestamp"""
        if window_type == WindowType.TUMBLING:
            # Align to window boundaries
            start_time = (timestamp // duration) * duration
            end_time = start_time + duration
        elif window_type == WindowType.SLIDING:
            # Sliding windows need multiple windows per timestamp
            # For simplicity, we'll use tumbling windows with overlap
            start_time = (timestamp // (duration / 2)) * (duration / 2)
            end_time = start_time + duration
        else:  # SESSION
            # Session windows require gap detection
            # Simplified: use fixed duration for now
            start_time = timestamp
            end_time = timestamp + duration
        
        window = TimeWindow(
            start_time=start_time,
            end_time=end_time,
            window_type=window_type,
            duration_seconds=duration
        )
        
        window_id = f"{window.start_time}_{window.end_time}"
        
        if window_id not in self._windows[window_key]:
            self._windows[window_key][window_id] = window
            # Add to pending windows
            # Store window in heap with end_time as key for proper ordering
            # We're using tuples for explicit ordering by end_time
            if window_key not in self._pending_windows:
                self._pending_windows[window_key] = []
            # The tuple (window.end_time, window) is a valid heap item
            # Python's heapq will compare the first elements (end_time) for ordering
            heapq.heappush(self._pending_windows[window_key], (window.end_time, window))
        
        return window
    
    def _extract_field_value(self, data: Dict[str, Any], field_path: str) -> Any:
        """Extract field value from nested dict using path"""
        parts = field_path.split('.')
        value = data
        
        for part in parts:
            if isinstance(value, dict) and part in value:
                value = value[part]
            else:
                return None
        
        return value
    
    def _trigger_expired_windows(self, topic: str, watermark: float):
        """Trigger computation for expired windows"""
        for config in self.aggregation_configs:
            for duration_str in config.window_durations:
                window_key = f"{topic}_{config.field_path}_{duration_str}"
                
                with self._window_locks[window_key]:
                    # Check pending windows
                    pending = self._pending_windows.get(window_key, [])
                    
                    while pending and pending[0][0] <= watermark:
                        # Unpack the tuple from the heap
                        end_time, window = heapq.heappop(pending)
                        window_id = f"{window.start_time}_{window.end_time}"
                        
                        # Compute aggregation
                        if window_id in self._window_data[window_key]:
                            values = self._window_data[window_key][window_id]
                            if values:
                                result = self._compute_aggregation(
                                    values, config, window
                                )
                                
                                # Publish result
                                asyncio.create_task(
                                    self._publish_aggregation(
                                        result, topic, duration_str
                                    )
                                )
                                
                                self._windows_triggered += 1
                            
                            # Clean up
                            del self._window_data[window_key][window_id]
                            if window_id in self._windows[window_key]:
                                del self._windows[window_key][window_id]
    
    def _compute_aggregation(self, values: List[Any], config: AggregationConfig, 
                           window: TimeWindow) -> AggregationResult:
        """Compute aggregation for values"""
        try:
            # Convert to numpy array for numerical operations
            if all(isinstance(v, (int, float)) for v in values):
                np_values = np.array(values, dtype=float)
            else:
                np_values = values
            
            # Compute based on type
            if config.aggregation_type == AggregationType.SUM:
                result = float(np.sum(np_values))
            elif config.aggregation_type == AggregationType.AVG:
                result = float(np.mean(np_values))
            elif config.aggregation_type == AggregationType.MIN:
                result = float(np.min(np_values))
            elif config.aggregation_type == AggregationType.MAX:
                result = float(np.max(np_values))
            elif config.aggregation_type == AggregationType.COUNT:
                result = len(values)
            elif config.aggregation_type == AggregationType.STDDEV:
                result = float(np.std(np_values))
            elif config.aggregation_type == AggregationType.PERCENTILE:
                result = float(np.percentile(np_values, config.percentile_value))
            elif config.aggregation_type == AggregationType.CUSTOM:
                if config.custom_function:
                    result = config.custom_function(values)
                else:
                    result = None
            else:
                result = None
            
            return AggregationResult(
                window=window,
                aggregation_type=config.aggregation_type,
                field_path=config.field_path,
                value=result,
                message_count=len(values),
                metadata={
                    'window_start': datetime.fromtimestamp(window.start_time).isoformat(),
                    'window_end': datetime.fromtimestamp(window.end_time).isoformat(),
                    'computation_time': time.time()
                }
            )
            
        except Exception as e:
            logger.error(f"Aggregation computation error: {e}")
            return AggregationResult(
                window=window,
                aggregation_type=config.aggregation_type,
                field_path=config.field_path,
                value=None,
                message_count=len(values),
                metadata={'error': str(e)}
            )
    
    async def _publish_aggregation(self, result: AggregationResult, 
                                  source_topic: str, window_duration: str):
        """Publish aggregation result"""
        # Generate output topic
        output_topic = self.output_topic_pattern.format(
            source_topic=source_topic,
            window=window_duration
        )
        
        # Get or create publisher
        if output_topic not in self._publishers:
            self._publishers[output_topic] = pubsub_v1.PublisherClient()
        
        publisher = self._publishers[output_topic]
        topic_path = f"projects/{self.project_id}/topics/{output_topic}"
        
        # Prepare message
        message_data = {
            'window_start': result.window.start_time,
            'window_end': result.window.end_time,
            'window_duration': result.window.duration_seconds,
            'aggregation_type': result.aggregation_type.value,
            'field_path': result.field_path,
            'value': result.value,
            'message_count': result.message_count,
            'metadata': result.metadata
        }
        
        # Publish
        try:
            future = publisher.publish(
                topic_path,
                json.dumps(message_data).encode('utf-8'),
                source_topic=source_topic,
                window_duration=window_duration,
                aggregation_type=result.aggregation_type.value
            )
            
            # Don't wait for result to avoid blocking
            future.add_done_callback(
                lambda f: logger.debug(f"Published aggregation: {f.result()}")
            )
            
        except Exception as e:
            logger.error(f"Failed to publish aggregation: {e}")
    
    def _aggregation_loop(self):
        """Main aggregation loop for time-based triggers"""
        while self._running:
            try:
                current_time = time.time()
                
                # Advance watermarks based on wall clock time
                for topic in list(self._watermarks.keys()):
                    # Conservative watermark advancement
                    max_delay = max(
                        config.watermark_delay_seconds 
                        for config in self.aggregation_configs
                    )
                    
                    new_watermark = current_time - max_delay
                    if new_watermark > self._watermarks[topic]:
                        self._watermarks[topic] = new_watermark
                        self._trigger_expired_windows(topic, new_watermark)
                
                # Checkpoint state if enabled
                if (self.enable_state_persistence and 
                    current_time - self._last_checkpoint > self._checkpoint_interval):
                    self._checkpoint_state()
                    self._last_checkpoint = current_time
                
                # Sleep briefly
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Aggregation loop error: {e}")
    
    def _checkpoint_state(self):
        """Save aggregator state for recovery"""
        with self._state_lock:
            state = {
                'watermarks': dict(self._watermarks),
                'messages_processed': self._messages_processed,
                'windows_triggered': self._windows_triggered,
                'timestamp': time.time()
            }
            
            # In production, save to persistent storage
            logger.debug(f"Checkpointed state: {self._messages_processed} messages processed")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get aggregator statistics"""
        stats = {
            'messages_processed': self._messages_processed,
            'windows_triggered': self._windows_triggered,
            'late_messages': self._late_messages,
            'active_windows': sum(
                len(windows) for windows in self._windows.values()
            ),
            'watermarks': dict(self._watermarks)
        }
        
        return stats
    
    async def stop(self):
        """Stop the aggregator"""
        logger.info("Stopping Streaming Aggregator...")
        
        self._running = False
        
        # Cancel subscription
        if self._subscription_future:
            self._subscription_future.cancel()
        
        # Wait for aggregation thread
        if self._aggregation_thread:
            self._aggregation_thread.join(timeout=5.0)
        
        # Trigger all pending windows
        for topic, watermark in self._watermarks.items():
            self._trigger_expired_windows(topic, float('inf'))
        
        # Final checkpoint
        if self.enable_state_persistence:
            self._checkpoint_state()
        
        logger.info(
            f"Streaming Aggregator stopped. "
            f"Processed {self._messages_processed} messages, "
            f"triggered {self._windows_triggered} windows"
        )


def create_market_data_aggregator(
    project_id: str,
    source_subscription: str
) -> StreamingAggregator:
    """
    Factory function to create a market data aggregator with common configurations.
    """
    configs = [
        # Price aggregations
        AggregationConfig(
            aggregation_type=AggregationType.AVG,
            field_path="last",
            window_durations=["1s", "5s", "1m", "5m"]
        ),
        AggregationConfig(
            aggregation_type=AggregationType.MIN,
            field_path="bid",
            window_durations=["1s", "5s", "1m", "5m"]
        ),
        AggregationConfig(
            aggregation_type=AggregationType.MAX,
            field_path="ask",
            window_durations=["1s", "5s", "1m", "5m"]
        ),
        # Volume aggregations
        AggregationConfig(
            aggregation_type=AggregationType.SUM,
            field_path="volume",
            window_durations=["1s", "5s", "1m", "5m"]
        ),
        # Volatility (standard deviation of price)
        AggregationConfig(
            aggregation_type=AggregationType.STDDEV,
            field_path="last",
            window_durations=["1m", "5m"]
        )
    ]
    
    return StreamingAggregator(
        project_id=project_id,
        source_subscription=source_subscription,
        aggregation_configs=configs,
        output_topic_pattern="market_data_{window}_aggregated"
    )
