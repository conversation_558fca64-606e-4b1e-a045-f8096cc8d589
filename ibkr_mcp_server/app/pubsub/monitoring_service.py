#!/usr/bin/env python3
"""
Monitoring Service for Google Cloud Pub/Sub Infrastructure

This module provides comprehensive monitoring and observability with:
- Prometheus metrics collection and export
- Google Cloud Monitoring integration
- Custom dashboard generation
- Alert policy management
- SLI/SLO tracking and reporting
- Performance analysis and recommendations

Academic Foundations:
- Based on Google's SRE Book principles for monitoring
- Implements the Four Golden Signals (latency, traffic, errors, saturation)
- Follows USE Method (Utilization, Saturation, Errors) by <PERSON>
- Incorporates RED Method (Rate, Errors, Duration) for microservices

Industrial Applications:
- Production-ready monitoring for Pub/Sub infrastructure
- Automated alert management with smart thresholds
- SLA compliance tracking and reporting
- Capacity planning and cost optimization insights

Author: IBKR Integration Team
Date: 2024
Version: 1.0.0
"""

# App-specific imports (added during migration)
# from app.core.config import settings  # Not used in this module
# from app.models.database_models import Base  # Not used in this module
# from app.services.base_service import BaseService  # Not used in this module


import asyncio
import json
import logging
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Any, Dict, List, Optional

# from google.api_core import retry  # Not used in current implementation
from google.cloud import monitoring_v3
# Dashboard functionality temporarily disabled due to import issues
# from google.cloud.monitoring_dashboards_v1 import DashboardsServiceClient
from prometheus_client import (
    CollectorRegistry, Counter, Gauge, Histogram, Summary,
    generate_latest, push_to_gateway, write_to_textfile
)
from prometheus_client.core import GaugeMetricFamily

# Monitoring Metrics
monitoring_collection_duration = Histogram(
    'monitoring_collection_duration_seconds',
    'Time spent collecting metrics',
    ['metric_type', 'source']
)

monitoring_export_total = Counter(
    'monitoring_export_total',
    'Total metric exports',
    ['exporter', 'status']
)

monitoring_alerts_total = Counter(
    'monitoring_alerts_total',
    'Total alerts triggered',
    ['severity', 'policy']
)

monitoring_slo_compliance = Gauge(
    'monitoring_slo_compliance_percentage',
    'Current SLO compliance percentage',
    ['slo_name', 'service']
)


class MetricType(Enum):
    """Types of metrics to collect"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"


class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class SLOType(Enum):
    """Types of Service Level Objectives"""
    AVAILABILITY = "availability"
    LATENCY = "latency"
    ERROR_RATE = "error_rate"
    THROUGHPUT = "throughput"


@dataclass
class MetricDefinition:
    """Definition of a metric to monitor"""
    name: str
    type: MetricType
    description: str
    labels: List[str]
    unit: str = ""
    buckets: Optional[List[float]] = None
    objectives: Optional[Dict[float, float]] = None


@dataclass
class AlertPolicy:
    """Alert policy configuration"""
    name: str
    condition: str
    threshold: float
    duration: timedelta
    severity: AlertSeverity
    notification_channels: List[str] = field(default_factory=list)
    documentation: str = ""
    enabled: bool = True


@dataclass
class SLO:
    """Service Level Objective definition"""
    name: str
    type: SLOType
    target: float  # e.g., 99.9 for 99.9%
    measurement_window: timedelta
    service: str
    description: str = ""
    
    def error_budget(self) -> float:
        """Calculate error budget percentage"""
        return 100.0 - self.target


@dataclass
class DashboardConfig:
    """Dashboard configuration"""
    name: str
    title: str
    widgets: List[Dict[str, Any]]
    grid_layout: Optional[Dict[str, Any]] = None
    mosaicLayout: Optional[Dict[str, Any]] = None


class MonitoringService:
    """
    Comprehensive monitoring service for Pub/Sub infrastructure
    
    Features:
    - Multi-source metric collection
    - Real-time and historical analysis
    - Automated alert management
    - SLI/SLO tracking and reporting
    - Custom dashboard generation
    """
    
    def __init__(
        self,
        project_id: str,
        prometheus_gateway: Optional[str] = None,
        export_interval: int = 60,
        retention_days: int = 30,
        enable_cloud_monitoring: bool = True
    ):
        """
        Initialize the Monitoring Service
        
        Args:
            project_id: Google Cloud project ID
            prometheus_gateway: Prometheus pushgateway URL
            export_interval: Metric export interval in seconds
            retention_days: Metric retention period
            enable_cloud_monitoring: Enable Google Cloud Monitoring
        """
        self.project_id = project_id
        self.prometheus_gateway = prometheus_gateway
        self.export_interval = export_interval
        self.retention_days = retention_days
        self.enable_cloud_monitoring = enable_cloud_monitoring
        
        # Clients
        self.monitoring_client = monitoring_v3.MetricServiceClient() if enable_cloud_monitoring else None
        self.alert_client = monitoring_v3.AlertPolicyServiceClient() if enable_cloud_monitoring else None
        # Dashboard client temporarily disabled due to import issues
        self.dashboard_client = None  # dashboard_v1.DashboardsServiceClient() if enable_cloud_monitoring else None
        
        # Registries
        self.registry = CollectorRegistry()
        self._metrics: Dict[str, Any] = {}
        self._custom_collectors: List[Any] = []
        
        # State management
        self._alert_policies: Dict[str, AlertPolicy] = {}
        self._slos: Dict[str, SLO] = {}
        self._sli_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self._dashboards: Dict[str, DashboardConfig] = {}
        
        # Performance tracking
        self._metric_buffer: deque = deque(maxlen=10000)
        self._export_stats = defaultdict(int)
        
        # Background tasks
        self._tasks: List[asyncio.Task] = []
        self._running = False
        
        # Logging
        self.logger = logging.getLogger(__name__)
        
        # Initialize standard metrics
        self._initialize_standard_metrics()
    
    def _initialize_standard_metrics(self) -> None:
        """Initialize standard Pub/Sub metrics"""
        # Define standard metrics
        standard_metrics = [
            MetricDefinition(
                name="pubsub_publish_latency",
                type=MetricType.HISTOGRAM,
                description="Message publish latency",
                labels=["topic", "status"],
                unit="seconds",
                buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0]
            ),
            MetricDefinition(
                name="pubsub_message_size",
                type=MetricType.HISTOGRAM,
                description="Message size distribution",
                labels=["topic"],
                unit="bytes",
                buckets=[100, 1000, 10000, 100000, 1000000, 10000000]
            ),
            MetricDefinition(
                name="pubsub_subscription_lag",
                type=MetricType.GAUGE,
                description="Subscription message lag",
                labels=["subscription", "topic"],
                unit="messages"
            ),
            MetricDefinition(
                name="pubsub_ack_latency",
                type=MetricType.SUMMARY,
                description="Message acknowledgment latency",
                labels=["subscription"],
                unit="seconds",
                objectives={0.5: 0.05, 0.9: 0.01, 0.99: 0.001}
            )
        ]
        
        # Register metrics
        for metric_def in standard_metrics:
            self.register_metric(metric_def)
    
    async def initialize(self) -> None:
        """Initialize the monitoring service and start background tasks"""
        self._running = True
        
        # Start background tasks
        self._tasks.append(asyncio.create_task(self._export_metrics_loop()))
        self._tasks.append(asyncio.create_task(self._check_alerts_loop()))
        self._tasks.append(asyncio.create_task(self._calculate_slos_loop()))
        self._tasks.append(asyncio.create_task(self._cleanup_old_data_loop()))
        
        # Create default dashboards
        await self._create_default_dashboards()
        
        self.logger.info(f"Monitoring Service initialized for project {self.project_id}")
    
    async def shutdown(self) -> None:
        """Shutdown the monitoring service and cleanup resources"""
        self._running = False
        
        # Export final metrics
        await self._export_metrics()
        
        # Cancel background tasks
        for task in self._tasks:
            task.cancel()
        
        await asyncio.gather(*self._tasks, return_exceptions=True)
        self._tasks.clear()
        
        self.logger.info("Monitoring Service shutdown complete")
    
    def register_metric(self, metric_def: MetricDefinition) -> None:
        """
        Register a custom metric
        
        Args:
            metric_def: Metric definition
        """
        if metric_def.type == MetricType.COUNTER:
            metric = Counter(
                metric_def.name,
                metric_def.description,
                metric_def.labels,
                registry=self.registry
            )
        elif metric_def.type == MetricType.GAUGE:
            metric = Gauge(
                metric_def.name,
                metric_def.description,
                metric_def.labels,
                registry=self.registry
            )
        elif metric_def.type == MetricType.HISTOGRAM:
            metric = Histogram(
                metric_def.name,
                metric_def.description,
                metric_def.labels,
                buckets=metric_def.buckets or Histogram.DEFAULT_BUCKETS,
                registry=self.registry
            )
        elif metric_def.type == MetricType.SUMMARY:
            metric = Summary(
                metric_def.name,
                metric_def.description,
                metric_def.labels,
                registry=self.registry
            )
        else:
            raise ValueError(f"Unknown metric type: {metric_def.type}")
        
        self._metrics[metric_def.name] = metric
        self.logger.info(f"Registered metric: {metric_def.name}")
    
    def record_metric(
        self,
        metric_name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None,
        timestamp: Optional[datetime] = None
    ) -> None:
        """
        Record a metric value
        
        Args:
            metric_name: Name of the metric
            value: Metric value
            labels: Label values
            timestamp: Optional timestamp
        """
        if metric_name not in self._metrics:
            self.logger.warning(f"Metric {metric_name} not registered")
            return
        
        metric = self._metrics[metric_name]
        labels = labels or {}
        
        # Record based on metric type
        if isinstance(metric, Counter):
            metric.labels(**labels).inc(value)
        elif isinstance(metric, Gauge):
            metric.labels(**labels).set(value)
        elif isinstance(metric, Histogram):
            metric.labels(**labels).observe(value)
        elif isinstance(metric, Summary):
            metric.labels(**labels).observe(value)
        
        # Buffer for batch export
        self._metric_buffer.append({
            "name": metric_name,
            "value": value,
            "labels": labels,
            "timestamp": timestamp or datetime.now(timezone.utc)
        })
    
    def create_alert_policy(self, policy: AlertPolicy) -> str:
        """
        Create or update an alert policy
        
        Args:
            policy: Alert policy configuration
            
        Returns:
            Policy ID
        """
        self._alert_policies[policy.name] = policy
        
        if self.enable_cloud_monitoring and self.alert_client:
            # Create Google Cloud Monitoring alert
            project_path = f"projects/{self.project_id}"
            
            # Build condition
            condition = monitoring_v3.AlertPolicy.Condition(
                display_name=policy.name,
                condition_threshold=monitoring_v3.AlertPolicy.Condition.MetricThreshold(
                    filter=policy.condition,
                    comparison=monitoring_v3.ComparisonType.COMPARISON_GT,
                    threshold_value=policy.threshold,
                    duration={"seconds": int(policy.duration.total_seconds())},
                    aggregations=[
                        monitoring_v3.Aggregation(
                            alignment_period={"seconds": 60},
                            per_series_aligner=monitoring_v3.Aggregation.Aligner.ALIGN_MEAN
                        )
                    ]
                )
            )
            
            # Create alert policy
            alert_policy = monitoring_v3.AlertPolicy(
                display_name=policy.name,
                conditions=[condition],
                notification_channels=policy.notification_channels,
                documentation=monitoring_v3.AlertPolicy.Documentation(
                    content=policy.documentation
                ),
                enabled=policy.enabled
            )
            
            created_policy = self.alert_client.create_alert_policy(
                request={
                    "name": project_path,
                    "alert_policy": alert_policy
                }
            )
            
            self.logger.info(f"Created alert policy: {created_policy.name}")
            return created_policy.name
        
        return policy.name
    
    def define_slo(self, slo: SLO) -> None:
        """
        Define a Service Level Objective
        
        Args:
            slo: SLO definition
        """
        self._slos[slo.name] = slo
        self.logger.info(
            f"Defined SLO: {slo.name} with target {slo.target}% "
            f"for {slo.service}"
        )
    
    async def record_sli(
        self,
        slo_name: str,
        success: bool,
        timestamp: Optional[datetime] = None
    ) -> None:
        """
        Record a Service Level Indicator measurement
        
        Args:
            slo_name: Name of the associated SLO
            success: Whether the measurement was successful
            timestamp: Measurement timestamp
        """
        if slo_name not in self._slos:
            self.logger.warning(f"SLO {slo_name} not defined")
            return
        
        timestamp = timestamp or datetime.now(timezone.utc)
        self._sli_data[slo_name].append({
            "timestamp": timestamp,
            "success": success
        })
    
    async def get_slo_status(self, slo_name: str) -> Dict[str, Any]:
        """
        Get current SLO status and compliance
        
        Args:
            slo_name: Name of the SLO
            
        Returns:
            SLO status including compliance percentage
        """
        if slo_name not in self._slos:
            raise ValueError(f"SLO {slo_name} not defined")
        
        slo = self._slos[slo_name]
        sli_data = list(self._sli_data[slo_name])
        
        if not sli_data:
            return {
                "slo": slo.name,
                "target": slo.target,
                "current": 100.0,
                "compliant": True,
                "error_budget_remaining": slo.error_budget(),
                "measurements": 0
            }
        
        # Filter by measurement window
        cutoff = datetime.now(timezone.utc) - slo.measurement_window
        recent_data = [d for d in sli_data if d["timestamp"] > cutoff]
        
        if not recent_data:
            return {
                "slo": slo.name,
                "target": slo.target,
                "current": 100.0,
                "compliant": True,
                "error_budget_remaining": slo.error_budget(),
                "measurements": 0
            }
        
        # Calculate compliance
        successes = sum(1 for d in recent_data if d["success"])
        total = len(recent_data)
        compliance = (successes / total) * 100
        
        # Calculate error budget consumption
        error_budget = slo.error_budget()
        error_rate = 100.0 - compliance
        budget_consumed = (error_rate / error_budget) * 100 if error_budget > 0 else 100
        
        # Update metric
        monitoring_slo_compliance.labels(
            slo_name=slo.name,
            service=slo.service
        ).set(compliance)
        
        return {
            "slo": slo.name,
            "target": slo.target,
            "current": compliance,
            "compliant": compliance >= slo.target,
            "error_budget_remaining": max(0, 100 - budget_consumed),
            "measurements": total,
            "window": str(slo.measurement_window),
            "service": slo.service
        }
    
    async def create_dashboard(self, config: DashboardConfig) -> str:
        """
        Create a custom dashboard
        
        Args:
            config: Dashboard configuration
            
        Returns:
            Dashboard ID
        """
        self._dashboards[config.name] = config
        
        if self.enable_cloud_monitoring and self.dashboard_client:
            # Create Google Cloud Monitoring dashboard
            # Dashboard functionality temporarily disabled due to import issues
            self.logger.warning("Dashboard creation temporarily disabled")
            return "dashboard_disabled"

            # project_path = f"projects/{self.project_id}"
            # dashboard = dashboard_v1.Dashboard(
            #     display_name=config.title,
            #     grid_layout=config.grid_layout,
            #     mosaic_layout=config.mosaicLayout
            # )
            
            # Dashboard creation code removed (unreachable after return statement above)
        
        return config.name
    
    async def _create_default_dashboards(self) -> None:
        """Create default monitoring dashboards"""
        # Pub/Sub Overview Dashboard
        overview_widgets = [
            {
                "title": "Message Publish Rate",
                "xyChart": {
                    "dataSets": [{
                        "timeSeriesQuery": {
                            "timeSeriesFilter": {
                                "filter": 'metric.type="pubsub.googleapis.com/topic/send_message_operation_count"',
                                "aggregation": {
                                    "alignmentPeriod": "60s",
                                    "perSeriesAligner": "ALIGN_RATE"
                                }
                            }
                        }
                    }]
                }
            },
            {
                "title": "Subscription Backlog",
                "xyChart": {
                    "dataSets": [{
                        "timeSeriesQuery": {
                            "timeSeriesFilter": {
                                "filter": 'metric.type="pubsub.googleapis.com/subscription/backlog_bytes"',
                                "aggregation": {
                                    "alignmentPeriod": "60s",
                                    "perSeriesAligner": "ALIGN_MEAN"
                                }
                            }
                        }
                    }]
                }
            },
            {
                "title": "Message Latency P99",
                "xyChart": {
                    "dataSets": [{
                        "timeSeriesQuery": {
                            "timeSeriesFilter": {
                                "filter": 'metric.type="custom.googleapis.com/pubsub_publish_latency"',
                                "aggregation": {
                                    "alignmentPeriod": "60s",
                                    "perSeriesAligner": "ALIGN_PERCENTILE_99"
                                }
                            }
                        }
                    }]
                }
            }
        ]
        
        overview_config = DashboardConfig(
            name="pubsub_overview",
            title="Pub/Sub System Overview",
            widgets=overview_widgets,
            grid_layout={"columns": 12}
        )
        
        await self.create_dashboard(overview_config)
        
        # SLO Dashboard
        slo_widgets = [
            {
                "title": "SLO Compliance",
                "scorecard": {
                    "timeSeriesQuery": {
                        "timeSeriesFilter": {
                            "filter": 'metric.type="custom.googleapis.com/monitoring_slo_compliance_percentage"',
                            "aggregation": {
                                "alignmentPeriod": "300s",
                                "perSeriesAligner": "ALIGN_MEAN"
                            }
                        }
                    },
                    "thresholds": [
                        {"value": 99.9, "color": "GREEN"},
                        {"value": 99.0, "color": "YELLOW"},
                        {"value": 0, "color": "RED"}
                    ]
                }
            }
        ]
        
        slo_config = DashboardConfig(
            name="slo_dashboard",
            title="SLO Compliance Dashboard",
            widgets=slo_widgets,
            grid_layout={"columns": 12}
        )
        
        await self.create_dashboard(slo_config)
    
    async def _export_metrics(self) -> None:
        """Export metrics to configured destinations"""
        with monitoring_collection_duration.labels(
            metric_type='export',
            source='prometheus'
        ).time():
            try:
                # Generate Prometheus format
                _ = generate_latest(self.registry)  # metrics_data not used currently
                
                # Export to Prometheus Gateway if configured
                if self.prometheus_gateway:
                    try:
                        push_to_gateway(
                            self.prometheus_gateway,
                            job='pubsub_monitoring',
                            registry=self.registry
                        )
                        monitoring_export_total.labels(
                            exporter='prometheus',
                            status='success'
                        ).inc()
                    except Exception as e:
                        self.logger.error(f"Failed to push to Prometheus: {e}")
                        monitoring_export_total.labels(
                            exporter='prometheus',
                            status='failure'
                        ).inc()
                
                # Export to Google Cloud Monitoring
                if self.enable_cloud_monitoring and self._metric_buffer:
                    await self._export_to_cloud_monitoring()
                
                # Write to local file for backup
                write_to_textfile('/tmp/pubsub_metrics.prom', self.registry)
                
                self._export_stats['total_exports'] += 1
                self._export_stats['last_export'] = int(datetime.now(timezone.utc).timestamp())
                
            except Exception as e:
                self.logger.error(f"Error exporting metrics: {e}")
                monitoring_export_total.labels(
                    exporter='all',
                    status='failure'
                ).inc()
    
    async def _export_to_cloud_monitoring(self) -> None:
        """Export buffered metrics to Google Cloud Monitoring"""
        if not self.monitoring_client:
            return
        
        project_path = f"projects/{self.project_id}"
        series_list = []
        
        # Process buffered metrics
        while self._metric_buffer:
            try:
                metric_data = self._metric_buffer.popleft()
                
                # Create time series
                series = monitoring_v3.TimeSeries()
                series.metric.type = f"custom.googleapis.com/{metric_data['name']}"
                
                # Add labels
                for key, value in metric_data['labels'].items():
                    series.metric.labels[key] = str(value)
                
                # Add resource
                series.resource.type = "global"
                series.resource.labels["project_id"] = self.project_id
                
                # Add point
                point = monitoring_v3.Point()
                point.value.double_value = float(metric_data['value'])
                point.interval.end_time.seconds = int(
                    metric_data['timestamp'].timestamp()
                )
                
                series.points.append(point)
                series_list.append(series)
                
                # Batch write
                if len(series_list) >= 200:  # Cloud Monitoring limit
                    self.monitoring_client.create_time_series(
                        request={
                            "name": project_path,
                            "time_series": series_list
                        }
                    )
                    series_list.clear()
                    
            except Exception as e:
                self.logger.error(f"Error creating time series: {e}")
        
        # Write remaining series
        if series_list:
            self.monitoring_client.create_time_series(
                request={
                    "name": project_path,
                    "time_series": series_list
                }
            )
    
    async def _check_alerts_loop(self) -> None:
        """Background task to check alert conditions"""
        while self._running:
            try:
                for policy_name, policy in self._alert_policies.items():
                    if not policy.enabled:
                        continue
                    
                    # Evaluate condition
                    # This is simplified - in production, you'd query actual metrics
                    should_alert = await self._evaluate_alert_condition(policy)
                    
                    if should_alert:
                        monitoring_alerts_total.labels(
                            severity=policy.severity.value,
                            policy=policy.name
                        ).inc()
                        
                        self.logger.warning(
                            f"Alert triggered: {policy.name} "
                            f"(severity: {policy.severity.value})"
                        )
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in alert checker: {e}")
                await asyncio.sleep(60)
    
    async def _evaluate_alert_condition(self, policy: AlertPolicy) -> bool:
        """Evaluate whether an alert condition is met"""
        # This is a simplified implementation
        # In production, you would:
        # Use the policy parameter to evaluate specific conditions
        _ = policy  # Acknowledge parameter for future implementation
        # 1. Parse the condition filter
        # 2. Query the appropriate metrics
        # 3. Apply aggregations and comparisons
        # 4. Check duration requirements
        return False  # Placeholder
    
    async def _calculate_slos_loop(self) -> None:
        """Background task to calculate SLO compliance"""
        while self._running:
            try:
                for slo_name in self._slos:
                    status = await self.get_slo_status(slo_name)
                    
                    if not status["compliant"]:
                        self.logger.warning(
                            f"SLO violation: {slo_name} at {status['current']:.2f}% "
                            f"(target: {status['target']}%)"
                        )
                
                await asyncio.sleep(60)  # Calculate every minute
                
            except Exception as e:
                self.logger.error(f"Error calculating SLOs: {e}")
                await asyncio.sleep(60)
    
    async def _cleanup_old_data_loop(self) -> None:
        """Background task to cleanup old metric data"""
        while self._running:
            try:
                cutoff = datetime.now(timezone.utc) - timedelta(days=self.retention_days)
                
                # Cleanup SLI data
                for slo_name in list(self._sli_data.keys()):
                    old_size = len(self._sli_data[slo_name])
                    self._sli_data[slo_name] = deque(
                        (d for d in self._sli_data[slo_name] 
                         if d["timestamp"] > cutoff),
                        maxlen=10000
                    )
                    
                    removed = old_size - len(self._sli_data[slo_name])
                    if removed > 0:
                        self.logger.info(
                            f"Cleaned up {removed} old SLI records for {slo_name}"
                        )
                
                await asyncio.sleep(3600)  # Cleanup every hour
                
            except Exception as e:
                self.logger.error(f"Error in cleanup: {e}")
                await asyncio.sleep(3600)
    
    async def _export_metrics_loop(self) -> None:
        """Background task to export metrics periodically"""
        while self._running:
            try:
                await self._export_metrics()
                await asyncio.sleep(self.export_interval)
                
            except Exception as e:
                self.logger.error(f"Error in export loop: {e}")
                await asyncio.sleep(self.export_interval)
    
    async def get_performance_report(
        self,
        time_range: timedelta = timedelta(hours=1)
    ) -> Dict[str, Any]:
        """
        Generate a performance report
        
        Args:
            time_range: Time range for the report
            
        Returns:
            Performance metrics and recommendations
        """
        report = {
            "timestamp": datetime.now(timezone.utc),
            "time_range": str(time_range),
            "metrics": {},
            "slos": {},
            "alerts": {},
            "recommendations": []
        }
        
        # Collect metric summaries
        # In production, query from time series database
        
        # SLO compliance
        for slo_name in self._slos:
            report["slos"][slo_name] = await self.get_slo_status(slo_name)
        
        # Alert summary
        report["alerts"]["total"] = sum(
            monitoring_alerts_total.labels(
                severity=severity.value,
                policy=""
            )._value.get()
            for severity in AlertSeverity
        )
        
        # Generate recommendations
        for slo_name, status in report["slos"].items():
            if status["error_budget_remaining"] < 20:
                report["recommendations"].append({
                    "type": "slo_risk",
                    "severity": "high",
                    "message": f"SLO {slo_name} has only {status['error_budget_remaining']:.1f}% error budget remaining",
                    "action": "Investigate recent errors and consider reliability improvements"
                })
        
        return report


# Factory functions for common configurations
def create_standard_monitoring_service(
    project_id: str,
    prometheus_gateway: Optional[str] = None
) -> MonitoringService:
    """Create a monitoring service with standard configuration"""
    service = MonitoringService(
        project_id=project_id,
        prometheus_gateway=prometheus_gateway,
        export_interval=60,
        retention_days=30,
        enable_cloud_monitoring=True
    )
    
    # Define standard SLOs
    standard_slos = [
        SLO(
            name="pubsub_availability",
            type=SLOType.AVAILABILITY,
            target=99.9,
            measurement_window=timedelta(days=30),
            service="pubsub",
            description="Overall Pub/Sub availability"
        ),
        SLO(
            name="publish_latency_p99",
            type=SLOType.LATENCY,
            target=99.0,  # 99% of publishes under threshold
            measurement_window=timedelta(hours=1),
            service="pubsub_publisher",
            description="99th percentile publish latency under 100ms"
        ),
        SLO(
            name="message_processing_success",
            type=SLOType.ERROR_RATE,
            target=99.5,
            measurement_window=timedelta(hours=24),
            service="pubsub_processor",
            description="Message processing success rate"
        )
    ]
    
    for slo in standard_slos:
        service.define_slo(slo)
    
    return service


def create_performance_alert_policies() -> List[AlertPolicy]:
    """Create standard performance alert policies"""
    return [
        AlertPolicy(
            name="high_publish_latency",
            condition='metric.type="custom.googleapis.com/pubsub_publish_latency" AND metric.label.status="success"',
            threshold=0.5,  # 500ms
            duration=timedelta(minutes=5),
            severity=AlertSeverity.WARNING,
            documentation="Publish latency exceeds 500ms for 5 minutes"
        ),
        AlertPolicy(
            name="subscription_backlog_critical",
            condition='metric.type="pubsub.googleapis.com/subscription/backlog_bytes"',
            threshold=1073741824,  # 1GB
            duration=timedelta(minutes=10),
            severity=AlertSeverity.CRITICAL,
            documentation="Subscription backlog exceeds 1GB for 10 minutes"
        ),
        AlertPolicy(
            name="high_error_rate",
            condition='metric.type="custom.googleapis.com/pubsub_error_rate"',
            threshold=0.05,  # 5% error rate
            duration=timedelta(minutes=5),
            severity=AlertSeverity.ERROR,
            documentation="Error rate exceeds 5% for 5 minutes"
        ),
        AlertPolicy(
            name="dlq_growth",
            condition='metric.type="custom.googleapis.com/pubsub_dlq_size"',
            threshold=10000,  # 10k messages
            duration=timedelta(minutes=15),
            severity=AlertSeverity.WARNING,
            documentation="DLQ size exceeds 10,000 messages"
        )
    ]


# Custom Prometheus collector for advanced metrics
class PubSubCollector:
    """Custom Prometheus collector for Pub/Sub metrics"""
    
    def __init__(self, monitoring_service: MonitoringService):
        self.monitoring_service = monitoring_service

    def collect(self):
        """Collect metrics for Prometheus"""
        # SLO compliance metrics
        for slo_name, _ in self.monitoring_service._slos.items():  # slo object not used in this implementation
            try:
                status = asyncio.run(
                    self.monitoring_service.get_slo_status(slo_name)
                )
                yield GaugeMetricFamily(
                    f'slo_compliance_{slo_name}',
                    f'SLO compliance for {slo_name}',
                    value=status['current'],
                    labels=['service'],
                    unit='percent'
                )
            except Exception as e:
                logging.error(f"Error collecting SLO metric: {e}")


# Example usage and testing
if __name__ == "__main__":
    async def test_monitoring_service():
        """Test the monitoring service functionality"""
        
        # Initialize service
        service = create_standard_monitoring_service(
            "your-project-id",
            prometheus_gateway="http://localhost:9091"
        )
        await service.initialize()
        
        try:
            # Record some metrics
            service.record_metric(
                "pubsub_publish_latency",
                0.025,  # 25ms
                {"topic": "test-topic", "status": "success"}
            )
            
            service.record_metric(
                "pubsub_message_size",
                1024,  # 1KB
                {"topic": "test-topic"}
            )
            
            # Record SLI measurements
            for i in range(100):
                success = i % 10 != 0  # 90% success rate
                await service.record_sli("pubsub_availability", success)
            
            # Create alert policies
            for policy in create_performance_alert_policies():
                service.create_alert_policy(policy)
            
            # Get SLO status
            for slo_name in ["pubsub_availability", "publish_latency_p99"]:
                status = await service.get_slo_status(slo_name)
                print(f"SLO {slo_name}: {json.dumps(status, indent=2)}")
            
            # Generate performance report
            report = await service.get_performance_report()
            print(f"Performance Report: {json.dumps(report, indent=2, default=str)}")
            
            # Wait for metrics export
            await asyncio.sleep(5)
            
        finally:
            await service.shutdown()
    
    # Run test
    asyncio.run(test_monitoring_service())
