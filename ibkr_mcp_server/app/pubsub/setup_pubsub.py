"""
Google Cloud Pub/Sub Setup Script

Sets up Google Cloud Pub/Sub topics and subscriptions for the IBKR MCP server.
Run this script once to initialize your Pub/Sub infrastructure.
"""

import logging
import os
from typing import Optional
from google.cloud import pubsub_v1
from google.api_core import exceptions

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PubSubSetup:
    """Setup utility for Google Cloud Pub/Sub infrastructure"""
    
    def __init__(self, project_id: str, credentials_path: Optional[str] = None):
        self.project_id = project_id
        self.credentials_path = credentials_path
        
        # Initialize clients
        if credentials_path:
            self.publisher = pubsub_v1.PublisherClient.from_service_account_json(credentials_path)
            self.subscriber = pubsub_v1.SubscriberClient.from_service_account_json(credentials_path)
        else:
            self.publisher = pubsub_v1.PublisherClient()
            self.subscriber = pubsub_v1.SubscriberClient()
    
    def create_topics(self):
        """Create all required topics"""
        topics = [
            'ibkr-market-data',
            'ibkr-orders', 
            'ibkr-positions',
            'ibkr-news',
            'ibkr-trading-signals',
            'ibkr-risk-alerts',
            'ibkr-system-events'
        ]
        
        for topic_name in topics:
            try:
                topic_path = self.publisher.topic_path(self.project_id, topic_name)
                
                self.publisher.create_topic(
                    request={
                        "name": topic_path,
                        "message_retention_duration": {"seconds": 604800}  # 7 days
                    }
                )
                logger.info(f"✅ Created topic: {topic_name}")
                
            except exceptions.AlreadyExists:
                logger.info(f"📋 Topic already exists: {topic_name}")
            except Exception as e:
                logger.error(f"❌ Failed to create topic {topic_name}: {e}")
    
    def create_subscriptions(self):
        """Create all required subscriptions"""
        subscriptions = [
            {
                'name': 'ibkr-market-data-processor',
                'topic': 'ibkr-market-data',
                'description': 'Processes market data for analytics'
            },
            {
                'name': 'ibkr-order-processor', 
                'topic': 'ibkr-orders',
                'description': 'Processes order updates and executions'
            },
            {
                'name': 'ibkr-signal-processor',
                'topic': 'ibkr-trading-signals', 
                'description': 'Processes trading signals for execution'
            },
            {
                'name': 'ibkr-risk-processor',
                'topic': 'ibkr-risk-alerts',
                'description': 'Processes risk alerts and notifications'
            },
            {
                'name': 'ibkr-websocket-broadcaster',
                'topic': 'ibkr-market-data',
                'description': 'Broadcasts data to WebSocket clients'
            },
            {
                'name': 'ibkr-position-tracker',
                'topic': 'ibkr-positions',
                'description': 'Tracks position changes and P&L'
            },
            {
                'name': 'ibkr-news-processor',
                'topic': 'ibkr-news',
                'description': 'Processes news and market events'
            }
        ]
        
        for sub_config in subscriptions:
            try:
                topic_path = self.publisher.topic_path(self.project_id, sub_config['topic'])
                subscription_path = self.subscriber.subscription_path(
                    self.project_id, sub_config['name']
                )
                
                self.subscriber.create_subscription(
                    request={
                        "name": subscription_path,
                        "topic": topic_path,
                        "ack_deadline_seconds": 60,
                        "enable_exactly_once_delivery": True,
                        "enable_message_ordering": True,
                        "message_retention_duration": {"seconds": 604800}
                    }
                )
                logger.info(f"✅ Created subscription: {sub_config['name']} -> {sub_config['topic']}")
                
            except exceptions.AlreadyExists:
                logger.info(f"📋 Subscription already exists: {sub_config['name']}")
            except Exception as e:
                logger.error(f"❌ Failed to create subscription {sub_config['name']}: {e}")
    
    def create_dead_letter_topics(self):
        """Create dead letter topics for failed message handling"""
        dead_letter_topics = [
            'ibkr-market-data-dlq',
            'ibkr-orders-dlq',
            'ibkr-signals-dlq',
            'ibkr-risk-alerts-dlq'
        ]
        
        for topic_name in dead_letter_topics:
            try:
                topic_path = self.publisher.topic_path(self.project_id, topic_name)
                
                self.publisher.create_topic(
                    request={
                        "name": topic_path,
                        "message_retention_duration": {"seconds": 2592000}  # 30 days
                    }
                )
                logger.info(f"✅ Created dead letter topic: {topic_name}")
                
            except exceptions.AlreadyExists:
                logger.info(f"📋 Dead letter topic already exists: {topic_name}")
            except Exception as e:
                logger.error(f"❌ Failed to create dead letter topic {topic_name}: {e}")
    
    def setup_iam_policies(self):
        """Setup IAM policies for Pub/Sub access"""
        logger.info("🔐 Setting up IAM policies...")
        
        # Note: This would require additional IAM setup
        # For now, ensure your service account has these roles:
        # - Pub/Sub Publisher
        # - Pub/Sub Subscriber  
        # - Pub/Sub Admin (for topic/subscription management)
        
        logger.info("📝 Ensure your service account has the following roles:")
        logger.info("   - roles/pubsub.publisher")
        logger.info("   - roles/pubsub.subscriber") 
        logger.info("   - roles/pubsub.admin")
    
    def verify_setup(self):
        """Verify that all topics and subscriptions are created"""
        logger.info("🔍 Verifying Pub/Sub setup...")
        
        # List topics
        project_path = f"projects/{self.project_id}"
        topics = list(self.publisher.list_topics(request={"project": project_path}))
        logger.info(f"📊 Found {len(topics)} topics")
        
        # List subscriptions
        subscriptions = list(self.subscriber.list_subscriptions(request={"project": project_path}))
        logger.info(f"📊 Found {len(subscriptions)} subscriptions")
        
        return len(topics) > 0 and len(subscriptions) > 0
    
    def run_setup(self):
        """Run complete Pub/Sub setup"""
        logger.info("🚀 Starting Google Cloud Pub/Sub setup for IBKR MCP Server")
        logger.info(f"📍 Project ID: {self.project_id}")
        
        try:
            # Create topics
            logger.info("\n📡 Creating topics...")
            self.create_topics()
            
            # Create dead letter topics
            logger.info("\n💀 Creating dead letter topics...")
            self.create_dead_letter_topics()
            
            # Create subscriptions
            logger.info("\n📥 Creating subscriptions...")
            self.create_subscriptions()
            
            # Setup IAM
            logger.info("\n🔐 IAM setup...")
            self.setup_iam_policies()
            
            # Verify setup
            logger.info("\n✅ Verifying setup...")
            if self.verify_setup():
                logger.info("🎉 Pub/Sub setup completed successfully!")
                return True
            else:
                logger.error("❌ Setup verification failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Setup failed: {e}")
            return False


def main():
    """Main setup function"""
    # Configuration
    project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'gen-lang-client-**********')
    credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS', 'pubsub/gcp-service-account-key.json')
    
    if project_id == 'your-gcp-project-id':
        logger.error("❌ Please set GOOGLE_CLOUD_PROJECT environment variable")
        return
    
    logger.info("=" * 60)
    logger.info("🏗️  IBKR MCP Server - Google Cloud Pub/Sub Setup")
    logger.info("=" * 60)
    
    # Run setup
    setup = PubSubSetup(project_id, credentials_path)
    success = setup.run_setup()
    
    if success:
        logger.info("\n🎯 Next steps:")
        logger.info("1. Update pubsub_manager.py with your project_id")
        logger.info("2. Set GOOGLE_APPLICATION_CREDENTIALS environment variable")
        logger.info("3. Start the IBKR MCP server with Pub/Sub enabled")
        logger.info("4. Monitor messages in Google Cloud Console")
    else:
        logger.error("\n❌ Setup failed. Please check the errors above.")


if __name__ == "__main__":
    main()
