#!/usr/bin/env python3
"""
Dead Letter Queue Manager for Google Cloud Pub/Sub

This module provides comprehensive dead letter queue (DLQ) management with:
- Failed message handling with categorization
- Configurable retry policies with exponential backoff
- Message inspection, filtering, and replay capabilities
- Error pattern analysis and alerting
- Integration with monitoring systems

Academic Foundations:
- Implements fault tolerance patterns from distributed systems literature
- Based on "Reactive Messaging Patterns" by Enterprise Integration Patterns
- Follows Google's SRE practices for error handling and recovery

Industrial Applications:
- Production-ready error recovery system
- Supports compliance requirements for message retention
- Enables debugging and audit trails
- Provides operational insights into system failures

Author: IBKR Integration Team
Date: 2024
Version: 1.0.0
"""

# App-specific imports (added during migration)
from app.core.config import settings
from app.models.database_models import Base
from app.services.base_service import BaseService


# App-specific imports (added during migration)
from app.core.config import settings
from app.models.database_models import Base
from app.services.base_service import BaseService

import asyncio
import json
import logging
import time
import zlib
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union

from google.api_core import retry
from google.cloud import error_reporting, pubsub_v1
from google.protobuf import field_mask_pb2
from prometheus_client import Counter, Gauge, Histogram, Summary

# Metrics
dlq_messages_total = Counter(
    'pubsub_dlq_messages_total',
    'Total messages sent to DLQ',
    ['topic', 'subscription', 'error_type']
)

dlq_retries_total = Counter(
    'pubsub_dlq_retries_total',
    'Total retry attempts',
    ['topic', 'subscription', 'retry_number']
)

dlq_replay_total = Counter(
    'pubsub_dlq_replay_total',
    'Total messages replayed from DLQ',
    ['topic', 'subscription', 'status']
)

dlq_size_gauge = Gauge(
    'pubsub_dlq_size',
    'Current size of DLQ',
    ['dlq_topic']
)

dlq_processing_time = Histogram(
    'pubsub_dlq_processing_seconds',
    'Time spent processing DLQ messages',
    ['operation']
)

dlq_age_seconds = Summary(
    'pubsub_dlq_message_age_seconds',
    'Age of messages in DLQ',
    ['dlq_topic']
)


class ErrorCategory(Enum):
    """Categories of errors for failed messages"""
    PARSING_ERROR = "parsing_error"
    VALIDATION_ERROR = "validation_error"
    PROCESSING_ERROR = "processing_error"
    TIMEOUT_ERROR = "timeout_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    AUTHORIZATION_ERROR = "authorization_error"
    INFRASTRUCTURE_ERROR = "infrastructure_error"
    UNKNOWN_ERROR = "unknown_error"


class RetryPolicy(Enum):
    """Retry policies for failed messages"""
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    LINEAR_BACKOFF = "linear_backoff"
    FIXED_DELAY = "fixed_delay"
    CUSTOM = "custom"


@dataclass
class DLQMessage:
    """Represents a message in the dead letter queue"""
    message_id: str
    original_topic: str
    original_subscription: str
    payload: bytes
    attributes: Dict[str, str]
    error_category: ErrorCategory
    error_message: str
    failure_count: int
    first_failure_time: datetime
    last_failure_time: datetime
    retry_after: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RetryConfig:
    """Configuration for retry policies"""
    policy: RetryPolicy = RetryPolicy.EXPONENTIAL_BACKOFF
    max_attempts: int = 5
    initial_delay_seconds: float = 1.0
    max_delay_seconds: float = 3600.0  # 1 hour
    backoff_multiplier: float = 2.0
    jitter: bool = True
    predicate: Optional[Callable[[DLQMessage], bool]] = None


@dataclass
class ReplayConfig:
    """Configuration for message replay"""
    target_topic: Optional[str] = None
    filter_predicate: Optional[Callable[[DLQMessage], bool]] = None
    max_messages: Optional[int] = None
    rate_limit: Optional[int] = None  # Messages per second
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    dry_run: bool = False


class DeadLetterQueueManager:
    """
    Manages dead letter queues for failed message handling
    
    Features:
    - Automatic DLQ topic creation and configuration
    - Smart retry policies with exponential backoff
    - Message categorization and error analysis
    - Replay capabilities with filtering
    - Integration with monitoring and alerting
    """
    
    def __init__(
        self,
        project_id: str,
        dlq_suffix: str = "_dlq",
        default_retention_days: int = 7,
        enable_monitoring: bool = True,
        error_reporter: Optional[error_reporting.Client] = None
    ):
        """
        Initialize the Dead Letter Queue Manager
        
        Args:
            project_id: Google Cloud project ID
            dlq_suffix: Suffix for DLQ topic names
            default_retention_days: Default retention for DLQ messages
            enable_monitoring: Enable Prometheus metrics
            error_reporter: Google Cloud Error Reporting client
        """
        self.project_id = project_id
        self.dlq_suffix = dlq_suffix
        self.default_retention_days = default_retention_days
        self.enable_monitoring = enable_monitoring
        
        # Clients
        self.publisher = pubsub_v1.PublisherClient()
        self.subscriber = pubsub_v1.SubscriberClient()
        self.error_reporter = error_reporter or error_reporting.Client()
        
        # State management
        self._dlq_topics: Dict[str, str] = {}  # source_topic -> dlq_topic
        self._retry_configs: Dict[str, RetryConfig] = {}
        self._error_patterns: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._active_retries: Set[str] = set()
        
        # Logging
        self.logger = logging.getLogger(__name__)
        
        # Background tasks
        self._tasks: List[asyncio.Task] = []
        self._running = False
    
    async def initialize(self) -> None:
        """Initialize the DLQ manager and start background tasks"""
        self._running = True
        
        # Start background tasks
        self._tasks.append(asyncio.create_task(self._monitor_dlqs()))
        self._tasks.append(asyncio.create_task(self._process_retries()))
        self._tasks.append(asyncio.create_task(self._analyze_error_patterns()))
        
        self.logger.info(f"DLQ Manager initialized for project {self.project_id}")
    
    async def shutdown(self) -> None:
        """Shutdown the DLQ manager and cleanup resources"""
        self._running = False
        
        # Cancel background tasks
        for task in self._tasks:
            task.cancel()
        
        await asyncio.gather(*self._tasks, return_exceptions=True)
        self._tasks.clear()
        
        self.logger.info("DLQ Manager shutdown complete")
    
    def create_dlq_for_subscription(
        self,
        subscription_name: str,
        max_delivery_attempts: int = 5,
        retention_days: Optional[int] = None
    ) -> str:
        """
        Create or update DLQ configuration for a subscription
        
        Args:
            subscription_name: Full subscription name
            max_delivery_attempts: Max delivery attempts before DLQ
            retention_days: Message retention in DLQ
            
        Returns:
            DLQ topic name
        """
        # Extract topic from subscription
        subscription = self.subscriber.get_subscription(
            request={"subscription": subscription_name}
        )
        source_topic = subscription.topic
        
        # Create DLQ topic
        dlq_topic = self._create_dlq_topic(source_topic, retention_days)
        
        # Update subscription with DLQ policy
        dead_letter_policy = {
            "dead_letter_topic": dlq_topic,
            "max_delivery_attempts": max_delivery_attempts
        }

        update_mask = field_mask_pb2.FieldMask(paths=["dead_letter_policy"])

        # Update subscription using the request format
        self.subscriber.update_subscription(
            request={
                "subscription": {
                    "name": subscription_name,
                    "dead_letter_policy": dead_letter_policy
                },
                "update_mask": update_mask
            }
        )
        
        self._dlq_topics[source_topic] = dlq_topic
        self.logger.info(
            f"DLQ configured for subscription {subscription_name} "
            f"with topic {dlq_topic}"
        )
        
        return dlq_topic
    
    def _create_dlq_topic(
        self,
        source_topic: str,
        retention_days: Optional[int] = None
    ) -> str:
        """Create DLQ topic if it doesn't exist"""
        # Generate DLQ topic name
        topic_parts = source_topic.split('/')
        dlq_topic_name = f"{topic_parts[-1]}{self.dlq_suffix}"
        dlq_topic = f"projects/{self.project_id}/topics/{dlq_topic_name}"
        
        try:
            # Create topic
            self.publisher.create_topic(
                request={"name": dlq_topic}
            )
            self.logger.info(f"Created DLQ topic: {dlq_topic}")
        except Exception as e:
            if "already exists" not in str(e):
                raise
        
        # Set retention policy
        retention_seconds = (retention_days or self.default_retention_days) * 86400
        self.publisher.update_topic(
            request={
                "topic": {
                    "name": dlq_topic,
                    "message_retention_duration": {"seconds": retention_seconds}
                },
                "update_mask": {"paths": ["message_retention_duration"]}
            }
        )
        
        return dlq_topic
    
    async def send_to_dlq(
        self,
        message: Any,  # pubsub_v1.PubsubMessage
        source_topic: str,
        source_subscription: str,
        error: Exception,
        error_category: Optional[ErrorCategory] = None
    ) -> str:
        """
        Send a failed message to the dead letter queue
        
        Args:
            message: Failed message
            source_topic: Original topic
            source_subscription: Original subscription
            error: Exception that caused the failure
            error_category: Category of error
            
        Returns:
            Published message ID
        """
        with dlq_processing_time.labels(operation='send_to_dlq').time():
            # Determine error category
            if error_category is None:
                error_category = self._categorize_error(error)
            
            # Get or create DLQ topic
            dlq_topic = self._dlq_topics.get(
                source_topic,
                self._create_dlq_topic(source_topic)
            )
            
            # Prepare DLQ message attributes
            dlq_attributes = {
                **message.attributes,
                "dlq_original_topic": source_topic,
                "dlq_original_subscription": source_subscription,
                "dlq_error_category": error_category.value,
                "dlq_error_message": str(error),
                "dlq_failure_count": str(
                    int(message.attributes.get("dlq_failure_count", "0")) + 1
                ),
                "dlq_first_failure_time": message.attributes.get(
                    "dlq_first_failure_time",
                    datetime.now(timezone.utc).isoformat()
                ),
                "dlq_last_failure_time": datetime.now(timezone.utc).isoformat()
            }
            
            # Publish to DLQ
            future = self.publisher.publish(
                dlq_topic,
                message.data,
                **dlq_attributes
            )
            
            message_id = future.result()
            
            # Update metrics
            dlq_messages_total.labels(
                topic=source_topic,
                subscription=source_subscription,
                error_type=error_category.value
            ).inc()
            
            # Record error pattern
            self._error_patterns[error_category.value].append({
                "timestamp": datetime.now(timezone.utc),
                "topic": source_topic,
                "error": str(error)
            })
            
            # Report to error reporting
            if self.error_reporter:
                self.error_reporter.report_exception()
            
            self.logger.warning(
                f"Message {message.message_id} sent to DLQ {dlq_topic} "
                f"due to {error_category.value}: {error}"
            )
            
            return message_id
    
    def _categorize_error(self, error: Exception) -> ErrorCategory:
        """Categorize an error based on exception type and message"""
        error_str = str(error).lower()
        error_type = type(error).__name__
        
        if "parsing" in error_str or "decode" in error_str:
            return ErrorCategory.PARSING_ERROR
        elif "validation" in error_str or "schema" in error_str:
            return ErrorCategory.VALIDATION_ERROR
        elif "timeout" in error_str or "deadline" in error_str:
            return ErrorCategory.TIMEOUT_ERROR
        elif "rate" in error_str or "quota" in error_str:
            return ErrorCategory.RATE_LIMIT_ERROR
        elif "auth" in error_str or "permission" in error_str:
            return ErrorCategory.AUTHORIZATION_ERROR
        elif "connect" in error_str or "network" in error_str:
            return ErrorCategory.INFRASTRUCTURE_ERROR
        elif error_type in ["ValueError", "KeyError", "AttributeError"]:
            return ErrorCategory.PROCESSING_ERROR
        else:
            return ErrorCategory.UNKNOWN_ERROR
    
    def configure_retry_policy(
        self,
        topic: str,
        retry_config: RetryConfig
    ) -> None:
        """
        Configure retry policy for a specific topic
        
        Args:
            topic: Topic to configure
            retry_config: Retry configuration
        """
        self._retry_configs[topic] = retry_config
        self.logger.info(
            f"Configured retry policy for {topic}: "
            f"{retry_config.policy.value} with max {retry_config.max_attempts} attempts"
        )
    
    async def _process_retries(self) -> None:
        """Background task to process message retries"""
        while self._running:
            try:
                # Check each DLQ for messages ready to retry
                for source_topic, dlq_topic in self._dlq_topics.items():
                    if source_topic in self._active_retries:
                        continue
                    
                    self._active_retries.add(source_topic)
                    asyncio.create_task(
                        self._process_topic_retries(source_topic, dlq_topic)
                    )
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Error in retry processor: {e}")
                await asyncio.sleep(30)
    
    async def _process_topic_retries(
        self,
        source_topic: str,
        dlq_topic: str
    ) -> None:
        """Process retries for a specific topic"""
        try:
            retry_config = self._retry_configs.get(
                source_topic,
                RetryConfig()  # Default config
            )
            
            # Create subscription for DLQ if not exists
            dlq_subscription = f"{dlq_topic}_retry_sub"
            try:
                self.subscriber.create_subscription(
                    request={
                        "name": dlq_subscription,
                        "topic": dlq_topic,
                        "ack_deadline_seconds": 60
                    }
                )
            except Exception as e:
                if "already exists" not in str(e):
                    raise
            
            # Pull messages ready for retry
            response = self.subscriber.pull(
                request={
                    "subscription": dlq_subscription,
                    "max_messages": 100,
                    "return_immediately": True
                }
            )
            
            for message in response.received_messages:
                await self._process_retry_message(
                    message,
                    source_topic,
                    retry_config
                )
            
        finally:
            self._active_retries.discard(source_topic)
    
    async def _process_retry_message(
        self,
        received_message: Any,  # pubsub_v1.ReceivedMessage
        source_topic: str,
        retry_config: RetryConfig
    ) -> None:
        """Process a single message for retry"""
        message = received_message.message
        attributes = dict(message.attributes)
        
        # Check if message should be retried
        failure_count = int(attributes.get("dlq_failure_count", "0"))
        if failure_count >= retry_config.max_attempts:
            # Max retries exceeded, acknowledge and skip
            self.subscriber.acknowledge(
                request={
                    "subscription": received_message.subscription,
                    "ack_ids": [received_message.ack_id]
                }
            )
            return
        
        # Calculate retry delay
        last_failure = datetime.fromisoformat(
            attributes["dlq_last_failure_time"]
        )
        retry_delay = self._calculate_retry_delay(
            failure_count,
            retry_config
        )
        retry_after = last_failure + timedelta(seconds=retry_delay)
        
        if datetime.now(timezone.utc) < retry_after:
            # Not ready for retry yet
            return
        
        # Apply predicate if configured
        if retry_config.predicate:
            dlq_message = self._create_dlq_message(message)
            if not retry_config.predicate(dlq_message):
                # Skip this message
                self.subscriber.acknowledge(
                    request={
                        "subscription": received_message.subscription,
                        "ack_ids": [received_message.ack_id]
                    }
                )
                return
        
        # Retry the message
        try:
            # Remove DLQ attributes before republishing
            clean_attributes = {
                k: v for k, v in attributes.items()
                if not k.startswith("dlq_")
            }
            clean_attributes["retry_count"] = str(failure_count)
            
            # Publish to original topic
            future = self.publisher.publish(
                source_topic,
                message.data,
                **clean_attributes
            )
            future.result()
            
            # Acknowledge from DLQ
            self.subscriber.acknowledge(
                request={
                    "subscription": received_message.subscription,
                    "ack_ids": [received_message.ack_id]
                }
            )
            
            # Update metrics
            dlq_retries_total.labels(
                topic=source_topic,
                subscription=attributes["dlq_original_subscription"],
                retry_number=str(failure_count)
            ).inc()
            
            self.logger.info(
                f"Retried message {message.message_id} to {source_topic} "
                f"(attempt {failure_count + 1})"
            )
            
        except Exception as e:
            self.logger.error(f"Failed to retry message: {e}")
            # Message will be redelivered by Pub/Sub
    
    def _calculate_retry_delay(
        self,
        failure_count: int,
        retry_config: RetryConfig
    ) -> float:
        """Calculate retry delay based on policy and failure count"""
        if retry_config.policy == RetryPolicy.EXPONENTIAL_BACKOFF:
            delay = min(
                retry_config.initial_delay_seconds * (
                    retry_config.backoff_multiplier ** failure_count
                ),
                retry_config.max_delay_seconds
            )
        elif retry_config.policy == RetryPolicy.LINEAR_BACKOFF:
            delay = min(
                retry_config.initial_delay_seconds * failure_count,
                retry_config.max_delay_seconds
            )
        elif retry_config.policy == RetryPolicy.FIXED_DELAY:
            delay = retry_config.initial_delay_seconds
        else:
            delay = retry_config.initial_delay_seconds
        
        # Add jitter if enabled
        if retry_config.jitter:
            import random
            delay *= (0.5 + random.random())
        
        return delay
    
    async def replay_messages(
        self,
        dlq_topic: str,
        replay_config: ReplayConfig
    ) -> Dict[str, Any]:
        """
        Replay messages from DLQ based on configuration
        
        Args:
            dlq_topic: DLQ topic to replay from
            replay_config: Replay configuration
            
        Returns:
            Replay results and statistics
        """
        with dlq_processing_time.labels(operation='replay').time():
            results = {
                "total_messages": 0,
                "replayed_messages": 0,
                "skipped_messages": 0,
                "failed_messages": 0,
                "dry_run": replay_config.dry_run
            }
            
            # Create temporary subscription for replay
            replay_subscription = f"{dlq_topic}_replay_{int(time.time())}"
            self.subscriber.create_subscription(
                request={
                    "name": replay_subscription,
                    "topic": dlq_topic,
                    "ack_deadline_seconds": 600  # 10 minutes for replay
                }
            )
            
            try:
                # Process messages in batches
                while (replay_config.max_messages is None or 
                       results["total_messages"] < replay_config.max_messages):
                    
                    # Pull batch
                    response = self.subscriber.pull(
                        request={
                            "subscription": replay_subscription,
                            "max_messages": min(
                                100,
                                replay_config.max_messages - results["total_messages"]
                                if replay_config.max_messages else 100
                            )
                        }
                    )
                    
                    if not response.received_messages:
                        break
                    
                    # Process batch
                    for received_message in response.received_messages:
                        results["total_messages"] += 1
                        
                        # Apply filters
                        if not self._should_replay_message(
                            received_message.message,
                            replay_config
                        ):
                            results["skipped_messages"] += 1
                            continue
                        
                        # Replay message
                        if not replay_config.dry_run:
                            try:
                                await self._replay_single_message(
                                    received_message.message,
                                    replay_config
                                )
                                results["replayed_messages"] += 1
                            except Exception as e:
                                self.logger.error(f"Failed to replay message: {e}")
                                results["failed_messages"] += 1
                        else:
                            results["replayed_messages"] += 1
                        
                        # Rate limiting
                        if replay_config.rate_limit:
                            await asyncio.sleep(1.0 / replay_config.rate_limit)
                    
                    # Acknowledge processed messages
                    if not replay_config.dry_run:
                        self.subscriber.acknowledge(
                            request={
                                "subscription": replay_subscription,
                                "ack_ids": [m.ack_id for m in response.received_messages]
                            }
                        )
                
            finally:
                # Cleanup temporary subscription
                self.subscriber.delete_subscription(
                    request={"subscription": replay_subscription}
                )
            
            # Update metrics
            dlq_replay_total.labels(
                topic=dlq_topic,
                subscription="replay",
                status="success" if results["failed_messages"] == 0 else "partial"
            ).inc(results["replayed_messages"])
            
            return results
    
    def _should_replay_message(
        self,
        message: Any,  # pubsub_v1.PubsubMessage
        replay_config: ReplayConfig
    ) -> bool:
        """Check if message should be replayed based on filters"""
        attributes = dict(message.attributes)
        
        # Time filter
        if replay_config.start_time or replay_config.end_time:
            message_time = datetime.fromisoformat(
                attributes.get("dlq_last_failure_time", "")
            )
            
            if replay_config.start_time and message_time < replay_config.start_time:
                return False
            if replay_config.end_time and message_time > replay_config.end_time:
                return False
        
        # Custom filter predicate
        if replay_config.filter_predicate:
            dlq_message = self._create_dlq_message(message)
            if not replay_config.filter_predicate(dlq_message):
                return False
        
        return True
    
    async def _replay_single_message(
        self,
        message: Any,  # pubsub_v1.PubsubMessage
        replay_config: ReplayConfig
    ) -> None:
        """Replay a single message"""
        attributes = dict(message.attributes)
        
        # Determine target topic
        target_topic = (
            replay_config.target_topic or
            attributes.get("dlq_original_topic", "")
        )
        
        if not target_topic:
            raise ValueError("No target topic specified for replay")
        
        # Clean attributes
        clean_attributes = {
            k: v for k, v in attributes.items()
            if not k.startswith("dlq_")
        }
        clean_attributes["replayed_from_dlq"] = "true"
        clean_attributes["replay_timestamp"] = datetime.now(timezone.utc).isoformat()
        
        # Publish message
        future = self.publisher.publish(
            target_topic,
            message.data,
            **clean_attributes
        )
        future.result()
    
    def _create_dlq_message(
        self,
        message: Any  # pubsub_v1.PubsubMessage
    ) -> DLQMessage:
        """Create DLQMessage from Pub/Sub message"""
        attributes = dict(message.attributes)
        
        return DLQMessage(
            message_id=message.message_id,
            original_topic=attributes.get("dlq_original_topic", ""),
            original_subscription=attributes.get("dlq_original_subscription", ""),
            payload=message.data,
            attributes=attributes,
            error_category=ErrorCategory(
                attributes.get("dlq_error_category", ErrorCategory.UNKNOWN_ERROR.value)
            ),
            error_message=attributes.get("dlq_error_message", ""),
            failure_count=int(attributes.get("dlq_failure_count", "0")),
            first_failure_time=datetime.fromisoformat(
                attributes.get("dlq_first_failure_time", datetime.now(timezone.utc).isoformat())
            ),
            last_failure_time=datetime.fromisoformat(
                attributes.get("dlq_last_failure_time", datetime.now(timezone.utc).isoformat())
            )
        )
    
    async def inspect_dlq(
        self,
        dlq_topic: str,
        limit: int = 100,
        filter_category: Optional[ErrorCategory] = None
    ) -> List[DLQMessage]:
        """
        Inspect messages in a DLQ without consuming them
        
        Args:
            dlq_topic: DLQ topic to inspect
            limit: Maximum messages to return
            filter_category: Filter by error category
            
        Returns:
            List of DLQ messages
        """
        # Create temporary subscription
        inspect_subscription = f"{dlq_topic}_inspect_{int(time.time())}"
        self.subscriber.create_subscription(
            request={
                "name": inspect_subscription,
                "topic": dlq_topic,
                "ack_deadline_seconds": 10
            }
        )
        
        try:
            messages = []
            
            # Pull messages
            response = self.subscriber.pull(
                request={
                    "subscription": inspect_subscription,
                    "max_messages": limit,
                    "return_immediately": True
                }
            )
            
            for received_message in response.received_messages:
                dlq_message = self._create_dlq_message(received_message.message)
                
                # Apply filter
                if filter_category and dlq_message.error_category != filter_category:
                    continue
                
                messages.append(dlq_message)
                
                # Update age metric
                age = (datetime.now(timezone.utc) - dlq_message.last_failure_time).total_seconds()
                dlq_age_seconds.labels(dlq_topic=dlq_topic).observe(age)
            
            # Update size metric
            dlq_size_gauge.labels(dlq_topic=dlq_topic).set(len(messages))
            
            return messages
            
        finally:
            # Cleanup temporary subscription
            self.subscriber.delete_subscription(
                request={"subscription": inspect_subscription}
            )
    
    async def get_error_statistics(
        self,
        time_window: timedelta = timedelta(hours=1)
    ) -> Dict[str, Any]:
        """
        Get error statistics across all DLQs
        
        Args:
            time_window: Time window for statistics
            
        Returns:
            Error statistics by category and topic
        """
        cutoff_time = datetime.now(timezone.utc) - time_window
        stats = {
            "by_category": defaultdict(int),
            "by_topic": defaultdict(lambda: defaultdict(int)),
            "top_errors": [],
            "error_rate": {}
        }
        
        # Analyze error patterns
        for category, errors in self._error_patterns.items():
            recent_errors = [
                e for e in errors
                if e["timestamp"] > cutoff_time
            ]
            
            stats["by_category"][category] = len(recent_errors)
            
            # Group by topic
            for error in recent_errors:
                stats["by_topic"][error["topic"]][category] += 1
        
        # Calculate error rates
        for topic, categories in stats["by_topic"].items():
            total = sum(categories.values())
            stats["error_rate"][topic] = total / time_window.total_seconds()
        
        # Find top error messages
        all_errors = []
        for errors in self._error_patterns.values():
            all_errors.extend([
                e["error"] for e in errors
                if e["timestamp"] > cutoff_time
            ])
        
        from collections import Counter
        error_counts = Counter(all_errors)
        stats["top_errors"] = error_counts.most_common(10)
        
        return dict(stats)
    
    async def _monitor_dlqs(self) -> None:
        """Background task to monitor DLQ health"""
        while self._running:
            try:
                for dlq_topic in self._dlq_topics.values():
                    # Get current size
                    messages = await self.inspect_dlq(dlq_topic, limit=1000)
                    dlq_size_gauge.labels(dlq_topic=dlq_topic).set(len(messages))
                    
                    # Check for old messages
                    old_messages = [
                        m for m in messages
                        if (datetime.now(timezone.utc) - m.last_failure_time).days > 3
                    ]
                    
                    if old_messages:
                        self.logger.warning(
                            f"DLQ {dlq_topic} has {len(old_messages)} messages "
                            f"older than 3 days"
                        )
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error monitoring DLQs: {e}")
                await asyncio.sleep(60)
    
    async def _analyze_error_patterns(self) -> None:
        """Background task to analyze error patterns and send alerts"""
        while self._running:
            try:
                stats = await self.get_error_statistics()
                
                # Check for anomalies
                for topic, rate in stats["error_rate"].items():
                    if rate > 10:  # More than 10 errors per second
                        self.logger.critical(
                            f"High error rate detected for {topic}: "
                            f"{rate:.2f} errors/second"
                        )
                        
                        # Could trigger alerts here
                        if self.error_reporter:
                            self.error_reporter.report_exception(
                                Exception(f"High error rate on {topic}")
                            )
                
                # Check for error category spikes
                total_errors = sum(stats["by_category"].values())
                if total_errors > 0:
                    for category, count in stats["by_category"].items():
                        percentage = (count / total_errors) * 100
                        if percentage > 50:  # More than 50% of errors
                            self.logger.warning(
                                f"Error category {category} represents "
                                f"{percentage:.1f}% of all errors"
                            )
                
                await asyncio.sleep(60)  # Analyze every minute
                
            except Exception as e:
                self.logger.error(f"Error analyzing patterns: {e}")
                await asyncio.sleep(60)


# Factory functions for common configurations
def create_standard_dlq_manager(project_id: str) -> DeadLetterQueueManager:
    """Create a DLQ manager with standard configuration"""
    return DeadLetterQueueManager(
        project_id=project_id,
        dlq_suffix="_dlq",
        default_retention_days=7,
        enable_monitoring=True
    )


def create_aggressive_retry_config() -> RetryConfig:
    """Create an aggressive retry configuration for transient errors"""
    return RetryConfig(
        policy=RetryPolicy.EXPONENTIAL_BACKOFF,
        max_attempts=10,
        initial_delay_seconds=0.5,
        max_delay_seconds=300,
        backoff_multiplier=1.5,
        jitter=True,
        predicate=lambda msg: msg.error_category in [
            ErrorCategory.TIMEOUT_ERROR,
            ErrorCategory.RATE_LIMIT_ERROR,
            ErrorCategory.INFRASTRUCTURE_ERROR
        ]
    )


def create_conservative_retry_config() -> RetryConfig:
    """Create a conservative retry configuration"""
    return RetryConfig(
        policy=RetryPolicy.EXPONENTIAL_BACKOFF,
        max_attempts=3,
        initial_delay_seconds=10,
        max_delay_seconds=3600,
        backoff_multiplier=3,
        jitter=True
    )


# Example usage and testing
if __name__ == "__main__":
    async def test_dlq_manager():
        """Test the DLQ manager functionality"""
        
        # Initialize manager
        manager = create_standard_dlq_manager("your-project-id")
        await manager.initialize()
        
        try:
            # Configure DLQ for a subscription
            dlq_topic = manager.create_dlq_for_subscription(
                "projects/your-project-id/subscriptions/test-subscription",
                max_delivery_attempts=5
            )
            print(f"Created DLQ: {dlq_topic}")
            
            # Configure retry policy
            manager.configure_retry_policy(
                "projects/your-project-id/topics/test-topic",
                create_aggressive_retry_config()
            )
            
            # Simulate message failure
            test_message = type('MockMessage', (), {
                'data': b'{"test": "data"}',
                'attributes': {"source": "test"},
                'message_id': "test-123"
            })()
            
            message_id = await manager.send_to_dlq(
                test_message,
                "projects/your-project-id/topics/test-topic",
                "projects/your-project-id/subscriptions/test-subscription",
                ValueError("Test processing error"),
                ErrorCategory.PROCESSING_ERROR
            )
            print(f"Sent to DLQ: {message_id}")
            
            # Inspect DLQ
            messages = await manager.inspect_dlq(dlq_topic, limit=10)
            print(f"DLQ contains {len(messages)} messages")
            
            # Get error statistics
            stats = await manager.get_error_statistics()
            print(f"Error statistics: {json.dumps(stats, indent=2, default=str)}")
            
            # Test replay (dry run)
            replay_config = ReplayConfig(
                target_topic="projects/your-project-id/topics/test-topic",
                max_messages=10,
                dry_run=True
            )
            
            results = await manager.replay_messages(dlq_topic, replay_config)
            print(f"Replay results: {results}")
            
        finally:
            await manager.shutdown()
    
    # Run test
    asyncio.run(test_dlq_manager())
