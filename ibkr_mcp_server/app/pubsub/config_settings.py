"""
Configuration Settings for Enhanced Pub/Sub Components

Add these settings to your app/core/config.py or .env file to enable
and configure the enhanced Pub/Sub components.
"""

# Add to app/core/config.py Settings class:

class Settings:
    # ... existing settings ...
    
    # Enhanced Pub/Sub Settings
    USE_ENHANCED_PUBSUB: bool = True
    
    # Circuit Breaker Settings
    CIRCUIT_BREAKER_THRESHOLD: int = 5
    CIRCUIT_BREAKER_TIMEOUT: int = 60
    
    # Message Deduplication
    ENABLE_MESSAGE_DEDUPLICATION: bool = True
    DEDUPLICATION_TTL: int = 300  # 5 minutes
    
    # Batch Publishing
    ENABLE_BATCH_PUBLISHER: bool = True
    PUBSUB_BATCH_MAX_MESSAGES: int = 1000
    PUBSUB_BATCH_MAX_BYTES: int = 10 * 1024 * 1024  # 10MB
    PUBSUB_BATCH_MAX_LATENCY: float = 0.01  # 10ms
    
    # Schema Registry
    ENABLE_SCHEMA_REGISTRY: bool = True
    STRICT_SCHEMA_VALIDATION: bool = False
    
    # Streaming Aggregator
    ENABLE_STREAMING_AGGREGATOR: bool = True
    AGGREGATION_WINDOWS: list = ["1s", "5s", "1m", "5m"]
    
    # Distributed Processor
    ENABLE_DISTRIBUTED_PROCESSOR: bool = True
    PROCESSOR_MIN_WORKERS: int = 2
    PROCESSOR_MAX_WORKERS: int = 10
    
    # Dead Letter Queue
    ENABLE_DLQ: bool = True
    DLQ_MAX_ATTEMPTS: int = 5
    DLQ_RETENTION_DAYS: int = 7
    
    # Monitoring
    ENABLE_MONITORING: bool = True
    PROMETHEUS_GATEWAY: str = "http://localhost:9091"
    
    # Multi-Region
    ENABLE_MULTI_REGION: bool = False
    PRIMARY_REGION: str = "us-central1"
    ENABLED_REGIONS: list = ["us-central1", "us-east1", "europe-west1"]
    
    # Performance Settings
    PUBSUB_MAX_MESSAGES: int = 100000
    PUBSUB_MAX_BYTES: int = 1024 * 1024 * 1024  # 1GB


# Add to .env file:
"""
# Enhanced Pub/Sub Configuration
USE_ENHANCED_PUBSUB=true

# Circuit Breaker
CIRCUIT_BREAKER_THRESHOLD=5
CIRCUIT_BREAKER_TIMEOUT=60

# Message Deduplication
ENABLE_MESSAGE_DEDUPLICATION=true
DEDUPLICATION_TTL=300

# Batch Publishing
ENABLE_BATCH_PUBLISHER=true
PUBSUB_BATCH_MAX_MESSAGES=1000
PUBSUB_BATCH_MAX_BYTES=10485760
PUBSUB_BATCH_MAX_LATENCY=0.01

# Schema Registry
ENABLE_SCHEMA_REGISTRY=true
STRICT_SCHEMA_VALIDATION=false

# Streaming Aggregator
ENABLE_STREAMING_AGGREGATOR=true
AGGREGATION_WINDOWS=["1s","5s","1m","5m"]

# Distributed Processor
ENABLE_DISTRIBUTED_PROCESSOR=true
PROCESSOR_MIN_WORKERS=2
PROCESSOR_MAX_WORKERS=10

# Dead Letter Queue
ENABLE_DLQ=true
DLQ_MAX_ATTEMPTS=5
DLQ_RETENTION_DAYS=7

# Monitoring
ENABLE_MONITORING=true
PROMETHEUS_GATEWAY=http://localhost:9091

# Multi-Region (disabled by default)
ENABLE_MULTI_REGION=false
PRIMARY_REGION=us-central1
ENABLED_REGIONS=["us-central1","us-east1","europe-west1"]
"""
