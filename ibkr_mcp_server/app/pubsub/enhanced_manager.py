"""
Enhanced Google Cloud Pub/Sub Manager for IBKR MCP Server

This module extends the base PubSubManager with advanced features including:
- Circuit breaker pattern for fault tolerance
- Message deduplication
- Advanced batching and flow control
- Integration with other optimization components

This is a refactored version that properly integrates with the existing
IBKR MCP Server infrastructure.
"""

import asyncio
import hashlib
import json
import logging
import time
from dataclasses import dataclass
from typing import Any, Dict, Optional

from google.cloud import pubsub_v1
from prometheus_client import Counter, Gauge, Histogram

# Import from existing infrastructure
from .pubsub_manager import PubSubManager, PubSubConfig, TradingMessage

# Import settings - we'll need to handle this differently
try:
    from ..core.settings import ApplicationSettings
    settings = ApplicationSettings()
except ImportError:
    try:
        from ..core.config import Config
        settings = Config()
    except ImportError:
        # Fallback to mock settings for testing
        class MockSettings:
            GCP_PROJECT_ID = "gen-lang-client-0397821884"
            GCP_CREDENTIALS_PATH = None
            PUBSUB_TOPICS = {}
            PUBSUB_SUBSCRIPTIONS = {}
            ENABLE_SCHEMA_REGISTRY = False
            ENABLE_BATCH_PUBLISHER = False
            ENABLE_MONITORING = False
            CIRCUIT_BREAKER_THRESHOLD = 5
            CIRCUIT_BREAKER_TIMEOUT = 60
            ENABLE_MESSAGE_DEDUPLICATION = True
            DEDUPLICATION_TTL = 300
            PUBSUB_BATCH_MAX_MESSAGES = 1000
            PUBSUB_BATCH_MAX_BYTES = 10 * 1024 * 1024
            PUBSUB_BATCH_MAX_LATENCY = 0.01
            STRICT_SCHEMA_VALIDATION = False
            PROMETHEUS_GATEWAY = "http://localhost:9091"

        settings = MockSettings()

# Metrics
messages_published_total = Counter(
    'pubsub_messages_published_total',
    'Total messages published',
    ['topic', 'status']
)

messages_deduplicated_total = Counter(
    'pubsub_messages_deduplicated_total',
    'Total messages deduplicated',
    ['topic']
)

circuit_breaker_state = Gauge(
    'pubsub_circuit_breaker_state',
    'Circuit breaker state (0=closed, 1=open, 2=half-open)',
    ['topic']
)

publish_duration = Histogram(
    'pubsub_publish_duration_seconds',
    'Message publish duration',
    ['topic']
)


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker"""
    failure_threshold: int = 5
    recovery_timeout: int = 60
    half_open_requests: int = 3


class CircuitBreakerState:
    """Circuit breaker state management"""
    CLOSED = 0
    OPEN = 1
    HALF_OPEN = 2


class EnhancedPubSubManager(PubSubManager):
    """
    Enhanced Pub/Sub Manager with advanced features
    
    Extends the base PubSubManager with:
    - Circuit breaker for fault tolerance
    - Message deduplication
    - Advanced flow control
    - Metrics and monitoring
    - Integration with other optimization components
    """
    
    def __init__(
        self,
        config: Optional[PubSubConfig] = None,
        circuit_breaker_config: Optional[CircuitBreakerConfig] = None,
        enable_deduplication: bool = True,
        deduplication_ttl: int = 300
    ):
        """
        Initialize Enhanced Pub/Sub Manager
        
        Args:
            config: Pub/Sub configuration (uses app settings if None)
            circuit_breaker_config: Circuit breaker configuration
            enable_deduplication: Enable message deduplication
            deduplication_ttl: TTL for deduplication cache (seconds)
        """
        # Use provided config or create from settings
        if config is None:
            config = self._create_config_from_settings()
        
        super().__init__(config)
        
        # Circuit breaker configuration
        self.circuit_breaker_config = circuit_breaker_config or CircuitBreakerConfig()
        self.circuit_breakers: Dict[str, Dict[str, Any]] = {}
        
        # Deduplication
        self.enable_deduplication = enable_deduplication
        self.deduplication_ttl = deduplication_ttl
        self.dedup_cache: Dict[str, float] = {}
        self.dedup_cleanup_interval = 60  # seconds
        
        # Flow control enhancements
        self.batch_settings = pubsub_v1.types.BatchSettings(
            max_messages=getattr(settings, 'PUBSUB_BATCH_MAX_MESSAGES', 1000),
            max_bytes=getattr(settings, 'PUBSUB_BATCH_MAX_BYTES', 10 * 1024 * 1024),
            max_latency=getattr(settings, 'PUBSUB_BATCH_MAX_LATENCY', 0.01)
        )
        
        # Component integration
        self._schema_registry = None
        self._batch_publisher = None
        self._monitoring_service = None
        
        # Enhanced logging
        self.logger = logging.getLogger(__name__)
    
    def _create_config_from_settings(self) -> PubSubConfig:
        """Create PubSubConfig from app settings"""
        config = PubSubConfig()
        config.project_id = getattr(settings, 'GCP_PROJECT_ID', 'gen-lang-client-0397821884')
        config.credentials_path = getattr(settings, 'GCP_CREDENTIALS_PATH', None)

        # Override with settings if available
        if hasattr(settings, 'PUBSUB_TOPICS'):
            config.topics.update(getattr(settings, 'PUBSUB_TOPICS', {}))
        if hasattr(settings, 'PUBSUB_SUBSCRIPTIONS'):
            config.subscriptions.update(getattr(settings, 'PUBSUB_SUBSCRIPTIONS', {}))

        return config
    
    async def initialize(self):
        """Initialize enhanced Pub/Sub manager"""
        # Initialize base manager
        await super().initialize()
        
        # Initialize circuit breakers for each topic
        for topic_key in self.config.topics:
            self._init_circuit_breaker(topic_key)
        
        # Start deduplication cleanup task
        if self.enable_deduplication:
            asyncio.create_task(self._dedup_cleanup_loop())
        
        # Initialize optional components if configured
        await self._initialize_components()
        
        self.logger.info("Enhanced Pub/Sub Manager initialized")
    
    async def _initialize_components(self):
        """Initialize optional optimization components"""
        # Schema Registry
        if getattr(settings, 'ENABLE_SCHEMA_REGISTRY', False):
            try:
                from .schema_registry import SchemaRegistry as MessageSchemaRegistry
                self._schema_registry = MessageSchemaRegistry(self.config.project_id)
                self.logger.info("Schema Registry initialized")
            except ImportError:
                self.logger.warning("Schema Registry not available")

        # Batch Publisher
        if getattr(settings, 'ENABLE_BATCH_PUBLISHER', False):
            try:
                from .batch_publisher import BatchPublisher
                self._batch_publisher = BatchPublisher(
                    project_id=self.config.project_id,
                    max_batch_size=getattr(settings, 'PUBSUB_BATCH_MAX_MESSAGES', 1000)
                )
                await self._batch_publisher.start()
                self.logger.info("Batch Publisher initialized")
            except ImportError:
                self.logger.warning("Batch Publisher not available")

        # Monitoring Service
        if getattr(settings, 'ENABLE_MONITORING', False):
            try:
                from .monitoring_service import MonitoringService
                self._monitoring_service = MonitoringService(
                    self.config.project_id,
                    prometheus_gateway=getattr(settings, 'PROMETHEUS_GATEWAY', 'http://localhost:9091')
                )
                await self._monitoring_service.initialize()
                self.logger.info("Monitoring Service initialized")
            except ImportError:
                self.logger.warning("Monitoring Service not available")
    
    def _init_circuit_breaker(self, topic_key: str):
        """Initialize circuit breaker for a topic"""
        self.circuit_breakers[topic_key] = {
            'state': CircuitBreakerState.CLOSED,
            'failure_count': 0,
            'last_failure_time': None,
            'success_count': 0
        }
        circuit_breaker_state.labels(topic=topic_key).set(CircuitBreakerState.CLOSED)
    
    async def publish_market_data(self, symbol: str, market_data: Dict[str, Any]) -> str:
        """
        Publish market data with enhanced features
        
        Adds schema validation and deduplication to base functionality
        """
        # Schema validation if available
        if self._schema_registry:
            try:
                validation_result = self._schema_registry.validate_message(
                    'ibkr.market_data.v1',
                    market_data,
                    1
                )
                if not validation_result.is_valid:
                    self.logger.error(f"Schema validation failed: {validation_result.errors}")
                    raise ValueError(f"Schema validation failed: {validation_result.errors}")
            except Exception as e:
                self.logger.error(f"Schema validation failed: {e}")
                raise
        
        # Create message
        message = TradingMessage(
            message_type='market_data_update',
            symbol=symbol,
            data=market_data
        )
        
        # Check deduplication
        if self.enable_deduplication:
            message_hash = self._compute_message_hash(message)
            if self._is_duplicate(message_hash):
                messages_deduplicated_total.labels(topic='market_data').inc()
                self.logger.debug(f"Duplicate message filtered: {message.message_id}")
                return f"deduplicated-{message.message_id}"
        
        # Use enhanced publish
        return await self._publish_with_circuit_breaker('market_data', message, ordering_key=symbol)
    
    async def _publish_with_circuit_breaker(
        self,
        topic_key: str,
        message: TradingMessage,
        ordering_key: Optional[str] = None
    ) -> str:
        """Publish message with circuit breaker protection"""
        breaker = self.circuit_breakers[topic_key]
        
        # Check circuit breaker state
        if breaker['state'] == CircuitBreakerState.OPEN:
            if not self._should_attempt_reset(breaker):
                raise Exception(f"Circuit breaker OPEN for topic {topic_key}")
            else:
                # Try half-open state
                breaker['state'] = CircuitBreakerState.HALF_OPEN
                breaker['success_count'] = 0
                circuit_breaker_state.labels(topic=topic_key).set(CircuitBreakerState.HALF_OPEN)
        
        # Attempt to publish
        try:
            with publish_duration.labels(topic=topic_key).time():
                # Use batch publisher if available
                if self._batch_publisher and topic_key in ['market_data', 'orders']:
                    result = await self._batch_publisher.publish_async(
                        message.to_dict(),
                        topic=self.config.topics[topic_key],
                        ordering_key=ordering_key
                    )
                else:
                    # Use regular publish
                    result = await self._publish_message(topic_key, message, ordering_key)
            
            # Update circuit breaker on success
            self._on_publish_success(breaker, topic_key)
            messages_published_total.labels(topic=topic_key, status='success').inc()
            
            return result
            
        except Exception as e:
            # Update circuit breaker on failure
            self._on_publish_failure(breaker, topic_key)
            messages_published_total.labels(topic=topic_key, status='failure').inc()
            raise
    
    def _should_attempt_reset(self, breaker: Dict[str, Any]) -> bool:
        """Check if circuit breaker should attempt reset"""
        if breaker['last_failure_time'] is None:
            return False
        
        time_since_failure = time.time() - breaker['last_failure_time']
        return time_since_failure >= self.circuit_breaker_config.recovery_timeout
    
    def _on_publish_success(self, breaker: Dict[str, Any], topic_key: str):
        """Update circuit breaker on successful publish"""
        if breaker['state'] == CircuitBreakerState.HALF_OPEN:
            breaker['success_count'] += 1
            if breaker['success_count'] >= self.circuit_breaker_config.half_open_requests:
                # Close circuit breaker
                breaker['state'] = CircuitBreakerState.CLOSED
                breaker['failure_count'] = 0
                breaker['success_count'] = 0
                circuit_breaker_state.labels(topic=topic_key).set(CircuitBreakerState.CLOSED)
                self.logger.info(f"Circuit breaker CLOSED for topic {topic_key}")
        else:
            # Reset failure count on success
            breaker['failure_count'] = 0
    
    def _on_publish_failure(self, breaker: Dict[str, Any], topic_key: str):
        """Update circuit breaker on failed publish"""
        breaker['failure_count'] += 1
        breaker['last_failure_time'] = time.time()
        
        if breaker['state'] == CircuitBreakerState.HALF_OPEN:
            # Immediately open on failure in half-open state
            breaker['state'] = CircuitBreakerState.OPEN
            circuit_breaker_state.labels(topic=topic_key).set(CircuitBreakerState.OPEN)
            self.logger.warning(f"Circuit breaker OPEN for topic {topic_key} (half-open failure)")
        elif breaker['failure_count'] >= self.circuit_breaker_config.failure_threshold:
            # Open circuit breaker
            breaker['state'] = CircuitBreakerState.OPEN
            circuit_breaker_state.labels(topic=topic_key).set(CircuitBreakerState.OPEN)
            self.logger.warning(f"Circuit breaker OPEN for topic {topic_key} (threshold reached)")
    
    def _compute_message_hash(self, message: TradingMessage) -> str:
        """Compute hash for message deduplication"""
        # Create hash from message content
        content = f"{message.message_type}:{message.symbol}:{json.dumps(message.data, sort_keys=True)}"
        return hashlib.sha256(content.encode()).hexdigest()
    
    def _is_duplicate(self, message_hash: str) -> bool:
        """Check if message is duplicate"""
        current_time = time.time()
        
        if message_hash in self.dedup_cache:
            # Check if still within TTL
            if current_time - self.dedup_cache[message_hash] < self.deduplication_ttl:
                return True
        
        # Add to cache
        self.dedup_cache[message_hash] = current_time
        return False
    
    async def _dedup_cleanup_loop(self):
        """Background task to cleanup expired deduplication entries"""
        while True:
            try:
                await asyncio.sleep(self.dedup_cleanup_interval)
                
                current_time = time.time()
                expired_keys = [
                    k for k, v in self.dedup_cache.items()
                    if current_time - v > self.deduplication_ttl
                ]
                
                for key in expired_keys:
                    del self.dedup_cache[key]
                
                if expired_keys:
                    self.logger.debug(f"Cleaned up {len(expired_keys)} expired dedup entries")
                    
            except Exception as e:
                self.logger.error(f"Error in dedup cleanup: {e}")
    
    def get_circuit_breaker_status(self) -> Dict[str, Any]:
        """Get status of all circuit breakers"""
        status = {}
        for topic_key, breaker in self.circuit_breakers.items():
            state_name = {
                CircuitBreakerState.CLOSED: "CLOSED",
                CircuitBreakerState.OPEN: "OPEN",
                CircuitBreakerState.HALF_OPEN: "HALF_OPEN"
            }.get(breaker['state'], "UNKNOWN")
            
            status[topic_key] = {
                'state': state_name,
                'failure_count': breaker['failure_count'],
                'last_failure_time': breaker['last_failure_time']
            }
        
        return status
    
    def get_enhanced_statistics(self) -> Dict[str, Any]:
        """Get enhanced statistics including circuit breaker and dedup info"""
        base_stats = self.get_statistics()
        
        enhanced_stats = {
            **base_stats,
            'circuit_breakers': self.get_circuit_breaker_status(),
            'deduplication': {
                'enabled': self.enable_deduplication,
                'cache_size': len(self.dedup_cache),
                'ttl_seconds': self.deduplication_ttl
            }
        }
        
        # Add component status if available
        if self._schema_registry:
            enhanced_stats['schema_registry'] = 'active'
        if self._batch_publisher:
            enhanced_stats['batch_publisher'] = 'active'
        if self._monitoring_service:
            enhanced_stats['monitoring_service'] = 'active'
        
        return enhanced_stats
    
    async def shutdown(self):
        """Shutdown enhanced Pub/Sub manager"""
        # Shutdown components
        if self._batch_publisher:
            await self._batch_publisher.stop()
        if self._monitoring_service:
            await self._monitoring_service.shutdown()
        
        # Shutdown base manager
        await super().shutdown()
        
        self.logger.info("Enhanced Pub/Sub Manager shutdown complete")


# Factory function for dependency injection
def get_enhanced_pubsub_manager() -> EnhancedPubSubManager:
    """
    Factory function to create Enhanced Pub/Sub Manager
    
    This can be used with FastAPI's dependency injection:
    
    @app.on_event("startup")
    async def startup():
        app.state.pubsub = get_enhanced_pubsub_manager()
        await app.state.pubsub.initialize()
    """
    return EnhancedPubSubManager(
        circuit_breaker_config=CircuitBreakerConfig(
            failure_threshold=getattr(settings, 'CIRCUIT_BREAKER_THRESHOLD', 5),
            recovery_timeout=getattr(settings, 'CIRCUIT_BREAKER_TIMEOUT', 60)
        ),
        enable_deduplication=getattr(settings, 'ENABLE_MESSAGE_DEDUPLICATION', True),
        deduplication_ttl=getattr(settings, 'DEDUPLICATION_TTL', 300)
    )
