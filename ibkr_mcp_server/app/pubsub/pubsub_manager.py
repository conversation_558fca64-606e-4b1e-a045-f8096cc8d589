"""
Google Cloud Pub/Sub Manager for IBKR MCP Server

Provides enterprise-grade message bus capabilities using Google Cloud Pub/Sub
for reliable, scalable, and ordered message delivery in trading systems.
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, Callable
from datetime import datetime, timezone
from concurrent.futures import ThreadPoolExecutor
import uuid

from google.cloud import pubsub_v1
# Use pubsub_v1.PubsubMessage instead of direct import

logger = logging.getLogger(__name__)


class PubSubConfig:
    """Configuration for Google Cloud Pub/Sub"""
    
    def __init__(self):
        # Project configuration
        self.project_id = "gen-lang-client-**********"  # Your actual project ID
        self.credentials_path: Optional[str] = None  # Path to service account JSON
        
        # Topic configuration
        self.topics = {
            'market_data': 'ibkr-market-data',
            'orders': 'ibkr-orders',
            'positions': 'ibkr-positions',
            'news': 'ibkr-news',
            'signals': 'ibkr-trading-signals',
            'risk_alerts': 'ibkr-risk-alerts',
            'system_events': 'ibkr-system-events'
        }
        
        # Subscription configuration
        self.subscriptions = {
            'market_data_processor': 'ibkr-market-data-processor',
            'order_processor': 'ibkr-order-processor',
            'signal_processor': 'ibkr-signal-processor',
            'risk_processor': 'ibkr-risk-processor',
            'websocket_broadcaster': 'ibkr-websocket-broadcaster'
        }
        
        # Message configuration
        self.enable_message_ordering = True
        self.enable_exactly_once_delivery = True
        self.message_retention_duration = 604800  # 7 days
        self.ack_deadline_seconds = 60
        self.max_extension_seconds = 600


class TradingMessage:
    """Standardized trading message format for Pub/Sub"""
    
    def __init__(self, message_type: str, symbol: str, data: Dict[str, Any],
                 strategy_id: Optional[str] = None, account: Optional[str] = None):
        self.message_id = str(uuid.uuid4())
        self.timestamp = datetime.now(timezone.utc).isoformat()
        self.message_type = message_type
        self.symbol = symbol
        self.data = data
        self.strategy_id = strategy_id
        self.account = account
        self.sequence_number: Optional[int] = None  # For ordered delivery
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'message_id': self.message_id,
            'timestamp': self.timestamp,
            'message_type': self.message_type,
            'symbol': self.symbol,
            'data': self.data,
            'strategy_id': self.strategy_id,
            'account': self.account,
            'sequence_number': self.sequence_number
        }
    
    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict())
    
    @classmethod
    def from_json(cls, json_str: str) -> 'TradingMessage':
        """Create from JSON string"""
        data = json.loads(json_str)
        msg = cls(
            message_type=data['message_type'],
            symbol=data['symbol'],
            data=data['data'],
            strategy_id=data.get('strategy_id'),
            account=data.get('account')
        )
        msg.message_id = data['message_id']
        msg.timestamp = data['timestamp']
        msg.sequence_number = data.get('sequence_number')
        return msg


class PubSubManager:
    """Google Cloud Pub/Sub manager for trading system"""
    
    def __init__(self, config: PubSubConfig):
        self.config = config
        self.publisher: Optional[Any] = None
        self.subscriber: Optional[Any] = None
        self.executor = ThreadPoolExecutor(max_workers=10)
        
        # Message handlers
        self.message_handlers: Dict[str, Callable] = {}
        
        # Statistics
        self.stats = {
            'messages_published': 0,
            'messages_received': 0,
            'messages_processed': 0,
            'messages_failed': 0,
            'last_activity': None
        }
        
        # Sequence tracking for ordered delivery
        self.sequence_counters: Dict[str, int] = {}
    
    async def initialize(self):
        """Initialize Pub/Sub clients and create topics/subscriptions"""
        try:
            # Initialize publisher and subscriber
            if self.config.credentials_path:
                # Use service account credentials
                self.publisher = pubsub_v1.PublisherClient.from_service_account_json(
                    self.config.credentials_path
                )
                self.subscriber = pubsub_v1.SubscriberClient.from_service_account_json(
                    self.config.credentials_path
                )
            else:
                # Use default credentials (ADC)
                self.publisher = pubsub_v1.PublisherClient()
                self.subscriber = pubsub_v1.SubscriberClient()
            
            # Create topics and subscriptions
            await self._create_topics_and_subscriptions()
            
            logger.info("Google Cloud Pub/Sub initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Pub/Sub: {e}")
            raise
    
    async def _create_topics_and_subscriptions(self):
        """Create topics and subscriptions if they don't exist"""
        if not self.publisher or not self.subscriber:
            raise RuntimeError("Publisher and subscriber must be initialized before creating topics/subscriptions")

        try:
            # Create topics
            for topic_name in self.config.topics.values():
                topic_path = self.publisher.topic_path(self.config.project_id, topic_name)
                
                try:
                    # Try to create topic
                    self.publisher.create_topic(
                        request={
                            "name": topic_path,
                            "message_retention_duration": {
                                "seconds": self.config.message_retention_duration
                            }
                        }
                    )
                    logger.info(f"Created topic: {topic_name}")
                except Exception as e:
                    if "already exists" in str(e).lower():
                        logger.debug(f"Topic already exists: {topic_name}")
                    else:
                        logger.error(f"Failed to create topic {topic_name}: {e}")
            
            # Create subscriptions
            for sub_name in self.config.subscriptions.values():
                # Map subscription to appropriate topic
                topic_name = self._get_topic_for_subscription(sub_name)
                if topic_name:
                    topic_path = self.publisher.topic_path(self.config.project_id, topic_name)
                    subscription_path = self.subscriber.subscription_path(
                        self.config.project_id, sub_name
                    )
                    
                    try:
                        self.subscriber.create_subscription(
                            request={
                                "name": subscription_path,
                                "topic": topic_path,
                                "ack_deadline_seconds": self.config.ack_deadline_seconds,
                                "enable_exactly_once_delivery": self.config.enable_exactly_once_delivery,
                                "enable_message_ordering": self.config.enable_message_ordering
                            }
                        )
                        logger.info(f"Created subscription: {sub_name}")
                    except Exception as e:
                        if "already exists" in str(e).lower():
                            logger.debug(f"Subscription already exists: {sub_name}")
                        else:
                            logger.error(f"Failed to create subscription {sub_name}: {e}")
            
        except Exception as e:
            logger.error(f"Error creating topics/subscriptions: {e}")
    
    def _get_topic_for_subscription(self, subscription_name: str) -> Optional[str]:
        """Map subscription to appropriate topic"""
        mapping = {
            'ibkr-market-data-processor': 'ibkr-market-data',
            'ibkr-order-processor': 'ibkr-orders',
            'ibkr-signal-processor': 'ibkr-trading-signals',
            'ibkr-risk-processor': 'ibkr-risk-alerts',
            'ibkr-websocket-broadcaster': 'ibkr-market-data'  # Can subscribe to multiple
        }
        return mapping.get(subscription_name)
    
    async def publish_market_data(self, symbol: str, market_data: Dict[str, Any]) -> str:
        """Publish market data message"""
        message = TradingMessage(
            message_type='market_data_update',
            symbol=symbol,
            data=market_data
        )
        
        # Add sequence number for ordering
        if self.config.enable_message_ordering:
            self.sequence_counters[symbol] = self.sequence_counters.get(symbol, 0) + 1
            message.sequence_number = self.sequence_counters[symbol]
        
        return await self._publish_message('market_data', message, ordering_key=symbol)
    
    async def publish_order_update(self, account: str, order_data: Dict[str, Any]) -> str:
        """Publish order update message"""
        message = TradingMessage(
            message_type='order_update',
            symbol=order_data.get('symbol', ''),
            data=order_data,
            account=account
        )
        
        return await self._publish_message('orders', message, ordering_key=account)
    
    async def publish_trading_signal(self, strategy_id: str, signal_data: Dict[str, Any]) -> str:
        """Publish trading signal message"""
        message = TradingMessage(
            message_type='trading_signal',
            symbol=signal_data.get('symbol', ''),
            data=signal_data,
            strategy_id=strategy_id
        )
        
        return await self._publish_message('signals', message, ordering_key=strategy_id)
    
    async def publish_risk_alert(self, alert_data: Dict[str, Any]) -> str:
        """Publish risk management alert"""
        message = TradingMessage(
            message_type='risk_alert',
            symbol=alert_data.get('symbol', ''),
            data=alert_data
        )
        
        return await self._publish_message('risk_alerts', message)
    
    async def _publish_message(self, topic_key: str, message: TradingMessage,
                             ordering_key: Optional[str] = None) -> str:
        """Publish message to specified topic"""
        if not self.publisher:
            raise RuntimeError("Publisher not initialized")

        try:
            topic_name = self.config.topics[topic_key]
            topic_path = self.publisher.topic_path(self.config.project_id, topic_name)
            
            # Create Pub/Sub message data and attributes
            message_data = message.to_json().encode('utf-8')
            message_attributes = {
                'message_type': message.message_type,
                'symbol': message.symbol,
                'timestamp': message.timestamp,
                'message_id': message.message_id
            }
            
            # Add ordering key if specified
            if ordering_key and self.config.enable_message_ordering:
                message_attributes['ordering_key'] = ordering_key

            # Publish message
            future = self.publisher.publish(topic_path, message_data, **message_attributes)
            
            # Wait for publish to complete
            message_id = future.result()
            
            # Update statistics
            self.stats['messages_published'] += 1
            self.stats['last_activity'] = datetime.now(timezone.utc).isoformat()
            
            logger.debug(f"Published message {message.message_id} to {topic_name}")
            return message_id
            
        except Exception as e:
            logger.error(f"Failed to publish message: {e}")
            self.stats['messages_failed'] += 1
            raise
    
    def register_message_handler(self, message_type: str, handler: Callable):
        """Register handler for specific message type"""
        self.message_handlers[message_type] = handler
        logger.info(f"Registered handler for message type: {message_type}")
    
    async def start_subscriber(self, subscription_name: str):
        """Start subscriber for specified subscription"""
        if not self.subscriber:
            raise RuntimeError("Subscriber not initialized")

        try:
            subscription_path = self.subscriber.subscription_path(
                self.config.project_id, subscription_name
            )
            
            # Configure flow control
            flow_control = pubsub_v1.types.FlowControl(max_messages=1000)
            
            # Start pulling messages
            logger.info(f"Starting subscriber for: {subscription_name}")
            
            # Use pull in a separate thread to avoid blocking
            def pull_messages():
                if self.subscriber:  # Additional safety check
                    self.subscriber.subscribe(
                        subscription_path,
                        callback=self._message_callback,
                        flow_control=flow_control
                    )
            
            # Run subscriber in executor
            self.executor.submit(pull_messages)
            
        except Exception as e:
            logger.error(f"Failed to start subscriber {subscription_name}: {e}")
            raise
    
    def _message_callback(self, message: Any):
        """Callback for received Pub/Sub messages"""
        try:
            # Parse message
            trading_message = TradingMessage.from_json(message.data.decode('utf-8'))
            
            # Update statistics
            self.stats['messages_received'] += 1
            
            # Find and execute handler
            handler = self.message_handlers.get(trading_message.message_type)
            if handler:
                # Execute handler asynchronously
                asyncio.create_task(self._execute_handler(handler, trading_message))
                self.stats['messages_processed'] += 1
            else:
                logger.warning(f"No handler for message type: {trading_message.message_type}")
            
            # Acknowledge message
            message.ack()
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            self.stats['messages_failed'] += 1
            message.nack()
    
    async def _execute_handler(self, handler: Callable, message: TradingMessage):
        """Execute message handler"""
        try:
            if asyncio.iscoroutinefunction(handler):
                await handler(message)
            else:
                handler(message)
        except Exception as e:
            logger.error(f"Handler execution failed: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get Pub/Sub statistics"""
        return {
            **self.stats,
            'topics': list(self.config.topics.values()),
            'subscriptions': list(self.config.subscriptions.values()),
            'sequence_counters': dict(self.sequence_counters)
        }
    
    async def shutdown(self):
        """Shutdown Pub/Sub manager"""
        try:
            if self.publisher:
                self.publisher.stop()
            if self.subscriber:
                self.subscriber.close()
            
            self.executor.shutdown(wait=True)
            logger.info("Pub/Sub manager shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


# Global Pub/Sub manager instance
pubsub_manager: Optional[PubSubManager] = None
