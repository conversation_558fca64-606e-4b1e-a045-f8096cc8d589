"""
Centralized Settings Management for IBKR MCP Server

Provides unified configuration management across all components
with environment-based overrides and validation.
"""

import os
from typing import Optional, List, Dict, Any
from dataclasses import dataclass, field
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class DatabaseSettings:
    """Database configuration settings"""
    supabase_url: str = field(default_factory=lambda: os.getenv('SUPABASE_URL', ''))
    supabase_service_key: str = field(default_factory=lambda: os.getenv('SUPABASE_SERVICE_ROLE_KEY', ''))
    supabase_anon_key: str = field(default_factory=lambda: os.getenv('SUPABASE_ANON_KEY', ''))
    connection_pool_size: int = field(default_factory=lambda: int(os.getenv('DB_POOL_SIZE', '10')))
    connection_timeout: int = field(default_factory=lambda: int(os.getenv('DB_TIMEOUT', '30')))


@dataclass
class IBKRSettings:
    """IBKR API configuration settings"""
    api_key: Optional[str] = field(default_factory=lambda: os.getenv('IBKR_API_KEY'))
    api_secret: Optional[str] = field(default_factory=lambda: os.getenv('IBKR_API_SECRET'))
    base_url: str = field(default_factory=lambda: os.getenv('IBKR_BASE_URL', 'https://api.ibkr.com/v1'))
    tws_host: str = field(default_factory=lambda: os.getenv('TWS_HOST', 'localhost'))
    tws_port: int = field(default_factory=lambda: int(os.getenv('TWS_PORT', '7497')))
    client_id: int = field(default_factory=lambda: int(os.getenv('TWS_CLIENT_ID', '1')))
    paper_trading: bool = field(default_factory=lambda: os.getenv('PAPER_TRADING', 'true').lower() == 'true')


@dataclass
class GoogleCloudSettings:
    """Google Cloud configuration settings"""
    project_id: str = field(default_factory=lambda: os.getenv('GOOGLE_CLOUD_PROJECT', ''))
    credentials_path: Optional[str] = field(default_factory=lambda: os.getenv('GOOGLE_APPLICATION_CREDENTIALS'))
    pubsub_topic_prefix: str = field(default_factory=lambda: os.getenv('PUBSUB_TOPIC_PREFIX', 'ibkr'))
    monitoring_enabled: bool = field(default_factory=lambda: os.getenv('GCP_MONITORING_ENABLED', 'false').lower() == 'true')


@dataclass
class LoggingSettings:
    """Logging configuration settings"""
    level: str = field(default_factory=lambda: os.getenv('LOG_LEVEL', 'INFO'))
    format: str = field(default_factory=lambda: os.getenv('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    file_path: Optional[str] = field(default_factory=lambda: os.getenv('LOG_FILE_PATH'))
    max_file_size: int = field(default_factory=lambda: int(os.getenv('LOG_MAX_FILE_SIZE', '10485760')))  # 10MB
    backup_count: int = field(default_factory=lambda: int(os.getenv('LOG_BACKUP_COUNT', '5')))


@dataclass
class SecuritySettings:
    """Security configuration settings"""
    secret_key: str = field(default_factory=lambda: os.getenv('SECRET_KEY', 'dev-secret-key'))
    jwt_algorithm: str = field(default_factory=lambda: os.getenv('JWT_ALGORITHM', 'HS256'))
    jwt_expiration: int = field(default_factory=lambda: int(os.getenv('JWT_EXPIRATION', '3600')))
    allowed_hosts: List[str] = field(default_factory=lambda: os.getenv('ALLOWED_HOSTS', 'localhost,127.0.0.1').split(','))
    cors_origins: List[str] = field(default_factory=lambda: os.getenv('CORS_ORIGINS', '*').split(','))


@dataclass
class PerformanceSettings:
    """Performance and optimization settings"""
    max_workers: int = field(default_factory=lambda: int(os.getenv('MAX_WORKERS', '4')))
    request_timeout: int = field(default_factory=lambda: int(os.getenv('REQUEST_TIMEOUT', '30')))
    batch_size: int = field(default_factory=lambda: int(os.getenv('BATCH_SIZE', '100')))
    cache_ttl: int = field(default_factory=lambda: int(os.getenv('CACHE_TTL', '300')))
    rate_limit_per_minute: int = field(default_factory=lambda: int(os.getenv('RATE_LIMIT_PER_MINUTE', '1000')))


@dataclass
class ApplicationSettings:
    """Main application settings container"""
    # Environment
    environment: str = field(default_factory=lambda: os.getenv('ENVIRONMENT', 'development'))
    debug: bool = field(default_factory=lambda: os.getenv('DEBUG', 'false').lower() == 'true')
    testing: bool = field(default_factory=lambda: os.getenv('TESTING', 'false').lower() == 'true')
    
    # Component settings
    database: DatabaseSettings = field(default_factory=DatabaseSettings)
    ibkr: IBKRSettings = field(default_factory=IBKRSettings)
    google_cloud: GoogleCloudSettings = field(default_factory=GoogleCloudSettings)
    logging: LoggingSettings = field(default_factory=LoggingSettings)
    security: SecuritySettings = field(default_factory=SecuritySettings)
    performance: PerformanceSettings = field(default_factory=PerformanceSettings)
    
    # Application metadata
    app_name: str = "IBKR MCP Server"
    version: str = "1.0.0"
    description: str = "Interactive Brokers Model Context Protocol Server"
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors"""
        errors = []
        
        # Database validation
        if not self.database.supabase_url:
            errors.append("SUPABASE_URL is required")
        if not self.database.supabase_service_key:
            errors.append("SUPABASE_SERVICE_ROLE_KEY is required")
        
        # IBKR validation
        if self.environment == 'production' and not self.ibkr.api_key:
            errors.append("IBKR_API_KEY is required in production")
        
        # Google Cloud validation
        if self.google_cloud.monitoring_enabled and not self.google_cloud.project_id:
            errors.append("GOOGLE_CLOUD_PROJECT is required when monitoring is enabled")
        
        # Security validation
        if self.environment == 'production' and self.security.secret_key == 'dev-secret-key':
            errors.append("SECRET_KEY must be set in production")
        
        return errors
    
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.environment.lower() == 'production'
    
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.environment.lower() == 'development'
    
    def is_testing(self) -> bool:
        """Check if running in testing environment"""
        return self.testing or self.environment.lower() == 'testing'
    
    def get_database_url(self) -> str:
        """Get formatted database URL"""
        return self.database.supabase_url
    
    def get_log_config(self) -> Dict[str, Any]:
        """Get logging configuration dictionary"""
        config = {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'standard': {
                    'format': self.logging.format
                }
            },
            'handlers': {
                'console': {
                    'class': 'logging.StreamHandler',
                    'level': self.logging.level,
                    'formatter': 'standard'
                }
            },
            'loggers': {
                '': {
                    'handlers': ['console'],
                    'level': self.logging.level,
                    'propagate': False
                }
            }
        }
        
        # Add file handler if path is specified
        if self.logging.file_path:
            config['handlers']['file'] = {
                'class': 'logging.handlers.RotatingFileHandler',
                'filename': self.logging.file_path,
                'maxBytes': self.logging.max_file_size,
                'backupCount': self.logging.backup_count,
                'level': self.logging.level,
                'formatter': 'standard'
            }
            config['loggers']['']['handlers'].append('file')
        
        return config


# Global settings instance
settings = ApplicationSettings()

# Validate settings on import
validation_errors = settings.validate()
if validation_errors and not settings.is_testing():
    import warnings
    for error in validation_errors:
        warnings.warn(f"Configuration warning: {error}")

# Export commonly used settings for backward compatibility
SUPABASE_URL = settings.database.supabase_url
SUPABASE_SERVICE_KEY = settings.database.supabase_service_key
DEBUG = settings.debug
ENVIRONMENT = settings.environment
