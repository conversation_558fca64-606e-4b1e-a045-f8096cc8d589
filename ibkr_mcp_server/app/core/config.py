import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    IBKR_API_KEY = os.getenv("IBKR_API_KEY")
    IBKR_API_SECRET = os.getenv("IBKR_API_SECRET")
    IBKR_BASE_URL = os.getenv("IBKR_BASE_URL", "https://api.ibkr.com/v1")

    # Supabase Configuration
    SUPABASE_URL = os.getenv("SUPABASE_URL", "")
    SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY", "")
    SUPABASE_ANON_KEY = os.getenv("SUPABASE_ANON_KEY", "")

    # Google Cloud Configuration
    GOOGLE_CLOUD_PROJECT = os.getenv("GOOGLE_CLOUD_PROJECT", "")
    GOOGLE_APPLICATION_CREDENTIALS = os.getenv("GOOGLE_APPLICATION_CREDENTIALS", "")

    # Pub/Sub Configuration
    PUBSUB_PROJECT_ID = os.getenv("PUBSUB_PROJECT_ID", "")
    PUBSUB_TOPIC_PREFIX = os.getenv("PUBSUB_TOPIC_PREFIX", "ibkr")

    # Logging Configuration
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    DEBUG = os.getenv("DEBUG", "false").lower() == "true"

# Create settings instance for backward compatibility
settings = Config()