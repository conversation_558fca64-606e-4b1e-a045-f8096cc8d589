"""
WebSocket Real-Time Streaming Package

Provides true real-time push-based data streaming via WebSockets
for the IBKR MCP trading system.

Components:
- WebSocket server with connection management
- FastAPI integration with dedicated endpoints
- Real-time data bridge from IBKR to WebSocket
- Broadcasting utilities for different data types
"""

from .websocket_server import (
    WebSocketServer,
    ConnectionManager,
    WebSocketMessage,
    connection_manager,
    websocket_server
)

from .fastapi_websocket import (
    websocket_router,
    WebSocketBroadcaster,
    broadcaster
)

from .realtime_bridge import (
    RealTimeDataBridge,
    realtime_bridge
)

__all__ = [
    # Core WebSocket infrastructure
    'WebSocketServer',
    'ConnectionManager', 
    'WebSocketMessage',
    'connection_manager',
    'websocket_server',
    
    # FastAPI integration
    'websocket_router',
    'WebSocketBroadcaster',
    'broadcaster',
    
    # Real-time data bridge
    'RealTimeDataBridge',
    'realtime_bridge'
]
