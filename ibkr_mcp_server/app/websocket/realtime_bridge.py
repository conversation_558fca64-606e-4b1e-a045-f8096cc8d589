"""
Real-Time Data Bridge

Bridges IBKR real-time data streams with WebSocket broadcasting
for true push-based real-time data delivery.
"""

import logging
from typing import Dict, Any, Set, Optional, List
from datetime import datetime, timezone

from .fastapi_websocket import broadcaster, connection_manager
try:
    from services.ibkr_service import IBKRService  # type: ignore
    from integrations.event_stream_manager import EventStreamManager  # type: ignore
except ImportError:
    try:
        from app.services.ibkr_service import IBKRService  # type: ignore
        from app.integrations.event_stream_manager import EventStreamManager  # type: ignore
    except ImportError:
        # Fallback - create dummy classes to prevent import errors
        class IBKRService:  # type: ignore
            def __init__(self):
                self.ib = None
            async def create_contract(self, symbol): return None  # noqa: ARG002

        class EventStreamManager:  # type: ignore
            def __init__(self): pass

logger = logging.getLogger(__name__)


class RealTimeDataBridge:
    """
    Bridges IBKR real-time data with WebSocket broadcasting
    
    Converts IBKR callbacks into WebSocket push messages for:
    - Market data (ticks, bars, depth)
    - Order updates
    - Position changes
    - News and alerts
    """
    
    def __init__(self, ibkr_service: IBKRService, event_manager: EventStreamManager):
        self.ibkr_service = ibkr_service
        self.event_manager = event_manager
        
        # Active subscriptions tracking
        self.market_data_subscriptions: Set[str] = set()
        self.order_subscriptions: Set[str] = set()
        self.news_subscriptions: Set[str] = set()
        
        # Data processing statistics
        self.stats = {
            'market_data_updates': 0,
            'order_updates': 0,
            'position_updates': 0,
            'news_updates': 0,
            'errors': 0,
            'last_update': None
        }
        
        # Register IBKR event handlers
        self._register_ibkr_handlers()
    
    def _register_ibkr_handlers(self):
        """Register handlers for IBKR real-time events"""
        if hasattr(self.ibkr_service, 'ib') and self.ibkr_service.ib:
            # Market data events
            self.ibkr_service.ib.pendingTickersEvent += self._handle_pending_tickers
            self.ibkr_service.ib.barUpdateEvent += self._handle_bar_update
            self.ibkr_service.ib.newsBulletinEvent += self._handle_news_bulletin

            # Order and position events
            self.ibkr_service.ib.orderStatusEvent += self._handle_order_status
            self.ibkr_service.ib.openOrderEvent += self._handle_open_order
            self.ibkr_service.ib.positionEvent += self._handle_position_update

            # Error events
            self.ibkr_service.ib.errorEvent += self._handle_error

            logger.info("IBKR event handlers registered for real-time bridge")
    
    async def _handle_pending_tickers(self, tickers):
        """Handle IBKR pending tickers updates and broadcast via WebSocket"""
        try:
            for ticker in tickers:
                symbol = ticker.contract.symbol

                # Extract relevant data
                market_data = {
                    'symbol': symbol,
                    'last': float(ticker.last) if ticker.last and ticker.last > 0 else None,
                    'bid': float(ticker.bid) if ticker.bid and ticker.bid > 0 else None,
                    'ask': float(ticker.ask) if ticker.ask and ticker.ask > 0 else None,
                    'bid_size': int(ticker.bidSize) if ticker.bidSize else None,
                    'ask_size': int(ticker.askSize) if ticker.askSize else None,
                    'volume': int(ticker.volume) if ticker.volume else None,
                    'high': float(ticker.high) if ticker.high and ticker.high > 0 else None,
                    'low': float(ticker.low) if ticker.low and ticker.low > 0 else None,
                    'close': float(ticker.close) if ticker.close and ticker.close > 0 else None,
                    'change': float(ticker.last - ticker.close) if ticker.last and ticker.close and ticker.last > 0 and ticker.close > 0 else None,
                    'change_percent': float((ticker.last - ticker.close) / ticker.close * 100) if ticker.last and ticker.close and ticker.last > 0 and ticker.close > 0 else None
                }

                # Broadcast to WebSocket subscribers
                await broadcaster.broadcast_market_data(symbol, market_data)

                # Update statistics
                self.stats['market_data_updates'] += 1

            self.stats['last_update'] = datetime.now(timezone.utc).isoformat()

        except Exception as e:
            logger.error(f"Error handling pending tickers: {e}")
            self.stats['errors'] += 1
    
    async def _handle_bar_update(self, bars, has_new_bar):
        """Handle IBKR real-time bar updates"""
        if not has_new_bar or not bars:
            return
        
        try:
            # Get the latest bar
            latest_bar = bars[-1]
            symbol = bars.contract.symbol
            
            bar_data = {
                'symbol': symbol,
                'timestamp': latest_bar.time,
                'open': float(latest_bar.open),
                'high': float(latest_bar.high),
                'low': float(latest_bar.low),
                'close': float(latest_bar.close),
                'volume': int(latest_bar.volume),
                'wap': float(latest_bar.wap) if latest_bar.wap else None,
                'count': int(latest_bar.count) if latest_bar.count else None
            }
            
            # Broadcast real-time bar update
            channel = f"bars:{symbol}"
            await connection_manager.broadcast_to_channel(channel, {
                'type': 'bar_update',
                'channel': channel,
                'data': bar_data
            })

            self.stats['market_data_updates'] += 1
            self.stats['last_update'] = datetime.now(timezone.utc).isoformat()
            
        except Exception as e:
            logger.error(f"Error handling bar update: {e}")
            self.stats['errors'] += 1
    
    async def _handle_order_status(self, trade):
        """Handle IBKR order status updates"""
        try:
            order_data = {
                'order_id': trade.order.orderId,
                'client_id': trade.order.clientId,
                'perm_id': trade.order.permId,
                'status': trade.orderStatus.status,
                'filled': float(trade.orderStatus.filled),
                'remaining': float(trade.orderStatus.remaining),
                'avg_fill_price': float(trade.orderStatus.avgFillPrice) if trade.orderStatus.avgFillPrice > 0 else None,
                'last_fill_price': float(trade.orderStatus.lastFillPrice) if trade.orderStatus.lastFillPrice > 0 else None,
                'symbol': trade.contract.symbol,
                'action': trade.order.action,
                'total_quantity': float(trade.order.totalQuantity),
                'order_type': trade.order.orderType,
                'limit_price': float(trade.order.lmtPrice) if trade.order.lmtPrice > 0 else None,
                'stop_price': float(trade.order.auxPrice) if trade.order.auxPrice > 0 else None
            }
            
            # Broadcast to order subscribers
            account = getattr(trade.order, 'account', 'default')
            await broadcaster.broadcast_order_update(account, order_data)
            
            self.stats['order_updates'] += 1
            self.stats['last_update'] = datetime.now(timezone.utc).isoformat()
            
        except Exception as e:
            logger.error(f"Error handling order status: {e}")
            self.stats['errors'] += 1
    
    async def _handle_open_order(self, trade):
        """Handle IBKR open order updates"""
        try:
            # Similar to order status but for open orders
            await self._handle_order_status(trade)
            
        except Exception as e:
            logger.error(f"Error handling open order: {e}")
            self.stats['errors'] += 1
    
    async def _handle_position_update(self, position):
        """Handle IBKR position updates"""
        try:
            position_data = {
                'account': position.account,
                'symbol': position.contract.symbol,
                'position': float(position.position),
                'market_price': float(position.marketPrice) if position.marketPrice else None,
                'market_value': float(position.marketValue) if position.marketValue else None,
                'average_cost': float(position.avgCost) if position.avgCost else None,
                'unrealized_pnl': float(position.unrealizedPNL) if position.unrealizedPNL else None,
                'realized_pnl': float(position.realizedPNL) if position.realizedPNL else None
            }
            
            # Broadcast position update
            channel = f"positions:{position.account}"
            await connection_manager.broadcast_to_channel(channel, {
                'type': 'position_update',
                'channel': channel,
                'data': position_data
            })

            self.stats['position_updates'] += 1
            self.stats['last_update'] = datetime.now(timezone.utc).isoformat()
            
        except Exception as e:
            logger.error(f"Error handling position update: {e}")
            self.stats['errors'] += 1
    
    async def _handle_news_bulletin(self, msg_id, msg_type, message, orig_exchange):
        """Handle IBKR news bulletins"""
        try:
            news_data = {
                'message_id': msg_id,
                'message_type': msg_type,
                'message': message,
                'exchange': orig_exchange,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

            # Broadcast news update
            await connection_manager.broadcast_to_channel("news", {
                'type': 'news_bulletin',
                'channel': 'news',
                'data': news_data
            })

            self.stats['news_updates'] += 1
            self.stats['last_update'] = datetime.now(timezone.utc).isoformat()
            
        except Exception as e:
            logger.error(f"Error handling news bulletin: {e}")
            self.stats['errors'] += 1
    
    async def _handle_error(self, req_id, error_code, error_string, contract):
        """Handle IBKR errors"""
        try:
            error_data = {
                'request_id': req_id,
                'error_code': error_code,
                'error_message': error_string,
                'contract': contract.symbol if contract else None,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            # Broadcast error to system channel
            await broadcaster.broadcast_system_status({
                'type': 'error',
                'severity': 'high' if error_code < 1000 else 'medium',
                **error_data
            })
            
            self.stats['errors'] += 1
            
        except Exception as e:
            logger.error(f"Error handling IBKR error: {e}")
    
    async def subscribe_market_data(self, symbol: str, data_types: Optional[List[str]] = None):
        """Subscribe to real-time market data for a symbol"""
        if symbol in self.market_data_subscriptions:
            return True

        try:
            # Check if IBKR service is available and has required methods
            if not hasattr(self.ibkr_service, 'create_contract') or not hasattr(self.ibkr_service, 'ib'):
                logger.warning(f"IBKR service not properly initialized for market data subscription")
                return False

            if not self.ibkr_service.ib:
                logger.warning(f"IBKR connection not available for market data subscription")
                return False

            # Create contract
            contract = await self.ibkr_service.create_contract(symbol)
            if not contract:
                logger.error(f"Failed to create contract for {symbol}")
                return False

            # Request market data from IBKR
            self.ibkr_service.ib.reqMktData(
                contract=contract,
                genericTickList='',
                snapshot=False,
                regulatorySnapshot=False
            )

            self.market_data_subscriptions.add(symbol)
            logger.info(f"Subscribed to real-time market data for {symbol}")
            return True

        except Exception as e:
            logger.error(f"Failed to subscribe to market data for {symbol}: {e}")
            return False
    
    async def unsubscribe_market_data(self, symbol: str):
        """Unsubscribe from real-time market data for a symbol"""
        if symbol not in self.market_data_subscriptions:
            return True

        try:
            # Check if IBKR service is available and has required methods
            if not hasattr(self.ibkr_service, 'create_contract') or not hasattr(self.ibkr_service, 'ib'):
                logger.warning(f"IBKR service not properly initialized for market data unsubscription")
                # Still remove from local tracking
                self.market_data_subscriptions.discard(symbol)
                return True

            if not self.ibkr_service.ib:
                logger.warning(f"IBKR connection not available for market data unsubscription")
                # Still remove from local tracking
                self.market_data_subscriptions.discard(symbol)
                return True

            # Cancel market data subscription
            contract = await self.ibkr_service.create_contract(symbol)
            if contract:
                self.ibkr_service.ib.cancelMktData(contract)

            self.market_data_subscriptions.discard(symbol)
            logger.info(f"Unsubscribed from real-time market data for {symbol}")
            return True

        except Exception as e:
            logger.error(f"Failed to unsubscribe from market data for {symbol}: {e}")
            # Still remove from local tracking even if IBKR call failed
            self.market_data_subscriptions.discard(symbol)
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get bridge statistics"""
        return {
            **self.stats,
            'active_subscriptions': {
                'market_data': len(self.market_data_subscriptions),
                'orders': len(self.order_subscriptions),
                'news': len(self.news_subscriptions)
            },
            'subscribed_symbols': list(self.market_data_subscriptions)
        }


# Global bridge instance (will be initialized in main server)
realtime_bridge: Optional[RealTimeDataBridge] = None
