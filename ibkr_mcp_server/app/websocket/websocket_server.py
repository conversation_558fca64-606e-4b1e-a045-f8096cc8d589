"""
WebSocket Server for Real-Time Trading Data Streaming

Provides true real-time push-based data streaming via WebSockets
for market data, order updates, and trading signals.
"""

import asyncio
import json
import logging
from typing import Dict, Set, List, Any, Optional, Callable
from datetime import datetime, timezone
from uuid import uuid4
from fastapi import WebSocket, WebSocketDisconnect
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class WebSocketMessage(BaseModel):
    """Standard WebSocket message format"""
    type: str
    channel: str
    data: Dict[str, Any]
    timestamp: str
    message_id: str


class ConnectionManager:
    """Manages WebSocket connections and subscriptions"""
    
    def __init__(self):
        # Active connections: connection_id -> WebSocket
        self.active_connections: Dict[str, WebSocket] = {}
        
        # Subscriptions: channel -> set of connection_ids
        self.subscriptions: Dict[str, Set[str]] = {}
        
        # Connection metadata: connection_id -> metadata
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        
        # Message statistics
        self.stats = {
            'total_connections': 0,
            'active_connections': 0,
            'messages_sent': 0,
            'messages_failed': 0,
            'subscriptions_total': 0
        }
    
    async def connect(self, websocket: WebSocket, connection_id: Optional[str] = None) -> str:
        """Accept new WebSocket connection"""
        if connection_id is None:
            connection_id = str(uuid4())
        
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        self.connection_metadata[connection_id] = {
            'connected_at': datetime.now(timezone.utc).isoformat(),
            'subscriptions': set(),
            'messages_sent': 0,
            'last_activity': datetime.now(timezone.utc).isoformat()
        }
        
        self.stats['total_connections'] += 1
        self.stats['active_connections'] = len(self.active_connections)
        
        logger.info(f"WebSocket connection established: {connection_id}")
        
        # Send welcome message
        await self.send_to_connection(connection_id, {
            'type': 'connection',
            'channel': 'system',
            'data': {
                'status': 'connected',
                'connection_id': connection_id,
                'server_time': datetime.now(timezone.utc).isoformat()
            }
        })
        
        return connection_id
    
    def disconnect(self, connection_id: str):
        """Remove WebSocket connection"""
        if connection_id in self.active_connections:
            # Remove from all subscriptions
            for channel in list(self.subscriptions.keys()):
                self.unsubscribe(connection_id, channel)
            
            # Remove connection
            del self.active_connections[connection_id]
            del self.connection_metadata[connection_id]
            
            self.stats['active_connections'] = len(self.active_connections)
            logger.info(f"WebSocket connection closed: {connection_id}")
    
    def subscribe(self, connection_id: str, channel: str):
        """Subscribe connection to a channel"""
        if connection_id not in self.active_connections:
            return False
        
        if channel not in self.subscriptions:
            self.subscriptions[channel] = set()
        
        self.subscriptions[channel].add(connection_id)
        self.connection_metadata[connection_id]['subscriptions'].add(channel)
        self.stats['subscriptions_total'] += 1
        
        logger.info(f"Connection {connection_id} subscribed to {channel}")
        return True
    
    def unsubscribe(self, connection_id: str, channel: str):
        """Unsubscribe connection from a channel"""
        if channel in self.subscriptions:
            self.subscriptions[channel].discard(connection_id)
            if not self.subscriptions[channel]:
                del self.subscriptions[channel]
        
        if connection_id in self.connection_metadata:
            self.connection_metadata[connection_id]['subscriptions'].discard(channel)
    
    async def send_to_connection(self, connection_id: str, message: Dict[str, Any]):
        """Send message to specific connection"""
        if connection_id not in self.active_connections:
            return False
        
        websocket = self.active_connections[connection_id]
        
        # Create standardized message
        ws_message = WebSocketMessage(
            type=message.get('type', 'data'),
            channel=message.get('channel', 'default'),
            data=message.get('data', {}),
            timestamp=datetime.now(timezone.utc).isoformat(),
            message_id=str(uuid4())
        )
        
        try:
            await websocket.send_text(ws_message.model_dump_json())
            
            # Update statistics
            self.stats['messages_sent'] += 1
            self.connection_metadata[connection_id]['messages_sent'] += 1
            self.connection_metadata[connection_id]['last_activity'] = datetime.now(timezone.utc).isoformat()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send message to {connection_id}: {e}")
            self.stats['messages_failed'] += 1
            # Remove dead connection
            self.disconnect(connection_id)
            return False
    
    async def broadcast_to_channel(self, channel: str, message: Dict[str, Any]):
        """Broadcast message to all subscribers of a channel"""
        if channel not in self.subscriptions:
            return 0
        
        subscribers = list(self.subscriptions[channel])
        successful_sends = 0
        
        # Send to all subscribers concurrently
        tasks = []
        for connection_id in subscribers:
            task = asyncio.create_task(
                self.send_to_connection(connection_id, message)
            )
            tasks.append(task)
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            successful_sends = sum(1 for result in results if result is True)
        
        logger.debug(f"Broadcast to {channel}: {successful_sends}/{len(subscribers)} successful")
        return successful_sends
    
    def get_channel_subscribers(self, channel: str) -> List[str]:
        """Get list of connection IDs subscribed to channel"""
        return list(self.subscriptions.get(channel, set()))
    
    def get_connection_subscriptions(self, connection_id: str) -> List[str]:
        """Get list of channels a connection is subscribed to"""
        if connection_id in self.connection_metadata:
            return list(self.connection_metadata[connection_id]['subscriptions'])
        return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get connection and messaging statistics"""
        return {
            **self.stats,
            'channels': {
                channel: len(subscribers) 
                for channel, subscribers in self.subscriptions.items()
            },
            'connections': {
                conn_id: {
                    'subscriptions': len(metadata['subscriptions']),
                    'messages_sent': metadata['messages_sent'],
                    'connected_duration': (
                        datetime.now(timezone.utc) -
                        datetime.fromisoformat(metadata['connected_at'])
                    ).total_seconds()
                }
                for conn_id, metadata in self.connection_metadata.items()
            }
        }


# Global connection manager instance
connection_manager = ConnectionManager()


class WebSocketServer:
    """Main WebSocket server for real-time trading data"""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager
        self.message_handlers: Dict[str, Callable] = {}
        self.running = False
        
        # Register default message handlers
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """Register default message handlers"""
        self.message_handlers.update({
            'subscribe': self._handle_subscribe,
            'unsubscribe': self._handle_unsubscribe,
            'ping': self._handle_ping,
            'get_subscriptions': self._handle_get_subscriptions
        })
    
    async def _handle_subscribe(self, connection_id: str, message: Dict[str, Any]):
        """Handle subscription request"""
        channel = message.get('channel')
        if not channel:
            return {'error': 'Channel required for subscription'}
        
        success = self.connection_manager.subscribe(connection_id, channel)
        return {
            'type': 'subscription_response',
            'channel': channel,
            'subscribed': success
        }
    
    async def _handle_unsubscribe(self, connection_id: str, message: Dict[str, Any]):
        """Handle unsubscription request"""
        channel = message.get('channel')
        if not channel:
            return {'error': 'Channel required for unsubscription'}
        
        self.connection_manager.unsubscribe(connection_id, channel)
        return {
            'type': 'unsubscription_response',
            'channel': channel,
            'unsubscribed': True
        }
    
    async def _handle_ping(self, _connection_id: str, _message: Dict[str, Any]):
        """Handle ping request"""
        return {
            'type': 'pong',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    
    async def _handle_get_subscriptions(self, connection_id: str, _message: Dict[str, Any]):
        """Handle get subscriptions request"""
        subscriptions = self.connection_manager.get_connection_subscriptions(connection_id)
        return {
            'type': 'subscriptions_list',
            'subscriptions': subscriptions
        }
    
    async def handle_websocket(self, websocket: WebSocket):
        """Handle individual WebSocket connection"""
        connection_id = await self.connection_manager.connect(websocket)
        
        try:
            while True:
                # Receive message
                data = await websocket.receive_text()
                
                try:
                    message = json.loads(data)
                    message_type = message.get('type')
                    
                    if message_type in self.message_handlers:
                        response = await self.message_handlers[message_type](
                            connection_id, message
                        )
                        
                        if response:
                            await self.connection_manager.send_to_connection(
                                connection_id, response
                            )
                    else:
                        logger.warning(f"Unknown message type: {message_type}")
                        
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON from {connection_id}: {data}")
                    
        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected: {connection_id}")
        except Exception as e:
            logger.error(f"WebSocket error for {connection_id}: {e}")
        finally:
            self.connection_manager.disconnect(connection_id)


# Global WebSocket server instance
websocket_server = WebSocketServer(connection_manager)
