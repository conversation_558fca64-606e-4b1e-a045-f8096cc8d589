"""
FastAPI WebSocket Integration

Integrates WebSocket server with FastAPI for real-time trading data streaming.
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException
from typing import Dict, List, Any, Optional
import logging
import json
from datetime import datetime, timezone

from websocket.websocket_server import websocket_server, connection_manager
from services.ibkr_service import IBKRService

logger = logging.getLogger(__name__)

# Create WebSocket router
websocket_router = APIRouter()


@websocket_router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """Main WebSocket endpoint for real-time data streaming"""
    await websocket_server.handle_websocket(websocket)


@websocket_router.websocket("/ws/market-data")
async def market_data_websocket(websocket: WebSocket):
    """Dedicated WebSocket endpoint for market data streaming"""
    connection_id = await connection_manager.connect(websocket)
    
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Handle market data specific messages
            if message.get('type') == 'subscribe_market_data':
                symbol = message.get('symbol')
                data_types = message.get('data_types', ['LAST', 'BID', 'ASK'])
                
                if symbol:
                    # Subscribe to market data channel
                    channel = f"market_data:{symbol}"
                    connection_manager.subscribe(connection_id, channel)
                    
                    # Start IBKR market data streaming for this symbol
                    # This will be implemented in the next phase
                    await connection_manager.send_to_connection(connection_id, {
                        'type': 'subscription_confirmed',
                        'channel': channel,
                        'symbol': symbol,
                        'data_types': data_types
                    })
                    
    except WebSocketDisconnect:
        logger.info(f"Market data WebSocket disconnected: {connection_id}")
    except Exception as e:
        logger.error(f"Market data WebSocket error: {e}")
    finally:
        connection_manager.disconnect(connection_id)


@websocket_router.websocket("/ws/orders")
async def orders_websocket(websocket: WebSocket):
    """Dedicated WebSocket endpoint for order updates"""
    connection_id = await connection_manager.connect(websocket)
    
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Handle order-specific messages
            if message.get('type') == 'subscribe_orders':
                account = message.get('account', 'default')
                
                # Subscribe to orders channel
                channel = f"orders:{account}"
                connection_manager.subscribe(connection_id, channel)
                
                await connection_manager.send_to_connection(connection_id, {
                    'type': 'subscription_confirmed',
                    'channel': channel,
                    'account': account
                })
                    
    except WebSocketDisconnect:
        logger.info(f"Orders WebSocket disconnected: {connection_id}")
    except Exception as e:
        logger.error(f"Orders WebSocket error: {e}")
    finally:
        connection_manager.disconnect(connection_id)


@websocket_router.websocket("/ws/signals")
async def signals_websocket(websocket: WebSocket):
    """Dedicated WebSocket endpoint for trading signals"""
    connection_id = await connection_manager.connect(websocket)
    
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Handle signal-specific messages
            if message.get('type') == 'subscribe_signals':
                strategy_id = message.get('strategy_id')
                
                if strategy_id:
                    channel = f"signals:{strategy_id}"
                else:
                    channel = "signals:all"
                
                connection_manager.subscribe(connection_id, channel)
                
                await connection_manager.send_to_connection(connection_id, {
                    'type': 'subscription_confirmed',
                    'channel': channel,
                    'strategy_id': strategy_id
                })
                    
    except WebSocketDisconnect:
        logger.info(f"Signals WebSocket disconnected: {connection_id}")
    except Exception as e:
        logger.error(f"Signals WebSocket error: {e}")
    finally:
        connection_manager.disconnect(connection_id)


# REST API endpoints for WebSocket management
@websocket_router.get("/ws/stats")
async def get_websocket_stats():
    """Get WebSocket server statistics"""
    return connection_manager.get_statistics()


@websocket_router.post("/ws/broadcast/{channel}")
async def broadcast_message(channel: str, message: Dict[str, Any]):
    """Broadcast message to all subscribers of a channel"""
    try:
        sent_count = await connection_manager.broadcast_to_channel(channel, {
            'type': 'broadcast',
            'channel': channel,
            'data': message
        })
        
        return {
            'success': True,
            'channel': channel,
            'subscribers_notified': sent_count
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@websocket_router.get("/ws/channels")
async def get_active_channels():
    """Get list of active channels and their subscriber counts"""
    stats = connection_manager.get_statistics()
    return {
        'channels': stats.get('channels', {}),
        'total_channels': len(stats.get('channels', {}))
    }


@websocket_router.get("/ws/connections")
async def get_active_connections():
    """Get list of active connections and their details"""
    stats = connection_manager.get_statistics()
    return {
        'active_connections': stats.get('active_connections', 0),
        'total_connections': stats.get('total_connections', 0),
        'connections': stats.get('connections', {})
    }


@websocket_router.delete("/ws/connections/{connection_id}")
async def disconnect_connection(connection_id: str):
    """Forcefully disconnect a specific connection"""
    if connection_id in connection_manager.active_connections:
        connection_manager.disconnect(connection_id)
        return {'success': True, 'message': f'Connection {connection_id} disconnected'}
    else:
        raise HTTPException(status_code=404, detail='Connection not found')


# WebSocket message broadcasting utilities
class WebSocketBroadcaster:
    """Utility class for broadcasting messages to WebSocket clients"""
    
    @staticmethod
    async def broadcast_market_data(symbol: str, data: Dict[str, Any]):
        """Broadcast market data update"""
        channel = f"market_data:{symbol}"
        await connection_manager.broadcast_to_channel(channel, {
            'type': 'market_data_update',
            'channel': channel,
            'data': {
                'symbol': symbol,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                **data
            }
        })
    
    @staticmethod
    async def broadcast_order_update(account: str, order_data: Dict[str, Any]):
        """Broadcast order status update"""
        channel = f"orders:{account}"
        await connection_manager.broadcast_to_channel(channel, {
            'type': 'order_update',
            'channel': channel,
            'data': {
                'account': account,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                **order_data
            }
        })
    
    @staticmethod
    async def broadcast_trading_signal(strategy_id: str, signal_data: Dict[str, Any]):
        """Broadcast trading signal"""
        # Broadcast to specific strategy
        if strategy_id:
            channel = f"signals:{strategy_id}"
            await connection_manager.broadcast_to_channel(channel, {
                'type': 'trading_signal',
                'channel': channel,
                'data': {
                    'strategy_id': strategy_id,
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    **signal_data
                }
            })
        
        # Also broadcast to general signals channel
        await connection_manager.broadcast_to_channel("signals:all", {
            'type': 'trading_signal',
            'channel': "signals:all",
            'data': {
                'strategy_id': strategy_id,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                **signal_data
            }
        })
    
    @staticmethod
    async def broadcast_risk_alert(alert_data: Dict[str, Any]):
        """Broadcast risk management alert"""
        await connection_manager.broadcast_to_channel("risk_alerts", {
            'type': 'risk_alert',
            'channel': 'risk_alerts',
            'data': {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                **alert_data
            }
        })
    
    @staticmethod
    async def broadcast_system_status(status_data: Dict[str, Any]):
        """Broadcast system status update"""
        await connection_manager.broadcast_to_channel("system", {
            'type': 'system_status',
            'channel': 'system',
            'data': {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                **status_data
            }
        })


# Global broadcaster instance
broadcaster = WebSocketBroadcaster()
