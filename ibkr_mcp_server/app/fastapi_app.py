"""
FastAPI Application for IBKR MCP Server with WebSocket Support

Provides REST API endpoints and WebSocket real-time streaming
for the IBKR MCP trading system.
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging
import uvicorn
from typing import Dict, Any
from uuid import UUID

# Import WebSocket components
from websocket import websocket_router, connection_manager, realtime_bridge
from services.ibkr_service import IBKRService
from integrations.event_stream_manager import EventStreamManager

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(_app: FastAPI):
    """Manage FastAPI application lifespan"""
    logger.info("Starting IBKR MCP FastAPI server with WebSocket support...")
    
    # Initialize services
    try:
        # Initialize IBKR service
        ibkr_service = IBKRService()
        await ibkr_service.connect()
        
        # Initialize event manager
        event_manager = EventStreamManager()
        await event_manager.start()
        
        # Initialize real-time bridge
        global realtime_bridge
        if realtime_bridge is None:
            from websocket.realtime_bridge import RealTimeDataBridge
            realtime_bridge = RealTimeDataBridge(ibkr_service, event_manager)  # type: ignore
        
        logger.info("All services initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        raise
    
    yield
    
    # Cleanup
    logger.info("Shutting down IBKR MCP FastAPI server...")
    try:
        if ibkr_service:
            await ibkr_service.disconnect()
        if event_manager:
            await event_manager.stop()
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# Create FastAPI app with WebSocket support
app = FastAPI(
    title="IBKR MCP Server with Real-Time WebSocket Streaming",
    description="Interactive Brokers MCP Server with true real-time push-based data streaming",
    version="2.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include WebSocket router
app.include_router(websocket_router, prefix="/api/v1")


# Health check endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "IBKR MCP Server",
        "version": "2.0.0",
        "websocket_enabled": True
    }


@app.get("/api/v1/status")
async def get_system_status():
    """Get comprehensive system status"""
    try:
        # Get WebSocket statistics
        ws_stats = connection_manager.get_statistics()
        
        # Get real-time bridge statistics
        bridge_stats = {}
        if realtime_bridge:
            bridge_stats = realtime_bridge.get_statistics()
        
        return {
            "system": {
                "status": "operational",
                "timestamp": "2024-12-01T00:00:00Z",
                "version": "2.0.0"
            },
            "websocket": {
                "enabled": True,
                "active_connections": ws_stats.get('active_connections', 0),
                "total_connections": ws_stats.get('total_connections', 0),
                "messages_sent": ws_stats.get('messages_sent', 0),
                "channels": ws_stats.get('channels', {})
            },
            "realtime_bridge": {
                "enabled": realtime_bridge is not None,
                **bridge_stats
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/market-data/subscribe")
async def subscribe_market_data(request: Dict[str, Any]):
    """Subscribe to real-time market data for symbols"""
    try:
        symbols = request.get('symbols', [])
        data_types = request.get('data_types', ['LAST', 'BID', 'ASK'])
        
        if not symbols:
            raise HTTPException(status_code=400, detail="Symbols required")
        
        results = {}
        if realtime_bridge:
            for symbol in symbols:
                success = await realtime_bridge.subscribe_market_data(symbol, data_types)
                results[symbol] = {
                    'subscribed': success,
                    'data_types': data_types,
                    'channel': f"market_data:{symbol}"
                }
        else:
            raise HTTPException(status_code=503, detail="Real-time bridge not available")
        
        return {
            'success': True,
            'subscriptions': results,
            'message': f'Subscribed to {len(symbols)} symbols'
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/market-data/unsubscribe")
async def unsubscribe_market_data(request: Dict[str, Any]):
    """Unsubscribe from real-time market data for symbols"""
    try:
        symbols = request.get('symbols', [])

        if not symbols:
            raise HTTPException(status_code=400, detail="Symbols required")

        results = {}
        if realtime_bridge:
            for symbol in symbols:
                success = await realtime_bridge.unsubscribe_market_data(symbol)
                results[symbol] = {
                    'unsubscribed': success,
                    'channel': f"market_data:{symbol}"
                }
        else:
            raise HTTPException(status_code=503, detail="Real-time bridge not available")

        return {
            'success': True,
            'unsubscriptions': results,
            'message': f'Unsubscribed from {len(symbols)} symbols'
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/klines")
async def get_klines(
    symbol: str,
    interval: str = "1m",
    limit: int = 100
):
    """Get historical kline/candlestick data for a symbol"""
    try:
        from datetime import datetime, timedelta
        import random

        # For demo purposes, generate mock historical data
        # In a real implementation, this would fetch from IBKR or a data provider

        if limit > 1000:
            limit = 1000  # Cap the limit

        # Generate mock data
        end_time = datetime.now()
        interval_minutes = 1  # Default to 1 minute

        if interval == "5m":
            interval_minutes = 5
        elif interval == "15m":
            interval_minutes = 15
        elif interval == "1h":
            interval_minutes = 60
        elif interval == "1d":
            interval_minutes = 1440

        klines = []
        base_price = 150.0  # Mock base price

        for i in range(limit):
            timestamp = end_time - timedelta(minutes=interval_minutes * (limit - i - 1))

            # Generate realistic OHLCV data
            open_price = base_price + random.uniform(-5, 5)
            close_price = open_price + random.uniform(-2, 2)
            high_price = max(open_price, close_price) + random.uniform(0, 1)
            low_price = min(open_price, close_price) - random.uniform(0, 1)
            volume = random.randint(1000, 10000)

            klines.append({
                "timestamp": int(timestamp.timestamp() * 1000),  # milliseconds
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "close": round(close_price, 2),
                "volume": volume
            })

            base_price = close_price  # Use close as next base

        return {
            'success': True,
            'data': klines,
            'symbol': symbol,
            'interval': interval,
            'count': len(klines)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/market-data/subscriptions")
async def get_market_data_subscriptions():
    """Get list of active market data subscriptions"""
    try:
        if realtime_bridge:
            stats = realtime_bridge.get_statistics()
            return {
                'active_subscriptions': stats.get('subscribed_symbols', []),
                'subscription_count': len(stats.get('subscribed_symbols', [])),
                'statistics': stats
            }
        else:
            return {
                'active_subscriptions': [],
                'subscription_count': 0,
                'message': 'Real-time bridge not available'
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Demo endpoint for testing WebSocket broadcasting
@app.post("/api/v1/demo/broadcast")
async def demo_broadcast(request: Dict[str, Any]):
    """Demo endpoint to test WebSocket broadcasting"""
    try:
        message_type = request.get('type', 'demo')
        channel = request.get('channel', 'demo')
        data = request.get('data', {'message': 'Hello WebSocket!'})
        
        sent_count = await connection_manager.broadcast_to_channel(channel, {
            'type': message_type,
            'channel': channel,
            'data': data
        })
        
        return {
            'success': True,
            'channel': channel,
            'subscribers_notified': sent_count,
            'message': f'Broadcast sent to {sent_count} subscribers'
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Backtesting Analytics Suite API Endpoints
@app.get("/api/v1/backtests")
async def get_all_backtests(limit: int = 100, offset: int = 0) -> Dict[str, Any]:
    """
    Get all backtest runs with pagination

    Args:
        limit: Maximum number of results to return (default: 100)
        offset: Number of results to skip (default: 0)

    Returns:
        List of backtest run summaries
    """
    try:
        from app.database.repositories.backtesting_repositories import BacktestRunRepository

        run_repo = BacktestRunRepository()
        runs = await run_repo.get_all_runs(limit=limit, offset=offset)

        return {
            "status": "success",
            "data": runs,
            "pagination": {
                "limit": limit,
                "offset": offset,
                "count": len(runs)
            }
        }

    except Exception as e:
        logger.error(f"Error fetching backtest runs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/backtests/{run_id}")
async def get_backtest_details(run_id: str) -> Dict[str, Any]:
    """
    Get comprehensive backtest data for a specific run

    Args:
        run_id: UUID of the backtest run

    Returns:
        Complete backtest data including summary, trades, and equity curve
    """
    try:
        from app.database.repositories.backtesting_repositories import (
            BacktestRunRepository,
            BacktestTradeRepository,
            BacktestEquityCurveRepository
        )

        # Validate UUID format
        try:
            run_uuid = UUID(run_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid run_id format")

        # Initialize repositories
        run_repo = BacktestRunRepository()
        trade_repo = BacktestTradeRepository()
        equity_repo = BacktestEquityCurveRepository()

        # Fetch all data
        run_summary = await run_repo.get_run_by_id(run_uuid)
        if not run_summary:
            raise HTTPException(status_code=404, detail="Backtest run not found")

        trades = await trade_repo.get_trades_by_run_id(run_uuid)
        equity_curve = await equity_repo.get_equity_curve_by_run_id(run_uuid)

        # Combine all data into comprehensive response
        return {
            "status": "success",
            "data": {
                "summary": run_summary,
                "trades": trades,
                "equity_curve": equity_curve,
                "statistics": {
                    "total_trades": len(trades),
                    "equity_points": len(equity_curve),
                    "date_range": {
                        "start": run_summary.get("start_date"),
                        "end": run_summary.get("end_date")
                    }
                }
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching backtest details for {run_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/backtests/strategy/{strategy_name}")
async def get_backtests_by_strategy(strategy_name: str) -> Dict[str, Any]:
    """
    Get all backtest runs for a specific strategy

    Args:
        strategy_name: Name of the strategy

    Returns:
        List of backtest runs for the strategy
    """
    try:
        from app.database.repositories.backtesting_repositories import BacktestRunRepository

        run_repo = BacktestRunRepository()
        runs = await run_repo.get_runs_by_strategy(strategy_name)

        return {
            "status": "success",
            "strategy_name": strategy_name,
            "data": runs,
            "count": len(runs)
        }

    except Exception as e:
        logger.error(f"Error fetching backtest runs for strategy {strategy_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/backtests/run")
async def run_backtest_and_save(request: Dict[str, Any]) -> Dict[str, Any]:
    """
    Run a backtest and save results to database

    Args:
        request: Backtest configuration including strategy_config, start_date, end_date, initial_capital

    Returns:
        Backtest results and database save status
    """
    try:
        from implementations.backtesting.backtesting_impl import BacktestingImplementation

        # Extract parameters
        strategy_config = request.get('strategy_config', {})
        start_date = request.get('start_date')
        end_date = request.get('end_date')
        initial_capital = request.get('initial_capital', 100000)
        save_to_db = request.get('save_to_db', True)

        # Validate required parameters
        if not strategy_config:
            raise HTTPException(status_code=400, detail="strategy_config is required")
        if not start_date:
            raise HTTPException(status_code=400, detail="start_date is required")
        if not end_date:
            raise HTTPException(status_code=400, detail="end_date is required")

        # Run backtest
        backtest_impl = BacktestingImplementation()
        results = await backtest_impl.run_backtest(
            strategy_config=strategy_config,
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital
        )

        if results.get('status') != 'success':
            raise HTTPException(status_code=400, detail=results.get('message', 'Backtest failed'))

        # Save to database if requested
        save_result = None
        if save_to_db:
            save_result = await backtest_impl.save_results_to_db(results)
            if save_result.get('status') != 'success':
                logger.warning(f"Failed to save backtest results: {save_result.get('message')}")

        return {
            "status": "success",
            "backtest_results": results,
            "database_save": save_result,
            "run_id": save_result.get('run_id') if save_result else None
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error running backtest: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s | %(levelname)s | %(name)s | %(message)s'
    )
    
    # Run the server
    uvicorn.run(
        "fastapi_app:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
