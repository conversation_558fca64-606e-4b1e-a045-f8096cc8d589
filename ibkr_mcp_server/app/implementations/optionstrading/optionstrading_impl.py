"""
OptionsTrading Implementation - Layer 3: Business Logic
Core business logic for OptionsTrading operations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional, List, Tuple
import logging
import asyncio
import time
import math
from datetime import datetime, timezone, timedelta
from ibapi.contract import Contract, ComboLeg
from ibapi.order import Order
import numpy as np
from scipy.stats import norm

logger = logging.getLogger(__name__)

class OptionsTradingImplementation:
    """Core implementation for OptionsTrading operations"""
    
    def __init__(self, ibkr_service=None, **kwargs):
        self.ibkr_service = ibkr_service
        self.services = kwargs
        self.options_cache = {}  # Cache options chain data
        self.greeks_cache = {}   # Cache Greeks calculations
        
        if not self.ibkr_service:
            logger.warning("OptionsTradingImplementation initialized without IBKR service")
    
    async def get_options_chain(
        self,
        symbol: str,
        expiration: Optional[str] = None,
        strike_range: Optional[Tuple[float, float]] = None,
        option_type: str = "BOTH",
        exchange: str = "SMART"
    ) -> Dict[str, Any]:
        """
        Get options chain for a symbol.
        
        Args:
            symbol: Underlying symbol (e.g., 'AAPL', 'SPY')
            expiration: Specific expiration date (YYYYMMDD) or None for all
            strike_range: Tuple of (min_strike, max_strike) or None for all
            option_type: 'CALL', 'PUT', or 'BOTH'
            exchange: Exchange for options (default: 'SMART')
            
        Returns:
            Dict containing options chain data with strikes, expirations, and market data
        """
        try:
            if not self.ibkr_service:
                return {
                    "error": "IBKR service not available",
                    "success": False
                }
            
            # Create underlying contract
            underlying = Contract()
            underlying.symbol = symbol.upper()
            underlying.secType = "STK"
            underlying.exchange = "SMART"
            underlying.currency = "USD"
            
            # Get contract details for the underlying
            req_id = int(time.time() * 1000) % 100000
            
            if hasattr(self.ibkr_service, 'get_contract_details'):
                try:
                    underlying_details = await self.ibkr_service.get_contract_details(underlying)
                    if not underlying_details:
                        return {
                            "error": f"Could not get contract details for {symbol}",
                            "success": False
                        }
                except Exception as e:
                    logger.error(f"Failed to get underlying contract details: {e}")
                    return {
                        "error": f"Failed to get underlying contract: {str(e)}",
                        "success": False
                    }
            
            # Get options chain
            options_chain = {}
            
            # If specific expiration requested
            if expiration:
                expirations = [expiration]
            else:
                # Get all available expirations (this would normally come from contract details)
                # For now, generate some common near-term expirations
                expirations = self._generate_common_expirations()
            
            for exp_date in expirations:
                options_chain[exp_date] = {
                    "calls": {},
                    "puts": {},
                    "expiration_date": exp_date
                }
                
                # Get current underlying price for strike range calculation
                try:
                    current_price = await self._get_current_price(symbol)
                    if not current_price:
                        current_price = 100.0  # Fallback
                except:
                    current_price = 100.0  # Fallback
                
                # Determine strike range
                if strike_range:
                    min_strike, max_strike = strike_range
                else:
                    # Use +/- 20% of current price
                    min_strike = current_price * 0.8
                    max_strike = current_price * 1.2
                
                # Generate strike prices (typically in $1, $2.50, $5 increments)
                strikes = self._generate_strike_prices(current_price, min_strike, max_strike)
                
                # Get options contracts for each strike
                for strike in strikes:
                    if option_type in ["CALL", "BOTH"]:
                        call_data = await self._get_option_data(symbol, exp_date, strike, "CALL", exchange)
                        if call_data:
                            options_chain[exp_date]["calls"][str(strike)] = call_data
                    
                    if option_type in ["PUT", "BOTH"]:
                        put_data = await self._get_option_data(symbol, exp_date, strike, "PUT", exchange)
                        if put_data:
                            options_chain[exp_date]["puts"][str(strike)] = put_data
            
            # Determine overall data quality and add warnings
            data_sources = set()
            real_data_count = 0
            estimated_data_count = 0

            for exp_date, options in options_chain.items():
                for option_type_key, strikes in options.items():
                    for strike, option_data in strikes.items():
                        source = option_data.get("data_source", "unknown")
                        data_sources.add(source)
                        if source == "real_market_data":
                            real_data_count += 1
                        elif "estimated" in source or "basic" in source:
                            estimated_data_count += 1

            # Create data quality summary
            data_quality = {
                "real_data_points": real_data_count,
                "estimated_data_points": estimated_data_count,
                "data_sources_used": list(data_sources),
                "overall_quality": "REAL" if real_data_count > 0 and estimated_data_count == 0 else
                                 "MIXED" if real_data_count > 0 else "ESTIMATED"
            }

            # Add warnings for estimated data
            warnings = []
            if estimated_data_count > 0:
                warnings.append("⚠️ CONTAINS ESTIMATED DATA - Not suitable for live trading decisions")
            if "basic_estimation" in data_sources:
                warnings.append("⚠️ BASIC ESTIMATION USED - Data may be significantly inaccurate")
            if not self.ibkr_service or not self.ibkr_service.connected:
                warnings.append("⚠️ NO MARKET DATA CONNECTION - All data is theoretical")

            return {
                "success": True,
                "symbol": symbol,
                "underlying_price": current_price,
                "option_type": option_type,
                "strike_range": strike_range,
                "options_chain": options_chain,
                "chain_count": sum(len(exp["calls"]) + len(exp["puts"]) for exp in options_chain.values()),
                "data_quality": data_quality,
                "warnings": warnings,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting options chain for {symbol}: {e}")
            return {
                "error": f"Failed to get options chain: {str(e)}",
                "success": False
            }
    
    async def calculate_greeks(
        self,
        symbol: str,
        strike: float,
        expiration: str,
        option_type: str,
        current_price: Optional[float] = None,
        risk_free_rate: float = 0.05,
        dividend_yield: float = 0.0
    ) -> Dict[str, Any]:
        """
        Calculate option Greeks using Black-Scholes model.
        
        Args:
            symbol: Underlying symbol
            strike: Strike price
            expiration: Expiration date (YYYYMMDD)
            option_type: 'CALL' or 'PUT'
            current_price: Current underlying price (auto-fetch if None)
            risk_free_rate: Risk-free interest rate (default: 5%)
            dividend_yield: Dividend yield (default: 0%)
            
        Returns:
            Dict containing calculated Greeks (Delta, Gamma, Theta, Vega, Rho)
        """
        try:
            # Get current price if not provided
            if current_price is None:
                current_price = await self._get_current_price(symbol)
                if not current_price:
                    return {
                        "error": f"Could not get current price for {symbol}",
                        "success": False
                    }
            
            # Calculate time to expiration
            exp_date = datetime.strptime(expiration, "%Y%m%d")
            current_date = datetime.now()
            time_to_exp = (exp_date - current_date).days / 365.0
            
            if time_to_exp <= 0:
                return {
                    "error": "Option has expired",
                    "success": False
                }
            
            # Get implied volatility (simplified - would normally get from market data)
            implied_vol = await self._get_implied_volatility(symbol, strike, expiration, option_type)
            
            # Calculate Greeks using Black-Scholes
            greeks = self._calculate_black_scholes_greeks(
                current_price, strike, time_to_exp, risk_free_rate, 
                dividend_yield, implied_vol, option_type
            )
            
            # Calculate theoretical option price
            theoretical_price = self._black_scholes_price(
                current_price, strike, time_to_exp, risk_free_rate,
                dividend_yield, implied_vol, option_type
            )
            
            return {
                "success": True,
                "symbol": symbol,
                "strike": strike,
                "expiration": expiration,
                "option_type": option_type,
                "current_price": current_price,
                "time_to_expiration": time_to_exp,
                "implied_volatility": implied_vol,
                "theoretical_price": theoretical_price,
                "greeks": greeks,
                "model": "Black-Scholes",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error calculating Greeks: {e}")
            return {
                "error": f"Failed to calculate Greeks: {str(e)}",
                "success": False
            }
    
    async def analyze_option_strategy(
        self,
        strategy_type: str,
        symbol: str,
        legs: List[Dict[str, Any]],
        analysis_range: Optional[Tuple[float, float]] = None
    ) -> Dict[str, Any]:
        """
        Analyze complex options strategy.
        
        Args:
            strategy_type: Strategy name (e.g., 'iron_condor', 'butterfly', 'straddle')
            symbol: Underlying symbol
            legs: List of option legs with strike, expiration, type, quantity
            analysis_range: Price range for P&L analysis
            
        Returns:
            Dict containing strategy analysis including P&L, Greeks, and risk metrics
        """
        try:
            if not legs:
                return {
                    "error": "No option legs provided",
                    "success": False
                }
            
            # Get current underlying price
            current_price = await self._get_current_price(symbol)
            if not current_price:
                return {
                    "error": f"Could not get current price for {symbol}",
                    "success": False
                }
            
            # Determine analysis range
            if analysis_range:
                min_price, max_price = analysis_range
            else:
                # Use +/- 30% of current price
                min_price = current_price * 0.7
                max_price = current_price * 1.3
            
            # Generate price points for analysis
            price_points = np.linspace(min_price, max_price, 50)
            
            # Calculate strategy metrics
            strategy_analysis = {
                "current_pnl": 0,
                "max_profit": float('-inf'),
                "max_loss": float('inf'),
                "breakeven_points": [],
                "total_premium": 0,
                "net_delta": 0,
                "net_gamma": 0,
                "net_theta": 0,
                "net_vega": 0,
                "pnl_curve": []
            }
            
            # Analyze each leg
            legs_analysis = []
            for i, leg in enumerate(legs):
                leg_analysis = await self._analyze_option_leg(
                    symbol, leg, current_price, price_points
                )
                legs_analysis.append(leg_analysis)
                
                # Accumulate strategy totals
                strategy_analysis["total_premium"] += leg_analysis.get("premium", 0) * leg.get("quantity", 1)
                strategy_analysis["net_delta"] += leg_analysis.get("delta", 0) * leg.get("quantity", 1)
                strategy_analysis["net_gamma"] += leg_analysis.get("gamma", 0) * leg.get("quantity", 1)
                strategy_analysis["net_theta"] += leg_analysis.get("theta", 0) * leg.get("quantity", 1)
                strategy_analysis["net_vega"] += leg_analysis.get("vega", 0) * leg.get("quantity", 1)
            
            # Calculate combined P&L curve
            for i, price in enumerate(price_points):
                total_pnl = 0
                for j, leg in enumerate(legs):
                    leg_pnl = legs_analysis[j]["pnl_curve"][i]
                    total_pnl += leg_pnl * leg.get("quantity", 1)
                
                strategy_analysis["pnl_curve"].append({
                    "price": price,
                    "pnl": total_pnl
                })
                
                # Update max profit/loss
                strategy_analysis["max_profit"] = max(strategy_analysis["max_profit"], total_pnl)
                strategy_analysis["max_loss"] = min(strategy_analysis["max_loss"], total_pnl)
            
            # Find breakeven points
            strategy_analysis["breakeven_points"] = self._find_breakeven_points(
                strategy_analysis["pnl_curve"]
            )
            
            # Calculate current P&L
            current_pnl = 0
            for j, leg in enumerate(legs):
                current_leg_pnl = self._calculate_current_leg_pnl(legs_analysis[j], current_price)
                current_pnl += current_leg_pnl * leg.get("quantity", 1)
            strategy_analysis["current_pnl"] = current_pnl
            
            # Risk assessment
            risk_assessment = self._assess_strategy_risk(strategy_analysis, strategy_type)
            
            return {
                "success": True,
                "strategy_type": strategy_type,
                "symbol": symbol,
                "current_price": current_price,
                "legs": legs,
                "legs_analysis": legs_analysis,
                "strategy_analysis": strategy_analysis,
                "risk_assessment": risk_assessment,
                "analysis_range": [min_price, max_price],
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing option strategy: {e}")
            return {
                "error": f"Failed to analyze strategy: {str(e)}",
                "success": False
            }
    
    async def get_implied_volatility_surface(
        self,
        symbol: str,
        expiration_range: Optional[Tuple[str, str]] = None
    ) -> Dict[str, Any]:
        """
        Get implied volatility surface for an underlying.
        
        Args:
            symbol: Underlying symbol
            expiration_range: Tuple of (start_date, end_date) in YYYYMMDD format
            
        Returns:
            Dict containing implied volatility surface data
        """
        try:
            # Get options chain data
            chain_data = await self.get_options_chain(symbol, option_type="BOTH")
            if not chain_data.get("success"):
                return chain_data
            
            options_chain = chain_data["options_chain"]
            current_price = chain_data["underlying_price"]
            
            iv_surface = {
                "expirations": [],
                "strikes": [],
                "iv_matrix": [],
                "surface_stats": {}
            }
            
            # Process each expiration
            for exp_date, exp_data in options_chain.items():
                # Skip if outside expiration range
                if expiration_range:
                    start_date, end_date = expiration_range
                    if exp_date < start_date or exp_date > end_date:
                        continue
                
                iv_surface["expirations"].append(exp_date)
                exp_ivs = []
                
                # Get all strikes for this expiration
                all_strikes = set()
                all_strikes.update(float(s) for s in exp_data["calls"].keys())
                all_strikes.update(float(s) for s in exp_data["puts"].keys())
                sorted_strikes = sorted(all_strikes)
                
                if not iv_surface["strikes"]:
                    iv_surface["strikes"] = sorted_strikes
                
                # Calculate IV for each strike
                for strike in sorted_strikes:
                    # Use ATM option type or average of call/put IVs
                    call_iv = exp_data["calls"].get(str(strike), {}).get("implied_volatility")
                    put_iv = exp_data["puts"].get(str(strike), {}).get("implied_volatility")
                    
                    if call_iv and put_iv:
                        avg_iv = (call_iv + put_iv) / 2
                    elif call_iv:
                        avg_iv = call_iv
                    elif put_iv:
                        avg_iv = put_iv
                    else:
                        # Estimate IV if not available
                        avg_iv = await self._estimate_implied_volatility(symbol, strike, exp_date)
                    
                    exp_ivs.append(avg_iv)
                
                iv_surface["iv_matrix"].append(exp_ivs)
            
            # Calculate surface statistics
            if iv_surface["iv_matrix"]:
                all_ivs = [iv for row in iv_surface["iv_matrix"] for iv in row if iv is not None]
                if all_ivs:
                    iv_surface["surface_stats"] = {
                        "min_iv": min(all_ivs),
                        "max_iv": max(all_ivs),
                        "avg_iv": sum(all_ivs) / len(all_ivs),
                        "iv_std": np.std(all_ivs) if len(all_ivs) > 1 else 0
                    }
            
            return {
                "success": True,
                "symbol": symbol,
                "current_price": current_price,
                "iv_surface": iv_surface,
                "expiration_count": len(iv_surface["expirations"]),
                "strike_count": len(iv_surface["strikes"]),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting IV surface for {symbol}: {e}")
            return {
                "error": f"Failed to get IV surface: {str(e)}",
                "success": False
            }
    
    async def create_options_strategy(
        self,
        strategy_name: str,
        symbol: str,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Create predefined options strategy.
        
        Args:
            strategy_name: Name of strategy ('iron_condor', 'butterfly', 'straddle', etc.)
            symbol: Underlying symbol
            parameters: Strategy-specific parameters
            
        Returns:
            Dict containing strategy definition and suggested legs
        """
        try:
            current_price = await self._get_current_price(symbol)
            if not current_price:
                return {
                    "error": f"Could not get current price for {symbol}",
                    "success": False
                }
            
            # Get available expirations and strikes
            chain_data = await self.get_options_chain(symbol)
            if not chain_data.get("success"):
                return chain_data
            
            # Generate strategy based on type
            strategy_legs = []
            if strategy_name.lower() == "iron_condor":
                strategy_legs = self._create_iron_condor(current_price, parameters, chain_data)
            elif strategy_name.lower() == "butterfly":
                strategy_legs = self._create_butterfly(current_price, parameters, chain_data)
            elif strategy_name.lower() == "straddle":
                strategy_legs = self._create_straddle(current_price, parameters, chain_data)
            elif strategy_name.lower() == "strangle":
                strategy_legs = self._create_strangle(current_price, parameters, chain_data)
            elif strategy_name.lower() == "covered_call":
                strategy_legs = self._create_covered_call(current_price, parameters, chain_data)
            else:
                return {
                    "error": f"Unknown strategy: {strategy_name}",
                    "success": False
                }
            
            if not strategy_legs:
                return {
                    "error": f"Could not create {strategy_name} strategy",
                    "success": False
                }
            
            # Analyze the created strategy
            strategy_analysis = await self.analyze_option_strategy(
                strategy_name, symbol, strategy_legs
            )
            
            return {
                "success": True,
                "strategy_name": strategy_name,
                "symbol": symbol,
                "current_price": current_price,
                "strategy_legs": strategy_legs,
                "analysis": strategy_analysis,
                "parameters_used": parameters,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error creating {strategy_name} strategy: {e}")
            return {
                "error": f"Failed to create strategy: {str(e)}",
                "success": False
            }
    
    # Helper methods
    
    def _generate_common_expirations(self) -> List[str]:
        """Generate common near-term expiration dates"""
        expirations = []
        base_date = datetime.now()
        
        # Add weekly expirations for next 4 weeks
        for weeks in range(1, 5):
            exp_date = base_date + timedelta(weeks=weeks)
            # Find next Friday
            days_to_friday = (4 - exp_date.weekday()) % 7
            friday = exp_date + timedelta(days=days_to_friday)
            expirations.append(friday.strftime("%Y%m%d"))
        
        # Add monthly expirations for next 3 months
        for months in range(1, 4):
            exp_date = base_date + timedelta(days=30*months)
            # Third Friday of the month (approximation)
            third_friday = exp_date.replace(day=15) + timedelta(days=(4-exp_date.replace(day=15).weekday())%7)
            expirations.append(third_friday.strftime("%Y%m%d"))
        
        return sorted(list(set(expirations)))
    
    def _generate_strike_prices(self, current_price: float, min_strike: float, max_strike: float) -> List[float]:
        """Generate appropriate strike prices based on underlying price"""
        strikes = []
        
        # Determine strike increment based on price level
        if current_price < 50:
            increment = 1.0
        elif current_price < 100:
            increment = 2.5
        elif current_price < 200:
            increment = 5.0
        else:
            increment = 10.0
        
        # Generate strikes
        strike = math.floor(min_strike / increment) * increment
        while strike <= max_strike:
            strikes.append(strike)
            strike += increment
        
        return strikes
    
    async def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for underlying symbol"""
        try:
            if self.ibkr_service and hasattr(self.ibkr_service, 'get_current_price'):
                return await self.ibkr_service.get_current_price(symbol)
            else:
                # Fallback - would implement market data request
                return None
        except:
            return None
    
    async def _get_option_data(self, symbol: str, expiration: str, strike: float, option_type: str, exchange: str) -> Optional[Dict[str, Any]]:
        """Get market data for specific option contract"""
        try:
            if not self.ibkr_service or not self.ibkr_service.connected:
                logger.warning("IBKR service not available for option data - using estimated values")
                return self._get_estimated_option_data(symbol, expiration, strike, option_type)

            # Create option contract
            contract = await self.ibkr_service.create_contract(
                symbol=symbol,
                sec_type="OPT",
                exchange=exchange,
                expiry=expiration,
                strike=strike,
                right=option_type
            )

            # Request market data for the option
            try:
                market_data = await self.ibkr_service.get_market_data(symbol)
                if market_data.get("status") == "success" and "data" in market_data:
                    data = market_data["data"]

                    # Get real option market data
                    ticker = self.ibkr_service.ib.reqMktData(contract, snapshot=True)
                    await asyncio.sleep(1)  # Wait for data to populate

                    # Extract real market data
                    bid = getattr(ticker, 'bid', None)
                    ask = getattr(ticker, 'ask', None)
                    last = getattr(ticker, 'last', None)
                    volume = getattr(ticker, 'volume', None)

                    # Calculate Greeks if we have underlying price
                    underlying_price = data.get("last", 0)
                    greeks = {}
                    if underlying_price > 0:
                        greeks = await self._calculate_real_greeks(
                            underlying_price, strike, expiration, option_type
                        )

                    return {
                        "strike": strike,
                        "expiration": expiration,
                        "option_type": option_type,
                        "bid": bid if bid and bid > 0 else None,
                        "ask": ask if ask and ask > 0 else None,
                        "last": last if last and last > 0 else None,
                        "volume": volume if volume and volume > 0 else 0,
                        "open_interest": getattr(ticker, 'openInterest', 0),
                        "implied_volatility": getattr(ticker, 'impliedVolatility', None),
                        "delta": greeks.get("delta"),
                        "gamma": greeks.get("gamma"),
                        "theta": greeks.get("theta"),
                        "vega": greeks.get("vega"),
                        "data_source": "real_market_data"
                    }
                else:
                    logger.warning(f"Could not get underlying market data for {symbol}")
                    return self._get_estimated_option_data(symbol, expiration, strike, option_type)

            except Exception as e:
                logger.warning(f"Error getting real option data for {symbol} {strike} {option_type}: {e}")
                return self._get_estimated_option_data(symbol, expiration, strike, option_type)

        except Exception as e:
            logger.error(f"Error in _get_option_data: {e}")
            return self._get_estimated_option_data(symbol, expiration, strike, option_type)

    def _get_estimated_option_data(self, symbol: str, expiration: str, strike: float, option_type: str) -> Dict[str, Any]:
        """Get estimated option data when real market data is not available"""
        try:
            # Use more sophisticated estimation based on Black-Scholes
            current_price = 100.0  # Default fallback

            # Calculate time to expiration
            try:
                exp_date = datetime.strptime(expiration, "%Y%m%d")
                time_to_exp = (exp_date - datetime.now()).days / 365.0
                time_to_exp = max(time_to_exp, 0.001)  # Minimum time
            except:
                time_to_exp = 0.25  # Default 3 months

            # Estimate implied volatility based on moneyness
            moneyness = strike / current_price
            base_iv = 0.20
            if moneyness < 0.9 or moneyness > 1.1:
                base_iv = 0.25  # Higher IV for OTM options

            # Calculate theoretical price and Greeks
            risk_free_rate = 0.05  # 5% risk-free rate assumption
            dividend_yield = 0.02  # 2% dividend yield assumption

            theoretical_price = self._black_scholes_price(
                current_price, strike, time_to_exp, risk_free_rate, dividend_yield, base_iv, option_type
            )

            greeks = self._calculate_black_scholes_greeks(
                current_price, strike, time_to_exp, risk_free_rate, dividend_yield, base_iv, option_type
            )

            # Estimate bid-ask spread (typically 5-10% of option price)
            spread = max(theoretical_price * 0.05, 0.05)

            return {
                "strike": strike,
                "expiration": expiration,
                "option_type": option_type,
                "bid": max(theoretical_price - spread/2, 0.01),
                "ask": theoretical_price + spread/2,
                "last": theoretical_price,
                "volume": 50,  # Estimated volume
                "open_interest": 200,  # Estimated open interest
                "implied_volatility": base_iv,
                "delta": greeks.get("delta", 0),
                "gamma": greeks.get("gamma", 0),
                "theta": greeks.get("theta", 0),
                "vega": greeks.get("vega", 0),
                "data_source": "estimated_black_scholes"
            }
        except Exception as e:
            logger.error(f"Error in estimated option data: {e}")
            # Fallback to very basic estimation
            return {
                "strike": strike,
                "expiration": expiration,
                "option_type": option_type,
                "bid": strike * 0.05,
                "ask": strike * 0.06,
                "last": strike * 0.055,
                "volume": 100,
                "open_interest": 500,
                "implied_volatility": 0.25,
                "delta": 0.5 if option_type == "CALL" else -0.5,
                "gamma": 0.02,
                "theta": -0.05,
                "vega": 0.15,
                "data_source": "basic_estimation"
            }
    
    async def _calculate_real_greeks(self, underlying_price: float, strike: float, expiration: str, option_type: str) -> Dict[str, float]:
        """Calculate Greeks using real market data and Black-Scholes model"""
        try:
            # Calculate time to expiration
            exp_date = datetime.strptime(expiration, "%Y%m%d")
            time_to_exp = (exp_date - datetime.now()).days / 365.0
            time_to_exp = max(time_to_exp, 0.001)  # Minimum time

            # Use estimated implied volatility (could be enhanced with real IV data)
            implied_vol = await self._estimate_implied_volatility("", strike, expiration)

            # Standard assumptions
            risk_free_rate = 0.05  # 5% risk-free rate
            dividend_yield = 0.02  # 2% dividend yield

            # Calculate Greeks using Black-Scholes
            greeks = self._calculate_black_scholes_greeks(
                underlying_price, strike, time_to_exp, risk_free_rate, dividend_yield, implied_vol, option_type
            )

            return greeks

        except Exception as e:
            logger.error(f"Error calculating real Greeks: {e}")
            # Return default Greeks
            return {
                "delta": 0.5 if option_type == "CALL" else -0.5,
                "gamma": 0.02,
                "theta": -0.05,
                "vega": 0.15,
                "rho": 0.1
            }

    async def _get_implied_volatility(self, symbol: str, strike: float, expiration: str, option_type: str) -> float:
        """Get implied volatility for option (simplified)"""
        # This would normally calculate from market prices
        # For now, return a reasonable estimate
        return 0.25  # 25% IV
    
    async def _estimate_implied_volatility(self, symbol: str, strike: float, expiration: str) -> float:
        """Estimate implied volatility when market data not available"""
        # Simple estimation based on moneyness and time to expiration
        current_price = await self._get_current_price(symbol)
        if not current_price:
            return 0.25
        
        moneyness = strike / current_price
        base_iv = 0.20
        
        # Adjust for moneyness (volatility smile)
        if moneyness < 0.9 or moneyness > 1.1:
            base_iv += 0.05  # Higher IV for OTM options
        
        return base_iv
    
    def _calculate_black_scholes_greeks(self, S, K, T, r, q, sigma, option_type):
        """Calculate Greeks using Black-Scholes model"""
        d1 = (np.log(S/K) + (r - q + 0.5*sigma**2)*T) / (sigma*np.sqrt(T))
        d2 = d1 - sigma*np.sqrt(T)
        
        if option_type.upper() == "CALL":
            delta = np.exp(-q*T) * norm.cdf(d1)
            theta = (-S*np.exp(-q*T)*norm.pdf(d1)*sigma/(2*np.sqrt(T)) 
                    - r*K*np.exp(-r*T)*norm.cdf(d2) 
                    + q*S*np.exp(-q*T)*norm.cdf(d1)) / 365
            rho = K*T*np.exp(-r*T)*norm.cdf(d2) / 100
        else:  # PUT
            delta = -np.exp(-q*T) * norm.cdf(-d1)
            theta = (-S*np.exp(-q*T)*norm.pdf(d1)*sigma/(2*np.sqrt(T)) 
                    + r*K*np.exp(-r*T)*norm.cdf(-d2) 
                    - q*S*np.exp(-q*T)*norm.cdf(-d1)) / 365
            rho = -K*T*np.exp(-r*T)*norm.cdf(-d2) / 100
        
        gamma = np.exp(-q*T)*norm.pdf(d1)/(S*sigma*np.sqrt(T))
        vega = S*np.exp(-q*T)*norm.pdf(d1)*np.sqrt(T) / 100
        
        return {
            "delta": float(delta),
            "gamma": float(gamma),
            "theta": float(theta),
            "vega": float(vega),
            "rho": float(rho)
        }
    
    def _black_scholes_price(self, S, K, T, r, q, sigma, option_type):
        """Calculate theoretical option price using Black-Scholes"""
        d1 = (np.log(S/K) + (r - q + 0.5*sigma**2)*T) / (sigma*np.sqrt(T))
        d2 = d1 - sigma*np.sqrt(T)
        
        if option_type.upper() == "CALL":
            price = S*np.exp(-q*T)*norm.cdf(d1) - K*np.exp(-r*T)*norm.cdf(d2)
        else:  # PUT
            price = K*np.exp(-r*T)*norm.cdf(-d2) - S*np.exp(-q*T)*norm.cdf(-d1)
        
        return float(price)
    
    async def _analyze_option_leg(self, symbol: str, leg: Dict[str, Any], current_price: float, price_points: np.ndarray) -> Dict[str, Any]:
        """Analyze individual option leg"""
        # Calculate Greeks and P&L for the leg
        greeks = await self.calculate_greeks(
            symbol, leg["strike"], leg["expiration"], leg["option_type"], current_price
        )
        
        # Calculate P&L curve
        pnl_curve = []
        for price in price_points:
            if leg["option_type"].upper() == "CALL":
                intrinsic = max(0, price - leg["strike"])
            else:  # PUT
                intrinsic = max(0, leg["strike"] - price)
            
            # Simplified P&L (would need actual option prices)
            pnl = intrinsic - leg.get("premium", leg["strike"] * 0.05)
            pnl_curve.append(pnl)
        
        return {
            "leg": leg,
            "greeks": greeks.get("greeks", {}),
            "premium": leg.get("premium", leg["strike"] * 0.05),
            "pnl_curve": pnl_curve,
            "delta": greeks.get("greeks", {}).get("delta", 0),
            "gamma": greeks.get("greeks", {}).get("gamma", 0),
            "theta": greeks.get("greeks", {}).get("theta", 0),
            "vega": greeks.get("greeks", {}).get("vega", 0)
        }
    
    def _find_breakeven_points(self, pnl_curve: List[Dict[str, float]]) -> List[float]:
        """Find breakeven points in P&L curve"""
        breakevens = []
        for i in range(len(pnl_curve) - 1):
            pnl1 = pnl_curve[i]["pnl"]
            pnl2 = pnl_curve[i + 1]["pnl"]
            
            # Check for sign change (crossing zero)
            if (pnl1 <= 0 <= pnl2) or (pnl2 <= 0 <= pnl1):
                # Linear interpolation to find exact breakeven
                price1 = pnl_curve[i]["price"]
                price2 = pnl_curve[i + 1]["price"]
                breakeven = price1 + (price2 - price1) * (-pnl1) / (pnl2 - pnl1)
                breakevens.append(breakeven)
        
        return breakevens
    
    def _calculate_current_leg_pnl(self, leg_analysis: Dict[str, Any], current_price: float) -> float:
        """Calculate current P&L for option leg"""
        leg = leg_analysis["leg"]
        if leg["option_type"].upper() == "CALL":
            intrinsic = max(0, current_price - leg["strike"])
        else:
            intrinsic = max(0, leg["strike"] - current_price)
        
        return intrinsic - leg_analysis["premium"]
    
    def _assess_strategy_risk(self, strategy_analysis: Dict[str, Any], strategy_type: str) -> Dict[str, Any]:
        """Assess risk characteristics of options strategy"""
        max_profit = strategy_analysis["max_profit"]
        max_loss = strategy_analysis["max_loss"]
        
        # Calculate risk metrics
        if max_loss != 0:
            reward_risk_ratio = max_profit / abs(max_loss)
        else:
            reward_risk_ratio = float('inf')
        
        # Determine risk level
        if abs(max_loss) > abs(max_profit) * 3:
            risk_level = "HIGH"
        elif abs(max_loss) > abs(max_profit):
            risk_level = "MEDIUM"
        else:
            risk_level = "LOW"
        
        return {
            "risk_level": risk_level,
            "max_profit": max_profit,
            "max_loss": max_loss,
            "reward_risk_ratio": reward_risk_ratio,
            "capital_requirement": abs(max_loss),
            "probability_analysis": self._estimate_probability_of_profit(strategy_analysis),
            "time_decay_risk": abs(strategy_analysis["net_theta"]),
            "volatility_risk": abs(strategy_analysis["net_vega"])
        }
    
    def _estimate_probability_of_profit(self, strategy_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate probability of profit for strategy"""
        pnl_curve = strategy_analysis["pnl_curve"]
        profitable_points = sum(1 for point in pnl_curve if point["pnl"] > 0)
        total_points = len(pnl_curve)
        
        if total_points > 0:
            prob_profit = profitable_points / total_points
        else:
            prob_profit = 0.0
        
        return {
            "probability_of_profit": prob_profit,
            "profitable_price_range": len([p for p in pnl_curve if p["pnl"] > 0]),
            "total_price_points": total_points
        }
    
    def _create_iron_condor(self, current_price: float, params: Dict[str, Any], chain_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create Iron Condor strategy legs"""
        # Iron Condor: Sell Call Spread + Sell Put Spread
        wing_width = params.get("wing_width", 10)
        expiration = params.get("expiration", list(chain_data["options_chain"].keys())[0])
        
        # Calculate strikes
        call_short_strike = current_price + 5
        call_long_strike = call_short_strike + wing_width
        put_short_strike = current_price - 5
        put_long_strike = put_short_strike - wing_width
        
        return [
            {"strike": put_long_strike, "expiration": expiration, "option_type": "PUT", "action": "BUY", "quantity": 1},
            {"strike": put_short_strike, "expiration": expiration, "option_type": "PUT", "action": "SELL", "quantity": 1},
            {"strike": call_short_strike, "expiration": expiration, "option_type": "CALL", "action": "SELL", "quantity": 1},
            {"strike": call_long_strike, "expiration": expiration, "option_type": "CALL", "action": "BUY", "quantity": 1}
        ]
    
    def _create_butterfly(self, current_price: float, params: Dict[str, Any], chain_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create Butterfly strategy legs"""
        wing_width = params.get("wing_width", 5)
        expiration = params.get("expiration", list(chain_data["options_chain"].keys())[0])
        option_type = params.get("option_type", "CALL")
        
        # Calculate strikes
        center_strike = current_price
        lower_strike = center_strike - wing_width
        upper_strike = center_strike + wing_width
        
        return [
            {"strike": lower_strike, "expiration": expiration, "option_type": option_type, "action": "BUY", "quantity": 1},
            {"strike": center_strike, "expiration": expiration, "option_type": option_type, "action": "SELL", "quantity": 2},
            {"strike": upper_strike, "expiration": expiration, "option_type": option_type, "action": "BUY", "quantity": 1}
        ]
    
    def _create_straddle(self, current_price: float, params: Dict[str, Any], chain_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create Straddle strategy legs"""
        expiration = params.get("expiration", list(chain_data["options_chain"].keys())[0])
        strike = params.get("strike", current_price)
        action = params.get("action", "BUY")  # Long or Short straddle
        
        return [
            {"strike": strike, "expiration": expiration, "option_type": "CALL", "action": action, "quantity": 1},
            {"strike": strike, "expiration": expiration, "option_type": "PUT", "action": action, "quantity": 1}
        ]
    
    def _create_strangle(self, current_price: float, params: Dict[str, Any], chain_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create Strangle strategy legs"""
        expiration = params.get("expiration", list(chain_data["options_chain"].keys())[0])
        call_strike = params.get("call_strike", current_price + 5)
        put_strike = params.get("put_strike", current_price - 5)
        action = params.get("action", "BUY")
        
        return [
            {"strike": call_strike, "expiration": expiration, "option_type": "CALL", "action": action, "quantity": 1},
            {"strike": put_strike, "expiration": expiration, "option_type": "PUT", "action": action, "quantity": 1}
        ]
    
    def _create_covered_call(self, current_price: float, params: Dict[str, Any], chain_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create Covered Call strategy legs"""
        expiration = params.get("expiration", list(chain_data["options_chain"].keys())[0])
        call_strike = params.get("call_strike", current_price + 5)
        
        return [
            {"strike": 0, "expiration": expiration, "option_type": "STOCK", "action": "BUY", "quantity": 100},  # Own stock
            {"strike": call_strike, "expiration": expiration, "option_type": "CALL", "action": "SELL", "quantity": 1}
        ]

