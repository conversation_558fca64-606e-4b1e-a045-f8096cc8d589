"""
News Implementation - Layer 3: Business Logic
Core business logic for News operations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional, List, Callable
import logging
import asyncio
import time
import re
from datetime import datetime, timezone, timedelta
from ibapi.contract import Contract

logger = logging.getLogger(__name__)

class NewsImplementation:
    """Core implementation for News operations"""
    
    def __init__(self, ibkr_service=None, **kwargs):
        self.ibkr_service = ibkr_service
        self.services = kwargs
        self.active_subscriptions = {}  # Track active news subscriptions
        self.news_cache = {}  # Cache recent news for analysis
        self.news_providers = [
            "BRFG", "DJNL", "BRFUPDN", "FOXBU", "DJNLT", "BEN<PERSON>INGA",
            "BZ", "FLY", "MT_NEWSWIRES", "STREETINSIDER"
        ]
        
        if not self.ibkr_service:
            logger.warning("NewsImplementation initialized without IBKR service")
    
    async def request_news_bulletins(
        self,
        all_msgs: bool = True,
        provider: Optional[str] = None,
        callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Request real-time news bulletins streaming.
        
        Args:
            all_msgs: Whether to receive all messages or just new ones
            provider: Specific news provider (optional)
            callback: Optional callback function for real-time updates
            
        Returns:
            Dict containing subscription details and initial setup
        """
        try:
            if not self.ibkr_service:
                return {
                    "error": "IBKR service not available",
                    "success": False
                }
            
            # Generate unique subscription ID
            subscription_id = f"news_bulletins_{int(time.time() * 1000) % 100000}"
            
            subscription_info = {
                "subscription_id": subscription_id,
                "type": "news_bulletins",
                "all_msgs": all_msgs,
                "provider": provider,
                "start_time": datetime.now(timezone.utc),
                "callback": callback,
                "message_count": 0,
                "status": "active"
            }
            
            self.active_subscriptions[subscription_id] = subscription_info
            
            # Start news bulletins request
            if hasattr(self.ibkr_service, 'request_news_bulletins'):
                try:
                    await self.ibkr_service.request_news_bulletins(all_msgs)
                except Exception as e:
                    logger.error(f"Failed to start news bulletins: {e}")
                    del self.active_subscriptions[subscription_id]
                    return {
                        "error": f"Failed to start news bulletins: {str(e)}",
                        "success": False
                    }
            
            return {
                "success": True,
                "subscription_id": subscription_id,
                "all_msgs": all_msgs,
                "provider": provider,
                "message": "News bulletins streaming started",
                "available_providers": self.news_providers
            }
            
        except Exception as e:
            logger.error(f"Error requesting news bulletins: {e}")
            return {
                "error": f"Failed to request news bulletins: {str(e)}",
                "success": False
            }
    
    async def cancel_news_bulletins(self, subscription_id: str) -> Dict[str, Any]:
        """
        Cancel news bulletins subscription.
        
        Args:
            subscription_id: ID of subscription to cancel
            
        Returns:
            Dict containing cancellation status and details
        """
        try:
            if subscription_id not in self.active_subscriptions:
                return {
                    "error": f"Subscription {subscription_id} not found",
                    "success": False
                }
            
            subscription = self.active_subscriptions[subscription_id]
            
            # Cancel the subscription with IBKR
            if self.ibkr_service and hasattr(self.ibkr_service, 'cancel_news_bulletins'):
                try:
                    await self.ibkr_service.cancel_news_bulletins()
                except Exception as e:
                    logger.error(f"Failed to cancel news bulletins: {e}")
            
            # Update subscription status
            subscription["status"] = "cancelled"
            subscription["end_time"] = datetime.now(timezone.utc)
            
            # Calculate statistics
            duration = (subscription["end_time"] - subscription["start_time"]).total_seconds()
            message_count = subscription.get("message_count", 0)
            
            # Remove from active subscriptions
            cancelled_info = self.active_subscriptions.pop(subscription_id)
            
            return {
                "success": True,
                "subscription_id": subscription_id,
                "duration_seconds": duration,
                "total_messages_received": message_count,
                "message": "News bulletins subscription cancelled"
            }
            
        except Exception as e:
            logger.error(f"Error cancelling news bulletins subscription {subscription_id}: {e}")
            return {
                "error": f"Failed to cancel subscription: {str(e)}",
                "success": False
            }
    
    async def request_historical_news(
        self,
        conid: int,
        provider_codes: str = "BRFG+DJNL",
        start_date_time: str = "",
        end_date_time: str = "",
        total_results: int = 10
    ) -> Dict[str, Any]:
        """
        Request historical news articles for a contract.
        
        Args:
            conid: Contract ID for the instrument
            provider_codes: News provider codes (e.g., "BRFG+DJNL")
            start_date_time: Start date in format "YYYYMMDD-HH:mm:ss"
            end_date_time: End date in format "YYYYMMDD-HH:mm:ss"
            total_results: Maximum number of articles to retrieve
            
        Returns:
            Dict containing historical news articles
        """
        try:
            if not self.ibkr_service:
                return {
                    "error": "IBKR service not available",
                    "success": False
                }
            
            # Generate unique request ID
            req_id = int(time.time() * 1000) % 100000
            
            # Set default date range if not provided
            if not end_date_time:
                end_date_time = datetime.now(timezone.utc).strftime("%Y%m%d-%H:%M:%S")
            
            if not start_date_time:
                start_time = datetime.now(timezone.utc) - timedelta(days=7)
                start_date_time = start_time.strftime("%Y%m%d-%H:%M:%S")
            
            if hasattr(self.ibkr_service, 'request_historical_news'):
                try:
                    result = await self.ibkr_service.request_historical_news(
                        req_id=req_id,
                        conid=conid,
                        provider_codes=provider_codes,
                        start_date_time=start_date_time,
                        end_date_time=end_date_time,
                        total_results=total_results
                    )
                    
                    if result and "news_articles" in result:
                        articles = result["news_articles"]
                        
                        # Perform basic analysis on articles
                        analysis = self._analyze_news_articles(articles)
                        
                        return {
                            "success": True,
                            "conid": conid,
                            "provider_codes": provider_codes,
                            "start_date": start_date_time,
                            "end_date": end_date_time,
                            "total_results": total_results,
                            "article_count": len(articles),
                            "articles": articles,
                            "analysis": analysis,
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        }
                    else:
                        return {
                            "error": "No historical news data received",
                            "success": False
                        }
                        
                except Exception as e:
                    logger.error(f"Failed to get historical news for conid {conid}: {e}")
                    return {
                        "error": f"Failed to get historical news: {str(e)}",
                        "success": False
                    }
            else:
                return {
                    "error": "Historical news method not available",
                    "success": False
                }
                
        except Exception as e:
            logger.error(f"Error getting historical news for conid {conid}: {e}")
            return {
                "error": f"Failed to get historical news: {str(e)}",
                "success": False
            }
    
    async def request_news_article(self, article_id: str, provider_code: str) -> Dict[str, Any]:
        """
        Request full content of a specific news article.
        
        Args:
            article_id: Unique identifier for the news article
            provider_code: Provider code for the article
            
        Returns:
            Dict containing full article content and metadata
        """
        try:
            if not self.ibkr_service:
                return {
                    "error": "IBKR service not available",
                    "success": False
                }
            
            # Generate unique request ID
            req_id = int(time.time() * 1000) % 100000
            
            if hasattr(self.ibkr_service, 'request_news_article'):
                try:
                    result = await self.ibkr_service.request_news_article(
                        req_id=req_id,
                        provider_code=provider_code,
                        article_id=article_id
                    )
                    
                    if result:
                        # Perform sentiment analysis on article content
                        content = result.get("article_text", "")
                        sentiment = self._analyze_sentiment(content)
                        
                        return {
                            "success": True,
                            "article_id": article_id,
                            "provider_code": provider_code,
                            "article": result,
                            "sentiment_analysis": sentiment,
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        }
                    else:
                        return {
                            "error": "No article content received",
                            "success": False
                        }
                        
                except Exception as e:
                    logger.error(f"Failed to get news article {article_id}: {e}")
                    return {
                        "error": f"Failed to get news article: {str(e)}",
                        "success": False
                    }
            else:
                return {
                    "error": "News article method not available",
                    "success": False
                }
                
        except Exception as e:
            logger.error(f"Error getting news article {article_id}: {e}")
            return {
                "error": f"Failed to get news article: {str(e)}",
                "success": False
            }
    
    async def get_news_providers(self) -> Dict[str, Any]:
        """
        Get list of available news providers and their details.
        
        Returns:
            Dict containing available news providers and their information
        """
        try:
            provider_details = {
                "BRFG": {
                    "name": "Briefing.com",
                    "type": "Financial News",
                    "description": "Real-time financial news and market analysis"
                },
                "DJNL": {
                    "name": "Dow Jones News",
                    "type": "Financial News",
                    "description": "Professional financial news service"
                },
                "BRFUPDN": {
                    "name": "Briefing Upgrades/Downgrades",
                    "type": "Analyst Ratings",
                    "description": "Analyst upgrades and downgrades"
                },
                "FOXBU": {
                    "name": "Fox Business",
                    "type": "Business News",
                    "description": "Business and financial news"
                },
                "DJNLT": {
                    "name": "Dow Jones News (Live Trading)",
                    "type": "Trading News",
                    "description": "Live trading floor news"
                },
                "BENZINGA": {
                    "name": "Benzinga",
                    "type": "Trading News",
                    "description": "Real-time trading news and insights"
                },
                "BZ": {
                    "name": "Benzinga Pro",
                    "type": "Professional Trading",
                    "description": "Professional trading news service"
                },
                "FLY": {
                    "name": "The Fly",
                    "type": "Market Intelligence",
                    "description": "Market intelligence and breaking news"
                },
                "MT_NEWSWIRES": {
                    "name": "MT Newswires",
                    "type": "Financial News",
                    "description": "Financial and economic news"
                },
                "STREETINSIDER": {
                    "name": "StreetInsider",
                    "type": "Market News",
                    "description": "Market news and analysis"
                }
            }
            
            return {
                "success": True,
                "providers": provider_details,
                "total_providers": len(provider_details),
                "recommended_combinations": [
                    "BRFG+DJNL",
                    "BENZINGA+FLY",
                    "BRFUPDN+DJNL"
                ],
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting news providers: {e}")
            return {
                "error": f"Failed to get news providers: {str(e)}",
                "success": False
            }
    
    async def get_active_subscriptions(self) -> Dict[str, Any]:
        """
        Get all active news subscriptions.
        
        Returns:
            Dict containing active subscription details
        """
        try:
            current_time = datetime.now(timezone.utc)
            subscription_list = []
            
            for sub_id, sub_info in self.active_subscriptions.items():
                duration = (current_time - sub_info["start_time"]).total_seconds()
                
                subscription_list.append({
                    "subscription_id": sub_id,
                    "type": sub_info["type"],
                    "provider": sub_info.get("provider"),
                    "all_msgs": sub_info.get("all_msgs"),
                    "start_time": sub_info["start_time"].isoformat(),
                    "duration_seconds": duration,
                    "message_count": sub_info.get("message_count", 0),
                    "status": sub_info["status"]
                })
            
            return {
                "success": True,
                "active_subscriptions": subscription_list,
                "total_active": len(subscription_list),
                "timestamp": current_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting active subscriptions: {e}")
            return {
                "error": f"Failed to get active subscriptions: {str(e)}",
                "success": False
            }
    
    def _analyze_news_articles(self, articles: List[Dict]) -> Dict[str, Any]:
        """
        Perform basic analysis on a collection of news articles.
        
        Args:
            articles: List of news articles
            
        Returns:
            Dict containing analysis results
        """
        try:
            if not articles:
                return {"total_articles": 0}
            
            # Analyze by provider
            provider_counts = {}
            sentiment_scores = []
            recent_articles = 0
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
            
            for article in articles:
                # Count by provider
                provider = article.get("provider_code", "unknown")
                provider_counts[provider] = provider_counts.get(provider, 0) + 1
                
                # Count recent articles
                article_time_str = article.get("date_time", "")
                try:
                    if article_time_str:
                        # Parse various date formats
                        article_time = datetime.fromisoformat(article_time_str.replace("Z", "+00:00"))
                        if article_time >= cutoff_time:
                            recent_articles += 1
                except:
                    pass
                
                # Basic sentiment analysis
                headline = article.get("headline", "")
                sentiment = self._analyze_sentiment(headline)
                if sentiment.get("score"):
                    sentiment_scores.append(sentiment["score"])
            
            # Calculate average sentiment
            avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0
            sentiment_label = (
                "positive" if avg_sentiment > 0.1 else
                "negative" if avg_sentiment < -0.1 else
                "neutral"
            )
            
            return {
                "total_articles": len(articles),
                "recent_articles_24h": recent_articles,
                "provider_distribution": provider_counts,
                "sentiment": {
                    "average_score": avg_sentiment,
                    "label": sentiment_label,
                    "analyzed_articles": len(sentiment_scores)
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing news articles: {e}")
            return {"error": str(e), "total_articles": len(articles) if articles else 0}
    
    def _analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """
        Perform basic sentiment analysis on text content.
        
        Args:
            text: Text content to analyze
            
        Returns:
            Dict containing sentiment analysis results
        """
        try:
            if not text:
                return {"score": 0, "label": "neutral", "confidence": 0}
            
            # Simple keyword-based sentiment analysis
            positive_words = [
                "bullish", "positive", "gains", "growth", "increased", "higher", 
                "upgraded", "beat", "strong", "robust", "outperform", "buy",
                "surge", "rally", "boost", "optimistic", "favorable", "improve"
            ]
            
            negative_words = [
                "bearish", "negative", "losses", "decline", "decreased", "lower",
                "downgraded", "miss", "weak", "poor", "underperform", "sell", 
                "plunge", "crash", "drop", "pessimistic", "unfavorable", "worsen"
            ]
            
            text_lower = text.lower()
            positive_count = sum(1 for word in positive_words if word in text_lower)
            negative_count = sum(1 for word in negative_words if word in text_lower)
            
            # Calculate sentiment score (-1 to 1)
            total_sentiment_words = positive_count + negative_count
            if total_sentiment_words == 0:
                score = 0
                confidence = 0
            else:
                score = (positive_count - negative_count) / total_sentiment_words
                confidence = min(total_sentiment_words / 10, 1.0)  # Confidence based on word count
            
            # Determine label
            if score > 0.2:
                label = "positive"
            elif score < -0.2:
                label = "negative"
            else:
                label = "neutral"
            
            return {
                "score": score,
                "label": label,
                "confidence": confidence,
                "positive_words_found": positive_count,
                "negative_words_found": negative_count
            }
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
            return {"score": 0, "label": "neutral", "confidence": 0, "error": str(e)}

