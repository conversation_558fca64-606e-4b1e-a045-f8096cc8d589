"""
Account Implementation - Layer 3: Implementation
Handles actual account and portfolio operations with IBKR service.
Generated by ArchitectureGenerator.
"""

import logging
from typing import Dict, Any, Optional
from ..base_implementation import BaseImplementation

logger = logging.getLogger('implementations.account.account_impl')

class AccountImplementation(BaseImplementation):
    """Implementation for account and portfolio operations"""
    
    def __init__(self, ibkr_service=None):
        super().__init__()
        self.ibkr_service = ibkr_service

        # CRITICAL FIX: If no service provided, try to get it from the global instance
        if not self.ibkr_service:
            try:
                from services.ibkr_service import ibkr_service as global_ibkr_service
                self.ibkr_service = global_ibkr_service
                logger.info("AccountImplementation: Using global IBKR service instance")
            except ImportError:
                logger.warning("AccountImplementation initialized without IBKR service")
    
    async def get_account_summary(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get comprehensive account summary"""
        # EMERGENCY FIX: Try to get global service if not available
        if not self.ibkr_service:
            try:
                from services.ibkr_service import ibkr_service as global_ibkr_service
                self.ibkr_service = global_ibkr_service
                logger.info("AccountImplementation: Emergency injection of global IBKR service")
            except ImportError:
                pass

        if not self.ibkr_service:
            return {"status": "error", "message": "IBKR service not initialized"}
        
        try:
            # Get account summary from IBKR service
            if hasattr(self.ibkr_service, 'get_account_summary'):
                return await self.ibkr_service.get_account_summary(tags=None)
            
            # Fallback: construct summary from available methods
            summary = {}
            
            # Get account values
            if hasattr(self.ibkr_service, 'ib') and self.ibkr_service.ib:
                account_values = self.ibkr_service.ib.accountSummary()
                if account_values:
                    summary['account_values'] = [
                        {
                            'tag': av.tag,
                            'value': av.value,
                            'currency': av.currency,
                            'account': av.account
                        } for av in account_values
                    ]
                
                # Get portfolio positions
                portfolio = self.ibkr_service.ib.portfolio()
                if portfolio:
                    summary['positions'] = [
                        {
                            'symbol': pos.contract.symbol,
                            'position': pos.position,
                            'market_price': pos.marketPrice,
                            'market_value': pos.marketValue,
                            'average_cost': pos.averageCost,
                            'unrealized_pnl': pos.unrealizedPNL,
                            'realized_pnl': pos.realizedPNL
                        } for pos in portfolio
                    ]
            
            return {
                "status": "success",
                "account": account or "default",
                "summary": summary
            }
            
        except Exception as e:
            logger.error(f"Error getting account summary: {e}")
            return {"status": "error", "message": str(e)}
    
    async def get_portfolio_positions(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get current portfolio positions"""
        if not self.ibkr_service:
            return {"status": "error", "message": "IBKR service not initialized"}
        
        try:
            if hasattr(self.ibkr_service, 'ib') and self.ibkr_service.ib:
                portfolio = self.ibkr_service.ib.portfolio()
                positions = []
                
                for pos in portfolio:
                    position_data = {
                        'symbol': pos.contract.symbol,
                        'sec_type': pos.contract.secType,
                        'exchange': pos.contract.exchange,
                        'currency': pos.contract.currency,
                        'position': pos.position,
                        'market_price': pos.marketPrice,
                        'market_value': pos.marketValue,
                        'average_cost': pos.averageCost,
                        'unrealized_pnl': pos.unrealizedPNL,
                        'realized_pnl': pos.realizedPNL,
                        'account': pos.account
                    }
                    positions.append(position_data)
                
                return {
                    "status": "success",
                    "account": account or "default",
                    "positions": positions,
                    "total_positions": len(positions)
                }
            
            return {"status": "error", "message": "No portfolio data available"}
            
        except Exception as e:
            logger.error(f"Error getting portfolio positions: {e}")
            return {"status": "error", "message": str(e)}
    
    async def get_account_balance(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get account cash balance"""
        if not self.ibkr_service:
            return {"status": "error", "message": "IBKR service not initialized"}
        
        try:
            if hasattr(self.ibkr_service, 'ib') and self.ibkr_service.ib:
                account_values = self.ibkr_service.ib.accountSummary()
                balance_info = {}
                
                for av in account_values:
                    if av.tag in ['TotalCashValue', 'CashBalance', 'AvailableFunds', 'NetLiquidation']:
                        balance_info[av.tag] = {
                            'value': float(av.value) if av.value else 0.0,
                            'currency': av.currency
                        }
                
                return {
                    "status": "success",
                    "account": account or "default",
                    "balance_info": balance_info
                }
            
            return {"status": "error", "message": "No account data available"}
            
        except Exception as e:
            logger.error(f"Error getting account balance: {e}")
            return {"status": "error", "message": str(e)}
    
    async def get_buying_power(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get available buying power"""
        if not self.ibkr_service:
            return {"status": "error", "message": "IBKR service not initialized"}
        
        try:
            if hasattr(self.ibkr_service, 'ib') and self.ibkr_service.ib:
                account_values = self.ibkr_service.ib.accountSummary()
                buying_power_info = {}
                
                for av in account_values:
                    if av.tag in ['BuyingPower', 'AvailableFunds', 'ExcessLiquidity']:
                        buying_power_info[av.tag] = {
                            'value': float(av.value) if av.value else 0.0,
                            'currency': av.currency
                        }
                
                return {
                    "status": "success",
                    "account": account or "default",
                    "buying_power": buying_power_info
                }
            
            return {"status": "error", "message": "No account data available"}
            
        except Exception as e:
            logger.error(f"Error getting buying power: {e}")
            return {"status": "error", "message": str(e)}
    
    async def get_pnl(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get profit and loss information"""
        if not self.ibkr_service:
            return {"status": "error", "message": "IBKR service not initialized"}
        
        try:
            if hasattr(self.ibkr_service, 'ib') and self.ibkr_service.ib:
                # Get P&L from portfolio positions
                portfolio = self.ibkr_service.ib.portfolio()
                total_unrealized_pnl = 0.0
                total_realized_pnl = 0.0
                position_pnl = []
                
                for pos in portfolio:
                    unrealized = float(pos.unrealizedPNL) if pos.unrealizedPNL else 0.0
                    realized = float(pos.realizedPNL) if pos.realizedPNL else 0.0
                    
                    total_unrealized_pnl += unrealized
                    total_realized_pnl += realized
                    
                    position_pnl.append({
                        'symbol': pos.contract.symbol,
                        'unrealized_pnl': unrealized,
                        'realized_pnl': realized,
                        'position': pos.position,
                        'market_value': pos.marketValue
                    })
                
                # Get account-level P&L from account summary
                account_values = self.ibkr_service.ib.accountSummary()
                account_pnl = {}
                
                for av in account_values:
                    if av.tag in ['UnrealizedPnL', 'RealizedPnL', 'DayTradesRemaining']:
                        account_pnl[av.tag] = {
                            'value': float(av.value) if av.value else 0.0,
                            'currency': av.currency
                        }
                
                return {
                    "status": "success",
                    "account": account or "default",
                    "total_unrealized_pnl": total_unrealized_pnl,
                    "total_realized_pnl": total_realized_pnl,
                    "position_pnl": position_pnl,
                    "account_pnl": account_pnl
                }
            
            return {"status": "error", "message": "No P&L data available"}
            
        except Exception as e:
            logger.error(f"Error getting P&L: {e}")
            return {"status": "error", "message": str(e)}

    async def get_account_info(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get detailed account information"""
        if not self.ibkr_service:
            return {"status": "error", "message": "IBKR service not initialized"}

        try:
            if hasattr(self.ibkr_service, 'ib') and self.ibkr_service.ib:
                # Get managed accounts
                managed_accounts = self.ibkr_service.ib.managedAccounts()

                # Get account summary
                account_values = self.ibkr_service.ib.accountSummary()
                account_info = {}

                for av in account_values:
                    account_info[av.tag] = {
                        'value': av.value,
                        'currency': av.currency,
                        'account': av.account
                    }

                return {
                    "status": "success",
                    "managed_accounts": managed_accounts,
                    "account_info": account_info,
                    "current_account": account or (managed_accounts[0] if managed_accounts else "default")
                }

            return {"status": "error", "message": "No account info available"}

        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return {"status": "error", "message": str(e)}

    async def get_margin_info(self, account: Optional[str] = None) -> Dict[str, Any]:
        """Get margin requirements and information"""
        if not self.ibkr_service:
            return {"status": "error", "message": "IBKR service not initialized"}

        try:
            if hasattr(self.ibkr_service, 'ib') and self.ibkr_service.ib:
                account_values = self.ibkr_service.ib.accountSummary()
                margin_info = {}

                margin_tags = [
                    'InitMarginReq', 'MaintMarginReq', 'ExcessLiquidity',
                    'Cushion', 'LookAheadInitMarginReq', 'LookAheadMaintMarginReq'
                ]

                for av in account_values:
                    if av.tag in margin_tags:
                        margin_info[av.tag] = {
                            'value': float(av.value) if av.value else 0.0,
                            'currency': av.currency
                        }

                return {
                    "status": "success",
                    "account": account or "default",
                    "margin_info": margin_info
                }

            return {"status": "error", "message": "No margin info available"}

        except Exception as e:
            logger.error(f"Error getting margin info: {e}")
            return {"status": "error", "message": str(e)}

    async def get_cash_balance(self, account: Optional[str] = None, currency: str = "USD") -> Dict[str, Any]:
        """Get cash balance for specific currency"""
        if not self.ibkr_service:
            return {"status": "error", "message": "IBKR service not initialized"}

        try:
            if hasattr(self.ibkr_service, 'ib') and self.ibkr_service.ib:
                account_values = self.ibkr_service.ib.accountSummary()
                cash_balances = {}

                for av in account_values:
                    if av.tag in ['CashBalance', 'TotalCashValue'] and av.currency == currency:
                        cash_balances[av.tag] = {
                            'value': float(av.value) if av.value else 0.0,
                            'currency': av.currency
                        }

                return {
                    "status": "success",
                    "account": account or "default",
                    "currency": currency,
                    "cash_balances": cash_balances
                }

            return {"status": "error", "message": "No cash balance data available"}

        except Exception as e:
            logger.error(f"Error getting cash balance: {e}")
            return {"status": "error", "message": str(e)}
