"""
RiskManagement Implementation - Layer 3: Business Logic
Core business logic for RiskManagement operations.
Provides comprehensive risk analysis, portfolio risk assessment, and risk monitoring.
"""

from typing import Dict, Any, Optional, List
import logging
import numpy as np
from datetime import datetime
import math
from statistics import mean, stdev

logger = logging.getLogger(__name__)

# Import safety infrastructure
try:
    from safety.safety_manager import SafetyManager, SafetyManagerConfig  # type: ignore
    from safety.circuit_breaker import CircuitBreakerManager  # type: ignore
    from safety.anomaly_detector import AnomalyDetector, AnomalyDetectorConfig  # type: ignore
    from safety.correlation_monitor import CorrelationMonitor, CorrelationMonitorConfig  # type: ignore
    from safety.system_monitor import SystemMonitor, SystemMonitorConfig  # type: ignore
    from safety.liquidation_manager import LiquidationManager, LiquidationConfig  # type: ignore
    SAFETY_AVAILABLE = True
except ImportError:
    try:
        from ...safety.safety_manager import SafetyManager, SafetyManagerConfig  # type: ignore
        from ...safety.circuit_breaker import CircuitBreakerManager  # type: ignore
        from ...safety.anomaly_detector import AnomalyDetector, AnomalyDetectorConfig  # type: ignore
        from ...safety.correlation_monitor import CorrelationMonitor, CorrelationMonitorConfig  # type: ignore
        from ...safety.system_monitor import SystemMonitor, SystemMonitorConfig  # type: ignore
        from ...safety.liquidation_manager import LiquidationManager, LiquidationConfig  # type: ignore
        SAFETY_AVAILABLE = True
    except ImportError as e:
        logger.warning(f"Safety infrastructure not available: {e}")
        SAFETY_AVAILABLE = False

        # Create dummy classes to prevent errors
        class Level:
            def __init__(self):
                self.value = 'safe'

        class SafetyStatus:
            def __init__(self):
                self.level = Level()
                self.active_alerts = []
                self.description = "Safety system unavailable"
                self.recommendations = []

        class SafetyManager:
            def __init__(self, *args, **kwargs): pass  # noqa: ARG002
            def add_safety_callback(self, *args): pass  # noqa: ARG002
            def get_current_status(self): return SafetyStatus()
            def get_statistics(self): return {}
            async def start_monitoring(self): pass
            async def stop_monitoring(self): pass
            async def emergency_stop(self, reason): pass  # noqa: ARG002
            @property
            def circuit_breaker_manager(self): return None

        class SafetyManagerConfig:
            def __init__(self, **kwargs): pass

        class CircuitBreakerManager:
            def __init__(self, *args, **kwargs): pass

        class AnomalyDetector:
            def __init__(self, *args, **kwargs): pass

        class AnomalyDetectorConfig:
            def __init__(self, **kwargs): pass

        class CorrelationMonitor:
            def __init__(self, *args, **kwargs): pass

        class CorrelationMonitorConfig:
            def __init__(self, **kwargs): pass

        class SystemMonitor:
            def __init__(self, *args, **kwargs): pass

        class SystemMonitorConfig:
            def __init__(self, **kwargs): pass

        class LiquidationManager:
            def __init__(self, *args, **kwargs): pass

        class LiquidationConfig:
            def __init__(self, **kwargs): pass

class RiskManagementImplementation:
    """Core implementation for RiskManagement operations with integrated safety features"""

    def __init__(self, ibkr_service=None, **kwargs):
        self.ibkr_service = ibkr_service
        self.services = kwargs

        # Risk calculation parameters
        self.default_confidence_level = 0.95
        self.default_time_horizon = 1  # 1 day
        self.market_risk_free_rate = 0.05  # 5% annual risk-free rate

        # Risk limits and thresholds
        self.default_var_limit = 0.02  # 2% daily VaR limit
        self.default_drawdown_limit = 0.10  # 10% maximum drawdown
        self.beta_threshold = 1.5  # High beta threshold

        # Initialize safety infrastructure
        self.safety_manager = None
        if SAFETY_AVAILABLE:
            self._initialize_safety_systems()

        if not self.ibkr_service:
            logger.warning(f"RiskManagementImplementation initialized without IBKR service")

    def _initialize_safety_systems(self):
        """Initialize integrated safety systems"""
        try:
            # Configure safety manager
            safety_config = SafetyManagerConfig(
                name="RiskManagementSafety",
                enable_circuit_breakers=True,
                enable_anomaly_detection=True,
                enable_correlation_monitoring=True,
                enable_system_monitoring=True,
                enable_liquidation_management=True,
                safety_check_interval=2.0
            )

            self.safety_manager = SafetyManager(safety_config, self.ibkr_service)

            # Add safety callbacks
            self.safety_manager.add_safety_callback(self._on_safety_event)

            logger.info("Safety systems initialized for risk management")

        except Exception as e:
            logger.error(f"Failed to initialize safety systems: {e}")
            self.safety_manager = None

    async def _on_safety_event(self, safety_status):
        """Handle safety events from the safety manager"""
        logger.warning(f"Safety event: {safety_status.level.value} - {safety_status.description}")

        # Take action based on safety level
        if safety_status.level.value in ['danger', 'critical']:
            logger.critical("High-risk safety event detected - implementing emergency protocols")
            # This would trigger emergency risk management protocols
    
    async def calculate_portfolio_var(self, 
                                    account: str,
                                    confidence_level: float = 0.95,
                                    time_horizon: int = 1,
                                    method: str = "historical") -> Dict[str, Any]:
        """
        Calculate Value at Risk (VaR) for portfolio
        
        Args:
            account: Account identifier
            confidence_level: Confidence level (0.95 = 95%)
            time_horizon: Time horizon in days
            method: VaR calculation method ('historical', 'parametric', 'monte_carlo')
            
        Returns:
            Dict containing VaR calculations and analysis
        """
        try:
            logger.info(f"Calculating portfolio VaR for account {account}")
            
            # Get portfolio positions with enhanced error handling
            positions = await self._get_portfolio_positions(account)
            if not positions:
                logger.warning(f"No positions found for account {account}")
                # Try to get account summary to check if account exists
                try:
                    if self.ibkr_service and hasattr(self.ibkr_service, 'get_account_summary'):
                        account_summary = await self.ibkr_service.get_account_summary()
                        if account_summary:
                            logger.info("Account exists but has no positions - returning zero VaR")
                        return {
                            "status": "success",
                            "account": account,
                            "var_calculation": {
                                "var_amount": 0.0,
                                "var_percentage": 0.0,
                                "method": method,
                                "confidence_level": confidence_level,
                                "time_horizon": time_horizon,
                                "note": "No positions in portfolio"
                            },
                            "risk_metrics": {"portfolio_value": 0.0, "position_count": 0},
                            "risk_assessment": {"overall_risk": "NONE", "risk_factors": [], "recommendations": ["Add positions to portfolio for meaningful risk analysis"]},
                            "calculation_time": datetime.now().isoformat(),
                            "method": method,
                            "confidence_level": confidence_level,
                            "time_horizon": time_horizon
                        }
                except Exception as e:
                    logger.error(f"Error checking account {account}: {e}")

                return {
                    "status": "error",
                    "message": f"No positions found for VaR calculation in account {account}. Ensure account has positions and is properly connected."
                }

            logger.info(f"Found {len(positions)} positions for VaR calculation")

            # Get historical price data for all positions with better error handling
            price_data = await self._get_historical_prices_for_positions(positions)
            if not price_data:
                logger.warning("No historical price data available - using simplified VaR calculation")
                return await self._calculate_simplified_var(positions, confidence_level, time_horizon, method)

            logger.info(f"Retrieved historical data for {len(price_data)} symbols")

            # Calculate returns
            returns_data = self._calculate_returns(price_data)
            if not returns_data:
                logger.warning("No return data calculated - using simplified VaR")
                return await self._calculate_simplified_var(positions, confidence_level, time_horizon, method)

            # Calculate portfolio returns
            portfolio_returns = self._calculate_portfolio_returns(returns_data, positions)
            if not portfolio_returns:
                logger.warning("No portfolio returns calculated - using simplified VaR")
                return await self._calculate_simplified_var(positions, confidence_level, time_horizon, method)

            logger.info(f"Calculated {len(portfolio_returns)} portfolio return periods")
            
            # Calculate VaR based on method
            if method == "historical":
                var_result = self._calculate_historical_var(portfolio_returns, confidence_level, time_horizon)
            elif method == "parametric":
                var_result = self._calculate_parametric_var(portfolio_returns, confidence_level, time_horizon)
            elif method == "monte_carlo":
                var_result = await self._calculate_monte_carlo_var(portfolio_returns, confidence_level, time_horizon)
            else:
                return {
                    "status": "error",
                    "message": f"Unknown VaR method: {method}"
                }
            
            # Calculate additional risk metrics
            risk_metrics = self._calculate_additional_risk_metrics(portfolio_returns, positions)
            
            # Assess risk status
            risk_assessment = self._assess_risk_status(var_result, risk_metrics)
            
            return {
                "status": "success",
                "account": account,
                "var_calculation": var_result,
                "risk_metrics": risk_metrics,
                "risk_assessment": risk_assessment,
                "calculation_time": datetime.now().isoformat(),
                "method": method,
                "confidence_level": confidence_level,
                "time_horizon": time_horizon
            }
            
        except Exception as e:
            logger.error(f"Error calculating portfolio VaR: {e}")
            return {
                "status": "error",
                "message": f"VaR calculation failed: {str(e)}"
            }
    
    async def analyze_position_risk(self,
                                  symbol: str,
                                  position_size: float,
                                  account: str,
                                  analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """
        Analyze risk for a specific position
        
        Args:
            symbol: Security symbol
            position_size: Position size (shares/contracts)
            account: Account identifier
            analysis_type: Type of analysis ('basic', 'comprehensive', 'stress_test')
            
        Returns:
            Dict containing position risk analysis
        """
        try:
            logger.info(f"Analyzing position risk for {symbol}")
            
            # Get current market data
            market_data = await self._get_current_market_data(symbol)
            
            # Get historical data for risk calculations
            historical_data = await self._get_historical_data_for_risk(symbol)
            
            # Calculate basic risk metrics
            basic_risk = self._calculate_basic_position_risk(
                symbol, position_size, market_data, historical_data
            )
            
            if analysis_type == "basic":
                return {
                    "status": "success",
                    "symbol": symbol,
                    "position_size": position_size,
                    "basic_risk": basic_risk
                }
            
            # Calculate comprehensive risk metrics
            comprehensive_risk = await self._calculate_comprehensive_position_risk(
                symbol, position_size, market_data, historical_data
            )
            
            # Perform stress testing if requested
            stress_test_results = None
            if analysis_type in ["comprehensive", "stress_test"]:
                stress_test_results = await self._perform_position_stress_test(
                    symbol, position_size, market_data, historical_data
                )
            
            # Generate risk recommendations
            recommendations = self._generate_position_risk_recommendations(
                basic_risk, comprehensive_risk, stress_test_results or {}
            )
            
            return {
                "status": "success",
                "symbol": symbol,
                "position_size": position_size,
                "basic_risk": basic_risk,
                "comprehensive_risk": comprehensive_risk,
                "stress_test": stress_test_results,
                "recommendations": recommendations,
                "analysis_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing position risk: {e}")
            return {
                "status": "error",
                "message": f"Position risk analysis failed: {str(e)}"
            }
    
    async def monitor_risk_limits(self,
                                account: str,
                                custom_limits: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Monitor portfolio against risk limits
        
        Args:
            account: Account identifier
            custom_limits: Custom risk limits override
            
        Returns:
            Dict containing risk limit monitoring results
        """
        try:
            logger.info(f"Monitoring risk limits for account {account}")
            
            # Use custom limits or defaults
            limits = custom_limits or {
                "max_portfolio_var": self.default_var_limit,
                "max_drawdown": self.default_drawdown_limit,
                "max_position_concentration": 0.15,  # 15% max single position
                "max_sector_concentration": 0.25,  # 25% max sector exposure
                "max_leverage": 2.0,  # 2:1 maximum leverage
                "min_liquidity_ratio": 0.10  # 10% minimum cash/liquid assets
            }
            
            # Get current portfolio status
            portfolio_status = await self._get_portfolio_status(account)
            
            # Calculate current risk metrics
            current_var = await self._calculate_current_var(account)
            current_drawdown = self._calculate_current_drawdown(portfolio_status)
            position_concentrations = self._calculate_position_concentrations(portfolio_status)
            sector_concentrations = await self._calculate_sector_concentrations(portfolio_status)
            leverage_ratio = self._calculate_leverage_ratio(portfolio_status)
            liquidity_ratio = self._calculate_liquidity_ratio(portfolio_status)
            
            # Check against limits
            limit_breaches = []
            warnings = []
            
            # VaR limit check
            if current_var["var_amount"] > limits["max_portfolio_var"] * portfolio_status["total_value"]:
                limit_breaches.append({
                    "type": "var_breach",
                    "current": current_var["var_percentage"],
                    "limit": limits["max_portfolio_var"] * 100,
                    "severity": "high"
                })
            elif current_var["var_amount"] > limits["max_portfolio_var"] * portfolio_status["total_value"] * 0.8:
                warnings.append({
                    "type": "var_warning",
                    "current": current_var["var_percentage"],
                    "limit": limits["max_portfolio_var"] * 100,
                    "severity": "medium"
                })
            
            # Drawdown limit check
            if current_drawdown > limits["max_drawdown"]:
                limit_breaches.append({
                    "type": "drawdown_breach",
                    "current": current_drawdown * 100,
                    "limit": limits["max_drawdown"] * 100,
                    "severity": "high"
                })
            
            # Position concentration checks
            for symbol, concentration in position_concentrations.items():
                if concentration > limits["max_position_concentration"]:
                    limit_breaches.append({
                        "type": "position_concentration_breach",
                        "symbol": symbol,
                        "current": concentration * 100,
                        "limit": limits["max_position_concentration"] * 100,
                        "severity": "medium"
                    })
            
            # Sector concentration checks
            for sector, concentration in sector_concentrations.items():
                if concentration > limits["max_sector_concentration"]:
                    limit_breaches.append({
                        "type": "sector_concentration_breach",
                        "sector": sector,
                        "current": concentration * 100,
                        "limit": limits["max_sector_concentration"] * 100,
                        "severity": "medium"
                    })
            
            # Leverage check
            if leverage_ratio > limits["max_leverage"]:
                limit_breaches.append({
                    "type": "leverage_breach",
                    "current": leverage_ratio,
                    "limit": limits["max_leverage"],
                    "severity": "high"
                })
            
            # Liquidity check
            if liquidity_ratio < limits["min_liquidity_ratio"]:
                warnings.append({
                    "type": "liquidity_warning",
                    "current": liquidity_ratio * 100,
                    "limit": limits["min_liquidity_ratio"] * 100,
                    "severity": "medium"
                })
            
            # Generate overall risk status
            overall_status = "GREEN"
            if limit_breaches:
                high_severity_breaches = [b for b in limit_breaches if b["severity"] == "high"]
                if high_severity_breaches:
                    overall_status = "RED"
                else:
                    overall_status = "YELLOW"
            elif warnings:
                overall_status = "YELLOW"
            
            return {
                "status": "success",
                "account": account,
                "overall_risk_status": overall_status,
                "limit_breaches": limit_breaches,
                "warnings": warnings,
                "current_metrics": {
                    "var": current_var,
                    "drawdown": current_drawdown,
                    "position_concentrations": position_concentrations,
                    "sector_concentrations": sector_concentrations,
                    "leverage_ratio": leverage_ratio,
                    "liquidity_ratio": liquidity_ratio
                },
                "limits": limits,
                "monitoring_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error monitoring risk limits: {e}")
            return {
                "status": "error",
                "message": f"Risk limit monitoring failed: {str(e)}"
            }
    
    async def calculate_correlation_matrix(self,
                                         symbols: List[str],
                                         lookback_days: int = 252,
                                         frequency: str = "daily") -> Dict[str, Any]:
        """
        Calculate correlation matrix for given securities
        
        Args:
            symbols: List of security symbols
            lookback_days: Number of days to look back
            frequency: Data frequency ('daily', 'weekly', 'monthly')
            
        Returns:
            Dict containing correlation matrix and analysis
        """
        try:
            logger.info(f"Calculating correlation matrix for {len(symbols)} symbols")
            
            # Get historical data for all symbols
            price_data = {}
            for symbol in symbols:
                data = await self._get_historical_data_for_correlation(symbol, lookback_days, frequency)
                if data:
                    price_data[symbol] = data
            
            if len(price_data) < 2:
                return {
                    "status": "error",
                    "message": "Need at least 2 securities with data for correlation analysis"
                }
            
            # Calculate returns for each security
            returns_data = {}
            for symbol, prices in price_data.items():
                returns_data[symbol] = self._calculate_price_returns(prices)
            
            # Create correlation matrix
            correlation_matrix = self._calculate_correlation_matrix(returns_data)
            
            # Analyze correlation patterns
            correlation_analysis = self._analyze_correlation_patterns(correlation_matrix)
            
            # Identify high correlations
            high_correlations = self._identify_high_correlations(correlation_matrix, threshold=0.7)
            
            # Calculate portfolio diversification metrics
            diversification_metrics = self._calculate_diversification_metrics(correlation_matrix)
            
            return {
                "status": "success",
                "symbols": symbols,
                "correlation_matrix": correlation_matrix,
                "correlation_analysis": correlation_analysis,
                "high_correlations": high_correlations,
                "diversification_metrics": diversification_metrics,
                "lookback_days": lookback_days,
                "frequency": frequency,
                "calculation_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error calculating correlation matrix: {e}")
            return {
                "status": "error",
                "message": f"Correlation matrix calculation failed: {str(e)}"
            }
    
    async def perform_stress_test(self,
                                account: str,
                                scenarios: List[Dict],
                                custom_shocks: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Perform stress testing on portfolio
        
        Args:
            account: Account identifier
            scenarios: List of stress test scenarios
            custom_shocks: Custom shock scenarios
            
        Returns:
            Dict containing stress test results
        """
        try:
            logger.info(f"Performing stress test for account {account}")
            
            # Get current portfolio
            portfolio = await self._get_portfolio_positions(account)
            if not portfolio:
                return {
                    "status": "error",
                    "message": "No portfolio positions found for stress testing"
                }
            
            # Define standard stress scenarios if none provided
            if not scenarios:
                scenarios = self._get_standard_stress_scenarios()
            
            stress_results = {}
            
            # Run each stress scenario
            for scenario in scenarios:
                scenario_name = scenario.get("name", "Unknown")
                logger.info(f"Running stress scenario: {scenario_name}")
                
                scenario_result = await self._run_stress_scenario(portfolio, scenario)
                stress_results[scenario_name] = scenario_result
            
            # Run custom shock scenarios if provided
            if custom_shocks:
                for shock_name, shock_params in custom_shocks.items():
                    logger.info(f"Running custom shock: {shock_name}")
                    shock_result = await self._run_custom_shock(portfolio, shock_params)
                    stress_results[f"Custom_{shock_name}"] = shock_result
            
            # Analyze stress test results
            stress_analysis = self._analyze_stress_test_results(stress_results)
            
            # Generate recommendations based on stress tests
            recommendations = self._generate_stress_test_recommendations(stress_analysis)
            
            return {
                "status": "success",
                "account": account,
                "stress_test_results": stress_results,
                "stress_analysis": stress_analysis,
                "recommendations": recommendations,
                "test_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error performing stress test: {e}")
            return {
                "status": "error",
                "message": f"Stress test failed: {str(e)}"
            }
    
    # Helper methods for risk calculations
    
    async def _get_portfolio_positions(self, account: str) -> Dict[str, Any]:
        """Get current portfolio positions"""
        try:
            if not self.ibkr_service or not self.ibkr_service.connected:
                return {}
            
            # Use the correct method from IBKR service
            portfolio_data = await self.ibkr_service.get_portfolio()
            
            # Convert to expected format
            positions = {}
            for pos in portfolio_data:
                symbol = pos.get('symbol', '')
                if symbol:
                    positions[symbol] = {
                        'position': pos.get('position', 0),
                        'avg_cost': pos.get('avgCost', 0),
                        'market_price': pos.get('marketPrice', 0),
                        'market_value': pos.get('marketValue', 0),
                        'unrealized_pnl': pos.get('unrealizedPNL', 0),
                        'realized_pnl': pos.get('realizedPNL', 0)
                    }
            
            return positions
        except Exception as e:
            logger.error(f"Error getting portfolio positions: {e}")
            return {}
    
    async def _get_historical_prices_for_positions(self, positions: Dict) -> Dict:
        """Get historical price data for all positions"""
        price_data = {}
        
        if not self.ibkr_service:
            return price_data
        
        for symbol in positions.keys():
            try:
                if hasattr(self.ibkr_service, 'ib') and hasattr(self.ibkr_service.ib, 'reqHistoricalDataAsync'):
                    # Create contract
                    contract = await self.ibkr_service.create_contract(symbol)
                    
                    # Request historical data
                    bars = await self.ibkr_service.ib.reqHistoricalDataAsync(
                        contract=contract,
                        endDateTime='',  # Empty means now
                        durationStr="1 Y",
                        barSizeSetting="1 day",
                        whatToShow='TRADES',
                        useRTH=True
                    )
                    
                    if bars:
                        # Convert BarData objects to dict format
                        price_data[symbol] = [
                            {
                                "date": bar.date,
                                "open": bar.open,
                                "high": bar.high,
                                "low": bar.low,
                                "close": bar.close,
                                "volume": bar.volume
                            } for bar in bars
                        ]
            except Exception as e:
                logger.warning(f"Could not get historical data for {symbol}: {e}")
                continue
        
        return price_data
    
    def _calculate_returns(self, price_data: Dict) -> Dict:
        """Calculate returns from price data"""
        returns_data = {}
        
        for symbol, bars in price_data.items():
            if len(bars) < 2:
                continue
            
            returns = []
            for i in range(1, len(bars)):
                prev_close = bars[i-1].get("close", 0)
                curr_close = bars[i].get("close", 0)
                
                if prev_close > 0:
                    return_val = (curr_close - prev_close) / prev_close
                    returns.append(return_val)
            
            returns_data[symbol] = returns
        
        return returns_data
    
    def _calculate_portfolio_returns(self, returns_data: Dict, positions: Dict) -> List[float]:
        """Calculate portfolio returns based on positions"""
        if not returns_data or not positions:
            return []
        
        # Get the minimum length of returns across all positions
        min_length = min(len(returns) for returns in returns_data.values() if returns)
        if min_length == 0:
            return []
        
        portfolio_returns = []
        total_value = sum(pos.get("market_value", 0) for pos in positions.values())
        
        if total_value == 0:
            return []
        
        for i in range(min_length):
            period_return = 0
            for symbol, returns in returns_data.items():
                if symbol in positions and i < len(returns):
                    weight = positions[symbol].get("market_value", 0) / total_value
                    period_return += weight * returns[i]
            
            portfolio_returns.append(period_return)
        
        return portfolio_returns
    
    def _calculate_historical_var(self, returns: List[float], confidence_level: float, time_horizon: int) -> Dict:
        """Calculate Historical VaR"""
        if not returns:
            return {"var_amount": 0, "var_percentage": 0}
        
        # Sort returns in ascending order
        sorted_returns = sorted(returns)
        
        # Find the percentile corresponding to the confidence level
        percentile_index = int((1 - confidence_level) * len(sorted_returns))
        var_return = sorted_returns[percentile_index] if percentile_index < len(sorted_returns) else sorted_returns[0]
        
        # Adjust for time horizon
        time_adjusted_var = var_return * math.sqrt(time_horizon)
        
        return {
            "var_percentage": abs(time_adjusted_var) * 100,
            "var_amount": abs(time_adjusted_var),
            "method": "historical",
            "confidence_level": confidence_level,
            "time_horizon": time_horizon
        }
    
    def _calculate_parametric_var(self, returns: List[float], confidence_level: float, time_horizon: int) -> Dict:
        """Calculate Parametric VaR assuming normal distribution"""
        if not returns:
            return {"var_amount": 0, "var_percentage": 0}
        
        # Calculate mean and standard deviation
        mean_return = mean(returns)
        std_return = stdev(returns) if len(returns) > 1 else 0
        
        # Z-score for the confidence level
        from scipy.stats import norm
        z_score = norm.ppf(1 - confidence_level)
        
        # Calculate VaR
        var_return = mean_return + z_score * std_return
        
        # Adjust for time horizon
        time_adjusted_var = var_return * math.sqrt(time_horizon)
        
        return {
            "var_percentage": abs(time_adjusted_var) * 100,
            "var_amount": abs(time_adjusted_var),
            "method": "parametric",
            "confidence_level": confidence_level,
            "time_horizon": time_horizon,
            "mean_return": mean_return,
            "std_return": std_return
        }
    
    async def _calculate_monte_carlo_var(self, returns: List[float], confidence_level: float, time_horizon: int) -> Dict:
        """Calculate Monte Carlo VaR"""
        if not returns:
            return {"var_amount": 0, "var_percentage": 0}
        
        # Calculate parameters from historical data
        mean_return = mean(returns)
        std_return = stdev(returns) if len(returns) > 1 else 0
        
        # Number of simulations
        num_simulations = 10000
        
        # Generate random scenarios
        simulated_returns = []
        for _ in range(num_simulations):
            # Generate random return assuming normal distribution
            random_return = np.random.normal(mean_return, std_return)
            # Adjust for time horizon
            time_adjusted_return = random_return * math.sqrt(time_horizon)
            simulated_returns.append(time_adjusted_return)
        
        # Calculate VaR from simulated returns
        sorted_simulated = sorted(simulated_returns)
        percentile_index = int((1 - confidence_level) * len(sorted_simulated))
        var_return = sorted_simulated[percentile_index] if percentile_index < len(sorted_simulated) else sorted_simulated[0]
        
        return {
            "var_percentage": abs(var_return) * 100,
            "var_amount": abs(var_return),
            "method": "monte_carlo",
            "confidence_level": confidence_level,
            "time_horizon": time_horizon,
            "num_simulations": num_simulations
        }
    
    def _calculate_additional_risk_metrics(self, returns: List[float], positions: Dict) -> Dict:
        """Calculate additional risk metrics"""
        if not returns:
            return {}
        
        metrics = {}
        
        # Sharpe ratio (assuming risk-free rate)
        if len(returns) > 1:
            mean_return = mean(returns)
            std_return = stdev(returns)
            if std_return > 0:
                sharpe_ratio = (mean_return - self.market_risk_free_rate / 252) / std_return
                metrics["sharpe_ratio"] = sharpe_ratio
        
        # Maximum drawdown
        cumulative_returns: List[float] = [1.0]
        for ret in returns:
            cumulative_returns.append(cumulative_returns[-1] * (1 + ret))
        
        peak = cumulative_returns[0]
        max_drawdown = 0
        for value in cumulative_returns[1:]:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            max_drawdown = max(max_drawdown, drawdown)
        
        metrics["max_drawdown"] = max_drawdown
        
        # Volatility (annualized)
        if len(returns) > 1:
            daily_vol = stdev(returns)
            annual_vol = daily_vol * math.sqrt(252)
            metrics["annual_volatility"] = annual_vol
        
        # Portfolio concentration
        if positions:
            total_value = sum(pos.get("market_value", 0) for pos in positions.values())
            if total_value > 0:
                concentrations = {symbol: pos.get("market_value", 0) / total_value 
                                for symbol, pos in positions.items()}
                # Herfindahl-Hirschman Index for concentration
                hhi = sum(conc ** 2 for conc in concentrations.values())
                metrics["concentration_hhi"] = hhi
                metrics["largest_position_weight"] = max(concentrations.values()) if concentrations else 0
        
        return metrics

    async def _calculate_simplified_var(self, positions: Dict, confidence_level: float, time_horizon: int, method: str) -> Dict[str, Any]:
        """Calculate simplified VaR when historical data is not available"""
        try:
            logger.info("Calculating simplified VaR based on position values and estimated volatilities")

            total_value = sum(pos.get("market_value", 0) for pos in positions.values())
            if total_value == 0:
                return {
                    "status": "success",
                    "var_calculation": {
                        "var_amount": 0.0,
                        "var_percentage": 0.0,
                        "var_amount_dollar": 0.0,
                        "method": f"{method}_simplified",
                        "confidence_level": confidence_level,
                        "time_horizon": time_horizon,
                        "note": "Simplified calculation - no historical data available"
                    },
                    "risk_metrics": {"portfolio_value": 0.0, "position_count": len(positions)},
                    "risk_assessment": {"overall_risk": "UNKNOWN", "risk_factors": ["Insufficient data for accurate risk assessment"], "recommendations": ["Ensure market data connection for better risk analysis"]},
                    "portfolio_summary": {
                        "total_value": 0.0,
                        "position_count": len(positions),
                        "symbols": list(positions.keys())
                    },
                    "calculation_time": datetime.now().isoformat(),
                    "method": f"{method}_simplified",
                    "confidence_level": confidence_level,
                    "time_horizon": time_horizon
                }

            # Use simplified assumptions for VaR calculation
            # Assume average daily volatility based on asset type
            estimated_daily_vol = 0.02  # 2% daily volatility assumption

            # Adjust volatility based on confidence level
            if confidence_level == 0.95:
                z_score = 1.645
            elif confidence_level == 0.99:
                z_score = 2.326
            else:
                # Approximate z-score for other confidence levels
                from scipy.stats import norm
                z_score = norm.ppf(confidence_level)

            # Calculate simplified VaR
            time_adjusted_vol = estimated_daily_vol * math.sqrt(time_horizon)
            var_percentage = z_score * time_adjusted_vol
            var_amount_dollar = var_percentage * total_value

            # Create basic risk metrics
            risk_metrics = {
                "portfolio_value": total_value,
                "position_count": len(positions),
                "estimated_daily_volatility": estimated_daily_vol,
                "estimated_annual_volatility": estimated_daily_vol * math.sqrt(252),
                "largest_position_value": max((pos.get("market_value", 0) for pos in positions.values()), default=0),
                "concentration_risk": "UNKNOWN - insufficient data"
            }

            # Basic risk assessment
            risk_level = "MEDIUM"  # Default assumption
            if var_percentage > 0.05:  # > 5%
                risk_level = "HIGH"
            elif var_percentage < 0.02:  # < 2%
                risk_level = "LOW"

            risk_assessment = {
                "overall_risk": risk_level,
                "risk_factors": [
                    "Simplified calculation due to lack of historical data",
                    "Volatility estimates may not reflect actual market conditions"
                ],
                "recommendations": [
                    "Establish market data connection for accurate risk assessment",
                    "Monitor positions closely until proper risk metrics are available",
                    "Consider reducing position sizes if uncertain about risk levels"
                ]
            }

            return {
                "status": "success",
                "var_calculation": {
                    "var_amount": var_percentage,
                    "var_percentage": var_percentage * 100,
                    "var_amount_dollar": var_amount_dollar,
                    "portfolio_value": total_value,
                    "method": f"{method}_simplified",
                    "confidence_level": confidence_level,
                    "time_horizon": time_horizon,
                    "note": "Simplified calculation using estimated volatilities - actual risk may differ"
                },
                "risk_metrics": risk_metrics,
                "risk_assessment": risk_assessment,
                "portfolio_summary": {
                    "total_value": total_value,
                    "position_count": len(positions),
                    "symbols": list(positions.keys())
                },
                "calculation_time": datetime.now().isoformat(),
                "method": f"{method}_simplified",
                "confidence_level": confidence_level,
                "time_horizon": time_horizon
            }

        except Exception as e:
            logger.error(f"Error in simplified VaR calculation: {e}")
            return {
                "status": "error",
                "message": f"Simplified VaR calculation failed: {str(e)}"
            }

    def _assess_risk_status(self, var_result: Dict, risk_metrics: Dict) -> Dict:
        """Assess overall risk status"""
        assessment = {
            "overall_risk": "MEDIUM",
            "risk_factors": [],
            "recommendations": []
        }
        
        # Assess VaR level
        var_percentage = var_result.get("var_percentage", 0)
        if var_percentage > 5:  # 5% daily VaR is high
            assessment["risk_factors"].append("High Value at Risk")
            assessment["overall_risk"] = "HIGH"
        elif var_percentage > 2:  # 2% daily VaR is moderate
            assessment["risk_factors"].append("Moderate Value at Risk")
        
        # Assess concentration
        concentration_hhi = risk_metrics.get("concentration_hhi", 0)
        if concentration_hhi > 0.25:  # High concentration
            assessment["risk_factors"].append("High portfolio concentration")
            if assessment["overall_risk"] != "HIGH":
                assessment["overall_risk"] = "MEDIUM"
        
        # Assess volatility
        annual_vol = risk_metrics.get("annual_volatility", 0)
        if annual_vol > 0.3:  # 30% annual volatility is high
            assessment["risk_factors"].append("High portfolio volatility")
        
        # Generate recommendations
        if var_percentage > 3:
            assessment["recommendations"].append("Consider reducing position sizes or hedging exposure")
        
        if concentration_hhi > 0.2:
            assessment["recommendations"].append("Consider diversifying portfolio to reduce concentration risk")
        
        if annual_vol > 0.25:
            assessment["recommendations"].append("Consider adding lower-volatility assets to reduce overall risk")
        
        return assessment
    
    # Additional helper methods for comprehensive risk management implementation
    
    async def _get_current_market_data(self, symbol: str) -> Dict:
        """Get current market data for a symbol"""
        try:
            if self.ibkr_service and self.ibkr_service.connected:
                # Use the correct method from IBKR service
                market_data = await self.ibkr_service.get_market_data(symbol)
                if market_data.get("status") == "success" and "data" in market_data:
                    data = market_data["data"]
                    return {
                        "last_price": data.get("last", 0),
                        "bid": data.get("bid", 0),
                        "ask": data.get("ask", 0),
                        "volume": data.get("volume", 0),
                        "high": data.get("high", 0),
                        "low": data.get("low", 0)
                    }
            return {}
        except Exception as e:
            logger.warning(f"Could not get market data for {symbol}: {e}")
            return {}
    
    async def _get_historical_data_for_risk(self, symbol: str, days: int = 252) -> Dict:
        """Get historical data for risk calculations"""
        try:
            if self.ibkr_service and self.ibkr_service.connected:
                # Create contract
                contract = await self.ibkr_service.create_contract(symbol)
                
                # Request historical data
                bars = await self.ibkr_service.ib.reqHistoricalDataAsync(
                    contract=contract,
                    endDateTime='',
                    durationStr=f"{days} D",
                    barSizeSetting="1 day",
                    whatToShow='TRADES',
                    useRTH=True
                )
                
                if bars:
                    # Convert to expected format
                    return {
                        "bars": [
                            {
                                "date": bar.date,
                                "open": bar.open,
                                "high": bar.high,
                                "low": bar.low,
                                "close": bar.close,
                                "volume": bar.volume
                            } for bar in bars
                        ]
                    }
            return {}
        except Exception as e:
            logger.warning(f"Could not get historical data for {symbol}: {e}")
            return {}
    
    def _calculate_basic_position_risk(self, symbol: str, position_size: float, market_data: Dict, historical_data: Dict) -> Dict:
        """Calculate basic risk metrics for a position"""
        risk_metrics = {
            "symbol": symbol,
            "position_size": position_size,
            "current_price": market_data.get("last_price", 0),
            "market_value": market_data.get("last_price", 0) * abs(position_size)
        }
        
        # Calculate basic volatility if historical data available
        if historical_data and "bars" in historical_data:
            bars = historical_data["bars"]
            if len(bars) > 1:
                returns = []
                for i in range(1, len(bars)):
                    prev_close = bars[i-1].get("close", 0)
                    curr_close = bars[i].get("close", 0)
                    if prev_close > 0:
                        returns.append((curr_close - prev_close) / prev_close)
                
                if returns:
                    daily_vol = stdev(returns) if len(returns) > 1 else 0
                    annual_vol = daily_vol * math.sqrt(252)
                    risk_metrics["daily_volatility"] = daily_vol
                    risk_metrics["annual_volatility"] = annual_vol
                    
                    # 1-day VaR at 95% confidence (assuming normal distribution)
                    one_day_var = 1.645 * daily_vol * risk_metrics["market_value"]
                    risk_metrics["one_day_var_95"] = one_day_var
        
        return risk_metrics
    
    async def _calculate_comprehensive_position_risk(self, symbol: str, position_size: float, market_data: Dict, historical_data: Dict) -> Dict:
        """Calculate comprehensive risk metrics for a position"""
        comprehensive_risk = {}
        
        # Beta calculation (simplified - assumes correlation with market)
        try:
            # Get market data (SPY as proxy)
            market_data_spy = await self._get_historical_data_for_risk("SPY")
            if market_data_spy and historical_data:
                beta = self._calculate_beta(historical_data, market_data_spy)
                comprehensive_risk["beta"] = beta
        except Exception as e:
            logger.warning(f"Could not calculate beta for {symbol}: {e}")
        
        # Liquidity metrics
        if historical_data and "bars" in historical_data:
            bars = historical_data["bars"]
            avg_volume = mean([bar.get("volume", 0) for bar in bars[-30:]]) if len(bars) >= 30 else 0
            comprehensive_risk["average_volume_30d"] = avg_volume
            
            # Position as percentage of average daily volume
            if avg_volume > 0:
                position_volume_ratio = abs(position_size) / avg_volume
                comprehensive_risk["position_volume_ratio"] = position_volume_ratio
                
                if position_volume_ratio > 0.1:
                    comprehensive_risk["liquidity_risk"] = "HIGH"
                elif position_volume_ratio > 0.05:
                    comprehensive_risk["liquidity_risk"] = "MEDIUM"
                else:
                    comprehensive_risk["liquidity_risk"] = "LOW"
        
        return comprehensive_risk
    
    async def _perform_position_stress_test(self, symbol: str, position_size: float, market_data: Dict, historical_data: Dict) -> Dict:
        """Perform stress testing on a position"""
        stress_scenarios = {
            "market_crash_10": -0.10,  # 10% market decline
            "market_crash_20": -0.20,  # 20% market decline
            "high_volatility": 2.0,    # 2x normal volatility
            "liquidity_crisis": 0.5    # 50% reduction in liquidity
        }
        
        current_price = market_data.get("last_price", 0)
        current_value = current_price * position_size
        
        stress_results = {}
        
        for scenario_name, shock in stress_scenarios.items():
            if "crash" in scenario_name:
                # Price shock scenario
                stressed_price = current_price * (1 + shock)
                stressed_value = stressed_price * position_size
                pnl = stressed_value - current_value
                
                stress_results[scenario_name] = {
                    "stressed_price": stressed_price,
                    "stressed_value": stressed_value,
                    "pnl": pnl,
                    "pnl_percentage": (pnl / abs(current_value)) * 100 if current_value != 0 else 0
                }
            
            elif scenario_name == "high_volatility":
                # Volatility shock - estimate impact on option-like instruments
                if historical_data and "bars" in historical_data:
                    bars = historical_data["bars"]
                    if len(bars) > 1:
                        returns = []
                        for i in range(1, len(bars)):
                            prev_close = bars[i-1].get("close", 0)
                            curr_close = bars[i].get("close", 0)
                            if prev_close > 0:
                                returns.append((curr_close - prev_close) / prev_close)
                        
                        if returns:
                            normal_vol = stdev(returns) if len(returns) > 1 else 0
                            stressed_vol = normal_vol * shock
                            
                            stress_results[scenario_name] = {
                                "normal_volatility": normal_vol,
                                "stressed_volatility": stressed_vol,
                                "volatility_impact": "Increased uncertainty and potential for larger price swings"
                            }
            
            elif scenario_name == "liquidity_crisis":
                # Liquidity stress - assume wider bid-ask spreads
                if current_price > 0:
                    normal_spread = current_price * 0.001  # Assume 0.1% normal spread
                    stressed_spread = normal_spread / shock  # Wider spread in crisis
                    liquidity_cost = stressed_spread * abs(position_size) / 2
                    
                    stress_results[scenario_name] = {
                        "normal_spread": normal_spread,
                        "stressed_spread": stressed_spread,
                        "liquidity_cost": liquidity_cost,
                        "impact": "Increased transaction costs and difficulty in position exit"
                    }
        
        return stress_results
    
    def _calculate_beta(self, asset_data: Dict, market_data: Dict) -> float:
        """Calculate beta relative to market"""
        try:
            asset_bars = asset_data.get("bars", [])
            market_bars = market_data.get("bars", [])
            
            if len(asset_bars) < 2 or len(market_bars) < 2:
                return 1.0  # Default beta
            
            # Calculate returns for both asset and market
            asset_returns = []
            market_returns = []
            
            min_length = min(len(asset_bars), len(market_bars))
            
            for i in range(1, min_length):
                # Asset returns
                asset_prev = asset_bars[i-1].get("close", 0)
                asset_curr = asset_bars[i].get("close", 0)
                if asset_prev > 0:
                    asset_returns.append((asset_curr - asset_prev) / asset_prev)
                
                # Market returns
                market_prev = market_bars[i-1].get("close", 0)
                market_curr = market_bars[i].get("close", 0)
                if market_prev > 0:
                    market_returns.append((market_curr - market_prev) / market_prev)
            
            if len(asset_returns) != len(market_returns) or len(asset_returns) < 2:
                return 1.0
            
            # Calculate beta using covariance and variance
            asset_mean = mean(asset_returns)
            market_mean = mean(market_returns)
            
            covariance = sum((asset_returns[i] - asset_mean) * (market_returns[i] - market_mean) 
                           for i in range(len(asset_returns))) / (len(asset_returns) - 1)
            
            market_variance = sum((ret - market_mean) ** 2 for ret in market_returns) / (len(market_returns) - 1)
            
            if market_variance > 0:
                beta = covariance / market_variance
                return beta
            
            return 1.0
            
        except Exception as e:
            logger.warning(f"Error calculating beta: {e}")
            return 1.0
    
    def _generate_position_risk_recommendations(self, basic_risk: Dict, comprehensive_risk: Dict, stress_test: Dict) -> List[str]:
        """Generate risk management recommendations for a position"""
        recommendations = []
        
        # Check volatility
        annual_vol = basic_risk.get("annual_volatility", 0)
        if annual_vol > 0.4:  # 40% annual volatility
            recommendations.append("Consider reducing position size due to high volatility")
        
        # Check beta
        beta = comprehensive_risk.get("beta", 1.0)
        if beta > 1.5:
            recommendations.append("High beta stock - consider hedging market exposure")
        elif beta < 0.5:
            recommendations.append("Low beta stock - good for defensive positioning")
        
        # Check liquidity
        liquidity_risk = comprehensive_risk.get("liquidity_risk", "UNKNOWN")
        if liquidity_risk == "HIGH":
            recommendations.append("High liquidity risk - consider gradual position building/unwinding")
        
        # Check VaR
        one_day_var = basic_risk.get("one_day_var_95", 0)
        market_value = basic_risk.get("market_value", 0)
        if market_value > 0 and (one_day_var / market_value) > 0.05:  # 5% daily VaR
            recommendations.append("High daily VaR - consider position size reduction or hedging")
        
        # Check stress test results
        if stress_test:
            market_crash_20_pnl = stress_test.get("market_crash_20", {}).get("pnl_percentage", 0)
            if market_crash_20_pnl < -25:  # More than 25% loss in 20% market crash
                recommendations.append("Position vulnerable to market crashes - consider protective strategies")
        
        if not recommendations:
            recommendations.append("Position risk appears manageable under current analysis")
        
        return recommendations
    
    async def _get_portfolio_status(self, account: str) -> Dict:
        """Get current portfolio status"""
        try:
            if self.ibkr_service and self.ibkr_service.connected:
                # Get positions using the correct method
                positions = await self._get_portfolio_positions(account)
                
                # Get account summary
                account_summary = await self.ibkr_service.get_account_summary()
                
                # Extract cash value
                cash_value = 0
                if "TotalCashValue" in account_summary:
                    cash_value = float(account_summary["TotalCashValue"].get("value", 0))
                
                # Calculate total positions value
                total_positions_value = sum(
                    pos.get("market_value", 0) for pos in positions.values()
                )
                
                return {
                    "total_value": total_positions_value + cash_value,
                    "positions_value": total_positions_value,
                    "cash_value": cash_value,
                    "positions": positions,
                    "account_info": account_summary
                }
            
            return {}
        except Exception as e:
            logger.error(f"Error getting portfolio status: {e}")
            return {}
    
    async def _calculate_current_var(self, account: str) -> Dict:
        """Calculate current portfolio VaR"""
        return await self.calculate_portfolio_var(account, method="historical")
    
    def _calculate_current_drawdown(self, portfolio_status: Dict) -> float:
        """Calculate current drawdown (simplified)"""
        # This is a simplified calculation - would need historical portfolio values for accuracy
        return 0.0  # Placeholder
    
    def _calculate_position_concentrations(self, portfolio_status: Dict) -> Dict[str, float]:
        """Calculate position concentrations"""
        concentrations = {}
        total_value = portfolio_status.get("total_value", 0)
        positions = portfolio_status.get("positions", {})
        
        if total_value > 0:
            for symbol, position in positions.items():
                market_value = position.get("market_value", 0)
                concentrations[symbol] = market_value / total_value
        
        return concentrations
    
    async def _calculate_sector_concentrations(self, portfolio_status: Dict) -> Dict[str, float]:
        """Calculate sector concentrations (simplified)"""
        # This would require sector classification data
        # For now, return empty dict - could be enhanced with sector mapping
        return {}
    
    def _calculate_leverage_ratio(self, portfolio_status: Dict) -> float:
        """Calculate leverage ratio"""
        total_value = portfolio_status.get("total_value", 0)
        cash_value = portfolio_status.get("cash_value", 0)
        
        if cash_value > 0:
            return total_value / cash_value
        return 1.0
    
    def _calculate_liquidity_ratio(self, portfolio_status: Dict) -> float:
        """Calculate liquidity ratio"""
        total_value = portfolio_status.get("total_value", 0)
        cash_value = portfolio_status.get("cash_value", 0)
        
        if total_value > 0:
            return cash_value / total_value
        return 0.0
    
    async def _get_historical_data_for_correlation(self, symbol: str, lookback_days: int, frequency: str) -> List[Dict]:
        """Get historical data for correlation analysis"""
        try:
            if self.ibkr_service and self.ibkr_service.connected:
                bar_size = "1 day" if frequency == "daily" else "1 week" if frequency == "weekly" else "1 month"
                
                # Create contract
                contract = await self.ibkr_service.create_contract(symbol)
                
                # Request historical data
                bars = await self.ibkr_service.ib.reqHistoricalDataAsync(
                    contract=contract,
                    endDateTime='',
                    durationStr=f"{lookback_days} D",
                    barSizeSetting=bar_size,
                    whatToShow='TRADES',
                    useRTH=True
                )
                
                if bars:
                    # Convert to dict format
                    return [
                        {
                            "date": bar.date,
                            "open": bar.open,
                            "high": bar.high,
                            "low": bar.low,
                            "close": bar.close,
                            "volume": bar.volume
                        } for bar in bars
                    ]
            return []
        except Exception as e:
            logger.warning(f"Could not get historical data for correlation {symbol}: {e}")
            return []
    
    def _calculate_price_returns(self, price_data: List[Dict]) -> List[float]:
        """Calculate price returns from historical data"""
        returns = []
        for i in range(1, len(price_data)):
            prev_close = price_data[i-1].get("close", 0)
            curr_close = price_data[i].get("close", 0)
            if prev_close > 0:
                returns.append((curr_close - prev_close) / prev_close)
        return returns
    
    def _calculate_correlation_matrix(self, returns_data: Dict[str, List[float]]) -> Dict[str, Dict[str, float]]:
        """Calculate correlation matrix from returns data"""
        symbols = list(returns_data.keys())
        correlation_matrix = {}
        
        for symbol1 in symbols:
            correlation_matrix[symbol1] = {}
            for symbol2 in symbols:
                if symbol1 == symbol2:
                    correlation_matrix[symbol1][symbol2] = 1.0
                else:
                    correlation = self._calculate_correlation(
                        returns_data[symbol1], 
                        returns_data[symbol2]
                    )
                    correlation_matrix[symbol1][symbol2] = correlation
        
        return correlation_matrix
    
    def _calculate_correlation(self, returns1: List[float], returns2: List[float]) -> float:
        """Calculate correlation between two return series"""
        try:
            if len(returns1) != len(returns2) or len(returns1) < 2:
                return 0.0
            
            mean1 = mean(returns1)
            mean2 = mean(returns2)
            
            numerator = sum((returns1[i] - mean1) * (returns2[i] - mean2) for i in range(len(returns1)))
            denominator1 = sum((ret - mean1) ** 2 for ret in returns1)
            denominator2 = sum((ret - mean2) ** 2 for ret in returns2)
            
            if denominator1 > 0 and denominator2 > 0:
                correlation = numerator / math.sqrt(denominator1 * denominator2)
                return max(-1.0, min(1.0, correlation))  # Clamp between -1 and 1
            
            return 0.0
        except Exception as e:
            logger.warning(f"Error calculating correlation: {e}")
            return 0.0
    
    def _analyze_correlation_patterns(self, correlation_matrix: Dict) -> Dict:
        """Analyze correlation patterns in the matrix"""
        if not correlation_matrix:
            return {}
        
        symbols = list(correlation_matrix.keys())
        correlations = []
        
        # Collect all correlation values (excluding diagonal)
        for i, symbol1 in enumerate(symbols):
            for j, symbol2 in enumerate(symbols):
                if i < j:  # Only collect upper triangle to avoid duplicates
                    corr = correlation_matrix[symbol1][symbol2]
                    correlations.append(corr)
        
        if not correlations:
            return {}
        
        analysis = {
            "average_correlation": mean(correlations),
            "max_correlation": max(correlations),
            "min_correlation": min(correlations),
            "correlation_std": stdev(correlations) if len(correlations) > 1 else 0
        }
        
        # Classify correlation levels
        high_corr_count = sum(1 for corr in correlations if corr > 0.7)
        medium_corr_count = sum(1 for corr in correlations if 0.3 < corr <= 0.7)
        low_corr_count = sum(1 for corr in correlations if -0.3 <= corr <= 0.3)
        negative_corr_count = sum(1 for corr in correlations if corr < -0.3)
        
        analysis["correlation_distribution"] = {
            "high_positive": high_corr_count,
            "medium_positive": medium_corr_count,
            "low": low_corr_count,
            "negative": negative_corr_count
        }
        
        return analysis
    
    def _identify_high_correlations(self, correlation_matrix: Dict, threshold: float = 0.7) -> List[Dict]:
        """Identify pairs with high correlation"""
        high_correlations = []
        symbols = list(correlation_matrix.keys())
        
        for i, symbol1 in enumerate(symbols):
            for j, symbol2 in enumerate(symbols):
                if i < j:  # Only check upper triangle
                    corr = correlation_matrix[symbol1][symbol2]
                    if abs(corr) > threshold:
                        high_correlations.append({
                            "symbol1": symbol1,
                            "symbol2": symbol2,
                            "correlation": corr,
                            "type": "positive" if corr > 0 else "negative"
                        })
        
        # Sort by absolute correlation value
        high_correlations.sort(key=lambda x: abs(x["correlation"]), reverse=True)
        return high_correlations
    
    def _calculate_diversification_metrics(self, correlation_matrix: Dict) -> Dict:
        """Calculate portfolio diversification metrics"""
        if not correlation_matrix:
            return {}
        
        symbols = list(correlation_matrix.keys())
        n = len(symbols)
        
        if n < 2:
            return {"diversification_ratio": 1.0, "effective_number_assets": 1}
        
        # Calculate average correlation
        total_correlation = 0
        count = 0
        
        for i, symbol1 in enumerate(symbols):
            for j, symbol2 in enumerate(symbols):
                if i != j:
                    total_correlation += correlation_matrix[symbol1][symbol2]
                    count += 1
        
        avg_correlation = total_correlation / count if count > 0 else 0
        
        # Diversification ratio (simplified)
        diversification_ratio = 1 / (1 + (n - 1) * avg_correlation) if avg_correlation >= 0 else 1
        
        # Effective number of assets
        effective_assets = 1 + (n - 1) * (1 - avg_correlation) if avg_correlation < 1 else 1
        
        return {
            "diversification_ratio": diversification_ratio,
            "effective_number_assets": effective_assets,
            "average_correlation": avg_correlation,
            "portfolio_concentration": 1 - diversification_ratio
        }
    
    def _get_standard_stress_scenarios(self) -> List[Dict]:
        """Get standard stress test scenarios"""
        return [
            {
                "name": "2008_Financial_Crisis",
                "description": "Replication of 2008 financial crisis conditions",
                "market_shock": -0.35,
                "volatility_shock": 2.5,
                "liquidity_shock": 0.3
            },
            {
                "name": "COVID_2020_Crash",
                "description": "March 2020 COVID-19 market crash",
                "market_shock": -0.30,
                "volatility_shock": 3.0,
                "liquidity_shock": 0.5
            },
            {
                "name": "Interest_Rate_Shock",
                "description": "Rapid interest rate increase",
                "interest_rate_shock": 0.03,  # 3% increase
                "bond_impact": -0.15,
                "growth_stock_impact": -0.20
            },
            {
                "name": "Inflation_Spike",
                "description": "Sudden inflation increase",
                "inflation_shock": 0.05,  # 5% increase
                "commodity_impact": 0.25,
                "real_estate_impact": 0.10,
                "bond_impact": -0.12
            }
        ]
    
    async def _run_stress_scenario(self, portfolio: Dict, scenario: Dict) -> Dict:
        """Run a specific stress scenario on portfolio"""
        scenario_results = {
            "scenario_name": scenario.get("name", "Unknown"),
            "description": scenario.get("description", ""),
            "portfolio_impact": {},
            "total_pnl": 0,
            "total_pnl_percentage": 0
        }
        
        total_current_value = 0
        total_stressed_value = 0
        
        for symbol, position in portfolio.items():
            current_value = position.get("market_value", 0)
            total_current_value += current_value
            
            # Apply scenario shocks
            stressed_value = current_value
            
            # Market shock (applies to all equity positions)
            if "market_shock" in scenario:
                stressed_value *= (1 + scenario["market_shock"])
            
            # Asset-specific shocks could be added here
            
            position_pnl = stressed_value - current_value
            scenario_results["portfolio_impact"][symbol] = {
                "current_value": current_value,
                "stressed_value": stressed_value,
                "pnl": position_pnl,
                "pnl_percentage": (position_pnl / current_value * 100) if current_value > 0 else 0
            }
            
            total_stressed_value += stressed_value
        
        scenario_results["total_pnl"] = total_stressed_value - total_current_value
        if total_current_value > 0:
            scenario_results["total_pnl_percentage"] = (scenario_results["total_pnl"] / total_current_value) * 100
        
        return scenario_results
    
    async def _run_custom_shock(self, portfolio: Dict, shock_params: Dict) -> Dict:
        """Run custom shock scenario"""
        # Similar to _run_stress_scenario but with custom parameters
        return await self._run_stress_scenario(portfolio, shock_params)
    
    def _analyze_stress_test_results(self, stress_results: Dict) -> Dict:
        """Analyze aggregated stress test results"""
        if not stress_results:
            return {}
        
        worst_scenario = None
        worst_loss = 0
        best_scenario = None
        best_result = float('-inf')
        
        total_losses = []
        
        for scenario_name, result in stress_results.items():
            pnl_percentage = result.get("total_pnl_percentage", 0)
            total_losses.append(pnl_percentage)
            
            if pnl_percentage < worst_loss:
                worst_loss = pnl_percentage
                worst_scenario = scenario_name
            
            if pnl_percentage > best_result:
                best_result = pnl_percentage
                best_scenario = scenario_name
        
        analysis = {
            "worst_case_scenario": worst_scenario,
            "worst_case_loss": worst_loss,
            "best_case_scenario": best_scenario,
            "best_case_result": best_result,
            "average_loss": mean(total_losses) if total_losses else 0,
            "stress_test_volatility": stdev(total_losses) if len(total_losses) > 1 else 0
        }
        
        # Risk assessment based on worst case
        if worst_loss < -30:
            analysis["risk_level"] = "EXTREME"
        elif worst_loss < -20:
            analysis["risk_level"] = "HIGH"
        elif worst_loss < -10:
            analysis["risk_level"] = "MEDIUM"
        else:
            analysis["risk_level"] = "LOW"
        
        return analysis
    
    def _generate_stress_test_recommendations(self, stress_analysis: Dict) -> List[str]:
        """Generate recommendations based on stress test results"""
        recommendations = []
        
        risk_level = stress_analysis.get("risk_level", "UNKNOWN")
        worst_case_loss = stress_analysis.get("worst_case_loss", 0)
        
        if risk_level == "EXTREME":
            recommendations.extend([
                "URGENT: Portfolio extremely vulnerable to stress scenarios",
                "Consider immediate diversification and hedging strategies",
                "Reduce position sizes and increase cash allocation",
                "Implement stop-loss orders on major positions"
            ])
        elif risk_level == "HIGH":
            recommendations.extend([
                "Portfolio shows high vulnerability to stress scenarios",
                "Consider protective hedging strategies",
                "Review and reduce concentration in vulnerable positions",
                "Consider increasing defensive asset allocation"
            ])
        elif risk_level == "MEDIUM":
            recommendations.extend([
                "Portfolio shows moderate stress vulnerability",
                "Monitor risk exposure and consider minor hedging",
                "Review correlation patterns and diversification"
            ])
        else:
            recommendations.append("Portfolio appears relatively resilient to tested stress scenarios")
        
        if worst_case_loss < -15:
            recommendations.append(f"Worst case scenario shows {worst_case_loss:.1f}% loss - consider tail risk hedging")
        
        return recommendations

    # Safety Integration Methods

    async def get_safety_status(self) -> Dict[str, Any]:
        """
        Get comprehensive safety status from all safety systems

        Returns:
            Dict containing safety status and recommendations
        """
        if not SAFETY_AVAILABLE or not self.safety_manager:
            return {
                "status": "warning",
                "message": "Safety systems not available",
                "safety_level": "unknown",
                "recommendations": ["Install safety infrastructure for production use"]
            }

        try:
            # Get current safety status
            safety_status = self.safety_manager.get_current_status()

            # Get detailed statistics
            safety_stats = self.safety_manager.get_statistics()

            return {
                "status": "success",
                "safety_level": safety_status.level.value,
                "active_alerts": safety_status.active_alerts,
                "description": safety_status.description,
                "recommendations": safety_status.recommendations,
                "detailed_stats": safety_stats,
                "monitoring_active": safety_stats.get('current_status', {}).get('monitoring_active', False),
                "last_check": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting safety status: {e}")
            return {
                "status": "error",
                "message": f"Failed to get safety status: {str(e)}"
            }

    async def start_safety_monitoring(self) -> Dict[str, Any]:
        """
        Start comprehensive safety monitoring

        Returns:
            Dict containing startup status
        """
        if not SAFETY_AVAILABLE or not self.safety_manager:
            return {
                "status": "error",
                "message": "Safety systems not available"
            }

        try:
            await self.safety_manager.start_monitoring()

            return {
                "status": "success",
                "message": "Safety monitoring started",
                "monitoring_active": True,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error starting safety monitoring: {e}")
            return {
                "status": "error",
                "message": f"Failed to start safety monitoring: {str(e)}"
            }

    async def stop_safety_monitoring(self) -> Dict[str, Any]:
        """
        Stop safety monitoring

        Returns:
            Dict containing stop status
        """
        if not SAFETY_AVAILABLE or not self.safety_manager:
            return {
                "status": "error",
                "message": "Safety systems not available"
            }

        try:
            await self.safety_manager.stop_monitoring()

            return {
                "status": "success",
                "message": "Safety monitoring stopped",
                "monitoring_active": False,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error stopping safety monitoring: {e}")
            return {
                "status": "error",
                "message": f"Failed to stop safety monitoring: {str(e)}"
            }

    async def trigger_emergency_stop(self, reason: str = "Manual emergency stop") -> Dict[str, Any]:
        """
        Trigger emergency stop of all trading operations

        Args:
            reason: Reason for emergency stop

        Returns:
            Dict containing emergency stop status
        """
        if not SAFETY_AVAILABLE or not self.safety_manager:
            logger.critical(f"EMERGENCY STOP REQUESTED: {reason} (Safety systems not available)")
            return {
                "status": "warning",
                "message": "Emergency stop requested but safety systems not available",
                "reason": reason,
                "timestamp": datetime.now().isoformat()
            }

        try:
            await self.safety_manager.emergency_stop(reason)

            logger.critical(f"EMERGENCY STOP EXECUTED: {reason}")

            return {
                "status": "success",
                "message": "Emergency stop executed",
                "reason": reason,
                "timestamp": datetime.now().isoformat(),
                "safety_level": "critical"
            }

        except Exception as e:
            logger.error(f"Error executing emergency stop: {e}")
            return {
                "status": "error",
                "message": f"Failed to execute emergency stop: {str(e)}",
                "reason": reason
            }

    async def check_circuit_breakers(self) -> Dict[str, Any]:
        """
        Check status of all circuit breakers

        Returns:
            Dict containing circuit breaker status
        """
        if not SAFETY_AVAILABLE or not self.safety_manager:
            return {
                "status": "warning",
                "message": "Circuit breakers not available",
                "circuit_breakers": {}
            }

        try:
            if hasattr(self.safety_manager, 'circuit_breaker_manager') and self.safety_manager.circuit_breaker_manager:
                breaker_status = self.safety_manager.circuit_breaker_manager.check_all_breakers()
                breaker_stats = self.safety_manager.circuit_breaker_manager.get_all_stats()

                return {
                    "status": "success",
                    "circuit_breakers": breaker_status,
                    "detailed_stats": breaker_stats,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "status": "warning",
                    "message": "Circuit breaker manager not initialized"
                }

        except Exception as e:
            logger.error(f"Error checking circuit breakers: {e}")
            return {
                "status": "error",
                "message": f"Failed to check circuit breakers: {str(e)}"
            }

