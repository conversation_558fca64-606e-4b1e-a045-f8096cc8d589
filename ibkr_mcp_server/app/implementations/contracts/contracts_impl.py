"""
Contracts Implementation - Layer 3: Implementation
Handles actual contract research and lookup operations with IBKR service.
Enhanced service injection and error handling.
"""

import logging
from typing import Dict, Any, Optional, List, TYPE_CHECKING
from ..base_implementation import BaseImplementation
from ib_async import Stock, Forex, Future, Option, Contract

if TYPE_CHECKING:
    from services.ibkr_service import IBKRService

logger = logging.getLogger('implementations.contracts.contracts_impl')

class ContractsImplementation(BaseImplementation):
    """Implementation for contract research and lookup operations"""
    
    def __init__(self, ibkr_service: Optional['IBKRService'] = None, **kwargs):
        super().__init__(ibkr_service=ibkr_service, **kwargs)
        logger.info(f"✅ ContractsImplementation initialized with service: {bool(self.ibkr_service)}")
    
    async def search_contracts(self, pattern: str, sec_type: str = "STK", exchange: str = "SMART", currency: str = "USD") -> Dict[str, Any]:
        """Search for contracts matching pattern"""
        if not self.ensure_service_available() or not self.ibkr_service:
            return self.create_error_response("IBKR service not available", "search_contracts")
        
        try:
            # Create contract based on security type
            if sec_type == "STK":
                contract = Stock(pattern, exchange, currency)
            elif sec_type == "CASH":
                contract = Forex(pattern)
            elif sec_type == "FUT":
                contract = Future(pattern, exchange)
            elif sec_type == "OPT":
                contract = Option(pattern, exchange, currency=currency)
            else:
                contract = Contract(symbol=pattern, secType=sec_type, exchange=exchange, currency=currency)
            
            # Qualify the contract to get details
            qualified_contracts = await self.ibkr_service.ib.qualifyContractsAsync(contract)
            
            contracts_data = []
            for qc in qualified_contracts:
                contract_info = {
                    'symbol': qc.symbol,
                    'sec_type': qc.secType,
                    'exchange': qc.exchange,
                    'primary_exchange': qc.primaryExchange,
                    'currency': qc.currency,
                    'local_symbol': qc.localSymbol,
                    'trading_class': qc.tradingClass,
                    'con_id': qc.conId,
                    'description': getattr(qc, 'longName', ''),
                    'industry': getattr(qc, 'industry', ''),
                    'category': getattr(qc, 'category', '')
                }
                
                # Add specific fields for different security types
                if sec_type == "OPT":
                    contract_info.update({
                        'strike': getattr(qc, 'strike', 0),
                        'right': getattr(qc, 'right', ''),
                        'expiry': getattr(qc, 'lastTradeDateOrContractMonth', '')
                    })
                elif sec_type == "FUT":
                    contract_info.update({
                        'expiry': getattr(qc, 'lastTradeDateOrContractMonth', ''),
                        'multiplier': getattr(qc, 'multiplier', '')
                    })
                
                contracts_data.append(contract_info)
            
            return self.create_success_response({
                "pattern": pattern,
                "sec_type": sec_type,
                "contracts": contracts_data,
                "total_found": len(contracts_data)
            })
            
        except Exception as e:
            logger.error(f"❌ Error searching contracts: {e}")
            return self.create_error_response(str(e), "search_contracts")
    
    async def get_contract_details(self, symbol: str, sec_type: str = "STK", exchange: str = "SMART", currency: str = "USD") -> Dict[str, Any]:
        """Get detailed contract information"""
        if not self.ensure_service_available() or not self.ibkr_service:
            return self.create_error_response("IBKR service not available", "get_contract_details")
        
        try:
            # Create and qualify contract
            if sec_type == "STK":
                contract = Stock(symbol, exchange, currency)
            elif sec_type == "CASH":
                contract = Forex(symbol)
            elif sec_type == "FUT":
                contract = Future(symbol, exchange)
            else:
                contract = Contract(symbol=symbol, secType=sec_type, exchange=exchange, currency=currency)
            
            qualified_contracts = await self.ibkr_service.ib.qualifyContractsAsync(contract)
            
            if not qualified_contracts:
                return self.create_error_response(f"Contract not found: {symbol}", "get_contract_details")
            
            contract = qualified_contracts[0]
            
            # Get contract details
            contract_details = await self.ibkr_service.ib.reqContractDetailsAsync(contract)
            
            if contract_details:
                cd = contract_details[0]
                details = {
                    'contract': {
                        'symbol': contract.symbol,
                        'sec_type': contract.secType,
                        'exchange': contract.exchange,
                        'primary_exchange': contract.primaryExchange,
                        'currency': contract.currency,
                        'local_symbol': contract.localSymbol,
                        'con_id': contract.conId
                    },
                    'details': {
                        'market_name': cd.marketName,
                        'min_tick': cd.minTick,
                        'order_types': cd.orderTypes,
                        'valid_exchanges': cd.validExchanges,
                        'price_magnifier': cd.priceMagnifier,
                        'underlying_con_id': cd.underConId,
                        'long_name': cd.longName,
                        'contract_month': cd.contractMonth,
                        'industry': cd.industry,
                        'category': cd.category,
                        'subcategory': cd.subcategory,
                        'time_zone': cd.timeZoneId,
                        'trading_hours': cd.tradingHours,
                        'liquid_hours': cd.liquidHours
                    }
                }
                
                return self.create_success_response({
                    "symbol": symbol,
                    "contract_details": details
                })
            
            return self.create_error_response("No contract details available", "get_contract_details")
            
        except Exception as e:
            logger.error(f"❌ Error getting contract details: {e}")
            return self.create_error_response(str(e), "get_contract_details")
    
    async def search_forex_pairs(self, base_currency: str = "USD", quote_currency: str = "EUR") -> Dict[str, Any]:
        """Search for forex currency pairs"""
        if not self.ensure_service_available() or not self.ibkr_service:
            return self.create_error_response("IBKR service not available", "search_forex_pairs")
        
        try:
            # Create forex contract - Forex expects the pair without dots (e.g., "EURUSD")
            # Try multiple formats to maximize compatibility
            forex_pairs_to_try = [
                f"{base_currency}{quote_currency}",  # e.g., "EURUSD"
                f"{base_currency}.{quote_currency}",  # e.g., "EUR.USD"
                f"{quote_currency}{base_currency}",  # Reverse pair
            ]
            
            all_forex_pairs = []
            for forex_symbol in forex_pairs_to_try:
                try:
                    # For Forex, we need to be specific about the contract type
                    contract = Contract()
                    contract.symbol = base_currency
                    contract.secType = "CASH"
                    contract.currency = quote_currency
                    contract.exchange = "IDEALPRO"  # Primary forex exchange for IB
            
                    qualified_contracts = await self.ibkr_service.ib.qualifyContractsAsync(contract)
                    
                    for qc in qualified_contracts:
                        # Avoid duplicates
                        if not any(fp['con_id'] == qc.conId for fp in all_forex_pairs):
                            pair_info = {
                                'symbol': qc.symbol,
                                'local_symbol': qc.localSymbol,
                                'exchange': qc.exchange,
                                'currency': qc.currency,
                                'con_id': qc.conId,
                                'base_currency': qc.symbol,  # IB uses symbol as base currency
                                'quote_currency': qc.currency,  # And currency as quote currency
                                'pair_string': f"{qc.symbol}{qc.currency}"
                            }
                            all_forex_pairs.append(pair_info)
                except Exception as e:
                    logger.debug(f"Could not qualify forex pair {forex_symbol}: {e}")
                    continue
            
            # If no results with the specific pairs, try a general search
            if not all_forex_pairs:
                # Try searching with just the base currency
                contract = Contract()
                contract.symbol = base_currency
                contract.secType = "CASH"
                contract.exchange = "IDEALPRO"
                
                try:
                    qualified_contracts = await self.ibkr_service.ib.qualifyContractsAsync(contract)
                    for qc in qualified_contracts:
                        pair_info = {
                            'symbol': qc.symbol,
                            'local_symbol': qc.localSymbol,
                            'exchange': qc.exchange,
                            'currency': qc.currency,
                            'con_id': qc.conId,
                            'base_currency': qc.symbol,
                            'quote_currency': qc.currency,
                            'pair_string': f"{qc.symbol}{qc.currency}"
                        }
                        all_forex_pairs.append(pair_info)
                except Exception as e:
                    logger.debug(f"General forex search failed: {e}")
            
            return self.create_success_response({
                "base_currency": base_currency,
                "quote_currency": quote_currency,
                "forex_pairs": all_forex_pairs,
                "total_found": len(all_forex_pairs)
            })
            
        except Exception as e:
            logger.error(f"❌ Error searching forex pairs: {e}")
            return self.create_error_response(str(e), "search_forex_pairs")

    async def search_futures(self, symbol: str, exchange: str = "CME", currency: str = "USD", include_expired: bool = False) -> Dict[str, Any]:
        """Search for futures contracts"""
        if not self.ensure_service_available() or not self.ibkr_service:
            return self.create_error_response("IBKR service not available", "search_futures")

        try:
            # For futures, we need to be more flexible with the contract specification
            # Try both specific contract and continuous contract
            contracts_to_try = []
            
            # 1. Try continuous contract (front month)
            cont_contract = Contract()
            cont_contract.symbol = symbol
            cont_contract.secType = "CONTFUT"  # Continuous future
            cont_contract.exchange = exchange
            cont_contract.currency = currency
            contracts_to_try.append(("continuous", cont_contract))
            
            # 2. Try generic future contract
            fut_contract = Contract()
            fut_contract.symbol = symbol
            fut_contract.secType = "FUT"
            fut_contract.exchange = exchange
            fut_contract.currency = currency
            fut_contract.includeExpired = include_expired
            contracts_to_try.append(("generic", fut_contract))
            
            all_futures_contracts = []
            
            for contract_type, contract in contracts_to_try:
                try:
                    qualified_contracts = await self.ibkr_service.ib.qualifyContractsAsync(contract)

                    for qc in qualified_contracts:
                        # Avoid duplicates
                        if not any(fc['con_id'] == qc.conId for fc in all_futures_contracts):
                            contract_info = {
                                'symbol': qc.symbol,
                                'local_symbol': qc.localSymbol,
                                'exchange': qc.exchange,
                                'currency': qc.currency,
                                'con_id': qc.conId,
                                'expiry': getattr(qc, 'lastTradeDateOrContractMonth', ''),
                                'multiplier': getattr(qc, 'multiplier', ''),
                                'trading_class': qc.tradingClass,
                                'contract_type': contract_type,
                                'sec_type': qc.secType
                            }

                            # Filter expired contracts if requested
                            if not include_expired and contract_info['expiry']:
                                from datetime import datetime
                                try:
                                    # Handle both YYYYMMDD and YYYYMM formats
                                    expiry_str = contract_info['expiry']
                                    if len(expiry_str) == 6:  # YYYYMM format
                                        expiry_date = datetime.strptime(expiry_str + "01", '%Y%m%d')
                                        # Use last day of month for comparison
                                        import calendar
                                        last_day = calendar.monthrange(expiry_date.year, expiry_date.month)[1]
                                        expiry_date = expiry_date.replace(day=last_day)
                                    else:  # YYYYMMDD format
                                        expiry_date = datetime.strptime(expiry_str, '%Y%m%d')
                                    
                                    if expiry_date < datetime.now():
                                        continue
                                except Exception as e:
                                    logger.debug(f"Could not parse expiry date {contract_info['expiry']}: {e}")
                                    # Include if we can't parse the date

                            all_futures_contracts.append(contract_info)
                except Exception as e:
                    logger.debug(f"Could not qualify {contract_type} futures contract: {e}")
                    continue
            
            # If we didn't find any contracts, try requesting contract details
            if not all_futures_contracts and symbol:
                # Sometimes we need to be more specific about common futures symbols
                common_mappings = {
                    "ES": {"exchange": "CME", "currency": "USD", "multiplier": "50"},  # E-mini S&P 500
                    "NQ": {"exchange": "CME", "currency": "USD", "multiplier": "20"},  # E-mini Nasdaq
                    "CL": {"exchange": "NYMEX", "currency": "USD", "multiplier": "1000"},  # Crude Oil
                    "GC": {"exchange": "COMEX", "currency": "USD", "multiplier": "100"},  # Gold
                    "EUR": {"exchange": "CME", "currency": "USD", "multiplier": "125000"},  # Euro FX
                }
                
                if symbol.upper() in common_mappings:
                    mapping = common_mappings[symbol.upper()]
                    specific_contract = Contract()
                    specific_contract.symbol = symbol.upper()
                    specific_contract.secType = "FUT"
                    specific_contract.exchange = mapping["exchange"]
                    specific_contract.currency = mapping["currency"]
                    specific_contract.multiplier = mapping["multiplier"]
                    specific_contract.includeExpired = include_expired
                    
                    try:
                        qualified_contracts = await self.ibkr_service.ib.qualifyContractsAsync(specific_contract)
                        for qc in qualified_contracts:
                            contract_info = {
                                'symbol': qc.symbol,
                                'local_symbol': qc.localSymbol,
                                'exchange': qc.exchange,
                                'currency': qc.currency,
                                'con_id': qc.conId,
                                'expiry': getattr(qc, 'lastTradeDateOrContractMonth', ''),
                                'multiplier': getattr(qc, 'multiplier', ''),
                                'trading_class': qc.tradingClass,
                                'contract_type': 'specific',
                                'sec_type': qc.secType
                            }
                            all_futures_contracts.append(contract_info)
                    except Exception as e:
                        logger.debug(f"Could not qualify specific futures contract for {symbol}: {e}")

            return self.create_success_response({
                "symbol": symbol,
                "exchange": exchange,
                "futures_contracts": all_futures_contracts,
                "total_found": len(all_futures_contracts),
                "include_expired": include_expired
            })

        except Exception as e:
            logger.error(f"❌ Error searching futures: {e}")
            return self.create_error_response(str(e), "search_futures")

    async def search_options(self, underlying_symbol: str, expiry: Optional[str] = None, strike: Optional[float] = None, right: str = "C") -> Dict[str, Any]:
        """Search for options contracts"""
        if not self.ensure_service_available() or not self.ibkr_service:
            return self.create_error_response("IBKR service not available", "search_options")

        try:
            # Create options contract
            contract = Option(underlying_symbol, expiry or '', strike or 0, right)

            qualified_contracts = await self.ibkr_service.ib.qualifyContractsAsync(contract)

            options_contracts = []
            for qc in qualified_contracts:
                contract_info = {
                    'symbol': qc.symbol,
                    'local_symbol': qc.localSymbol,
                    'exchange': qc.exchange,
                    'currency': qc.currency,
                    'con_id': qc.conId,
                    'strike': getattr(qc, 'strike', 0),
                    'right': getattr(qc, 'right', ''),
                    'expiry': getattr(qc, 'lastTradeDateOrContractMonth', ''),
                    'multiplier': getattr(qc, 'multiplier', ''),
                    'trading_class': qc.tradingClass
                }
                options_contracts.append(contract_info)

            return self.create_success_response({
                "underlying_symbol": underlying_symbol,
                "expiry": expiry,
                "strike": strike,
                "right": right,
                "options_contracts": options_contracts,
                "total_found": len(options_contracts)
            })

        except Exception as e:
            logger.error(f"❌ Error searching options: {e}")
            return self.create_error_response(str(e), "search_options")

    async def search_stocks(self, pattern: str, exchange: str = "SMART", currency: str = "USD") -> Dict[str, Any]:
        """Search for stock contracts"""
        return await self.search_contracts(pattern=pattern, sec_type="STK", exchange=exchange, currency=currency)

    async def get_contract_specifications(self, symbol: str, sec_type: str = "STK", exchange: str = "SMART") -> Dict[str, Any]:
        """Get contract specifications and trading details"""
        return await self.get_contract_details(symbol=symbol, sec_type=sec_type, exchange=exchange)

    async def validate_contract(self, symbol: str, sec_type: str = "STK", exchange: str = "SMART", currency: str = "USD") -> Dict[str, Any]:
        """Validate if a contract exists and is tradeable"""
        if not self.ensure_service_available() or not self.ibkr_service:
            return self.create_error_response("IBKR service not available", "validate_contract")

        try:
            # Create contract
            if sec_type == "STK":
                contract = Stock(symbol, exchange, currency)
            elif sec_type == "CASH":
                contract = Forex(symbol)
            elif sec_type == "FUT":
                contract = Future(symbol, exchange)
            else:
                contract = Contract(symbol=symbol, secType=sec_type, exchange=exchange, currency=currency)

            # Try to qualify the contract
            qualified_contracts = await self.ibkr_service.ib.qualifyContractsAsync(contract)

            if qualified_contracts:
                qc = qualified_contracts[0]
                return self.create_success_response({
                    "valid": True,
                    "symbol": symbol,
                    "contract": {
                        'symbol': qc.symbol,
                        'sec_type': qc.secType,
                        'exchange': qc.exchange,
                        'currency': qc.currency,
                        'con_id': qc.conId,
                        'local_symbol': qc.localSymbol
                    },
                    "message": "Contract is valid and tradeable"
                })
            else:
                return self.create_success_response({
                    "valid": False,
                    "symbol": symbol,
                    "message": "Contract not found or not tradeable"
                })

        except Exception as e:
            logger.error(f"❌ Error validating contract: {e}")
            return self.create_error_response(str(e), "validate_contract")
