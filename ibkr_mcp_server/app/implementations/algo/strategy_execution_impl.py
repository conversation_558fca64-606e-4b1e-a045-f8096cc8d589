"""
Strategy Execution Implementation

Focused implementation for algorithmic trading strategy execution.
Contains only the business logic for strategy management.
"""
import logging
from typing import Dict, List, Any, Optional
from decimal import Decimal
from datetime import datetime

from services.algo import (
    StrategyType,
    StrategyParameters,
    StrategyExecutionService
)

logger = logging.getLogger('strategy-execution-impl')


class StrategyExecutionImpl:
    """
    Implementation for strategy execution operations.
    
    Handles the business logic for:
    - Strategy parameter validation
    - Strategy execution coordination
    - Strategy lifecycle management
    """
    
    def __init__(self, strategy_service: Optional[StrategyExecutionService] = None):
        self.strategy_service = strategy_service
        self.logger = logger
    
    async def initialize(self, strategy_service: StrategyExecutionService):
        """Initialize with strategy service"""
        self.strategy_service = strategy_service
        logger.info("Strategy execution implementation initialized")
    
    async def execute_strategy(self, strategy_type: str, symbols: List[str], 
                              max_position_size: float, risk_limit: float, 
                              account: str, custom_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute algorithmic trading strategy
        
        Business logic:
        1. Validate input parameters
        2. Create strategy parameters object
        3. Delegate to strategy service
        4. Handle results and errors
        """
        if not self.strategy_service:
            return {"status": "error", "message": "Strategy service not initialized"}
        
        try:
            # Validate parameters
            validation_result = self._validate_strategy_params(
                strategy_type, symbols, max_position_size, risk_limit, account
            )
            if validation_result:
                return validation_result
            
            # Convert to service-level parameters
            strategy_params = self._build_strategy_parameters(
                strategy_type, symbols, max_position_size, risk_limit, account, custom_params
            )
            
            # Execute via strategy service
            result = await self.strategy_service.start_strategy(
                strategy_name=strategy_type,
                strategy_id=f"{strategy_type}_{hash(str(symbols))}",
                account=account,
                symbols=symbols,
                parameters=custom_params or {},
                risk_limits={
                    "max_position_size": Decimal(str(max_position_size)),
                    "risk_limit": Decimal(str(risk_limit))
                }
            )
            
            return self._format_strategy_result(result)
            
        except Exception as e:
            logger.error(f"Strategy execution failed: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    async def stop_strategy(self, strategy_id: str) -> Dict[str, Any]:
        """Stop running strategy"""
        if not self.strategy_service:
            return {"status": "error", "message": "Strategy service not initialized"}
        
        try:
            result = await self.strategy_service.stop_strategy(strategy_id)
            return result
        except Exception as e:
            logger.error(f"Failed to stop strategy: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    async def start_trading_strategy(self, strategy_type: str, parameters: Dict[str, Any],
                                   account: str) -> Dict[str, Any]:
        """
        Start a new trading strategy
        
        Args:
            strategy_type: Type of strategy to start
            parameters: Strategy configuration parameters
            account: Trading account
            
        Returns:
            Strategy startup result with strategy ID
        """
        if not self.strategy_service:
            return {"status": "error", "message": "Strategy service not initialized"}
            
        try:
            # Validate strategy type
            if strategy_type not in ["momentum", "mean_reversion", "arbitrage", "market_making"]:
                return {"status": "error", "message": f"Invalid strategy type: {strategy_type}"}
            
            # Validate account
            if not account or len(account.strip()) == 0:
                return {"status": "error", "message": "Account is required"}
            
            result = await self.strategy_service.start_strategy(
                strategy_name=strategy_type,
                strategy_id=f"{strategy_type}_{account}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                account=account,
                symbols=parameters.get("symbols", []),
                parameters=parameters,
                risk_limits={k: Decimal(str(v)) for k, v in parameters.get("risk_limits", {}).items()}
            )
            self.logger.info(f"Started {strategy_type} strategy for account {account}")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to start strategy {strategy_type}: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    async def stop_trading_strategy(self, strategy_id: str) -> Dict[str, Any]:
        """
        Stop a running trading strategy
        
        Args:
            strategy_id: ID of strategy to stop
            
        Returns:
            Strategy shutdown result
        """
        if not self.strategy_service:
            return {"status": "error", "message": "Strategy service not initialized"}
            
        try:
            if not strategy_id or len(strategy_id.strip()) == 0:
                return {"status": "error", "message": "Strategy ID is required"}
            
            result = await self.strategy_service.stop_strategy(strategy_id)
            self.logger.info(f"Stopped strategy {strategy_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to stop strategy {strategy_id}: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    async def get_strategy_status(self, strategy_id: str) -> Dict[str, Any]:
        """
        Get status of a trading strategy
        
        Args:
            strategy_id: ID of strategy to check
            
        Returns:
            Strategy status information
        """
        if not self.strategy_service:
            return {"status": "error", "message": "Strategy service not initialized"}
            
        try:
            if not strategy_id or len(strategy_id.strip()) == 0:
                return {"status": "error", "message": "Strategy ID is required"}
            
            result = await self.strategy_service.get_strategy_status(strategy_id)
            self.logger.debug(f"Retrieved status for strategy {strategy_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to get strategy status for {strategy_id}: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    async def get_active_algo_strategies(self) -> Dict[str, Any]:
        """
        Get list of all active algorithmic strategies
        
        Returns:
            List of active strategies with their details
        """
        if not self.strategy_service:
            return {"status": "error", "message": "Strategy service not initialized"}
            
        try:
            result = await self.strategy_service.get_active_strategies()
            self.logger.debug("Retrieved list of active algorithmic strategies")
            return {"status": "success", "strategies": result}
            
        except Exception as e:
            self.logger.error(f"Failed to get active strategies: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    def _validate_strategy_params(self, strategy_type: str, symbols: List[str], 
                                 max_position_size: float, risk_limit: float, 
                                 account: str) -> Optional[Dict[str, Any]]:
        """Validate strategy parameters"""
        if not symbols:
            return {"status": "error", "message": "At least one symbol is required"}
        
        if max_position_size <= 0:
            return {"status": "error", "message": "Max position size must be positive"}
        
        if risk_limit <= 0:
            return {"status": "error", "message": "Risk limit must be positive"}
        
        if not account:
            return {"status": "error", "message": "Account is required"}
        
        # Validate strategy type
        try:
            StrategyType(strategy_type)
        except ValueError:
            return {"status": "error", "message": f"Invalid strategy type: {strategy_type}"}
        
        return None
    
    def _build_strategy_parameters(self, strategy_type: str, symbols: List[str], 
                                  max_position_size: float, risk_limit: float, 
                                  account: str, custom_params: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Build strategy parameters object"""
        return {
            "strategy_type": strategy_type,
            "symbols": symbols,
            "max_position_size": max_position_size,
            "risk_limit": risk_limit,
            "account": account,
            "custom_params": custom_params or {}
        }
    
    def _format_strategy_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Format strategy execution result"""
        return {
            "status": result.get("status", "unknown"),
            "strategy_id": result.get("strategy_id"),
            "message": result.get("message", "Strategy execution completed"),
            "details": result
        }
