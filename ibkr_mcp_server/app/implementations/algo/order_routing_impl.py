"""
Order Routing Implementation

Layer 3 implementation for smart order routing and microstructure analysis.
Focused on order execution and market microstructure operations.
"""
import logging
from typing import Dict, Any, Optional
from decimal import Decimal

from services.algo import (
    AdvancedOrderRoutingService
)
from services.algo.order_routing_service import (
    RoutingConfig,
    RoutingAlgorithm
)

logger = logging.getLogger('order-routing-impl')


class OrderRoutingImpl:
    """Order routing and market microstructure business logic implementation"""
    
    def __init__(self, order_service: AdvancedOrderRoutingService):
        self.order_service = order_service
        self.logger = logger
    
    async def route_smart_order(
        self,
        symbol: str,
        action: str,
        quantity: int,
        account: str,
        order_type: str = "MKT",
        limit_price: Optional[float] = None,
        time_in_force: str = "DAY"
    ) -> Dict[str, Any]:
        """
        Route smart order with optimal execution
        
        Args:
            symbol: Symbol to trade
            action: BUY or SELL
            quantity: Order quantity
            account: Account ID for the order
            order_type: Order type (MKT, LMT, STP, etc.)
            limit_price: Limit price for limit orders
            time_in_force: Time in force (DAY, GTC, IOC, FOK)
            
        Returns:
            Order routing result with execution details
        """
        try:
            # Validate action
            if action.upper() not in ["BUY", "SELL"]:
                return {"status": "error", "message": "Action must be BUY or SELL"}
            
            # Validate quantity
            if quantity <= 0:
                return {"status": "error", "message": "Quantity must be positive"}
            
            # Convert strings to enums - use string validation instead
            valid_order_types = ["MKT", "LMT", "STP", "STP_LMT", "TRAIL", "MOC", "LOC"]
            valid_tif = ["DAY", "GTC", "IOC", "FOK", "OPG", "DTC"]
            
            if order_type.upper() not in valid_order_types:
                return {"status": "error", "message": f"Invalid order type: {order_type}"}
            
            if time_in_force.upper() not in valid_tif:
                return {"status": "error", "message": f"Invalid time in force: {time_in_force}"}
            
            # Validate limit price for limit orders
            if order_type.upper() in ["LMT", "STP_LMT"] and limit_price is None:
                return {"status": "error", "message": f"{order_type} orders require a limit price"}
            
            # Create routing configuration
            config = RoutingConfig(
                algorithm=RoutingAlgorithm.SMART,  # Default to SMART routing
                symbol=symbol,
                action=action.upper(),
                quantity=Decimal(str(quantity)),
                account=account,
                urgency="Normal",
                custom_params={
                    "order_type": order_type.upper(),
                    "limit_price": limit_price,
                    "time_in_force": time_in_force.upper()
                }
            )
            
            result = await self.order_service.route_order(config=config)
            
            # Convert RoutingResult to Dict for consistent API
            return {
                "status": "success",
                "order_id": result.order_id,
                "algorithm": result.routing_algorithm.value,
                "exchanges_used": result.exchanges_used,
                "avg_price": float(result.avg_price),
                "execution_time": str(result.execution_time),
                "metrics": result.metrics
            }
            
        except Exception as e:
            self.logger.error(f"Failed to route order for {symbol}: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    async def analyze_market_microstructure(
        self,
        symbol: str,
        timeframe: str = "1min",
        lookback_periods: int = 100
    ) -> Dict[str, Any]:
        """
        Analyze market microstructure patterns
        
        Args:
            symbol: Symbol to analyze
            timeframe: Time interval for analysis
            lookback_periods: Number of periods to analyze
            
        Returns:
            Microstructure analysis results
        """
        try:
            # Validate timeframe
            valid_timeframes = ["1sec", "5sec", "15sec", "30sec", "1min", "2min", "5min", "15min"]
            if timeframe not in valid_timeframes:
                return {
                    "status": "error", 
                    "message": f"Invalid timeframe. Valid options: {valid_timeframes}"
                }
            
            # Validate lookback periods
            if lookback_periods <= 0:
                return {"status": "error", "message": "Lookback periods must be positive"}
            
            if lookback_periods > 1000:  # Reasonable limit
                self.logger.warning(f"Limiting lookback periods from {lookback_periods} to 1000")
                lookback_periods = 1000
            
            result = await self.order_service.analyze_market_microstructure(
                symbol=symbol,
                size=lookback_periods
            )
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to analyze microstructure for {symbol}: {str(e)}")
            return {"status": "error", "message": str(e)}
