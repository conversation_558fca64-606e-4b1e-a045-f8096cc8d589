"""
Backtesting Implementation - Layer 3: Implementation
Handles actual backtesting and strategy optimization operations.
Generated by ArchitectureGenerator.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta, date
from uuid import UUID, uuid4
from decimal import Decimal
from implementations.base_implementation import BaseImplementation

logger = logging.getLogger('implementations.backtesting.backtesting_impl')

class BacktestingImplementation(BaseImplementation):
    """Implementation for backtesting and strategy optimization operations"""
    
    def __init__(self, ibkr_service=None):
        super().__init__()
        self.ibkr_service = ibkr_service
        if not self.ibkr_service:
            logger.warning("BacktestingImplementation initialized without IBKR service")
    
    def _safe_scalar_to_float(self, value) -> float:
        """Safely convert pandas scalar to float, handling NaN and complex values"""
        try:
            if pd.isna(value):
                return 0.0
            # For pandas scalars, use .item() to extract the Python scalar
            if hasattr(value, 'item'):
                return float(value.item())
            return float(value)
        except (ValueError, TypeError, OverflowError):
            return 0.0

    async def run_backtest(self, strategy_config: Dict[str, Any], start_date: str, end_date: str, initial_capital: float = 100000) -> Dict[str, Any]:
        """Run a backtest on historical data"""
        try:
            # Validate inputs
            if not strategy_config:
                return {"status": "error", "message": "Strategy configuration is required"}
            
            # Parse dates
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            
            if start_dt >= end_dt:
                return {"status": "error", "message": "Start date must be before end date"}
            
            # Get symbols from strategy config
            symbols = strategy_config.get('symbols', ['SPY'])
            strategy_type = strategy_config.get('type', 'buy_and_hold')
            
            # Simulate backtest results (placeholder implementation)
            # In a real implementation, this would:
            # 1. Fetch historical data for the symbols
            # 2. Apply the strategy logic
            # 3. Calculate returns and metrics
            
            num_days = (end_dt - start_dt).days
            
            # Generate synthetic returns for demonstration
            np.random.seed(42)  # For reproducible results
            daily_returns = np.random.normal(0.0008, 0.02, num_days)  # ~20% annual vol, 20% annual return
            
            # Calculate cumulative returns
            cumulative_returns = np.cumprod(1 + daily_returns)
            final_value = initial_capital * cumulative_returns[-1]
            
            # Calculate basic metrics
            total_return = (final_value - initial_capital) / initial_capital
            annual_return = (final_value / initial_capital) ** (252 / num_days) - 1
            volatility = np.std(daily_returns) * np.sqrt(252)
            sharpe_ratio = annual_return / volatility if volatility > 0 else 0
            
            # Calculate max drawdown
            peak = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - peak) / peak
            max_drawdown = np.min(drawdown)
            
            return {
                "status": "success",
                "strategy_config": strategy_config,
                "backtest_period": {
                    "start_date": start_date,
                    "end_date": end_date,
                    "num_days": num_days
                },
                "performance": {
                    "initial_capital": initial_capital,
                    "final_value": final_value,
                    "total_return": total_return,
                    "annual_return": annual_return,
                    "volatility": volatility,
                    "sharpe_ratio": sharpe_ratio,
                    "max_drawdown": max_drawdown,
                    "calmar_ratio": annual_return / abs(max_drawdown) if max_drawdown != 0 else 0
                },
                "daily_returns": daily_returns.tolist()[:100],  # Limit for response size
                "cumulative_returns": cumulative_returns.tolist()[:100]
            }
            
        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            return {"status": "error", "message": str(e)}
    
    async def optimize_strategy(self, strategy_config: Dict[str, Any], parameter_ranges: Dict[str, Any], optimization_metric: str = "sharpe_ratio") -> Dict[str, Any]:
        """Optimize strategy parameters"""
        try:
            if not parameter_ranges:
                return {"status": "error", "message": "Parameter ranges are required for optimization"}
            
            # Simulate parameter optimization (placeholder implementation)
            # In a real implementation, this would:
            # 1. Generate parameter combinations
            # 2. Run backtests for each combination
            # 3. Find optimal parameters based on the metric
            
            optimization_results = []
            best_metric_value = float('-inf')
            best_parameters = {}
            
            # Generate a few sample parameter combinations
            for i in range(10):
                # Sample random parameters within ranges
                test_params = {}
                for param, param_range in parameter_ranges.items():
                    if isinstance(param_range, list) and len(param_range) == 2:
                        min_val, max_val = param_range
                        test_params[param] = np.random.uniform(min_val, max_val)
                    else:
                        test_params[param] = param_range
                
                # Simulate metric calculation
                np.random.seed(i)
                if optimization_metric == "sharpe_ratio":
                    metric_value = np.random.normal(1.2, 0.3)  # Simulate Sharpe ratios
                elif optimization_metric == "total_return":
                    metric_value = np.random.normal(0.15, 0.05)  # Simulate returns
                else:
                    metric_value = np.random.normal(0.1, 0.02)
                
                optimization_results.append({
                    "parameters": test_params,
                    "metric_value": metric_value,
                    "annual_return": np.random.normal(0.12, 0.03),
                    "volatility": np.random.normal(0.18, 0.02),
                    "max_drawdown": np.random.uniform(-0.25, -0.05)
                })
                
                if metric_value > best_metric_value:
                    best_metric_value = metric_value
                    best_parameters = test_params.copy()
            
            return {
                "status": "success",
                "optimization_metric": optimization_metric,
                "best_parameters": best_parameters,
                "best_metric_value": best_metric_value,
                "optimization_results": optimization_results,
                "total_combinations_tested": len(optimization_results)
            }
            
        except Exception as e:
            logger.error(f"Error optimizing strategy: {e}")
            return {"status": "error", "message": str(e)}
    
    async def walk_forward_analysis(self, strategy_config: Dict[str, Any], window_size: int = 252, step_size: int = 63) -> Dict[str, Any]:
        """Perform walk-forward analysis"""
        try:
            # Simulate walk-forward analysis (placeholder implementation)
            # In a real implementation, this would:
            # 1. Split data into overlapping windows
            # 2. Optimize on in-sample data
            # 3. Test on out-of-sample data
            # 4. Roll forward and repeat
            
            num_windows = 8  # Simulate 8 walk-forward windows
            wf_results = []
            
            for i in range(num_windows):
                np.random.seed(i + 100)
                
                # Simulate in-sample optimization results
                in_sample_return = np.random.normal(0.15, 0.03)
                in_sample_sharpe = np.random.normal(1.2, 0.2)
                
                # Simulate out-of-sample test results (typically worse than in-sample)
                out_sample_return = np.random.normal(0.10, 0.05)
                out_sample_sharpe = np.random.normal(0.8, 0.3)
                
                wf_results.append({
                    "window": i + 1,
                    "in_sample_period": f"Window {i+1} - In Sample",
                    "out_sample_period": f"Window {i+1} - Out Sample",
                    "in_sample_metrics": {
                        "annual_return": in_sample_return,
                        "sharpe_ratio": in_sample_sharpe,
                        "volatility": np.random.normal(0.18, 0.02)
                    },
                    "out_sample_metrics": {
                        "annual_return": out_sample_return,
                        "sharpe_ratio": out_sample_sharpe,
                        "volatility": np.random.normal(0.20, 0.03)
                    },
                    "degradation": {
                        "return_degradation": (in_sample_return - out_sample_return) / in_sample_return,
                        "sharpe_degradation": (in_sample_sharpe - out_sample_sharpe) / in_sample_sharpe
                    }
                })
            
            # Calculate overall statistics
            avg_in_sample_return = np.mean([w["in_sample_metrics"]["annual_return"] for w in wf_results])
            avg_out_sample_return = np.mean([w["out_sample_metrics"]["annual_return"] for w in wf_results])
            avg_degradation = np.mean([w["degradation"]["return_degradation"] for w in wf_results])
            
            return {
                "status": "success",
                "strategy_config": strategy_config,
                "window_size": window_size,
                "step_size": step_size,
                "num_windows": num_windows,
                "walk_forward_results": wf_results,
                "summary_statistics": {
                    "avg_in_sample_return": avg_in_sample_return,
                    "avg_out_sample_return": avg_out_sample_return,
                    "avg_degradation": avg_degradation,
                    "consistency_score": 1 - avg_degradation  # Simple consistency metric
                }
            }
            
        except Exception as e:
            logger.error(f"Error in walk-forward analysis: {e}")
            return {"status": "error", "message": str(e)}

    async def monte_carlo_simulation(self, strategy_config: Dict[str, Any], num_simulations: int = 1000, confidence_level: float = 0.95) -> Dict[str, Any]:
        """Run Monte Carlo simulation"""
        try:
            # Simulate Monte Carlo analysis (placeholder implementation)
            # In a real implementation, this would:
            # 1. Bootstrap historical returns or use parametric distributions
            # 2. Run many simulations with random return sequences
            # 3. Calculate distribution of outcomes

            np.random.seed(42)

            # Simulate final portfolio values from many runs
            final_values = []
            annual_returns = []
            max_drawdowns = []

            initial_capital = 100000

            for i in range(num_simulations):
                # Generate random daily returns for 1 year
                daily_returns = np.random.normal(0.0008, 0.02, 252)
                cumulative_returns = np.cumprod(1 + daily_returns)

                final_value = initial_capital * cumulative_returns[-1]
                annual_return = (final_value / initial_capital) - 1

                # Calculate max drawdown for this simulation
                peak = np.maximum.accumulate(cumulative_returns)
                drawdown = (cumulative_returns - peak) / peak
                max_dd = np.min(drawdown)

                final_values.append(final_value)
                annual_returns.append(annual_return)
                max_drawdowns.append(max_dd)

            # Calculate statistics
            final_values = np.array(final_values)
            annual_returns = np.array(annual_returns)
            max_drawdowns = np.array(max_drawdowns)

            # Calculate confidence intervals
            alpha = 1 - confidence_level
            lower_percentile = (alpha / 2) * 100
            upper_percentile = (1 - alpha / 2) * 100

            return {
                "status": "success",
                "strategy_config": strategy_config,
                "simulation_parameters": {
                    "num_simulations": num_simulations,
                    "confidence_level": confidence_level,
                    "initial_capital": initial_capital
                },
                "results": {
                    "final_values": {
                        "mean": np.mean(final_values),
                        "median": np.median(final_values),
                        "std": np.std(final_values),
                        "min": np.min(final_values),
                        "max": np.max(final_values),
                        f"percentile_{lower_percentile}": np.percentile(final_values, lower_percentile),
                        f"percentile_{upper_percentile}": np.percentile(final_values, upper_percentile)
                    },
                    "annual_returns": {
                        "mean": np.mean(annual_returns),
                        "median": np.median(annual_returns),
                        "std": np.std(annual_returns),
                        "min": np.min(annual_returns),
                        "max": np.max(annual_returns),
                        f"percentile_{lower_percentile}": np.percentile(annual_returns, lower_percentile),
                        f"percentile_{upper_percentile}": np.percentile(annual_returns, upper_percentile)
                    },
                    "max_drawdowns": {
                        "mean": np.mean(max_drawdowns),
                        "median": np.median(max_drawdowns),
                        "std": np.std(max_drawdowns),
                        "worst": np.min(max_drawdowns),
                        "best": np.max(max_drawdowns),
                        f"percentile_{lower_percentile}": np.percentile(max_drawdowns, lower_percentile),
                        f"percentile_{upper_percentile}": np.percentile(max_drawdowns, upper_percentile)
                    }
                },
                "risk_metrics": {
                    "probability_of_loss": np.sum(annual_returns < 0) / num_simulations,
                    "probability_of_large_loss": np.sum(annual_returns < -0.20) / num_simulations,
                    "expected_shortfall_5pct": np.mean(annual_returns[annual_returns <= np.percentile(annual_returns, 5)]),
                    "value_at_risk_5pct": np.percentile(annual_returns, 5)
                }
            }

        except Exception as e:
            logger.error(f"Error in Monte Carlo simulation: {e}")
            return {"status": "error", "message": str(e)}

    async def calculate_performance_metrics(self, returns: List[float], benchmark_returns: Optional[List[float]] = None) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics"""
        try:
            if not returns:
                return {"status": "error", "message": "Returns data is required"}

            returns_array = np.array(returns)

            # Basic metrics
            total_return = np.prod(1 + returns_array) - 1
            annual_return = np.mean(returns_array) * 252  # Assuming daily returns
            volatility = np.std(returns_array) * np.sqrt(252)

            # Risk-adjusted metrics
            sharpe_ratio = annual_return / volatility if volatility > 0 else 0

            # Downside metrics
            downside_returns = returns_array[returns_array < 0]
            downside_deviation = np.std(downside_returns) * np.sqrt(252) if len(downside_returns) > 0 else 0
            sortino_ratio = annual_return / downside_deviation if downside_deviation > 0 else 0

            # Drawdown metrics
            cumulative_returns = np.cumprod(1 + returns_array)
            peak = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - peak) / peak
            max_drawdown = np.min(drawdown)
            calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0

            metrics = {
                "status": "success",
                "basic_metrics": {
                    "total_return": total_return,
                    "annual_return": annual_return,
                    "volatility": volatility,
                    "num_periods": len(returns)
                },
                "risk_adjusted_metrics": {
                    "sharpe_ratio": sharpe_ratio,
                    "sortino_ratio": sortino_ratio,
                    "calmar_ratio": calmar_ratio
                },
                "drawdown_metrics": {
                    "max_drawdown": max_drawdown,
                    "avg_drawdown": np.mean(drawdown[drawdown < 0]) if np.any(drawdown < 0) else 0,
                    "drawdown_duration": len(drawdown[drawdown < 0])
                },
                "distribution_metrics": {
                    "skewness": self._safe_scalar_to_float(pd.Series(returns).skew()) if len(returns) > 2 else 0.0,
                    "kurtosis": self._safe_scalar_to_float(pd.Series(returns).kurtosis()) if len(returns) > 3 else 0.0,
                    "var_95": float(np.percentile(returns_array, 5)),
                    "cvar_95": float(np.mean(returns_array[returns_array <= np.percentile(returns_array, 5)]))
                }
            }

            # Add benchmark comparison if provided
            if benchmark_returns:
                benchmark_array = np.array(benchmark_returns)
                if len(benchmark_array) == len(returns_array):
                    excess_returns = returns_array - benchmark_array
                    tracking_error = np.std(excess_returns) * np.sqrt(252)
                    information_ratio = np.mean(excess_returns) * 252 / tracking_error if tracking_error > 0 else 0

                    # Beta calculation
                    covariance = np.cov(returns_array, benchmark_array)[0, 1]
                    benchmark_variance = np.var(benchmark_array)
                    beta = covariance / benchmark_variance if benchmark_variance > 0 else 0

                    # Alpha calculation (CAPM)
                    risk_free_rate = 0.02  # Assume 2% risk-free rate
                    benchmark_annual_return = np.mean(benchmark_array) * 252
                    alpha = annual_return - (risk_free_rate + beta * (benchmark_annual_return - risk_free_rate))

                    metrics["benchmark_comparison"] = {
                        "tracking_error": tracking_error,
                        "information_ratio": information_ratio,
                        "beta": beta,
                        "alpha": alpha,
                        "correlation": np.corrcoef(returns_array, benchmark_array)[0, 1]
                    }

            return metrics

        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return {"status": "error", "message": str(e)}

    async def compare_strategies(self, strategy_results: List[Dict[str, Any]], comparison_metrics: Optional[List[str]] = None) -> Dict[str, Any]:
        """Compare multiple strategy results"""
        try:
            if not strategy_results:
                return {"status": "error", "message": "Strategy results are required for comparison"}

            if comparison_metrics is None:
                comparison_metrics = ["annual_return", "sharpe_ratio", "max_drawdown", "volatility"]

            # Extract metrics for comparison
            comparison_data = {}
            strategy_names = []

            for i, result in enumerate(strategy_results):
                strategy_name = result.get("strategy_name", f"Strategy_{i+1}")
                strategy_names.append(strategy_name)

                performance = result.get("performance", {})
                comparison_data[strategy_name] = {}

                for metric in comparison_metrics:
                    comparison_data[strategy_name][metric] = performance.get(metric, 0)

            # Calculate rankings
            rankings = {}
            for metric in comparison_metrics:
                metric_values = [(name, data[metric]) for name, data in comparison_data.items()]

                # Sort based on metric (higher is better for most metrics, except drawdown and volatility)
                reverse_sort = metric not in ["max_drawdown", "volatility"]
                sorted_strategies = sorted(metric_values, key=lambda x: x[1], reverse=reverse_sort)

                rankings[metric] = {
                    "ranking": [{"strategy": name, "value": value, "rank": i+1}
                              for i, (name, value) in enumerate(sorted_strategies)],
                    "best_strategy": sorted_strategies[0][0],
                    "worst_strategy": sorted_strategies[-1][0]
                }

            # Calculate overall score (simple average of normalized ranks)
            overall_scores = {}
            for strategy_name in strategy_names:
                total_rank = sum(rankings[metric]["ranking"][
                    next(i for i, item in enumerate(rankings[metric]["ranking"])
                         if item["strategy"] == strategy_name)
                ]["rank"] for metric in comparison_metrics)

                overall_scores[strategy_name] = {
                    "total_rank": total_rank,
                    "average_rank": total_rank / len(comparison_metrics)
                }

            # Find overall best strategy
            best_overall = min(overall_scores.items(), key=lambda x: x[1]["average_rank"])

            return {
                "status": "success",
                "comparison_metrics": comparison_metrics,
                "strategy_comparison": comparison_data,
                "rankings": rankings,
                "overall_scores": overall_scores,
                "best_overall_strategy": {
                    "name": best_overall[0],
                    "average_rank": best_overall[1]["average_rank"]
                },
                "summary": {
                    "num_strategies": len(strategy_results),
                    "metrics_compared": len(comparison_metrics)
                }
            }

        except Exception as e:
            logger.error(f"Error comparing strategies: {e}")
            return {"status": "error", "message": str(e)}

    async def generate_backtest_report(self, backtest_results: Dict[str, Any], include_charts: bool = True) -> Dict[str, Any]:
        """Generate comprehensive backtest report"""
        try:
            if not backtest_results:
                return {"status": "error", "message": "Backtest results are required"}

            # Extract key information
            performance = backtest_results.get("performance", {})
            strategy_config = backtest_results.get("strategy_config", {})

            # Generate executive summary
            executive_summary = {
                "strategy_name": strategy_config.get("name", "Unnamed Strategy"),
                "strategy_type": strategy_config.get("type", "Unknown"),
                "backtest_period": backtest_results.get("backtest_period", {}),
                "key_metrics": {
                    "total_return": performance.get("total_return", 0),
                    "annual_return": performance.get("annual_return", 0),
                    "sharpe_ratio": performance.get("sharpe_ratio", 0),
                    "max_drawdown": performance.get("max_drawdown", 0)
                },
                "risk_assessment": self._assess_risk_level(performance),
                "recommendation": self._generate_recommendation(performance)
            }

            # Generate detailed analysis
            detailed_analysis = {
                "return_analysis": {
                    "consistency": "High" if performance.get("sharpe_ratio", 0) > 1.0 else "Medium" if performance.get("sharpe_ratio", 0) > 0.5 else "Low",
                    "risk_adjusted_performance": "Excellent" if performance.get("sharpe_ratio", 0) > 2.0 else "Good" if performance.get("sharpe_ratio", 0) > 1.0 else "Fair"
                },
                "risk_analysis": {
                    "drawdown_severity": "Low" if abs(performance.get("max_drawdown", 0)) < 0.10 else "Medium" if abs(performance.get("max_drawdown", 0)) < 0.20 else "High",
                    "volatility_level": "Low" if performance.get("volatility", 0) < 0.15 else "Medium" if performance.get("volatility", 0) < 0.25 else "High"
                }
            }

            # Chart data (placeholder for actual chart generation)
            chart_data = {}
            if include_charts:
                chart_data = {
                    "equity_curve": {
                        "description": "Portfolio value over time",
                        "data_points": len(backtest_results.get("cumulative_returns", [])),
                        "chart_type": "line"
                    },
                    "drawdown_chart": {
                        "description": "Drawdown periods",
                        "chart_type": "area"
                    },
                    "monthly_returns": {
                        "description": "Monthly return distribution",
                        "chart_type": "heatmap"
                    }
                }

            return {
                "status": "success",
                "report_generated": datetime.now().isoformat(),
                "executive_summary": executive_summary,
                "detailed_analysis": detailed_analysis,
                "full_performance_metrics": performance,
                "strategy_configuration": strategy_config,
                "charts": chart_data if include_charts else {},
                "recommendations": self._generate_detailed_recommendations(performance),
                "risk_warnings": self._generate_risk_warnings(performance)
            }

        except Exception as e:
            logger.error(f"Error generating backtest report: {e}")
            return {"status": "error", "message": str(e)}

    async def validate_strategy(self, strategy_config: Dict[str, Any], validation_period: str = "1Y") -> Dict[str, Any]:
        """Validate strategy on out-of-sample data"""
        try:
            # Simulate strategy validation (placeholder implementation)
            # In a real implementation, this would:
            # 1. Reserve recent data for out-of-sample testing
            # 2. Run the strategy on this data
            # 3. Compare results to in-sample performance

            # Simulate validation results
            np.random.seed(123)

            # Simulate in-sample vs out-of-sample performance
            in_sample_return = np.random.normal(0.12, 0.02)
            out_sample_return = np.random.normal(0.08, 0.04)  # Typically worse

            in_sample_sharpe = np.random.normal(1.1, 0.1)
            out_sample_sharpe = np.random.normal(0.7, 0.2)

            # Calculate degradation
            return_degradation = (in_sample_return - out_sample_return) / in_sample_return
            sharpe_degradation = (in_sample_sharpe - out_sample_sharpe) / in_sample_sharpe

            # Determine validation status
            validation_passed = (
                return_degradation < 0.3 and  # Less than 30% degradation in returns
                sharpe_degradation < 0.4 and  # Less than 40% degradation in Sharpe
                out_sample_sharpe > 0.5       # Minimum acceptable Sharpe ratio
            )

            return {
                "status": "success",
                "strategy_config": strategy_config,
                "validation_period": validation_period,
                "validation_results": {
                    "validation_passed": validation_passed,
                    "confidence_level": "High" if validation_passed else "Low",
                    "in_sample_metrics": {
                        "annual_return": in_sample_return,
                        "sharpe_ratio": in_sample_sharpe,
                        "volatility": np.random.normal(0.18, 0.02)
                    },
                    "out_sample_metrics": {
                        "annual_return": out_sample_return,
                        "sharpe_ratio": out_sample_sharpe,
                        "volatility": np.random.normal(0.22, 0.03)
                    },
                    "degradation_analysis": {
                        "return_degradation": return_degradation,
                        "sharpe_degradation": sharpe_degradation,
                        "acceptable_degradation": return_degradation < 0.3
                    }
                },
                "recommendations": [
                    "Strategy shows good out-of-sample performance" if validation_passed else "Strategy may be overfitted",
                    "Consider additional validation periods" if not validation_passed else "Strategy ready for live trading consideration",
                    "Monitor performance closely in live trading" if validation_passed else "Revise strategy parameters"
                ]
            }

        except Exception as e:
            logger.error(f"Error validating strategy: {e}")
            return {"status": "error", "message": str(e)}

    def _assess_risk_level(self, performance: Dict[str, Any]) -> str:
        """Assess overall risk level based on performance metrics"""
        max_dd = abs(performance.get("max_drawdown", 0))
        volatility = performance.get("volatility", 0)

        if max_dd > 0.25 or volatility > 0.30:
            return "High"
        elif max_dd > 0.15 or volatility > 0.20:
            return "Medium"
        else:
            return "Low"

    def _generate_recommendation(self, performance: Dict[str, Any]) -> str:
        """Generate overall recommendation"""
        sharpe = performance.get("sharpe_ratio", 0)
        max_dd = abs(performance.get("max_drawdown", 0))

        if sharpe > 1.5 and max_dd < 0.15:
            return "Highly Recommended"
        elif sharpe > 1.0 and max_dd < 0.20:
            return "Recommended"
        elif sharpe > 0.5:
            return "Consider with Caution"
        else:
            return "Not Recommended"

    async def save_results_to_db(self, backtest_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Save backtest results to Supabase database

        Args:
            backtest_results: Complete backtest results from run_backtest

        Returns:
            Dict containing save status and run_id
        """
        try:
            from app.database.repositories.backtesting_repositories import (
                BacktestRunRepository,
                BacktestTradeRepository,
                BacktestEquityCurveRepository
            )

            # Initialize repositories
            run_repo = BacktestRunRepository()
            trade_repo = BacktestTradeRepository()
            equity_repo = BacktestEquityCurveRepository()

            # Extract data from backtest results
            strategy_config = backtest_results.get('strategy_config', {})
            backtest_period = backtest_results.get('backtest_period', {})
            performance = backtest_results.get('performance', {})

            # Create run record
            run_data = {
                'strategy_name': strategy_config.get('name', strategy_config.get('type', 'Unknown')),
                'strategy_config': strategy_config,
                'start_date': backtest_period.get('start_date'),
                'end_date': backtest_period.get('end_date'),
                'initial_capital': Decimal(str(performance.get('initial_capital', 0))),
                'final_value': Decimal(str(performance.get('final_value', 0))),
                'total_return': Decimal(str(performance.get('total_return', 0))),
                'annual_return': Decimal(str(performance.get('annual_return', 0))),
                'volatility': Decimal(str(performance.get('volatility', 0))),
                'sharpe_ratio': Decimal(str(performance.get('sharpe_ratio', 0))) if performance.get('sharpe_ratio') else None,
                'max_drawdown': Decimal(str(performance.get('max_drawdown', 0))),
                'calmar_ratio': Decimal(str(performance.get('calmar_ratio', 0))) if performance.get('calmar_ratio') else None,
                'total_trades': 0,  # Will be updated if trades are provided
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': None,
                'profit_factor': None
            }

            # Save run record
            saved_run = await run_repo.create_run(run_data)
            run_id = UUID(saved_run['id'])

            # Save trades if provided
            trades_data = backtest_results.get('trades', [])
            if trades_data:
                trade_records = []
                winning_trades = 0
                losing_trades = 0

                for trade in trades_data:
                    pnl = trade.get('pnl', 0)
                    if pnl > 0:
                        winning_trades += 1
                    elif pnl < 0:
                        losing_trades += 1

                    trade_record = {
                        'backtest_run_id': run_id,
                        'symbol': trade.get('symbol', 'UNKNOWN'),
                        'side': trade.get('side', 'BUY'),
                        'quantity': Decimal(str(trade.get('quantity', 0))),
                        'entry_price': Decimal(str(trade.get('entry_price', 0))),
                        'exit_price': Decimal(str(trade.get('exit_price', 0))) if trade.get('exit_price') else None,
                        'entry_date': trade.get('entry_date'),
                        'exit_date': trade.get('exit_date'),
                        'pnl': Decimal(str(pnl)) if pnl else None,
                        'pnl_percent': Decimal(str(trade.get('pnl_percent', 0))) if trade.get('pnl_percent') else None,
                        'commission': Decimal(str(trade.get('commission', 0))),
                        'trade_duration_hours': trade.get('trade_duration_hours'),
                        'stop_loss': Decimal(str(trade.get('stop_loss', 0))) if trade.get('stop_loss') else None,
                        'take_profit': Decimal(str(trade.get('take_profit', 0))) if trade.get('take_profit') else None,
                        'trade_type': trade.get('trade_type', 'MARKET')
                    }
                    trade_records.append(trade_record)

                if trade_records:
                    await trade_repo.create_trades(trade_records)

                    # Update run statistics
                    total_trades = len(trade_records)
                    win_rate = winning_trades / total_trades if total_trades > 0 else 0

                    # Calculate profit factor
                    gross_profit = sum(t['pnl'] for t in trade_records if t['pnl'] and t['pnl'] > 0)
                    gross_loss = abs(sum(t['pnl'] for t in trade_records if t['pnl'] and t['pnl'] < 0))
                    profit_factor = gross_profit / gross_loss if gross_loss > 0 else None

                    # Update run record with trade statistics
                    await run_repo.update(run_id, {
                        'total_trades': total_trades,
                        'winning_trades': winning_trades,
                        'losing_trades': losing_trades,
                        'win_rate': Decimal(str(win_rate)),
                        'profit_factor': Decimal(str(profit_factor)) if profit_factor else None
                    })

            # Save equity curve if provided
            daily_returns = backtest_results.get('daily_returns', [])
            cumulative_returns = backtest_results.get('cumulative_returns', [])

            if daily_returns and cumulative_returns:
                equity_records = []
                start_date = datetime.strptime(backtest_period.get('start_date'), '%Y-%m-%d').date()
                initial_capital = performance.get('initial_capital', 100000)

                for i, (daily_ret, cum_ret) in enumerate(zip(daily_returns, cumulative_returns)):
                    current_date = start_date + timedelta(days=i)
                    equity_value = initial_capital * cum_ret

                    # Calculate drawdown (simplified)
                    peak_value = initial_capital * max(cumulative_returns[:i+1])
                    drawdown = (equity_value - peak_value) / peak_value if peak_value > 0 else 0

                    equity_record = {
                        'backtest_run_id': run_id,
                        'date': current_date,
                        'equity_value': Decimal(str(equity_value)),
                        'daily_return': Decimal(str(daily_ret)),
                        'cumulative_return': Decimal(str(cum_ret - 1)),  # Convert to percentage
                        'drawdown': Decimal(str(drawdown))
                    }
                    equity_records.append(equity_record)

                if equity_records:
                    await equity_repo.create_equity_points(equity_records)

            logger.info(f"Successfully saved backtest results to database with run_id: {run_id}")

            return {
                'status': 'success',
                'run_id': str(run_id),
                'message': f'Backtest results saved successfully',
                'records_created': {
                    'run': 1,
                    'trades': len(trades_data),
                    'equity_points': len(daily_returns)
                }
            }

        except Exception as e:
            logger.error(f"Error saving backtest results to database: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }

    def _generate_detailed_recommendations(self, performance: Dict[str, Any]) -> List[str]:
        """Generate detailed recommendations"""
        recommendations = []

        sharpe = performance.get("sharpe_ratio", 0)
        max_dd = abs(performance.get("max_drawdown", 0))
        volatility = performance.get("volatility", 0)

        if sharpe < 1.0:
            recommendations.append("Consider improving risk-adjusted returns")
        if max_dd > 0.20:
            recommendations.append("Implement stronger risk management")
        if volatility > 0.25:
            recommendations.append("Consider position sizing adjustments")

        return recommendations

    def _generate_risk_warnings(self, performance: Dict[str, Any]) -> List[str]:
        """Generate risk warnings"""
        warnings = []

        max_dd = abs(performance.get("max_drawdown", 0))
        if max_dd > 0.30:
            warnings.append("High drawdown risk - strategy may experience significant losses")

        return warnings
