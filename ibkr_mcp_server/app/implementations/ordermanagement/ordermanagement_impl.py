"""
OrderManagement Implementation - Layer 3: Business Logic
Core business logic for OrderManagement operations.
Integrated with existing OrderManagementService.
"""

from typing import Dict, Any, Optional, List
import logging
from datetime import datetime

# Fix import paths for the app structure
try:
    from services.order_management_service import OrderManagementService
    from services.ibkr_service import IBKRService
except ImportError:
    try:
        from app.services.order_management_service import OrderManagementService
        from app.services.ibkr_service import IBKRService
    except ImportError:
        from ibkr_mcp_server.app.services.order_management_service import OrderManagementService
        from ibkr_mcp_server.app.services.ibkr_service import IBKRService

try:
    from implementations.base_implementation import BaseImplementation
except ImportError:
    try:
        from app.implementations.base_implementation import BaseImplementation
    except ImportError:
        from ibkr_mcp_server.app.implementations.base_implementation import BaseImplementation

from ibkr_mcp_server.app.models.order_models import OrderInfo, OrderStatus

logger = logging.getLogger(__name__)

class OrderManagementImplementation(BaseImplementation):
    """Core implementation for OrderManagement operations"""
    
    def __init__(self, ibkr_service=None, **kwargs):
        # Call parent constructor which handles emergency service injection
        super().__init__(ibkr_service=ibkr_service, **kwargs)
        self.order_service = None
        self._initialize_order_service()
    
    def _initialize_order_service(self):
        """Initialize the order management service with proper error handling"""
        if self.ensure_service_available() and self.ibkr_service is not None:
            try:
                # Initialize the order management service with the IBKR service
                # Type assertion to help the type checker understand the type
                ibkr_svc: IBKRService = self.ibkr_service
                self.order_service = OrderManagementService(ibkr_svc)
                logger.info("OrderManagementService initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize OrderManagementService: {e}")
                self.order_service = None
        else:
            logger.warning("Cannot initialize OrderManagementService - IBKR service not available")
    
    def _ensure_order_service(self) -> bool:
        """Ensure order service is initialized before operations"""
        if not self.order_service:
            # Try to initialize again
            self._initialize_order_service()
        
        return self.order_service is not None
    
    async def place_order(
        self,
        symbol: str,
        action: str,
        quantity: float,
        order_type: str = "MKT",
        price: Optional[float] = None,
        stop_price: Optional[float] = None,
        time_in_force: str = "DAY",
        account: Optional[str] = None
    ) -> Dict[str, Any]:
        """Place a new order"""
        if not self._ensure_order_service():
            return self.create_error_response(
                "Order service not initialized. Please ensure IBKR is connected.",
                "place_order"
            )
        
        try:
            # Get default account if not provided
            if not account:
                # Get the default account from IBKRService
                if self.ibkr_service:
                    try:
                        accounts = await self.ibkr_service.get_accounts()
                        if accounts and 'accounts' in accounts and accounts['accounts']:
                            account = accounts['accounts'][0]['accountId']
                            logger.info(f"Using default account: {account}")
                        else:
                            return {"status": "error", "message": "No account available and none specified"}
                    except Exception as e:
                        logger.error(f"Failed to get accounts: {e}")
                        return {"status": "error", "message": f"Failed to get accounts: {str(e)}"}
                else:
                    return {"status": "error", "message": "IBKR service not available"}
            
            # Ensure account is a string
            account_str = str(account) if account else ""
            if not account_str:
                return {"status": "error", "message": "Valid account required"}
            
            # Delegate to the order service with correct parameter order
            if self.order_service is not None:
                return await self.order_service.place_order(
                    order_type=order_type,
                    symbol=symbol,
                    action=action,
                    quantity=quantity,
                    account=account_str,
                    price=price,
                    stop_price=stop_price,
                    time_in_force=time_in_force
                )
            else:
                return self.create_error_response("Order service not available", "place_order")
        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return self.create_error_response(str(e), "place_order")
    
    async def cancel_order(self, order_id: int) -> Dict[str, Any]:
        """Cancel an existing order"""
        if not self._ensure_order_service():
            return self.create_error_response(
                "Order service not initialized. Please ensure IBKR is connected.",
                "cancel_order"
            )
        
        try:
            if self.order_service is not None:
                return await self.order_service.cancel_order(order_id)
            else:
                return self.create_error_response("Order service not available", "cancel_order")
        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            return self.create_error_response(str(e), "cancel_order")
    
    async def modify_order(self, order_id: int, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Modify an existing order"""
        if not self._ensure_order_service():
            return self.create_error_response(
                "Order service not initialized. Please ensure IBKR is connected.",
                "modify_order"
            )
        
        try:
            if self.order_service is not None:
                return await self.order_service.modify_order(order_id, parameters)
            else:
                return self.create_error_response("Order service not available", "modify_order")
        except Exception as e:
            logger.error(f"Error modifying order: {e}")
            return self.create_error_response(str(e), "modify_order")
    
    async def get_order_status(self, order_id: int) -> Dict[str, Any]:
        """Get the status of an order"""
        if not self._ensure_order_service():
            return self.create_error_response(
                "Order service not initialized. Please ensure IBKR is connected.",
                "get_order_status"
            )
        
        try:
            if self.order_service is not None:
                return await self.order_service.get_order_status(order_id)
            else:
                return self.create_error_response("Order service not available", "get_order_status")
        except Exception as e:
            logger.error(f"Error getting order status: {e}")
            return self.create_error_response(str(e), "get_order_status")
    
    async def get_active_orders(self) -> Dict[str, Any]:
        """Get all active orders"""
        if not self._ensure_order_service():
            return self.create_error_response(
                "Order service not initialized. Please ensure IBKR is connected.",
                "get_active_orders"
            )
        
        try:
            if self.order_service is not None:
                return await self.order_service.get_active_orders()
            else:
                return self.create_error_response("Order service not available", "get_active_orders")
        except Exception as e:
            logger.error(f"Error getting active orders: {e}")
            return self.create_error_response(str(e), "get_active_orders")
    
    async def get_order_history(self, symbol: Optional[str] = None, 
                               account: Optional[str] = None,
                               start_date: Optional[str] = None,
                               end_date: Optional[str] = None) -> Dict[str, Any]:
        """Get order history with optional filtering"""
        if not self._ensure_order_service():
            return self.create_error_response(
                "Order service not initialized. Please ensure IBKR is connected.",
                "get_order_history"
            )
        
        try:
            if self.order_service is not None:
                return await self.order_service.get_order_history(
                    symbol=symbol,
                    account=account,
                    start_date=start_date,
                    end_date=end_date
                )
            else:
                return self.create_error_response("Order service not available", "get_order_history")
        except Exception as e:
            logger.error(f"Error getting order history: {e}")
            return self.create_error_response(str(e), "get_order_history")

    async def place_bracket_order(
        self,
        symbol: str,
        action: str,
        quantity: float,
        entry_price: float,
        profit_price: float,
        stop_price: float,
        account: Optional[str] = None
    ) -> Dict[str, Any]:
        """Place a bracket order with entry, profit target, and stop loss"""
        if not self.ensure_service_available():
            return self.create_error_response(
                "IBKR service not connected. Please ensure connection is established.",
                "place_bracket_order"
            )

        try:
            # Use the existing bracket order functionality from IBKR service
            if self.ibkr_service is not None:
                result = await self.ibkr_service.place_bracket_order(
                    symbol=symbol,
                    action=action,
                    quantity=quantity,
                    entry_price=entry_price,
                    profit_price=profit_price,
                    stop_price=stop_price,
                    account=account or "default"
                )
            else:
                return self.create_error_response("IBKR service not available", "place_bracket_order")

            return {
                "status": "success",
                "order_type": "bracket",
                "symbol": symbol,
                "action": action,
                "quantity": quantity,
                "entry_price": entry_price,
                "profit_price": profit_price,
                "stop_price": stop_price,
                "result": result
            }

        except Exception as e:
            logger.error(f"Error placing bracket order: {e}")
            return self.create_error_response(str(e), "place_bracket_order")

    async def place_adaptive_order(
        self,
        symbol: str,
        action: str,
        quantity: float,
        price: float,
        priority: str = "Normal",
        account: Optional[str] = None
    ) -> Dict[str, Any]:
        """Place an adaptive algorithm order"""
        if not self.ensure_service_available():
            return self.create_error_response(
                "IBKR service not connected. Please ensure connection is established.",
                "place_adaptive_order"
            )

        try:
            # Use the existing adaptive order functionality from IBKR service
            if self.ibkr_service is not None:
                result = await self.ibkr_service.place_adaptive_order(
                    symbol=symbol,
                    action=action,
                    quantity=quantity,
                    price=price,
                    priority=priority,
                    account=account or "default"
                )
            else:
                return self.create_error_response("IBKR service not available", "place_adaptive_order")

            return {
                "status": "success",
                "order_type": "adaptive",
                "symbol": symbol,
                "action": action,
                "quantity": quantity,
                "price": price,
                "priority": priority,
                "result": result
            }

        except Exception as e:
            logger.error(f"Error placing adaptive order: {e}")
            return self.create_error_response(str(e), "place_adaptive_order")

    async def place_auction_order(
        self,
        symbol: str,
        action: str,
        quantity: float,
        price: float,
        account: Optional[str] = None
    ) -> Dict[str, Any]:
        """Place an auction order for pre-market execution"""
        if not self.ensure_service_available():
            return self.create_error_response(
                "IBKR service not connected. Please ensure connection is established.",
                "place_auction_order"
            )

        try:
            # Use the existing auction order functionality from IBKR service
            if self.ibkr_service is not None:
                result = await self.ibkr_service.place_auction_order(
                    symbol=symbol,
                    action=action,
                    quantity=quantity,
                    price=price,
                    account=account or "default"
                )
            else:
                return self.create_error_response("IBKR service not available", "place_auction_order")

            return {
                "status": "success",
                "order_type": "auction",
                "symbol": symbol,
                "action": action,
                "quantity": quantity,
                "price": price,
                "result": result
            }

        except Exception as e:
            logger.error(f"Error placing auction order: {e}")
            return self.create_error_response(str(e), "place_auction_order")

    async def start_market_making(
        self,
        symbols: List[str],
        mode: str = "neutral",
        parameters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Start market making strategy for specified symbols"""
        try:
            # Simulate market making start (placeholder implementation)
            # In a real implementation, this would:
            # 1. Initialize market making strategy
            # 2. Start continuous bid/ask quote management
            # 3. Monitor positions and risk

            return {
                "status": "success",
                "message": f"Market making started for {len(symbols)} symbols",
                "symbols": symbols,
                "mode": mode,
                "parameters": parameters or {},
                "strategy_id": f"mm_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            }

        except Exception as e:
            logger.error(f"Error starting market making: {e}")
            return self.create_error_response(str(e), "start_market_making")

    async def stop_market_making(self, symbols: Optional[List[str]] = None) -> Dict[str, Any]:
        """Stop market making strategy"""
        try:
            # Simulate market making stop (placeholder implementation)
            # In a real implementation, this would:
            # 1. Cancel all active quotes
            # 2. Close positions if needed
            # 3. Stop the strategy

            return {
                "status": "success",
                "message": f"Market making stopped for {symbols if symbols else 'all symbols'}",
                "symbols_stopped": symbols or ["all"]
            }

        except Exception as e:
            logger.error(f"Error stopping market making: {e}")
            return self.create_error_response(str(e), "stop_market_making")

    async def analyze_order_flow(
        self,
        symbol: str,
        time_window: int = 300,
        analysis_type: str = "comprehensive"
    ) -> Dict[str, Any]:
        """Analyze order flow patterns and liquidity for a symbol"""
        try:
            # Simulate order flow analysis (placeholder implementation)
            # In a real implementation, this would:
            # 1. Collect tick-by-tick data
            # 2. Analyze order imbalances
            # 3. Calculate flow metrics
            # 4. Identify patterns

            import random
            import numpy as np

            # Generate synthetic order flow metrics
            np.random.seed(42)

            buy_volume = np.random.normal(100000, 20000)
            sell_volume = np.random.normal(95000, 18000)

            order_flow_metrics = {
                "symbol": symbol,
                "time_window_seconds": time_window,
                "analysis_type": analysis_type,
                "volume_metrics": {
                    "total_buy_volume": max(0, buy_volume),
                    "total_sell_volume": max(0, sell_volume),
                    "net_volume": buy_volume - sell_volume,
                    "volume_imbalance": (buy_volume - sell_volume) / (buy_volume + sell_volume) if (buy_volume + sell_volume) > 0 else 0
                },
                "order_metrics": {
                    "total_orders": np.random.randint(500, 1500),
                    "buy_orders": np.random.randint(250, 750),
                    "sell_orders": np.random.randint(250, 750),
                    "avg_order_size": np.random.normal(200, 50),
                    "large_orders_count": np.random.randint(10, 50)
                },
                "liquidity_metrics": {
                    "bid_ask_spread": np.random.normal(0.02, 0.005),
                    "market_depth": np.random.normal(50000, 10000),
                    "price_impact": np.random.normal(0.001, 0.0002)
                },
                "flow_patterns": {
                    "trend_direction": random.choice(["bullish", "bearish", "neutral"]),
                    "momentum_strength": np.random.uniform(0, 1),
                    "volatility_level": random.choice(["low", "medium", "high"]),
                    "institutional_activity": np.random.uniform(0, 1)
                }
            }

            return {
                "status": "success",
                "order_flow_analysis": order_flow_metrics,
                "recommendations": [
                    "Monitor for continued volume imbalance" if abs(order_flow_metrics["volume_metrics"]["volume_imbalance"]) > 0.1 else "Volume appears balanced",
                    "Consider position sizing based on liquidity metrics",
                    f"Flow pattern suggests {order_flow_metrics['flow_patterns']['trend_direction']} sentiment"
                ]
            }

        except Exception as e:
            logger.error(f"Error analyzing order flow: {e}")
            return self.create_error_response(str(e), "analyze_order_flow")
