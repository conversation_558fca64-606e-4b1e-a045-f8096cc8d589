"""
TickByTick Implementation - Layer 3: Business Logic
Core business logic for TickByTick operations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional, List
import logging
from datetime import datetime
import asyncio
from ib_async import Contract, Stock
from implementations.base_implementation import BaseImplementation

logger = logging.getLogger(__name__)

class TickByTickImplementation(BaseImplementation):
    """Core implementation for TickByTick operations"""
    
    def __init__(self, ibkr_service=None, **kwargs):
        super().__init__(ibkr_service=ibkr_service, **kwargs)
        self.market_data_service = kwargs.get('market_data_streaming_service')
        self.active_subscriptions = {}  # Track active tick subscriptions
        self.tick_buffers = {}  # Buffer recent ticks for each symbol
    
    async def start_tick_by_tick_data(
        self,
        symbol: str,
        tick_type: str = "AllLast",
        number_of_ticks: int = 0,
        ignore_size: bool = False
    ) -> Dict[str, Any]:
        """Start receiving tick-by-tick data for a symbol"""
        if not self.ensure_service_available():
            return self.create_error_response(
                "IBKR service not connected. Please ensure connection is established.",
                "start_tick_by_tick_data"
            )
        
        try:
            # Create contract
            contract = Stock(symbol, 'SMART', 'USD')
            if self.ibkr_service and self.ibkr_service.ib:
                qualified = await self.ibkr_service.ib.qualifyContractsAsync(contract)
                if not qualified:
                    return self.create_error_response(f"Cannot qualify contract for {symbol}", "start_tick_by_tick_data")

                contract = qualified[0]
            else:
                return self.create_error_response("IBKR service or IB connection not available", "start_tick_by_tick_data")

            # Check if already subscribed
            subscription_key = f"{symbol}_{tick_type}"
            if subscription_key in self.active_subscriptions:
                return self.create_success_response({
                    "message": "Already subscribed to tick data",
                    "symbol": symbol,
                    "tick_type": tick_type,
                    "subscription_id": subscription_key
                })

            # Request tick-by-tick data
            ticker = self.ibkr_service.ib.reqTickByTickData(
                contract=contract,
                tickType=tick_type,
                numberOfTicks=number_of_ticks,
                ignoreSize=ignore_size
            )
            

            
            # Initialize buffer for this symbol
            if symbol not in self.tick_buffers:
                self.tick_buffers[symbol] = []
            
            # Set up event handler to collect ticks
            def on_tick_update(ticker_obj):
                # Process new tick-by-tick data
                if hasattr(ticker_obj, 'tickByTicks') and ticker_obj.tickByTicks:
                    # Get the latest tick
                    latest_tick = ticker_obj.tickByTicks[-1]
                    tick_data = self._format_tick_by_tick_data(latest_tick, tick_type)
                    self.tick_buffers[symbol].append(tick_data)
                    # Keep only last 1000 ticks in buffer
                    if len(self.tick_buffers[symbol]) > 1000:
                        self.tick_buffers[symbol] = self.tick_buffers[symbol][-1000:]

            # Store the event handler for later cleanup
            self.active_subscriptions[subscription_key] = {
                "ticker": ticker,
                "contract": contract,
                "tick_type": tick_type,
                "started_at": datetime.now(),
                "event_handler": on_tick_update
            }

            # Connect the event handler properly
            # Note: The type checker may complain about this, but it's the correct ib_async pattern
            try:
                # This is the correct way to attach event handlers in ib_async
                ticker.updateEvent += on_tick_update  # type: ignore
            except Exception as e:
                logger.warning(f"Could not attach event handler: {e}. Tick data will still be available in ticker.tickByTicks")
            
            return self.create_success_response({
                "message": "Tick-by-tick subscription started",
                "symbol": symbol,
                "tick_type": tick_type,
                "subscription_id": subscription_key,
                "is_streaming": number_of_ticks == 0
            })
            
        except Exception as e:
            logger.error(f"Error starting tick-by-tick data: {e}")
            return self.create_error_response(str(e), "start_tick_by_tick_data")
    
    async def stop_tick_by_tick_data(self, symbol: str, subscription_id: Optional[str] = None) -> Dict[str, Any]:
        """Stop receiving tick-by-tick data for a symbol"""
        if not self.ensure_service_available():
            return self.create_error_response(
                "IBKR service not connected.",
                "stop_tick_by_tick_data"
            )
        
        try:
            # Find matching subscriptions
            keys_to_remove = []
            if subscription_id:
                if subscription_id in self.active_subscriptions:
                    keys_to_remove.append(subscription_id)
            else:
                # Remove all subscriptions for this symbol
                keys_to_remove = [k for k in self.active_subscriptions.keys() if k.startswith(f"{symbol}_")]
            
            if not keys_to_remove:
                return self.create_error_response(
                    f"No active subscriptions found for {symbol}",
                    "stop_tick_by_tick_data"
                )
            
            # Cancel subscriptions
            for key in keys_to_remove:
                sub_info = self.active_subscriptions[key]
                contract = sub_info["contract"]
                tick_type = sub_info["tick_type"]

                # Cancel the tick data
                if self.ibkr_service and self.ibkr_service.ib:
                    self.ibkr_service.ib.cancelTickByTickData(contract, tick_type)

                # Remove event handler if it exists
                if "event_handler" in sub_info and "ticker" in sub_info:
                    try:
                        sub_info["ticker"].updateEvent -= sub_info["event_handler"]  # type: ignore
                    except Exception as e:
                        logger.warning(f"Could not remove event handler: {e}")

                # Remove from active subscriptions
                del self.active_subscriptions[key]
            
            return self.create_success_response({
                "message": "Tick-by-tick subscription cancelled",
                "symbol": symbol,
                "cancelled_subscriptions": keys_to_remove
            })
            
        except Exception as e:
            logger.error(f"Error stopping tick-by-tick data: {e}")
            return self.create_error_response(str(e), "stop_tick_by_tick_data")
    
    async def get_recent_tick_data(self, symbol: str, count: int = 100) -> Dict[str, Any]:
        """Get recent tick-by-tick data from buffer"""
        try:
            if symbol not in self.tick_buffers:
                return self.create_success_response({
                    "symbol": symbol,
                    "ticks": [],
                    "message": f"No tick data available for {symbol}. Start a subscription first."
                })
            
            # Get last 'count' ticks
            recent_ticks = self.tick_buffers[symbol][-count:] if count > 0 else self.tick_buffers[symbol]
            
            return self.create_success_response({
                "symbol": symbol,
                "ticks": recent_ticks,
                "count": len(recent_ticks),
                "buffer_size": len(self.tick_buffers[symbol])
            })
            
        except Exception as e:
            logger.error(f"Error getting recent tick data: {e}")
            return self.create_error_response(str(e), "get_recent_tick_data")
    
    async def analyze_tick_data(self, symbol: str, lookback_minutes: int = 5) -> Dict[str, Any]:
        """Analyze recent tick data for patterns and statistics"""
        try:
            if symbol not in self.tick_buffers or not self.tick_buffers[symbol]:
                return self.create_error_response(
                    f"No tick data available for {symbol}",
                    "analyze_tick_data"
                )
            
            # Filter ticks within lookback period
            cutoff_time = datetime.now().timestamp() - (lookback_minutes * 60)
            recent_ticks = [
                tick for tick in self.tick_buffers[symbol]
                if tick.get("timestamp", 0) > cutoff_time
            ]
            
            if not recent_ticks:
                return self.create_success_response({
                    "symbol": symbol,
                    "message": "No ticks in the specified time window",
                    "lookback_minutes": lookback_minutes
                })
            
            # Calculate statistics
            prices = [t["price"] for t in recent_ticks if "price" in t and t["price"] is not None]
            volumes = [t["size"] for t in recent_ticks if "size" in t and t["size"] is not None]
            
            analysis = {
                "symbol": symbol,
                "lookback_minutes": lookback_minutes,
                "tick_count": len(recent_ticks),
                "time_range": {
                    "start": recent_ticks[0].get("time") if recent_ticks else None,
                    "end": recent_ticks[-1].get("time") if recent_ticks else None
                }
            }
            
            if prices:
                analysis["price_stats"] = {
                    "min": min(prices),
                    "max": max(prices),
                    "avg": sum(prices) / len(prices),
                    "last": prices[-1],
                    "change": prices[-1] - prices[0] if len(prices) > 1 else 0,
                    "change_pct": ((prices[-1] - prices[0]) / prices[0] * 100) if len(prices) > 1 and prices[0] != 0 else 0
                }
            
            if volumes:
                analysis["volume_stats"] = {
                    "total": sum(volumes),
                    "avg": sum(volumes) / len(volumes),
                    "min": min(volumes),
                    "max": max(volumes)
                }
            
            # Tick frequency analysis
            if len(recent_ticks) > 1:
                time_diffs = []
                for i in range(1, len(recent_ticks)):
                    if "timestamp" in recent_ticks[i] and "timestamp" in recent_ticks[i-1]:
                        diff = recent_ticks[i]["timestamp"] - recent_ticks[i-1]["timestamp"]
                        time_diffs.append(diff)
                
                if time_diffs:
                    analysis["tick_frequency"] = {
                        "avg_seconds_between_ticks": sum(time_diffs) / len(time_diffs),
                        "min_gap": min(time_diffs),
                        "max_gap": max(time_diffs)
                    }
            
            return self.create_success_response(analysis)
            
        except Exception as e:
            logger.error(f"Error analyzing tick data: {e}")
            return self.create_error_response(str(e), "analyze_tick_data")
    
    async def get_active_tick_subscriptions(self) -> Dict[str, Any]:
        """Get list of active tick-by-tick subscriptions"""
        try:
            subscriptions = []
            for key, info in self.active_subscriptions.items():
                symbol, tick_type = key.split("_", 1)
                subscriptions.append({
                    "subscription_id": key,
                    "symbol": symbol,
                    "tick_type": tick_type,
                    "started_at": info["started_at"].isoformat(),
                    "buffer_size": len(self.tick_buffers.get(symbol, []))
                })
            
            return self.create_success_response({
                "active_subscriptions": subscriptions,
                "count": len(subscriptions)
            })
            
        except Exception as e:
            logger.error(f"Error getting active subscriptions: {e}")
            return self.create_error_response(str(e), "get_active_tick_subscriptions")
    
    def _format_tick(self, tick, tick_type: str) -> Dict[str, Any]:
        """Format tick data into standard structure"""
        formatted = {
            "time": datetime.now().isoformat(),
            "timestamp": datetime.now().timestamp(),
            "tick_type": tick_type
        }

        # Add fields based on what's available in the tick
        if hasattr(tick, 'time') and tick.time:
            formatted["time"] = tick.time.isoformat()
            formatted["timestamp"] = tick.time.timestamp()

        if hasattr(tick, 'price') and tick.price is not None:
            formatted["price"] = float(tick.price)

        if hasattr(tick, 'size') and tick.size is not None:
            formatted["size"] = int(tick.size)

        if hasattr(tick, 'bid') and tick.bid is not None:
            formatted["bid"] = float(tick.bid)

        if hasattr(tick, 'ask') and tick.ask is not None:
            formatted["ask"] = float(tick.ask)

        if hasattr(tick, 'bidSize') and tick.bidSize is not None:
            formatted["bid_size"] = int(tick.bidSize)

        if hasattr(tick, 'askSize') and tick.askSize is not None:
            formatted["ask_size"] = int(tick.askSize)

        if hasattr(tick, 'lastSize') and tick.lastSize is not None:
            formatted["last_size"] = int(tick.lastSize)

        return formatted

    def _format_tick_by_tick_data(self, tick, tick_type: str) -> Dict[str, Any]:
        """Format tick-by-tick data into standard structure"""
        formatted = {
            "time": datetime.now().isoformat(),
            "timestamp": datetime.now().timestamp(),
            "tick_type": tick_type
        }

        # Add fields based on what's available in the tick-by-tick data
        if hasattr(tick, 'time') and tick.time:
            formatted["time"] = tick.time.isoformat()
            formatted["timestamp"] = tick.time.timestamp()

        # Handle different tick-by-tick types
        if hasattr(tick, 'price') and tick.price is not None:
            formatted["price"] = float(tick.price)

        if hasattr(tick, 'size') and tick.size is not None:
            formatted["size"] = int(tick.size)

        # For BidAsk ticks
        if hasattr(tick, 'bidPrice') and tick.bidPrice is not None:
            formatted["bid"] = float(tick.bidPrice)

        if hasattr(tick, 'askPrice') and tick.askPrice is not None:
            formatted["ask"] = float(tick.askPrice)

        if hasattr(tick, 'bidSize') and tick.bidSize is not None:
            formatted["bid_size"] = int(tick.bidSize)

        if hasattr(tick, 'askSize') and tick.askSize is not None:
            formatted["ask_size"] = int(tick.askSize)

        # For MidPoint ticks
        if hasattr(tick, 'midPoint') and tick.midPoint is not None:
            formatted["midpoint"] = float(tick.midPoint)

        # For AllLast ticks
        if hasattr(tick, 'exchange') and tick.exchange:
            formatted["exchange"] = str(tick.exchange)

        if hasattr(tick, 'specialConditions') and tick.specialConditions:
            formatted["special_conditions"] = str(tick.specialConditions)

        return formatted
    
    # Keep the original method for backward compatibility but redirect to new one
    async def get_real_time_ticks(
        self,
        symbol: str,
        tick_type: str = "Last",
        number_of_ticks: int = 0,
        ignore_size: bool = False
    ) -> Dict[str, Any]:
        """
        Get real-time tick-by-tick data for a symbol
        
        Args:
            symbol: The ticker symbol
            tick_type: "Last", "AllLast", "BidAsk", or "MidPoint" 
            number_of_ticks: Number of ticks to retrieve (0 for streaming)
            ignore_size: Ignore size parameter in tick data
            
        Returns:
            Real-time tick data or streaming subscription details
        """
        try:
            # Use market data streaming service if available
            if self.market_data_service:
                return await self.market_data_service.get_tick_by_tick_data(
                    symbol=symbol,
                    tick_type=tick_type,
                    number_of_ticks=number_of_ticks,
                    ignore_size=ignore_size
                )
            
            # Fallback to direct IBKR service
            if not self.ibkr_service or not self.ibkr_service.ib:
                return {
                    "status": "error",
                    "message": "IBKR service not available"
                }
                
            contract = await self.ibkr_service.create_contract(symbol)
            
            # Request tick-by-tick data (synchronous method, then wait for data)
            ticker = self.ibkr_service.ib.reqTickByTickData(
                contract=contract,
                tickType=tick_type,
                numberOfTicks=number_of_ticks,
                ignoreSize=ignore_size
            )

            # Wait for tick data to be populated
            await asyncio.sleep(2)  # Give time for data to arrive

            # Get the tick data from the ticker
            ticks = ticker.tickByTicks if hasattr(ticker, 'tickByTicks') else []
            
            # Format tick data using the proper formatting method
            tick_data = []
            for tick in ticks:
                tick_info = self._format_tick_by_tick_data(tick, tick_type)
                tick_data.append(tick_info)
            
            return {
                "status": "success",
                "symbol": symbol,
                "tickType": tick_type,
                "numberOfTicks": len(tick_data),
                "isStreaming": number_of_ticks == 0,
                "data": tick_data,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get tick-by-tick data for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "symbol": symbol,
                "tickType": tick_type
            }
    
    async def get_historical_ticks(
        self,
        symbol: str,
        start_date: str,
        end_date: str,
        tick_type: str = "TRADES",
        number_of_ticks: int = 1000,
        use_rth: bool = True,
        ignore_size: bool = False
    ) -> Dict[str, Any]:
        """
        Get historical tick data for a symbol
        
        Args:
            symbol: The ticker symbol
            start_date: Start date in YYYYMMDD HH:MM:SS format
            end_date: End date in YYYYMMDD HH:MM:SS format
            tick_type: "TRADES", "BID_ASK", or "MIDPOINT"
            number_of_ticks: Maximum number of ticks to retrieve
            use_rth: Use regular trading hours only
            ignore_size: Ignore size parameter in tick data
            
        Returns:
            Historical tick data
        """
        try:
            if not self.ibkr_service or not self.ibkr_service.ib:
                return {
                    "status": "error",
                    "message": "IBKR service not available"
                }
                
            contract = await self.ibkr_service.create_contract(symbol)
            
            # Request historical ticks
            ticks = await self.ibkr_service.ib.reqHistoricalTicksAsync(
                contract=contract,
                startDateTime=start_date,
                endDateTime=end_date,
                numberOfTicks=number_of_ticks,
                whatToShow=tick_type,
                useRth=use_rth,
                ignoreSize=ignore_size
            )
            
            # Format tick data
            tick_data = []
            for tick in ticks:
                tick_info = {
                    "time": tick.time.isoformat() if hasattr(tick, 'time') and tick.time else None,
                    "tickType": tick_type
                }
                
                # Add price and size based on tick type
                if hasattr(tick, 'price'):
                    tick_info["price"] = float(tick.price) if tick.price else None
                if hasattr(tick, 'size') and not ignore_size:
                    tick_info["size"] = int(tick.size) if tick.size else None
                if hasattr(tick, 'bid'):
                    tick_info["bid"] = float(tick.bid) if tick.bid else None
                if hasattr(tick, 'ask'):
                    tick_info["ask"] = float(tick.ask) if tick.ask else None
                if hasattr(tick, 'bidSize'):
                    tick_info["bidSize"] = int(tick.bidSize) if tick.bidSize else None
                if hasattr(tick, 'askSize'):
                    tick_info["askSize"] = int(tick.askSize) if tick.askSize else None
                    
                tick_data.append(tick_info)
            
            return {
                "status": "success",
                "symbol": symbol,
                "tickType": tick_type,
                "startDate": start_date,
                "endDate": end_date,
                "numberOfTicks": len(tick_data),
                "data": tick_data,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get historical ticks for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "symbol": symbol,
                "tickType": tick_type
            }
    
    async def get_tick_types(self) -> Dict[str, Any]:
        """
        Get available tick types and their descriptions
        
        Returns:
            Dictionary of available tick types
        """
        try:
            tick_types = {
                "Last": "Last traded price and size",
                "AllLast": "All last traded prices including those outside regular trading hours",
                "BidAsk": "Bid and ask prices with sizes",
                "MidPoint": "Midpoint between bid and ask"
            }
            
            historical_tick_types = {
                "TRADES": "Trade ticks",
                "BID_ASK": "Bid/Ask ticks", 
                "MIDPOINT": "Midpoint ticks"
            }
            
            return {
                "status": "success",
                "realTimeTicks": tick_types,
                "historicalTicks": historical_tick_types,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get tick types: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }

