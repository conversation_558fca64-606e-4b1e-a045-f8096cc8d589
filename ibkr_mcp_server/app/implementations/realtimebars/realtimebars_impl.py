"""
RealtimeBars Implementation - Layer 3: Business Logic
Core business logic for RealtimeBars operations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional, List, Callable
import logging
import asyncio
import time
from datetime import datetime, timezone, timedelta
from ibapi.contract import Contract
from ibapi.common import BarData

logger = logging.getLogger(__name__)

class RealtimeBarsImplementation:
    """Core implementation for RealtimeBars operations"""
    
    def __init__(self, ibkr_service=None, **kwargs):
        self.ibkr_service = ibkr_service
        self.services = kwargs
        self.active_subscriptions = {}  # Track active real-time bar subscriptions
        self.bar_data_cache = {}  # Cache recent bar data for analysis
        
        if not self.ibkr_service:
            logger.warning("RealtimeBarsImplementation initialized without IBKR service")
    
    async def request_real_time_bars(
        self,
        symbol: str,
        bar_size: str = "5 secs",
        what_to_show: str = "TRADES",
        use_rth: bool = True,
        duration_hours: int = 1,
        callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Request real-time bar data streaming for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., 'AAPL', 'MSFT')
            bar_size: Bar size - IBKR real-time bars are always 5 seconds
            what_to_show: Data type ('TRADES', 'BID', 'ASK', 'MIDPOINT')
            use_rth: Whether to use regular trading hours only
            duration_hours: Duration to stream data (hours)
            callback: Optional callback function for real-time updates
            
        Returns:
            Dict containing subscription details and initial setup
        """
        try:
            if not self.ibkr_service:
                return {
                    "error": "IBKR service not available",
                    "success": False
                }
            
            # Create contract for the symbol
            contract = Contract()
            contract.symbol = symbol.upper()
            contract.secType = "STK"
            contract.exchange = "SMART"
            contract.currency = "USD"
            
            # Generate unique request ID
            req_id = int(time.time() * 1000) % 100000
            
            # Set up subscription tracking
            subscription_id = f"rtb_{symbol}_{req_id}"
            
            subscription_info = {
                "subscription_id": subscription_id,
                "req_id": req_id,
                "symbol": symbol,
                "contract": contract,
                "bar_size": bar_size,
                "what_to_show": what_to_show,
                "use_rth": use_rth,
                "start_time": datetime.now(timezone.utc),
                "duration_hours": duration_hours,
                "callback": callback,
                "bar_count": 0,
                "status": "active"
            }
            
            self.active_subscriptions[subscription_id] = subscription_info
            
            # Initialize cache for this symbol
            if symbol not in self.bar_data_cache:
                self.bar_data_cache[symbol] = []
            
            # Start real-time bars request
            if hasattr(self.ibkr_service, 'ib') and hasattr(self.ibkr_service.ib, 'reqRealTimeBars'):
                try:
                    # ib_async uses synchronous methods for real-time bars
                    # The callback will be handled via event handlers
                    bar_list = self.ibkr_service.ib.reqRealTimeBars(
                        contract=contract,
                        barSize=5,  # Real-time bars are always 5 seconds
                        whatToShow=what_to_show,
                        useRTH=use_rth,
                        realTimeBarsOptions=[]
                    )
                    
                    # Store the bar list for this subscription
                    subscription_info["bar_list"] = bar_list
                    
                    # Set up callback handler
                    def on_bar_update(bars, hasNewBar):
                        if hasNewBar:
                            latest_bar = bars[-1]
                            # Update bar count
                            subscription_info["bar_count"] += 1
                            
                            # Cache the bar data
                            bar_data = {
                                "timestamp": latest_bar.time,
                                "open": latest_bar.open,
                                "high": latest_bar.high,
                                "low": latest_bar.low,
                                "close": latest_bar.close,
                                "volume": latest_bar.volume,
                                "wap": latest_bar.wap,
                                "count": latest_bar.count
                            }
                            self.bar_data_cache[symbol].append(bar_data)
                            
                            # Limit cache size to last 1000 bars
                            if len(self.bar_data_cache[symbol]) > 1000:
                                self.bar_data_cache[symbol] = self.bar_data_cache[symbol][-1000:]
                            
                            # Call user callback if provided
                            if callback:
                                try:
                                    callback(bar_data)
                                except Exception as cb_error:
                                    logger.error(f"User callback error: {cb_error}")
                    
                    # Attach the callback
                    bar_list.updateEvent += on_bar_update
                    
                except Exception as e:
                    logger.error(f"Failed to start real-time bars for {symbol}: {e}")
                    del self.active_subscriptions[subscription_id]
                    return {
                        "error": f"Failed to start real-time bars: {str(e)}",
                        "success": False
                    }
            else:
                logger.error("IBKR service does not have real-time bars capability")
                return {
                    "error": "Real-time bars not available in IBKR service",
                    "success": False
                }
            
            return {
                "success": True,
                "subscription_id": subscription_id,
                "symbol": symbol,
                "bar_size": bar_size,
                "what_to_show": what_to_show,
                "use_rth": use_rth,
                "duration_hours": duration_hours,
                "message": f"Real-time bars streaming started for {symbol}",
                "req_id": req_id
            }
            
        except Exception as e:
            logger.error(f"Error requesting real-time bars for {symbol}: {e}")
            return {
                "error": f"Failed to request real-time bars: {str(e)}",
                "success": False
            }
    
    async def cancel_real_time_bars(self, subscription_id: str) -> Dict[str, Any]:
        """
        Cancel real-time bars subscription.
        
        Args:
            subscription_id: ID of subscription to cancel
            
        Returns:
            Dict containing cancellation status and details
        """
        try:
            if subscription_id not in self.active_subscriptions:
                return {
                    "error": f"Subscription {subscription_id} not found",
                    "success": False
                }
            
            subscription = self.active_subscriptions[subscription_id]
            req_id = subscription["req_id"]
            
            # Cancel the subscription with IBKR
            if self.ibkr_service and hasattr(self.ibkr_service.ib, 'cancelRealTimeBars'):
                try:
                    # Get the bar list from subscription info
                    bar_list = subscription.get("bar_list")
                    if bar_list:
                        # Cancel the real-time bars subscription
                        self.ibkr_service.ib.cancelRealTimeBars(bar_list)
                except Exception as e:
                    logger.error(f"Failed to cancel real-time bars subscription {req_id}: {e}")
            
            # Update subscription status
            subscription["status"] = "cancelled"
            subscription["end_time"] = datetime.now(timezone.utc)
            
            # Calculate statistics
            duration = (subscription["end_time"] - subscription["start_time"]).total_seconds()
            bar_count = subscription.get("bar_count", 0)
            
            # Remove from active subscriptions but keep brief history
            cancelled_info = self.active_subscriptions.pop(subscription_id)
            
            return {
                "success": True,
                "subscription_id": subscription_id,
                "symbol": cancelled_info["symbol"],
                "duration_seconds": duration,
                "total_bars_received": bar_count,
                "message": f"Real-time bars subscription cancelled for {cancelled_info['symbol']}"
            }
            
        except Exception as e:
            logger.error(f"Error cancelling real-time bars subscription {subscription_id}: {e}")
            return {
                "error": f"Failed to cancel subscription: {str(e)}",
                "success": False
            }
    
    async def get_historical_real_time_bars(
        self,
        symbol: str,
        duration: str = "1 D",
        bar_size: str = "5 secs",
        what_to_show: str = "TRADES",
        use_rth: bool = True,
        format_date: int = 1
    ) -> Dict[str, Any]:
        """
        Get historical real-time bar data (5-second bars) for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., 'AAPL', 'MSFT')
            duration: Duration of historical data ('1 D', '1 W', etc.)
            bar_size: Bar size (real-time bars are always 5 seconds)
            what_to_show: Data type ('TRADES', 'BID', 'ASK', 'MIDPOINT')
            use_rth: Whether to use regular trading hours only
            format_date: Date format (1 for yyyyMMdd HH:mm:ss, 2 for epoch)
            
        Returns:
            Dict containing historical real-time bar data
        """
        try:
            if not self.ibkr_service:
                return {
                    "error": "IBKR service not available",
                    "success": False
                }
            
            # Create contract for the symbol
            contract = Contract()
            contract.symbol = symbol.upper()
            contract.secType = "STK"
            contract.exchange = "SMART"
            contract.currency = "USD"
            
            # Request historical data using 5-second bars
            req_id = int(time.time() * 1000) % 100000
            
            # Get current time for end date
            end_date_time = ""  # Empty string means current time
            
            if hasattr(self.ibkr_service, 'ib') and hasattr(self.ibkr_service.ib, 'reqHistoricalDataAsync'):
                try:
                    # Use ib_async's historical data method directly
                    bars_data = await self.ibkr_service.ib.reqHistoricalDataAsync(
                        contract=contract,
                        endDateTime=end_date_time,
                        durationStr=duration,
                        barSizeSetting="5 secs",
                        whatToShow=what_to_show,
                        useRTH=use_rth,
                        formatDate=format_date
                    )
                    
                    # Convert BarData objects to dict format
                    result = {
                        "bars": [{
                            "date": bar.date,
                            "open": bar.open,
                            "high": bar.high,
                            "low": bar.low,
                            "close": bar.close,
                            "volume": bar.volume,
                            "wap": bar.wap,
                            "count": bar.count
                        } for bar in bars_data]
                    }
                    
                    if result and "bars" in result:
                        bars = result["bars"]
                        
                        # Calculate bar statistics
                        if bars:
                            total_volume = sum(bar.get("volume", 0) for bar in bars)
                            avg_volume = total_volume / len(bars) if bars else 0
                            
                            # Calculate VWAP if possible
                            total_vwap_volume = sum(
                                bar.get("volume", 0) * ((bar.get("high", 0) + bar.get("low", 0) + bar.get("close", 0)) / 3)
                                for bar in bars if bar.get("volume", 0) > 0
                            )
                            vwap = total_vwap_volume / total_volume if total_volume > 0 else 0
                            
                            first_bar = bars[0]
                            last_bar = bars[-1]
                            
                            price_change = last_bar.get("close", 0) - first_bar.get("open", 0)
                            price_change_pct = (price_change / first_bar.get("open", 1)) * 100 if first_bar.get("open", 0) > 0 else 0
                        else:
                            total_volume = avg_volume = vwap = price_change = price_change_pct = 0
                        
                        return {
                            "success": True,
                            "symbol": symbol,
                            "duration": duration,
                            "bar_size": "5 secs",
                            "what_to_show": what_to_show,
                            "use_rth": use_rth,
                            "bar_count": len(bars),
                            "bars": bars,
                            "statistics": {
                                "total_volume": total_volume,
                                "average_volume_per_bar": avg_volume,
                                "vwap": vwap,
                                "price_change": price_change,
                                "price_change_percent": price_change_pct
                            },
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        }
                    else:
                        return {
                            "error": "No historical bar data received",
                            "success": False
                        }
                        
                except Exception as e:
                    logger.error(f"Failed to get historical real-time bars for {symbol}: {e}")
                    return {
                        "error": f"Failed to get historical data: {str(e)}",
                        "success": False
                    }
            else:
                return {
                    "error": "Historical data method not available in IBKR service",
                    "success": False
                }
                
        except Exception as e:
            logger.error(f"Error getting historical real-time bars for {symbol}: {e}")
            return {
                "error": f"Failed to get historical real-time bars: {str(e)}",
                "success": False
            }
    
    async def get_active_subscriptions(self) -> Dict[str, Any]:
        """
        Get all active real-time bars subscriptions.
        
        Returns:
            Dict containing active subscription details
        """
        try:
            current_time = datetime.now(timezone.utc)
            subscription_list = []
            
            for sub_id, sub_info in self.active_subscriptions.items():
                duration = (current_time - sub_info["start_time"]).total_seconds()
                
                subscription_list.append({
                    "subscription_id": sub_id,
                    "symbol": sub_info["symbol"],
                    "bar_size": sub_info["bar_size"],
                    "what_to_show": sub_info["what_to_show"],
                    "use_rth": sub_info["use_rth"],
                    "start_time": sub_info["start_time"].isoformat(),
                    "duration_seconds": duration,
                    "duration_hours": sub_info["duration_hours"],
                    "bar_count": sub_info.get("bar_count", 0),
                    "status": sub_info["status"],
                    "req_id": sub_info["req_id"]
                })
            
            return {
                "success": True,
                "active_subscriptions": subscription_list,
                "total_active": len(subscription_list),
                "timestamp": current_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting active subscriptions: {e}")
            return {
                "error": f"Failed to get active subscriptions: {str(e)}",
                "success": False
            }
    
    async def get_bar_analysis(
        self,
        symbol: str,
        lookback_minutes: int = 30
    ) -> Dict[str, Any]:
        """
        Get analysis of recent real-time bar data for a symbol.
        
        Args:
            symbol: Trading symbol to analyze
            lookback_minutes: Number of minutes to look back for analysis
            
        Returns:
            Dict containing bar analysis and trading metrics
        """
        try:
            # Get cached bar data for the symbol
            if symbol not in self.bar_data_cache:
                return {
                    "error": f"No cached bar data available for {symbol}",
                    "success": False
                }
            
            bars = self.bar_data_cache[symbol]
            if not bars:
                return {
                    "error": f"No bar data found for {symbol}",
                    "success": False
                }
            
            # Filter bars by lookback period
            cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=lookback_minutes)
            recent_bars = [
                bar for bar in bars 
                if bar.get("timestamp", datetime.min.replace(tzinfo=timezone.utc)) >= cutoff_time
            ]
            
            if not recent_bars:
                return {
                    "error": f"No recent bar data found for {symbol} in last {lookback_minutes} minutes",
                    "success": False
                }
            
            # Calculate comprehensive analysis
            prices = [bar["close"] for bar in recent_bars if "close" in bar]
            volumes = [bar["volume"] for bar in recent_bars if "volume" in bar]
            highs = [bar["high"] for bar in recent_bars if "high" in bar]
            lows = [bar["low"] for bar in recent_bars if "low" in bar]
            
            if not prices:
                return {
                    "error": f"No price data available for analysis",
                    "success": False
                }
            
            # Basic statistics
            current_price = prices[-1]
            price_range = max(prices) - min(prices)
            volatility = (price_range / current_price) * 100 if current_price > 0 else 0
            
            # Volume analysis
            total_volume = sum(volumes) if volumes else 0
            avg_volume = total_volume / len(volumes) if volumes else 0
            
            # Price trend analysis
            if len(prices) >= 2:
                price_change = prices[-1] - prices[0]
                price_change_pct = (price_change / prices[0]) * 100 if prices[0] > 0 else 0
                
                # Calculate simple trend
                if len(prices) >= 10:
                    recent_trend = sum(prices[-5:]) / 5 - sum(prices[-10:-5]) / 5
                    trend_direction = "bullish" if recent_trend > 0 else "bearish" if recent_trend < 0 else "neutral"
                else:
                    trend_direction = "bullish" if price_change > 0 else "bearish" if price_change < 0 else "neutral"
            else:
                price_change = price_change_pct = 0
                trend_direction = "neutral"
            
            # Volume-weighted average price (VWAP)
            if volumes and all(v > 0 for v in volumes):
                vwap_numerator = sum(
                    bar["volume"] * ((bar["high"] + bar["low"] + bar["close"]) / 3)
                    for bar in recent_bars
                    if all(k in bar for k in ["volume", "high", "low", "close"])
                )
                vwap = vwap_numerator / total_volume if total_volume > 0 else 0
            else:
                vwap = sum(prices) / len(prices) if prices else 0
            
            # Momentum indicators
            momentum_score = 0
            if len(prices) >= 5:
                recent_avg = sum(prices[-3:]) / 3
                earlier_avg = sum(prices[-6:-3]) / 3 if len(prices) >= 6 else prices[0]
                momentum_score = ((recent_avg - earlier_avg) / earlier_avg) * 100 if earlier_avg > 0 else 0
            
            return {
                "success": True,
                "symbol": symbol,
                "analysis_period_minutes": lookback_minutes,
                "bar_count": len(recent_bars),
                "current_price": current_price,
                "price_analysis": {
                    "price_change": price_change,
                    "price_change_percent": price_change_pct,
                    "price_range": price_range,
                    "volatility_percent": volatility,
                    "high": max(highs) if highs else 0,
                    "low": min(lows) if lows else 0,
                    "vwap": vwap
                },
                "volume_analysis": {
                    "total_volume": total_volume,
                    "average_volume_per_bar": avg_volume,
                    "volume_trend": "increasing" if len(volumes) >= 2 and volumes[-1] > avg_volume else "decreasing"
                },
                "trend_analysis": {
                    "direction": trend_direction,
                    "momentum_score": momentum_score,
                    "strength": "strong" if abs(momentum_score) > 1 else "moderate" if abs(momentum_score) > 0.5 else "weak"
                },
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing bars for {symbol}: {e}")
            return {
                "error": f"Failed to analyze bar data: {str(e)}",
                "success": False
            }

