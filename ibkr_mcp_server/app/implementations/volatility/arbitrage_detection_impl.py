"""
Arbitrage Detection Implementation for Volatility Surface Domain
Layer 3: Focused implementation for volatility arbitrage detection and analysis
"""

from typing import Dict, List, Optional, Tuple, Any
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from scipy.optimize import minimize_scalar

logger = logging.getLogger(__name__)

@dataclass
class ArbitrageOpportunity:
    """Detected arbitrage opportunity"""
    arbitrage_type: str
    severity: float
    confidence: float
    instruments: List[str]
    expected_profit: float
    risk_metrics: Dict[str, float]
    execution_strategy: Dict[str, Any]
    time_sensitivity: str

@dataclass
class ArbitrageAnalysis:
    """Complete arbitrage analysis results"""
    opportunities: List[ArbitrageOpportunity]
    surface_violations: List[Dict[str, Any]]
    calendar_arbitrages: List[Dict[str, Any]]
    butterfly_arbitrages: List[Dict[str, Any]]
    put_call_violations: List[Dict[str, Any]]
    statistical_metrics: Dict[str, float]

class ArbitrageDetectionImplementation:
    """Handles detection and analysis of volatility arbitrage opportunities"""
    
    def __init__(self, ibkr_service=None, **kwargs):
        self.ibkr_service = ibkr_service
        self.services = kwargs
        self.logger = logging.getLogger(__name__)
        self.min_profit_threshold = 0.01  # 1% minimum expected profit
        self.confidence_threshold = 0.7   # 70% minimum confidence
        
        if not self.ibkr_service:
            logger.warning("ArbitrageDetectionImplementation initialized without IBKR service")
    
    async def detect_arbitrage_opportunities(self, symbol: str, option_chain_data: Dict, 
                                           surface_data: Dict) -> ArbitrageAnalysis:
        """
        Comprehensive arbitrage detection across volatility surface
        
        Args:
            symbol: Underlying symbol
            option_chain_data: Complete option chain data
            surface_data: Fitted volatility surface data
            
        Returns:
            ArbitrageAnalysis with all detected opportunities
        """
        try:
            # Initialize analysis results
            opportunities = []
            surface_violations = []
            calendar_arbitrages = []
            butterfly_arbitrages = []
            put_call_violations = []
            
            # 1. Surface arbitrage detection
            surface_violations = await self._detect_surface_arbitrage(
                option_chain_data, surface_data
            )
            
            # 2. Calendar arbitrage detection
            calendar_arbitrages = await self._detect_calendar_arbitrage(option_chain_data)
            
            # 3. Butterfly arbitrage detection
            butterfly_arbitrages = await self._detect_butterfly_arbitrage(option_chain_data)
            
            # 4. Put-call parity violations
            put_call_violations = await self._detect_put_call_parity_violations(
                option_chain_data
            )
            
            # 5. Statistical arbitrage opportunities
            statistical_arbitrages = await self._detect_statistical_arbitrage(
                option_chain_data, surface_data
            )
            
            # Consolidate all opportunities
            all_arbitrages = (
                surface_violations + calendar_arbitrages + 
                butterfly_arbitrages + put_call_violations + statistical_arbitrages
            )
            
            # Filter and rank opportunities
            opportunities = self._filter_and_rank_opportunities(all_arbitrages)
            
            # Calculate statistical metrics
            statistical_metrics = self._calculate_arbitrage_statistics(all_arbitrages)
            
            return ArbitrageAnalysis(
                opportunities=opportunities,
                surface_violations=surface_violations,
                calendar_arbitrages=calendar_arbitrages,
                butterfly_arbitrages=butterfly_arbitrages,
                put_call_violations=put_call_violations,
                statistical_metrics=statistical_metrics
            )
            
        except Exception as e:
            self.logger.error(f"Arbitrage detection failed for {symbol}: {e}")
            raise
    
    async def _detect_surface_arbitrage(self, option_chain_data: Dict, 
                                      surface_data: Dict) -> List[Dict[str, Any]]:
        """Detect arbitrage opportunities in the volatility surface"""
        violations = []
        
        try:
            # Extract surface grid
            surface_grid = surface_data.get('surface_grid', {})
            if not surface_grid:
                return violations
            
            # Check for no-arbitrage conditions
            violations.extend(self._check_strike_arbitrage(option_chain_data))
            violations.extend(self._check_calendar_arbitrage_conditions(option_chain_data))
            violations.extend(self._check_surface_monotonicity(surface_grid))
            
        except Exception as e:
            self.logger.error(f"Surface arbitrage detection failed: {e}")
        
        return violations
    
    def _check_strike_arbitrage(self, option_chain_data: Dict) -> List[Dict[str, Any]]:
        """Check for strike arbitrage violations"""
        violations = []
        
        for expiry_str, expiry_data in option_chain_data.items():
            try:
                if 'calls' not in expiry_data or 'puts' not in expiry_data:
                    continue
                
                calls = expiry_data['calls']
                puts = expiry_data['puts']
                
                # Check call option monotonicity (prices should decrease with strike)
                call_strikes = sorted([float(s) for s in calls.keys()])
                call_prices = []
                
                for strike in call_strikes:
                    strike_str = str(strike)
                    if strike_str in calls and 'lastPrice' in calls[strike_str]:
                        call_prices.append(calls[strike_str]['lastPrice'])
                    else:
                        call_prices.append(None)
                
                # Detect violations in call monotonicity
                for i in range(len(call_prices) - 1):
                    if call_prices[i] is not None and call_prices[i + 1] is not None:
                        if call_prices[i] < call_prices[i + 1]:
                            profit = call_prices[i + 1] - call_prices[i]
                            violations.append({
                                'type': 'call_strike_arbitrage',
                                'expiry': expiry_str,
                                'lower_strike': call_strikes[i],
                                'higher_strike': call_strikes[i + 1],
                                'lower_price': call_prices[i],
                                'higher_price': call_prices[i + 1],
                                'profit_potential': profit,
                                'confidence': 0.9,
                                'strategy': 'buy_lower_sell_higher'
                            })
                
                # Check put option monotonicity (prices should increase with strike)
                put_strikes = sorted([float(s) for s in puts.keys()])
                put_prices = []
                
                for strike in put_strikes:
                    strike_str = str(strike)
                    if strike_str in puts and 'lastPrice' in puts[strike_str]:
                        put_prices.append(puts[strike_str]['lastPrice'])
                    else:
                        put_prices.append(None)
                
                # Detect violations in put monotonicity
                for i in range(len(put_prices) - 1):
                    if put_prices[i] is not None and put_prices[i + 1] is not None:
                        if put_prices[i] > put_prices[i + 1]:
                            profit = put_prices[i] - put_prices[i + 1]
                            violations.append({
                                'type': 'put_strike_arbitrage',
                                'expiry': expiry_str,
                                'lower_strike': put_strikes[i],
                                'higher_strike': put_strikes[i + 1],
                                'lower_price': put_prices[i],
                                'higher_price': put_prices[i + 1],
                                'profit_potential': profit,
                                'confidence': 0.9,
                                'strategy': 'sell_lower_buy_higher'
                            })
                            
            except Exception as e:
                self.logger.warning(f"Strike arbitrage check failed for {expiry_str}: {e}")
                continue
        
        return violations
    
    async def _detect_calendar_arbitrage(self, option_chain_data: Dict) -> List[Dict[str, Any]]:
        """Detect calendar arbitrage opportunities"""
        arbitrages = []
        
        # Group options by strike and type
        option_groups = {}
        for expiry_str, expiry_data in option_chain_data.items():
            expiry_date = datetime.strptime(expiry_str, '%Y%m%d')
            
            for option_type in ['calls', 'puts']:
                if option_type in expiry_data:
                    for strike_str, option_data in expiry_data[option_type].items():
                        key = (float(strike_str), option_type[:-1])  # Remove 's'
                        if key not in option_groups:
                            option_groups[key] = []
                        
                        option_groups[key].append({
                            'expiry': expiry_date,
                            'expiry_str': expiry_str,
                            'price': option_data.get('lastPrice', 0),
                            'volume': option_data.get('volume', 0),
                            'data': option_data
                        })
        
        # Check for calendar arbitrage in each group
        for (strike, option_type), options in option_groups.items():
            if len(options) < 2:
                continue
            
            # Sort by expiry
            options.sort(key=lambda x: x['expiry'])
            
            # Check consecutive pairs
            for i in range(len(options) - 1):
                near_option = options[i]
                far_option = options[i + 1]
                
                # Calendar arbitrage condition: near option should be cheaper
                if near_option['price'] > far_option['price']:
                    profit = near_option['price'] - far_option['price']
                    
                    if profit > self.min_profit_threshold:
                        arbitrages.append({
                            'type': 'calendar_arbitrage',
                            'strike': strike,
                            'option_type': option_type,
                            'near_expiry': near_option['expiry_str'],
                            'far_expiry': far_option['expiry_str'],
                            'near_price': near_option['price'],
                            'far_price': far_option['price'],
                            'profit_potential': profit,
                            'confidence': 0.8,
                            'strategy': 'sell_near_buy_far'
                        })
        
        return arbitrages
    
    async def _detect_butterfly_arbitrage(self, option_chain_data: Dict) -> List[Dict[str, Any]]:
        """Detect butterfly arbitrage opportunities"""
        arbitrages = []
        
        for expiry_str, expiry_data in option_chain_data.items():
            for option_type in ['calls', 'puts']:
                if option_type not in expiry_data:
                    continue
                
                options = expiry_data[option_type]
                strikes = sorted([float(s) for s in options.keys()])
                
                # Check all possible butterfly combinations
                for i in range(len(strikes) - 2):
                    low_strike = strikes[i]
                    mid_strike = strikes[i + 1]
                    high_strike = strikes[i + 2]
                    
                    # Check if strikes are evenly spaced (approximately)
                    if abs((mid_strike - low_strike) - (high_strike - mid_strike)) > 0.01:
                        continue
                    
                    try:
                        low_price = options[str(low_strike)]['lastPrice']
                        mid_price = options[str(mid_strike)]['lastPrice']
                        high_price = options[str(high_strike)]['lastPrice']
                        
                        # Butterfly arbitrage condition
                        butterfly_value = low_price + high_price - 2 * mid_price
                        max_profit = min(mid_strike - low_strike, high_strike - mid_strike)
                        
                        # Check for arbitrage opportunity
                        if butterfly_value < 0:  # Negative butterfly value
                            profit_potential = abs(butterfly_value)
                            
                            if profit_potential > self.min_profit_threshold:
                                arbitrages.append({
                                    'type': 'butterfly_arbitrage',
                                    'expiry': expiry_str,
                                    'option_type': option_type[:-1],
                                    'low_strike': low_strike,
                                    'mid_strike': mid_strike,
                                    'high_strike': high_strike,
                                    'butterfly_value': butterfly_value,
                                    'profit_potential': profit_potential,
                                    'max_profit': max_profit,
                                    'confidence': 0.85,
                                    'strategy': 'long_butterfly'
                                })
                        
                        elif butterfly_value > max_profit:  # Overpriced butterfly
                            profit_potential = butterfly_value - max_profit
                            
                            if profit_potential > self.min_profit_threshold:
                                arbitrages.append({
                                    'type': 'butterfly_arbitrage',
                                    'expiry': expiry_str,
                                    'option_type': option_type[:-1],
                                    'low_strike': low_strike,
                                    'mid_strike': mid_strike,
                                    'high_strike': high_strike,
                                    'butterfly_value': butterfly_value,
                                    'profit_potential': profit_potential,
                                    'max_profit': max_profit,
                                    'confidence': 0.85,
                                    'strategy': 'short_butterfly'
                                })
                                
                    except (KeyError, TypeError):
                        continue
        
        return arbitrages
    
    async def _detect_put_call_parity_violations(self, option_chain_data: Dict) -> List[Dict[str, Any]]:
        """Detect put-call parity violations"""
        violations = []
        
        for expiry_str, expiry_data in option_chain_data.items():
            if 'calls' not in expiry_data or 'puts' not in expiry_data:
                continue
            
            calls = expiry_data['calls']
            puts = expiry_data['puts']
            
            # Get common strikes
            call_strikes = set(calls.keys())
            put_strikes = set(puts.keys())
            common_strikes = call_strikes.intersection(put_strikes)
            
            for strike_str in common_strikes:
                try:
                    call_data = calls[strike_str]
                    put_data = puts[strike_str]
                    
                    call_price = call_data.get('lastPrice', 0)
                    put_price = put_data.get('lastPrice', 0)
                    strike = float(strike_str)
                    
                    # Get underlying price (assume it's in the data)
                    underlying_price = call_data.get('underlyingPrice', 
                                                   put_data.get('underlyingPrice', 100))
                    
                    # Calculate time to expiry (simplified)
                    expiry_date = datetime.strptime(expiry_str, '%Y%m%d')
                    time_to_expiry = (expiry_date - datetime.now()).days / 365.0
                    
                    # Risk-free rate (simplified - use 2%)
                    risk_free_rate = 0.02
                    
                    # Put-call parity: C - P = S - K * e^(-r*T)
                    pv_strike = strike * np.exp(-risk_free_rate * time_to_expiry)
                    theoretical_diff = underlying_price - pv_strike
                    actual_diff = call_price - put_price
                    
                    parity_violation = actual_diff - theoretical_diff
                    
                    if abs(parity_violation) > self.min_profit_threshold:
                        violations.append({
                            'type': 'put_call_parity_violation',
                            'expiry': expiry_str,
                            'strike': strike,
                            'call_price': call_price,
                            'put_price': put_price,
                            'underlying_price': underlying_price,
                            'theoretical_diff': theoretical_diff,
                            'actual_diff': actual_diff,
                            'parity_violation': parity_violation,
                            'profit_potential': abs(parity_violation),
                            'confidence': 0.9,
                            'strategy': 'conversion' if parity_violation > 0 else 'reversal'
                        })
                        
                except (KeyError, ValueError, TypeError) as e:
                    self.logger.warning(f"Put-call parity check failed for {strike_str}: {e}")
                    continue
        
        return violations
    
    async def _detect_statistical_arbitrage(self, option_chain_data: Dict, 
                                          surface_data: Dict) -> List[Dict[str, Any]]:
        """Detect statistical arbitrage opportunities"""
        arbitrages = []
        
        try:
            # Get theoretical surface prices
            surface_grid = surface_data.get('surface_grid', {})
            if not surface_grid:
                return arbitrages
            
            # Compare market prices with theoretical prices
            for expiry_str, expiry_data in option_chain_data.items():
                for option_type in ['calls', 'puts']:
                    if option_type not in expiry_data:
                        continue
                    
                    for strike_str, option_data in expiry_data[option_type].items():
                        try:
                            market_price = option_data.get('lastPrice', 0)
                            if market_price <= 0:
                                continue
                            
                            # Get theoretical price from surface
                            theoretical_price = self._get_theoretical_price(
                                surface_grid, expiry_str, strike_str, option_type[:-1]
                            )
                            
                            if theoretical_price is None or theoretical_price <= 0:
                                continue
                            
                            # Calculate mispricing
                            price_diff = market_price - theoretical_price
                            relative_diff = price_diff / theoretical_price
                            
                            # Check for significant mispricing
                            if abs(relative_diff) > 0.1:  # 10% threshold
                                arbitrages.append({
                                    'type': 'statistical_arbitrage',
                                    'expiry': expiry_str,
                                    'strike': float(strike_str),
                                    'option_type': option_type[:-1],
                                    'market_price': market_price,
                                    'theoretical_price': theoretical_price,
                                    'price_difference': price_diff,
                                    'relative_mispricing': relative_diff,
                                    'profit_potential': abs(price_diff),
                                    'confidence': min(abs(relative_diff) * 5, 0.95),
                                    'strategy': 'sell' if price_diff > 0 else 'buy'
                                })
                                
                        except Exception as e:
                            continue
            
        except Exception as e:
            self.logger.error(f"Statistical arbitrage detection failed: {e}")
        
        return arbitrages
    
    def _get_theoretical_price(self, surface_grid: Dict, expiry_str: str, 
                             strike_str: str, option_type: str) -> Optional[float]:
        """Get theoretical price from volatility surface"""
        try:
            if expiry_str in surface_grid and strike_str in surface_grid[expiry_str]:
                vol_data = surface_grid[expiry_str][strike_str]
                return vol_data.get(f'{option_type}_theoretical_price')
        except Exception:
            pass
        return None
    
    def _check_calendar_arbitrage_conditions(self, option_chain_data: Dict) -> List[Dict[str, Any]]:
        """Check general calendar arbitrage conditions"""
        violations = []
        # Implementation would check time value decay patterns
        return violations
    
    def _check_surface_monotonicity(self, surface_grid: Dict) -> List[Dict[str, Any]]:
        """Check volatility surface monotonicity conditions"""
        violations = []
        # Implementation would check surface smoothness and monotonicity
        return violations
    
    def _filter_and_rank_opportunities(self, all_arbitrages: List[Dict[str, Any]]) -> List[ArbitrageOpportunity]:
        """Filter and rank arbitrage opportunities by profitability and confidence"""
        opportunities = []
        
        for arb in all_arbitrages:
            profit = arb.get('profit_potential', 0)
            confidence = arb.get('confidence', 0)
            
            if profit >= self.min_profit_threshold and confidence >= self.confidence_threshold:
                opportunity = ArbitrageOpportunity(
                    arbitrage_type=arb['type'],
                    severity=profit,
                    confidence=confidence,
                    instruments=self._extract_instruments(arb),
                    expected_profit=profit,
                    risk_metrics=self._calculate_risk_metrics(arb),
                    execution_strategy=self._create_execution_strategy(arb),
                    time_sensitivity=self._assess_time_sensitivity(arb)
                )
                opportunities.append(opportunity)
        
        # Sort by expected profit * confidence
        opportunities.sort(key=lambda x: x.expected_profit * x.confidence, reverse=True)
        
        return opportunities
    
    def _extract_instruments(self, arbitrage: Dict[str, Any]) -> List[str]:
        """Extract involved instruments from arbitrage opportunity"""
        instruments = []
        arb_type = arbitrage['type']
        
        if 'strike' in arbitrage:
            base = f"{arbitrage.get('option_type', '')}"
            if 'expiry' in arbitrage:
                instruments.append(f"{base}_{arbitrage['expiry']}_{arbitrage['strike']}")
        
        # Add specific instruments based on arbitrage type
        if 'butterfly' in arb_type:
            instruments.extend([
                f"strike_{arbitrage.get('low_strike')}",
                f"strike_{arbitrage.get('mid_strike')}",
                f"strike_{arbitrage.get('high_strike')}"
            ])
        elif 'calendar' in arb_type:
            instruments.extend([
                f"expiry_{arbitrage.get('near_expiry')}",
                f"expiry_{arbitrage.get('far_expiry')}"
            ])
        
        return instruments
    
    def _calculate_risk_metrics(self, arbitrage: Dict[str, Any]) -> Dict[str, float]:
        """Calculate risk metrics for arbitrage opportunity"""
        return {
            'max_loss': arbitrage.get('profit_potential', 0) * 0.1,  # Simplified
            'success_probability': arbitrage.get('confidence', 0),
            'liquidity_risk': 0.1,  # Placeholder
            'execution_risk': 0.05   # Placeholder
        }
    
    def _create_execution_strategy(self, arbitrage: Dict[str, Any]) -> Dict[str, Any]:
        """Create execution strategy for arbitrage opportunity"""
        return {
            'strategy_type': arbitrage.get('strategy', 'unknown'),
            'order_sequence': self._determine_order_sequence(arbitrage),
            'position_sizing': 'equal_weighted',
            'execution_timing': 'immediate'
        }
    
    def _determine_order_sequence(self, arbitrage: Dict[str, Any]) -> List[str]:
        """Determine optimal order sequence for execution"""
        arb_type = arbitrage['type']
        
        if 'butterfly' in arb_type:
            return ['buy_wings', 'sell_body']
        elif 'calendar' in arb_type:
            return ['sell_near', 'buy_far']
        elif 'put_call_parity' in arb_type:
            return ['buy_undervalued', 'sell_overvalued']
        else:
            return ['simultaneous']
    
    def _assess_time_sensitivity(self, arbitrage: Dict[str, Any]) -> str:
        """Assess time sensitivity of arbitrage opportunity"""
        arb_type = arbitrage['type']
        confidence = arbitrage.get('confidence', 0)
        
        if confidence > 0.9:
            return 'high'
        elif 'statistical' in arb_type:
            return 'medium'
        else:
            return 'low'
    
    def _calculate_arbitrage_statistics(self, all_arbitrages: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate overall arbitrage statistics"""
        if not all_arbitrages:
            return {}
        
        profits = [arb.get('profit_potential', 0) for arb in all_arbitrages]
        confidences = [arb.get('confidence', 0) for arb in all_arbitrages]
        
        return {
            'total_opportunities': len(all_arbitrages),
            'average_profit': float(np.mean(profits)),
            'max_profit': float(max(profits)),
            'average_confidence': float(np.mean(confidences)),
            'high_confidence_count': len([c for c in confidences if c > 0.8]),
            'total_expected_value': float(sum(p * c for p, c in zip(profits, confidences)))
        }
