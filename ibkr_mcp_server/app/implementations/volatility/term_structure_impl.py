"""
Term Structure Implementation for Volatility Surface Domain
Layer 3: Focused implementation for term structure analysis and modeling
"""

from typing import Dict, List, Optional, Tuple, Any
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class TermStructurePoint:
    """Single point in volatility term structure"""
    expiry: datetime
    days_to_expiry: int
    implied_vol: float
    moneyness: float
    strike: float
    option_type: str
    volume: Optional[int] = None
    open_interest: Optional[int] = None

@dataclass
class TermStructureAnalysis:
    """Complete term structure analysis results"""
    structure_points: List[TermStructurePoint]
    term_structure_slope: float
    volatility_skew: Dict[str, float]
    calendar_spreads: List[Dict[str, Any]]
    risk_metrics: Dict[str, float]
    market_regime: str
    trading_opportunities: List[Dict[str, Any]]

class TermStructureImplementation:
    """Handles term structure analysis and modeling for volatility surfaces"""
    
    def __init__(self, ibkr_service=None, **kwargs):
        self.ibkr_service = ibkr_service
        self.services = kwargs
        self.logger = logging.getLogger(__name__)
        
        if not self.ibkr_service:
            logger.warning("TermStructureImplementation initialized without IBKR service")
    
    async def analyze_term_structure(self, symbol: str, option_chain_data: Dict) -> TermStructureAnalysis:
        """
        Analyze volatility term structure for comprehensive insights
        
        Args:
            symbol: Underlying symbol
            option_chain_data: Complete option chain data
            
        Returns:
            TermStructureAnalysis with complete analysis
        """
        try:
            # Extract term structure points
            structure_points = self._extract_term_structure_points(option_chain_data)
            
            # Calculate term structure slope
            slope = self._calculate_term_structure_slope(structure_points)
            
            # Analyze volatility skew across terms
            skew_analysis = self._analyze_volatility_skew(structure_points)
            
            # Identify calendar spread opportunities
            calendar_spreads = self._identify_calendar_spreads(structure_points)
            
            # Calculate risk metrics
            risk_metrics = self._calculate_term_structure_risks(structure_points)
            
            # Determine market regime
            market_regime = self._determine_market_regime(structure_points, slope)
            
            # Find trading opportunities
            opportunities = self._find_term_structure_opportunities(
                structure_points, slope, skew_analysis
            )
            
            return TermStructureAnalysis(
                structure_points=structure_points,
                term_structure_slope=slope,
                volatility_skew=skew_analysis,
                calendar_spreads=calendar_spreads,
                risk_metrics=risk_metrics,
                market_regime=market_regime,
                trading_opportunities=opportunities
            )
            
        except Exception as e:
            self.logger.error(f"Term structure analysis failed for {symbol}: {e}")
            raise
    
    def _extract_term_structure_points(self, option_chain_data: Dict) -> List[TermStructurePoint]:
        """Extract structured term structure points from option chain"""
        points = []
        
        for expiry_str, expiry_data in option_chain_data.items():
            try:
                expiry_date = datetime.strptime(expiry_str, '%Y%m%d')
                days_to_expiry = (expiry_date - datetime.now()).days
                
                if days_to_expiry <= 0:
                    continue
                
                # Process calls and puts for this expiry
                for option_type in ['calls', 'puts']:
                    if option_type in expiry_data:
                        for strike_str, option_data in expiry_data[option_type].items():
                            if 'impliedVolatility' in option_data and option_data['impliedVolatility'] > 0:
                                strike = float(strike_str)
                                spot_price = option_data.get('underlyingPrice', 100)
                                moneyness = strike / spot_price
                                
                                point = TermStructurePoint(
                                    expiry=expiry_date,
                                    days_to_expiry=days_to_expiry,
                                    implied_vol=option_data['impliedVolatility'],
                                    moneyness=moneyness,
                                    strike=strike,
                                    option_type=option_type[:-1],  # Remove 's'
                                    volume=option_data.get('volume'),
                                    open_interest=option_data.get('openInterest')
                                )
                                points.append(point)
                                
            except Exception as e:
                self.logger.warning(f"Failed to process expiry {expiry_str}: {e}")
                continue
        
        return sorted(points, key=lambda x: x.days_to_expiry)
    
    def _calculate_term_structure_slope(self, points: List[TermStructurePoint]) -> float:
        """Calculate the overall slope of the volatility term structure"""
        if len(points) < 2:
            return 0.0
        
        # Group by expiry and calculate ATM volatility for each
        expiry_vols = {}
        for point in points:
            if 0.95 <= point.moneyness <= 1.05:  # Near ATM
                expiry_key = point.days_to_expiry
                if expiry_key not in expiry_vols:
                    expiry_vols[expiry_key] = []
                expiry_vols[expiry_key].append(point.implied_vol)
        
        # Calculate average vol for each expiry
        term_structure_data = []
        for days, vols in expiry_vols.items():
            if vols:
                avg_vol = np.mean(vols)
                term_structure_data.append((days, avg_vol))
        
        if len(term_structure_data) < 2:
            return 0.0
        
        # Linear regression to find slope
        days = np.array([x[0] for x in term_structure_data])
        vols = np.array([x[1] for x in term_structure_data])
        
        slope = np.polyfit(days, vols, 1)[0]
        return float(slope)
    
    def _analyze_volatility_skew(self, points: List[TermStructurePoint]) -> Dict[str, float]:
        """Analyze volatility skew patterns across different terms"""
        skew_metrics = {
            'short_term_skew': 0.0,
            'medium_term_skew': 0.0,
            'long_term_skew': 0.0,
            'skew_term_structure': 0.0
        }
        
        # Group by term buckets
        short_term = [p for p in points if p.days_to_expiry <= 30]
        medium_term = [p for p in points if 30 < p.days_to_expiry <= 90]
        long_term = [p for p in points if p.days_to_expiry > 90]
        
        # Calculate skew for each term bucket
        skew_metrics['short_term_skew'] = self._calculate_skew_for_term(short_term)
        skew_metrics['medium_term_skew'] = self._calculate_skew_for_term(medium_term)
        skew_metrics['long_term_skew'] = self._calculate_skew_for_term(long_term)
        
        # Calculate term structure of skew
        skews = [skew_metrics['short_term_skew'], 
                skew_metrics['medium_term_skew'], 
                skew_metrics['long_term_skew']]
        
        if len([s for s in skews if s != 0]) >= 2:
            skew_metrics['skew_term_structure'] = skews[-1] - skews[0]
        
        return skew_metrics
    
    def _calculate_skew_for_term(self, points: List[TermStructurePoint]) -> float:
        """Calculate volatility skew for a specific term bucket"""
        if len(points) < 3:
            return 0.0
        
        # Find OTM puts (moneyness < 1) and OTM calls (moneyness > 1)
        otm_puts = [p for p in points if p.option_type == 'put' and p.moneyness < 0.95]
        otm_calls = [p for p in points if p.option_type == 'call' and p.moneyness > 1.05]
        atm_options = [p for p in points if 0.95 <= p.moneyness <= 1.05]
        
        if not otm_puts or not otm_calls or not atm_options:
            return 0.0
        
        # Calculate average volatilities
        put_vol = np.mean([p.implied_vol for p in otm_puts])
        call_vol = np.mean([p.implied_vol for p in otm_calls])
        atm_vol = np.mean([p.implied_vol for p in atm_options])
        
        # Skew measure: (put_vol - call_vol) / atm_vol
        if atm_vol > 0:
            return float((put_vol - call_vol) / atm_vol)
        
        return 0.0
    
    def _identify_calendar_spreads(self, points: List[TermStructurePoint]) -> List[Dict[str, Any]]:
        """Identify potential calendar spread opportunities"""
        calendar_spreads = []
        
        # Group by strike and option type
        option_groups = {}
        for point in points:
            key = (point.strike, point.option_type)
            if key not in option_groups:
                option_groups[key] = []
            option_groups[key].append(point)
        
        # Analyze calendar spreads for each strike/type
        for (strike, option_type), group in option_groups.items():
            if len(group) < 2:
                continue
            
            # Sort by expiry
            group.sort(key=lambda x: x.days_to_expiry)
            
            # Check consecutive expiries for calendar opportunities
            for i in range(len(group) - 1):
                near_option = group[i]
                far_option = group[i + 1]
                
                # Calculate calendar spread metrics
                vol_diff = far_option.implied_vol - near_option.implied_vol
                time_decay_advantage = self._calculate_time_decay_advantage(
                    near_option, far_option
                )
                
                if abs(vol_diff) > 0.05:  # Significant vol difference
                    calendar_spreads.append({
                        'strike': strike,
                        'option_type': option_type,
                        'near_expiry': near_option.expiry.strftime('%Y%m%d'),
                        'far_expiry': far_option.expiry.strftime('%Y%m%d'),
                        'near_vol': near_option.implied_vol,
                        'far_vol': far_option.implied_vol,
                        'vol_difference': vol_diff,
                        'time_decay_advantage': time_decay_advantage,
                        'recommendation': 'sell_calendar' if vol_diff > 0 else 'buy_calendar'
                    })
        
        return calendar_spreads
    
    def _calculate_time_decay_advantage(self, near_option: TermStructurePoint, 
                                      far_option: TermStructurePoint) -> float:
        """Calculate time decay advantage for calendar spreads"""
        # Simplified time decay calculation
        near_theta = near_option.days_to_expiry / 365
        far_theta = far_option.days_to_expiry / 365
        
        # Time decay advantage (higher is better for calendar seller)
        return near_theta - far_theta
    
    def _calculate_term_structure_risks(self, points: List[TermStructurePoint]) -> Dict[str, float]:
        """Calculate various risk metrics for the term structure"""
        if not points:
            return {}
        
        vols = [p.implied_vol for p in points]
        days = [p.days_to_expiry for p in points]
        
        return {
            'vol_volatility': float(np.std(vols)),
            'max_vol': float(max(vols)),
            'min_vol': float(min(vols)),
            'vol_range': float(max(vols) - min(vols)),
            'term_structure_curvature': self._calculate_curvature(points),
            'concentration_risk': self._calculate_concentration_risk(points)
        }
    
    def _calculate_curvature(self, points: List[TermStructurePoint]) -> float:
        """Calculate term structure curvature (convexity)"""
        if len(points) < 3:
            return 0.0
        
        # Group by expiry and get ATM vols
        expiry_vols = {}
        for point in points:
            if 0.95 <= point.moneyness <= 1.05:
                expiry_key = point.days_to_expiry
                if expiry_key not in expiry_vols:
                    expiry_vols[expiry_key] = []
                expiry_vols[expiry_key].append(point.implied_vol)
        
        if len(expiry_vols) < 3:
            return 0.0
        
        # Get sorted data points
        sorted_data = sorted([(days, np.mean(vols)) for days, vols in expiry_vols.items()])
        
        if len(sorted_data) < 3:
            return 0.0
        
        # Calculate second derivative (curvature)
        days = np.array([x[0] for x in sorted_data])
        vols = np.array([x[1] for x in sorted_data])
        
        # Fit quadratic and get curvature coefficient
        coeffs = np.polyfit(days, vols, 2)
        return float(coeffs[0])  # Second-order coefficient
    
    def _calculate_concentration_risk(self, points: List[TermStructurePoint]) -> float:
        """Calculate concentration risk in the term structure"""
        if not points:
            return 0.0
        
        # Calculate Herfindahl index for expiry concentration
        expiry_counts = {}
        for point in points:
            expiry_key = point.days_to_expiry
            expiry_counts[expiry_key] = expiry_counts.get(expiry_key, 0) + 1
        
        total_points = len(points)
        herfindahl = sum((count / total_points) ** 2 for count in expiry_counts.values())
        
        return float(herfindahl)
    
    def _determine_market_regime(self, points: List[TermStructurePoint], slope: float) -> str:
        """Determine current market volatility regime"""
        if not points:
            return "unknown"
        
        avg_vol = np.mean([p.implied_vol for p in points])
        vol_std = np.std([p.implied_vol for p in points])
        
        # Regime classification based on term structure slope and volatility levels
        if slope > 0.001:  # Positive slope
            if avg_vol > 0.3:
                return "high_vol_contango"
            else:
                return "normal_contango"
        elif slope < -0.001:  # Negative slope
            if avg_vol > 0.3:
                return "high_vol_backwardation"
            else:
                return "normal_backwardation"
        else:  # Flat structure
            if vol_std > 0.05:
                return "volatile_flat"
            else:
                return "stable_flat"
    
    def _find_term_structure_opportunities(self, points: List[TermStructurePoint], 
                                         slope: float, skew_analysis: Dict[str, float]) -> List[Dict[str, Any]]:
        """Identify trading opportunities based on term structure analysis"""
        opportunities = []
        
        # Term structure slope opportunities
        if abs(slope) > 0.002:  # Significant slope
            opportunities.append({
                'type': 'term_structure_trade',
                'strategy': 'calendar_spread' if slope > 0 else 'reverse_calendar',
                'rationale': f"Term structure slope of {slope:.4f} suggests opportunity",
                'confidence': min(abs(slope) * 1000, 100)
            })
        
        # Skew opportunities
        if abs(skew_analysis.get('short_term_skew', 0)) > 0.1:
            opportunities.append({
                'type': 'skew_trade',
                'strategy': 'straddle' if skew_analysis['short_term_skew'] < -0.1 else 'strangle',
                'rationale': f"Short-term skew of {skew_analysis['short_term_skew']:.3f}",
                'confidence': min(abs(skew_analysis['short_term_skew']) * 500, 100)
            })
        
        # Term structure of skew opportunities
        skew_ts = skew_analysis.get('skew_term_structure', 0)
        if abs(skew_ts) > 0.05:
            opportunities.append({
                'type': 'skew_calendar',
                'strategy': 'diagonal_spread',
                'rationale': f"Skew term structure differential of {skew_ts:.3f}",
                'confidence': min(abs(skew_ts) * 1000, 100)
            })
        
        return opportunities
