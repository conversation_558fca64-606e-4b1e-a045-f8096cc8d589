"""
Real-time Correlation Breakdown Detection

Provides comprehensive correlation monitoring for trading safety:
- Real-time correlation calculation and monitoring
- Correlation breakdown detection algorithms
- Multi-timeframe correlation analysis
- Alert system for correlation regime changes
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Any, List, Optional, Tuple, Callable
from dataclasses import dataclass, field
from collections import deque, defaultdict
import warnings

# Suppress pandas warnings
warnings.filterwarnings('ignore', category=FutureWarning)

logger = logging.getLogger(__name__)


class CorrelationRegime(Enum):
    """Correlation regime states"""
    STABLE = "stable"
    UNSTABLE = "unstable"
    BREAKDOWN = "breakdown"
    RECOVERY = "recovery"


@dataclass
class CorrelationBreakdown:
    """Correlation breakdown event"""
    timestamp: datetime
    symbol_pair: Tuple[str, str]
    previous_correlation: float
    current_correlation: float
    correlation_change: float
    regime: CorrelationRegime
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL
    timeframe: str
    confidence: float
    description: str
    metadata: Dict[str, Any]


@dataclass
class CorrelationMonitorConfig:
    """Configuration for correlation monitor"""
    name: str
    window_size: int = 50
    short_window: int = 10
    breakdown_threshold: float = 0.3  # Correlation change threshold
    stability_threshold: float = 0.1  # Stability threshold
    min_correlation: float = 0.5  # Minimum correlation to monitor
    update_frequency: int = 5  # Update every N data points
    timeframes: List[str] = field(default_factory=lambda: ['1min', '5min', '15min', '1hour'])


class CorrelationMonitor:
    """
    Real-time correlation breakdown detection system
    
    Monitors correlations between trading pairs and detects
    significant breakdowns that could indicate regime changes.
    """
    
    def __init__(self, config: CorrelationMonitorConfig):
        self.config = config
        
        # Data storage for different timeframes
        self.price_data: Dict[str, Dict[str, deque]] = defaultdict(
            lambda: defaultdict(lambda: deque(maxlen=config.window_size))
        )
        
        # Correlation tracking
        self.correlations: Dict[str, Dict[Tuple[str, str], deque]] = defaultdict(
            lambda: defaultdict(lambda: deque(maxlen=100))
        )
        
        # Breakdown tracking
        self.recent_breakdowns = deque(maxlen=100)
        self.breakdown_callbacks: List[Callable] = []
        
        # Regime tracking
        self.correlation_regimes: Dict[Tuple[str, str], CorrelationRegime] = {}
        
        # Performance metrics
        self.monitor_stats = {
            'total_updates': 0,
            'breakdowns_detected': 0,
            'pairs_monitored': 0,
            'last_update': datetime.now()
        }
        
        logger.info(f"Correlation monitor '{config.name}' initialized")
    
    async def update_prices(self, symbol: str, price: float, timestamp: Optional[datetime] = None):
        """
        Update price data for a symbol
        
        Args:
            symbol: Trading symbol
            price: Current price
            timestamp: Optional timestamp (defaults to now)
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        # Store price data for all timeframes
        for timeframe in self.config.timeframes:
            self.price_data[timeframe][symbol].append((timestamp, price))
        
        # Update correlations periodically
        self.monitor_stats['total_updates'] += 1
        if self.monitor_stats['total_updates'] % self.config.update_frequency == 0:
            await self._update_correlations()
    
    async def _update_correlations(self):
        """Update correlation calculations and detect breakdowns"""
        for timeframe in self.config.timeframes:
            await self._update_correlations_for_timeframe(timeframe)
    
    async def _update_correlations_for_timeframe(self, timeframe: str):
        """Update correlations for a specific timeframe"""
        symbols = list(self.price_data[timeframe].keys())
        
        if len(symbols) < 2:
            return
        
        # Calculate correlations for all pairs
        for i, symbol1 in enumerate(symbols):
            for j, symbol2 in enumerate(symbols[i+1:], i+1):
                await self._calculate_pair_correlation(timeframe, symbol1, symbol2)
    
    async def _calculate_pair_correlation(self, timeframe: str, symbol1: str, symbol2: str):
        """Calculate correlation for a specific pair"""
        data1 = self.price_data[timeframe][symbol1]
        data2 = self.price_data[timeframe][symbol2]
        
        if len(data1) < self.config.window_size or len(data2) < self.config.window_size:
            return
        
        # Extract prices and align timestamps
        prices1, prices2 = self._align_price_data(data1, data2)
        
        if len(prices1) < self.config.short_window:
            return
        
        # Calculate rolling correlations
        long_corr = self._calculate_correlation(prices1, prices2)
        short_corr = self._calculate_correlation(
            prices1[-self.config.short_window:], 
            prices2[-self.config.short_window:]
        )
        
        if long_corr is None or short_corr is None:
            return
        
        # Store correlation
        pair = (symbol1, symbol2)
        self.correlations[timeframe][pair].append((datetime.now(), long_corr, short_corr))
        
        # Check for breakdown
        await self._check_correlation_breakdown(timeframe, pair, long_corr, short_corr)
    
    def _align_price_data(self, data1: deque, data2: deque) -> Tuple[List[float], List[float]]:
        """Align price data by timestamp"""
        # Convert to lists for easier processing
        list1 = list(data1)
        list2 = list(data2)
        
        # Create timestamp-price mappings
        prices1_dict = {ts: price for ts, price in list1}
        prices2_dict = {ts: price for ts, price in list2}
        
        # Find common timestamps
        common_timestamps = set(prices1_dict.keys()) & set(prices2_dict.keys())
        common_timestamps = sorted(common_timestamps)
        
        # Extract aligned prices
        prices1 = [prices1_dict[ts] for ts in common_timestamps]
        prices2 = [prices2_dict[ts] for ts in common_timestamps]
        
        return prices1, prices2
    
    def _calculate_correlation(self, prices1: List[float], prices2: List[float]) -> Optional[float]:
        """Calculate correlation between two price series"""
        if len(prices1) != len(prices2) or len(prices1) < 3:
            return None
        
        try:
            # Calculate returns
            returns1 = np.diff(prices1) / np.array(prices1[:-1])
            returns2 = np.diff(prices2) / np.array(prices2[:-1])
            
            # Handle edge cases
            if len(returns1) == 0 or np.std(returns1) == 0 or np.std(returns2) == 0:
                return None
            
            # Calculate correlation
            correlation = np.corrcoef(returns1, returns2)[0, 1]
            
            # Handle NaN
            if np.isnan(correlation):
                return None
            
            return correlation
        
        except Exception as e:
            logger.debug(f"Correlation calculation error: {e}")
            return None
    
    async def _check_correlation_breakdown(self, timeframe: str, pair: Tuple[str, str], 
                                         long_corr: float, short_corr: float):
        """Check for correlation breakdown"""
        # Calculate correlation change
        corr_change = abs(long_corr - short_corr)
        
        # Check if correlation is significant enough to monitor
        if abs(long_corr) < self.config.min_correlation:
            return
        
        # Determine current regime
        current_regime = self._determine_regime(pair, long_corr, short_corr, corr_change)
        
        # Check for breakdown
        if corr_change > self.config.breakdown_threshold:
            severity = self._calculate_breakdown_severity(corr_change, long_corr, short_corr)
            confidence = min(corr_change / self.config.breakdown_threshold, 1.0)
            
            breakdown = CorrelationBreakdown(
                timestamp=datetime.now(),
                symbol_pair=pair,
                previous_correlation=long_corr,
                current_correlation=short_corr,
                correlation_change=corr_change,
                regime=current_regime,
                severity=severity,
                timeframe=timeframe,
                confidence=confidence,
                description=f"Correlation breakdown: {pair[0]}-{pair[1]} "
                           f"changed by {corr_change:.3f} in {timeframe}",
                metadata={
                    'long_correlation': long_corr,
                    'short_correlation': short_corr,
                    'window_size': self.config.window_size,
                    'short_window': self.config.short_window
                }
            )
            
            # Store breakdown
            self.recent_breakdowns.append(breakdown)
            self.monitor_stats['breakdowns_detected'] += 1
            
            # Update regime
            self.correlation_regimes[pair] = current_regime
            
            # Trigger callbacks
            await self._trigger_callbacks(breakdown)
            
            logger.warning(
                f"Correlation breakdown detected: {pair[0]}-{pair[1]} "
                f"({timeframe}) - Change: {corr_change:.3f}, Severity: {severity}"
            )
    
    def _determine_regime(self, pair: Tuple[str, str], long_corr: float, 
                         short_corr: float, corr_change: float) -> CorrelationRegime:
        """Determine correlation regime"""
        previous_regime = self.correlation_regimes.get(pair, CorrelationRegime.STABLE)
        
        if corr_change > self.config.breakdown_threshold:
            if abs(short_corr) < abs(long_corr) * 0.5:  # Significant drop
                return CorrelationRegime.BREAKDOWN
            else:
                return CorrelationRegime.UNSTABLE
        
        elif corr_change < self.config.stability_threshold:
            if previous_regime in [CorrelationRegime.BREAKDOWN, CorrelationRegime.UNSTABLE]:
                return CorrelationRegime.RECOVERY
            else:
                return CorrelationRegime.STABLE
        
        else:
            return CorrelationRegime.UNSTABLE
    
    def _calculate_breakdown_severity(self, corr_change: float, long_corr: float, 
                                    short_corr: float) -> str:
        """Calculate breakdown severity"""
        # Base severity on correlation change magnitude
        if corr_change > 0.7:
            return "CRITICAL"
        elif corr_change > 0.5:
            return "HIGH"
        elif corr_change > 0.3:
            return "MEDIUM"
        else:
            return "LOW"
    
    async def _trigger_callbacks(self, breakdown: CorrelationBreakdown):
        """Trigger registered callbacks for breakdown events"""
        for callback in self.breakdown_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(breakdown)
                else:
                    callback(breakdown)
            except Exception as e:
                logger.error(f"Correlation breakdown callback error: {e}")
    
    def add_callback(self, callback: Callable):
        """Add callback for correlation breakdown events"""
        self.breakdown_callbacks.append(callback)
    
    def get_current_correlations(self, timeframe: str = "1min") -> Dict[Tuple[str, str], float]:
        """Get current correlations for all pairs"""
        current_correlations = {}
        
        for pair, corr_history in self.correlations[timeframe].items():
            if corr_history:
                _, long_corr, _ = corr_history[-1]
                current_correlations[pair] = long_corr
        
        return current_correlations
    
    def get_correlation_history(self, symbol1: str, symbol2: str, 
                              timeframe: str = "1min", limit: int = 50) -> List[Tuple[datetime, float, float]]:
        """Get correlation history for a specific pair"""
        pair = (symbol1, symbol2)
        if pair not in self.correlations[timeframe]:
            # Try reverse pair
            pair = (symbol2, symbol1)
        
        if pair in self.correlations[timeframe]:
            return list(self.correlations[timeframe][pair])[-limit:]
        
        return []
    
    def get_recent_breakdowns(self, limit: int = 10) -> List[CorrelationBreakdown]:
        """Get recent correlation breakdowns"""
        return list(self.recent_breakdowns)[-limit:]
    
    def get_regime_status(self) -> Dict[Tuple[str, str], CorrelationRegime]:
        """Get current regime status for all pairs"""
        return self.correlation_regimes.copy()
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get monitoring statistics"""
        # Count unique pairs being monitored
        unique_pairs = set()
        for timeframe_correlations in self.correlations.values():
            unique_pairs.update(timeframe_correlations.keys())
        
        self.monitor_stats['pairs_monitored'] = len(unique_pairs)
        self.monitor_stats['last_update'] = datetime.now()
        
        return {
            'config': {
                'name': self.config.name,
                'window_size': self.config.window_size,
                'breakdown_threshold': self.config.breakdown_threshold,
                'timeframes': self.config.timeframes
            },
            'stats': self.monitor_stats.copy(),
            'recent_breakdown_count': len(self.recent_breakdowns),
            'regime_count': len(self.correlation_regimes)
        }
    
    async def check_pair_stability(self, symbol1: str, symbol2: str, 
                                 timeframe: str = "1min") -> Dict[str, Any]:
        """Check stability of a specific correlation pair"""
        pair = (symbol1, symbol2)
        if pair not in self.correlations[timeframe]:
            pair = (symbol2, symbol1)
        
        if pair not in self.correlations[timeframe]:
            return {
                'status': 'unknown',
                'message': 'Pair not being monitored'
            }
        
        corr_history = list(self.correlations[timeframe][pair])
        if len(corr_history) < 5:
            return {
                'status': 'insufficient_data',
                'message': 'Not enough correlation history'
            }
        
        # Get recent correlations
        recent_correlations = [corr for _, corr, _ in corr_history[-10:]]
        correlation_std = np.std(recent_correlations)
        
        # Determine stability
        if correlation_std < self.config.stability_threshold:
            status = 'stable'
        elif correlation_std < self.config.breakdown_threshold:
            status = 'unstable'
        else:
            status = 'breakdown'
        
        current_regime = self.correlation_regimes.get(pair, CorrelationRegime.STABLE)
        
        return {
            'status': status,
            'regime': current_regime.value,
            'correlation_std': correlation_std,
            'recent_correlations': recent_correlations,
            'current_correlation': recent_correlations[-1] if recent_correlations else None,
            'stability_score': max(0.0, 1.0 - float(correlation_std) / self.config.breakdown_threshold)
        }
    
    def reset(self):
        """Reset monitor state"""
        self.price_data.clear()
        self.correlations.clear()
        self.recent_breakdowns.clear()
        self.correlation_regimes.clear()
        
        self.monitor_stats = {
            'total_updates': 0,
            'breakdowns_detected': 0,
            'pairs_monitored': 0,
            'last_update': datetime.now()
        }
        
        logger.info(f"Correlation monitor '{self.config.name}' reset")
