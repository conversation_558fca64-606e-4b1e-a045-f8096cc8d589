"""
Advanced Anomaly Detection System

Provides comprehensive anomaly detection for trading safety:
- Statistical anomaly detection (Z-score based)
- Machine learning anomaly detection (Isolation Forest)
- Real-time price and volume anomaly monitoring
- Multi-dimensional anomaly analysis
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Any, List, Optional, Tuple, Callable
from dataclasses import dataclass
from collections import deque
import warnings

# Suppress sklearn warnings for cleaner logs
warnings.filterwarnings('ignore', category=UserWarning)

try:
    from sklearn.ensemble import IsolationForest
    from sklearn.preprocessing import StandardScaler
    from scipy import stats
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logging.warning("scikit-learn not available, using statistical methods only")

logger = logging.getLogger(__name__)


class AnomalyType(Enum):
    """Types of anomalies that can be detected"""
    PRICE_SPIKE = "price_spike"
    VOLUME_SPIKE = "volume_spike"
    VOLATILITY_SPIKE = "volatility_spike"
    CORRELATION_BREAKDOWN = "correlation_breakdown"
    LIQUIDITY_DROUGHT = "liquidity_drought"
    ORDER_FLOW_ANOMALY = "order_flow_anomaly"
    SYSTEM_PERFORMANCE = "system_performance"


@dataclass
class AnomalyAlert:
    """Anomaly detection alert"""
    timestamp: datetime
    symbol: str
    anomaly_type: AnomalyType
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL
    confidence: float  # 0.0 to 1.0
    value: float
    threshold: float
    description: str
    metadata: Dict[str, Any]


@dataclass
class AnomalyDetectorConfig:
    """Configuration for anomaly detector"""
    name: str
    window_size: int = 100
    zscore_threshold: float = 3.0
    contamination: float = 0.1  # For Isolation Forest
    min_samples: int = 20
    update_frequency: int = 10  # Update model every N samples
    enable_ml: bool = True


class AnomalyDetector:
    """
    Advanced anomaly detection system for trading safety
    
    Combines statistical and machine learning approaches to detect
    various types of market and system anomalies.
    """
    
    def __init__(self, config: AnomalyDetectorConfig):
        self.config = config
        self.data_buffer = deque(maxlen=config.window_size)
        self.model_update_counter = 0
        
        # Statistical components
        self.scaler = StandardScaler() if SKLEARN_AVAILABLE else None
        self.isolation_forest = None
        
        # Anomaly tracking
        self.recent_anomalies = deque(maxlen=50)
        self.anomaly_callbacks: List[Callable] = []
        
        # Performance metrics
        self.detection_stats = {
            'total_samples': 0,
            'anomalies_detected': 0,
            'false_positives': 0,
            'last_update': datetime.now()
        }
        
        if SKLEARN_AVAILABLE and config.enable_ml:
            self._initialize_ml_models()
        
        logger.info(f"Anomaly detector '{config.name}' initialized")
    
    def _initialize_ml_models(self):
        """Initialize machine learning models"""
        if not SKLEARN_AVAILABLE:
            return
        
        self.isolation_forest = IsolationForest(
            contamination=self.config.contamination,
            random_state=42,
            n_estimators=100
        )
        
        logger.info("ML models initialized for anomaly detection")
    
    async def detect_price_anomaly(self, symbol: str, price_data: List[float], 
                                 volume_data: Optional[List[float]] = None) -> List[AnomalyAlert]:
        """
        Detect price anomalies using multiple methods
        
        Args:
            symbol: Trading symbol
            price_data: List of price values
            volume_data: Optional volume data
            
        Returns:
            List of anomaly alerts
        """
        alerts = []
        
        if len(price_data) < self.config.min_samples:
            return alerts
        
        # Statistical anomaly detection
        statistical_anomalies = self._detect_statistical_anomalies(
            symbol, price_data, AnomalyType.PRICE_SPIKE
        )
        alerts.extend(statistical_anomalies)
        
        # Volume anomaly detection if data available
        if volume_data and len(volume_data) == len(price_data):
            volume_anomalies = self._detect_statistical_anomalies(
                symbol, volume_data, AnomalyType.VOLUME_SPIKE
            )
            alerts.extend(volume_anomalies)
        
        # Volatility anomaly detection
        if len(price_data) > 2:
            volatility_anomalies = self._detect_volatility_anomalies(symbol, price_data)
            alerts.extend(volatility_anomalies)
        
        # ML-based anomaly detection
        if SKLEARN_AVAILABLE and self.config.enable_ml:
            ml_anomalies = await self._detect_ml_anomalies(symbol, price_data, volume_data)
            alerts.extend(ml_anomalies)
        
        # Update statistics
        self.detection_stats['total_samples'] += len(price_data)
        self.detection_stats['anomalies_detected'] += len(alerts)
        self.detection_stats['last_update'] = datetime.now()
        
        # Store recent anomalies
        for alert in alerts:
            self.recent_anomalies.append(alert)
        
        # Trigger callbacks
        for alert in alerts:
            await self._trigger_callbacks(alert)
        
        return alerts
    
    def _detect_statistical_anomalies(self, symbol: str, data: List[float], 
                                    anomaly_type: AnomalyType) -> List[AnomalyAlert]:
        """Detect anomalies using statistical methods"""
        alerts = []
        
        if len(data) < self.config.min_samples:
            return alerts
        
        # Convert to numpy array
        data_array = np.array(data)
        
        # Calculate Z-scores
        z_scores = np.abs(stats.zscore(data_array))
        
        # Find anomalies
        anomaly_indices = np.where(z_scores > self.config.zscore_threshold)[0]
        
        for idx in anomaly_indices:
            severity = self._calculate_severity(z_scores[idx])
            confidence = min(z_scores[idx] / 5.0, 1.0)  # Normalize to 0-1
            
            alert = AnomalyAlert(
                timestamp=datetime.now(),
                symbol=symbol,
                anomaly_type=anomaly_type,
                severity=severity,
                confidence=confidence,
                value=float(data_array[idx]),
                threshold=self.config.zscore_threshold,
                description=f"{anomaly_type.value} detected: Z-score {z_scores[idx]:.2f}",
                metadata={
                    'z_score': z_scores[idx],
                    'data_index': idx,
                    'method': 'statistical'
                }
            )
            alerts.append(alert)
        
        return alerts
    
    def _detect_volatility_anomalies(self, symbol: str, price_data: List[float]) -> List[AnomalyAlert]:
        """Detect volatility anomalies"""
        alerts = []
        
        if len(price_data) < 10:
            return alerts
        
        # Calculate returns
        prices = np.array(price_data)
        returns = np.diff(prices) / prices[:-1]
        
        # Calculate rolling volatility
        window = min(20, len(returns) // 2)
        if window < 5:
            return alerts
        
        volatilities = []
        for i in range(window, len(returns)):
            vol = np.std(returns[i-window:i])
            volatilities.append(vol)
        
        if len(volatilities) < 5:
            return alerts
        
        # Detect volatility spikes
        vol_array = np.array(volatilities)
        vol_z_scores = np.abs(stats.zscore(vol_array))
        
        anomaly_indices = np.where(vol_z_scores > self.config.zscore_threshold)[0]
        
        for idx in anomaly_indices:
            severity = self._calculate_severity(vol_z_scores[idx])
            confidence = min(vol_z_scores[idx] / 4.0, 1.0)
            
            alert = AnomalyAlert(
                timestamp=datetime.now(),
                symbol=symbol,
                anomaly_type=AnomalyType.VOLATILITY_SPIKE,
                severity=severity,
                confidence=confidence,
                value=float(vol_array[idx]),
                threshold=self.config.zscore_threshold,
                description=f"Volatility spike detected: {vol_array[idx]:.4f}",
                metadata={
                    'volatility_z_score': vol_z_scores[idx],
                    'volatility_window': window,
                    'method': 'volatility_analysis'
                }
            )
            alerts.append(alert)
        
        return alerts
    
    async def _detect_ml_anomalies(self, symbol: str, price_data: List[float], 
                                 volume_data: Optional[List[float]] = None) -> List[AnomalyAlert]:
        """Detect anomalies using machine learning"""
        alerts = []
        
        if not SKLEARN_AVAILABLE or not self.config.enable_ml:
            return alerts
        
        # Prepare feature matrix
        features = self._prepare_features(price_data, volume_data)
        if features is None or len(features) < self.config.min_samples:
            return alerts
        
        # Update model periodically
        self.model_update_counter += 1
        if (self.model_update_counter % self.config.update_frequency == 0 or 
            self.isolation_forest is None):
            await self._update_ml_models(features)
        
        # Predict anomalies
        if self.isolation_forest is not None:
            try:
                predictions = self.isolation_forest.predict(features)
                scores = self.isolation_forest.decision_function(features)
                
                # Find anomalies (prediction = -1)
                anomaly_indices = np.where(predictions == -1)[0]
                
                for idx in anomaly_indices:
                    # Convert score to confidence (scores are negative for anomalies)
                    confidence = min(float(abs(scores[idx])) / 0.5, 1.0)
                    severity = self._calculate_severity_from_score(float(scores[idx]))
                    
                    alert = AnomalyAlert(
                        timestamp=datetime.now(),
                        symbol=symbol,
                        anomaly_type=AnomalyType.PRICE_SPIKE,  # Default type
                        severity=severity,
                        confidence=confidence,
                        value=price_data[idx] if idx < len(price_data) else 0,
                        threshold=self.config.contamination,
                        description=f"ML anomaly detected: score {scores[idx]:.3f}",
                        metadata={
                            'ml_score': scores[idx],
                            'data_index': idx,
                            'method': 'isolation_forest'
                        }
                    )
                    alerts.append(alert)
            
            except Exception as e:
                logger.error(f"ML anomaly detection error: {e}")
        
        return alerts
    
    def _prepare_features(self, price_data: List[float], 
                         volume_data: Optional[List[float]] = None) -> Optional[np.ndarray]:
        """Prepare feature matrix for ML models"""
        if len(price_data) < 5:
            return None
        
        prices = np.array(price_data)
        features = []
        
        # Price-based features
        returns = np.diff(prices) / prices[:-1]
        features.append(returns)
        
        # Moving averages
        if len(prices) >= 5:
            ma5 = np.convolve(prices, np.ones(5)/5, mode='valid')
            ma_ratio = prices[4:] / ma5
            features.append(ma_ratio)
        
        # Volume features if available
        if volume_data and len(volume_data) == len(price_data):
            volumes = np.array(volume_data)
            vol_returns = np.diff(volumes) / (volumes[:-1] + 1e-8)
            features.append(vol_returns[:len(returns)])
        
        # Ensure all features have same length
        min_length = min(len(f) for f in features)
        features = [f[:min_length] for f in features]
        
        if min_length < 3:
            return None
        
        # Stack features
        feature_matrix = np.column_stack(features)
        
        # Handle NaN and infinite values
        feature_matrix = np.nan_to_num(feature_matrix, nan=0.0, posinf=1e6, neginf=-1e6)
        
        return feature_matrix
    
    async def _update_ml_models(self, features: np.ndarray):
        """Update machine learning models with new data"""
        if not SKLEARN_AVAILABLE:
            return
        
        try:
            # Fit isolation forest
            if self.isolation_forest is not None:
                self.isolation_forest.fit(features)
                logger.debug(f"Updated ML models with {len(features)} samples")
        
        except Exception as e:
            logger.error(f"Error updating ML models: {e}")
    
    def _calculate_severity(self, z_score: float) -> str:
        """Calculate severity based on Z-score"""
        if z_score >= 5.0:
            return "CRITICAL"
        elif z_score >= 4.0:
            return "HIGH"
        elif z_score >= 3.5:
            return "MEDIUM"
        else:
            return "LOW"
    
    def _calculate_severity_from_score(self, score: float) -> str:
        """Calculate severity from ML score"""
        abs_score = abs(score)
        if abs_score >= 0.5:
            return "CRITICAL"
        elif abs_score >= 0.3:
            return "HIGH"
        elif abs_score >= 0.2:
            return "MEDIUM"
        else:
            return "LOW"
    
    async def _trigger_callbacks(self, alert: AnomalyAlert):
        """Trigger registered callbacks for anomaly alerts"""
        for callback in self.anomaly_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(alert)
                else:
                    callback(alert)
            except Exception as e:
                logger.error(f"Anomaly callback error: {e}")
    
    def add_callback(self, callback: Callable):
        """Add callback for anomaly alerts"""
        self.anomaly_callbacks.append(callback)
    
    def get_recent_anomalies(self, limit: int = 10) -> List[AnomalyAlert]:
        """Get recent anomaly alerts"""
        return list(self.recent_anomalies)[-limit:]
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get detection statistics"""
        return {
            'config': {
                'name': self.config.name,
                'window_size': self.config.window_size,
                'zscore_threshold': self.config.zscore_threshold,
                'ml_enabled': self.config.enable_ml and SKLEARN_AVAILABLE
            },
            'stats': self.detection_stats.copy(),
            'recent_anomaly_count': len(self.recent_anomalies),
            'buffer_size': len(self.data_buffer)
        }
    
    def reset(self):
        """Reset detector state"""
        self.data_buffer.clear()
        self.recent_anomalies.clear()
        self.model_update_counter = 0
        
        if SKLEARN_AVAILABLE and self.config.enable_ml:
            self._initialize_ml_models()
        
        self.detection_stats = {
            'total_samples': 0,
            'anomalies_detected': 0,
            'false_positives': 0,
            'last_update': datetime.now()
        }
        
        logger.info(f"Anomaly detector '{self.config.name}' reset")
