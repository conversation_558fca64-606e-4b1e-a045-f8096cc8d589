"""
System Overload Protection

Provides comprehensive system monitoring and overload protection:
- Resource monitoring (CPU, memory, network)
- Request rate limiting and queue management
- Graceful degradation mechanisms
- Performance-based circuit breaking
"""

import asyncio
import logging
import psutil
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from collections import deque
import threading

logger = logging.getLogger(__name__)


class SystemState(Enum):
    """System performance states"""
    OPTIMAL = "optimal"
    DEGRADED = "degraded"
    OVERLOADED = "overloaded"
    CRITICAL = "critical"


class ResourceType(Enum):
    """Types of system resources"""
    CPU = "cpu"
    MEMORY = "memory"
    DISK = "disk"
    NETWORK = "network"
    CONNECTIONS = "connections"


@dataclass
class SystemAlert:
    """System performance alert"""
    timestamp: datetime
    resource_type: ResourceType
    current_value: float
    threshold: float
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL
    state: SystemState
    description: str
    metadata: Dict[str, Any]


@dataclass
class SystemMonitorConfig:
    """Configuration for system monitor"""
    name: str
    cpu_threshold: float = 80.0  # CPU usage percentage
    memory_threshold: float = 85.0  # Memory usage percentage
    disk_threshold: float = 90.0  # Disk usage percentage
    network_threshold: float = 100.0  # MB/s
    connection_threshold: int = 1000  # Max connections
    check_interval: float = 5.0  # seconds
    history_size: int = 100
    enable_auto_throttle: bool = True


class SystemOverloadProtection:
    """
    System overload protection with automatic throttling
    """
    
    def __init__(self, config: SystemMonitorConfig):
        self.config = config
        self.current_state = SystemState.OPTIMAL
        self.throttle_factor = 1.0  # 1.0 = no throttling, 0.5 = 50% throttling
        
        # Request rate limiting
        self.request_queue = asyncio.Queue()
        self.active_requests = 0
        self.max_concurrent_requests = 100
        
        # Performance tracking
        self.request_times = deque(maxlen=1000)
        self.error_count = 0
        self.total_requests = 0
        
        logger.info("System overload protection initialized")
    
    async def execute_with_protection(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with overload protection
        
        Args:
            func: Function to execute
            *args, **kwargs: Function arguments
            
        Returns:
            Function result
            
        Raises:
            SystemOverloadError: If system is overloaded
        """
        # Check if we should throttle
        if self.throttle_factor < 1.0:
            delay = (1.0 - self.throttle_factor) * 0.1  # Max 100ms delay
            await asyncio.sleep(delay)
        
        # Check concurrent request limit
        if self.active_requests >= self.max_concurrent_requests:
            raise SystemOverloadError("Too many concurrent requests")
        
        # Check system state
        if self.current_state == SystemState.CRITICAL:
            raise SystemOverloadError("System in critical state")
        
        start_time = time.time()
        self.active_requests += 1
        self.total_requests += 1
        
        try:
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # Record successful execution time
            execution_time = time.time() - start_time
            self.request_times.append(execution_time)
            
            return result
        
        except Exception as e:
            self.error_count += 1
            raise
        
        finally:
            self.active_requests -= 1
    
    def update_throttle_factor(self, system_state: SystemState, resource_usage: Dict[str, float]):
        """Update throttling based on system state"""
        if not self.config.enable_auto_throttle:
            return
        
        if system_state == SystemState.CRITICAL:
            self.throttle_factor = 0.1  # Heavy throttling
        elif system_state == SystemState.OVERLOADED:
            self.throttle_factor = 0.3  # Moderate throttling
        elif system_state == SystemState.DEGRADED:
            self.throttle_factor = 0.7  # Light throttling
        else:
            self.throttle_factor = 1.0  # No throttling
        
        # Adjust based on specific resource usage
        cpu_usage = resource_usage.get('cpu', 0)
        memory_usage = resource_usage.get('memory', 0)
        
        if cpu_usage > 90 or memory_usage > 95:
            self.throttle_factor = min(self.throttle_factor, 0.2)
        
        self.current_state = system_state
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        avg_response_time = 0
        if self.request_times:
            avg_response_time = sum(self.request_times) / len(self.request_times)
        
        error_rate = 0
        if self.total_requests > 0:
            error_rate = self.error_count / self.total_requests
        
        return {
            'current_state': self.current_state.value,
            'throttle_factor': self.throttle_factor,
            'active_requests': self.active_requests,
            'total_requests': self.total_requests,
            'error_count': self.error_count,
            'error_rate': error_rate,
            'avg_response_time': avg_response_time,
            'max_concurrent_requests': self.max_concurrent_requests
        }


class SystemMonitor:
    """
    Comprehensive system monitoring and overload protection
    """
    
    def __init__(self, config: SystemMonitorConfig):
        self.config = config
        self.overload_protection = SystemOverloadProtection(config)
        
        # Resource monitoring
        self.resource_history: Dict[ResourceType, deque] = {
            resource: deque(maxlen=config.history_size)
            for resource in ResourceType
        }
        
        # Alert system
        self.recent_alerts = deque(maxlen=50)
        self.alert_callbacks: List[Callable] = []
        
        # Monitoring state
        self.monitoring_active = False
        self.monitor_task = None
        
        # Performance baselines
        self.baseline_cpu = 0.0
        self.baseline_memory = 0.0
        self.baseline_established = False
        
        logger.info(f"System monitor '{config.name}' initialized")
    
    async def start_monitoring(self):
        """Start system monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        logger.info("System monitoring started")
    
    async def stop_monitoring(self):
        """Stop system monitoring"""
        self.monitoring_active = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("System monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                await self._collect_metrics()
                await self._analyze_system_state()
                await asyncio.sleep(self.config.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
                await asyncio.sleep(self.config.check_interval)
    
    async def _collect_metrics(self):
        """Collect system metrics"""
        timestamp = datetime.now()
        
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=None)
        self.resource_history[ResourceType.CPU].append((timestamp, cpu_percent))
        
        # Memory usage
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        self.resource_history[ResourceType.MEMORY].append((timestamp, memory_percent))
        
        # Disk usage
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        self.resource_history[ResourceType.DISK].append((timestamp, disk_percent))
        
        # Network I/O
        try:
            network = psutil.net_io_counters()
            # Calculate network rate (simplified)
            network_rate = (network.bytes_sent + network.bytes_recv) / (1024 * 1024)  # MB
            self.resource_history[ResourceType.NETWORK].append((timestamp, network_rate))
        except Exception:
            self.resource_history[ResourceType.NETWORK].append((timestamp, 0))
        
        # Connection count (simplified)
        try:
            connections = len(psutil.net_connections())
            self.resource_history[ResourceType.CONNECTIONS].append((timestamp, connections))
        except Exception:
            self.resource_history[ResourceType.CONNECTIONS].append((timestamp, 0))
        
        # Establish baseline if not done
        if not self.baseline_established and len(self.resource_history[ResourceType.CPU]) >= 10:
            self._establish_baseline()
    
    def _establish_baseline(self):
        """Establish performance baseline"""
        cpu_values = [value for _, value in list(self.resource_history[ResourceType.CPU])[-10:]]
        memory_values = [value for _, value in list(self.resource_history[ResourceType.MEMORY])[-10:]]
        
        self.baseline_cpu = sum(cpu_values) / len(cpu_values)
        self.baseline_memory = sum(memory_values) / len(memory_values)
        self.baseline_established = True
        
        logger.info(f"Performance baseline established: CPU {self.baseline_cpu:.1f}%, Memory {self.baseline_memory:.1f}%")
    
    async def _analyze_system_state(self):
        """Analyze current system state and generate alerts"""
        current_metrics = self._get_current_metrics()
        system_state = self._determine_system_state(current_metrics)
        
        # Update overload protection
        self.overload_protection.update_throttle_factor(system_state, current_metrics)
        
        # Check for threshold violations
        await self._check_thresholds(current_metrics, system_state)
    
    def _get_current_metrics(self) -> Dict[str, float]:
        """Get current resource metrics"""
        metrics = {}
        
        for resource_type in ResourceType:
            history = self.resource_history[resource_type]
            if history:
                _, current_value = history[-1]
                metrics[resource_type.value] = current_value
        
        return metrics
    
    def _determine_system_state(self, metrics: Dict[str, float]) -> SystemState:
        """Determine overall system state"""
        cpu = metrics.get('cpu', 0)
        memory = metrics.get('memory', 0)
        disk = metrics.get('disk', 0)
        
        # Critical state
        if cpu > 95 or memory > 98 or disk > 98:
            return SystemState.CRITICAL
        
        # Overloaded state
        if cpu > self.config.cpu_threshold or memory > self.config.memory_threshold:
            return SystemState.OVERLOADED
        
        # Degraded state
        if (cpu > self.config.cpu_threshold * 0.8 or 
            memory > self.config.memory_threshold * 0.8):
            return SystemState.DEGRADED
        
        return SystemState.OPTIMAL
    
    async def _check_thresholds(self, metrics: Dict[str, float], system_state: SystemState):
        """Check resource thresholds and generate alerts"""
        alerts = []
        
        # CPU threshold
        cpu = metrics.get('cpu', 0)
        if cpu > self.config.cpu_threshold:
            severity = self._calculate_severity(cpu, self.config.cpu_threshold)
            alert = SystemAlert(
                timestamp=datetime.now(),
                resource_type=ResourceType.CPU,
                current_value=cpu,
                threshold=self.config.cpu_threshold,
                severity=severity,
                state=system_state,
                description=f"CPU usage {cpu:.1f}% exceeds threshold {self.config.cpu_threshold:.1f}%",
                metadata={'baseline': self.baseline_cpu}
            )
            alerts.append(alert)
        
        # Memory threshold
        memory = metrics.get('memory', 0)
        if memory > self.config.memory_threshold:
            severity = self._calculate_severity(memory, self.config.memory_threshold)
            alert = SystemAlert(
                timestamp=datetime.now(),
                resource_type=ResourceType.MEMORY,
                current_value=memory,
                threshold=self.config.memory_threshold,
                severity=severity,
                state=system_state,
                description=f"Memory usage {memory:.1f}% exceeds threshold {self.config.memory_threshold:.1f}%",
                metadata={'baseline': self.baseline_memory}
            )
            alerts.append(alert)
        
        # Disk threshold
        disk = metrics.get('disk', 0)
        if disk > self.config.disk_threshold:
            severity = self._calculate_severity(disk, self.config.disk_threshold)
            alert = SystemAlert(
                timestamp=datetime.now(),
                resource_type=ResourceType.DISK,
                current_value=disk,
                threshold=self.config.disk_threshold,
                severity=severity,
                state=system_state,
                description=f"Disk usage {disk:.1f}% exceeds threshold {self.config.disk_threshold:.1f}%",
                metadata={}
            )
            alerts.append(alert)
        
        # Process alerts
        for alert in alerts:
            self.recent_alerts.append(alert)
            await self._trigger_callbacks(alert)
            
            logger.warning(f"System alert: {alert.description} (Severity: {alert.severity})")
    
    def _calculate_severity(self, current_value: float, threshold: float) -> str:
        """Calculate alert severity based on threshold violation"""
        ratio = current_value / threshold
        
        if ratio >= 1.2:  # 20% over threshold
            return "CRITICAL"
        elif ratio >= 1.1:  # 10% over threshold
            return "HIGH"
        elif ratio >= 1.05:  # 5% over threshold
            return "MEDIUM"
        else:
            return "LOW"
    
    async def _trigger_callbacks(self, alert: SystemAlert):
        """Trigger registered callbacks for system alerts"""
        for callback in self.alert_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(alert)
                else:
                    callback(alert)
            except Exception as e:
                logger.error(f"System alert callback error: {e}")
    
    def add_callback(self, callback: Callable):
        """Add callback for system alerts"""
        self.alert_callbacks.append(callback)
    
    async def execute_with_protection(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with system overload protection"""
        return await self.overload_protection.execute_with_protection(func, *args, **kwargs)
    
    def get_current_state(self) -> SystemState:
        """Get current system state"""
        return self.overload_protection.current_state
    
    def get_resource_usage(self) -> Dict[str, float]:
        """Get current resource usage"""
        return self._get_current_metrics()
    
    def get_recent_alerts(self, limit: int = 10) -> List[SystemAlert]:
        """Get recent system alerts"""
        return list(self.recent_alerts)[-limit:]
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get monitoring statistics"""
        performance_stats = self.overload_protection.get_performance_stats()
        current_metrics = self._get_current_metrics()
        
        return {
            'config': {
                'name': self.config.name,
                'cpu_threshold': self.config.cpu_threshold,
                'memory_threshold': self.config.memory_threshold,
                'check_interval': self.config.check_interval
            },
            'current_metrics': current_metrics,
            'performance': performance_stats,
            'baseline': {
                'cpu': self.baseline_cpu,
                'memory': self.baseline_memory,
                'established': self.baseline_established
            },
            'monitoring_active': self.monitoring_active,
            'recent_alert_count': len(self.recent_alerts)
        }


class SystemOverloadError(Exception):
    """Exception raised when system is overloaded"""
    pass
