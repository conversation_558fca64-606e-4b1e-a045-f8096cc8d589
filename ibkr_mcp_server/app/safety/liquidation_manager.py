"""
Automated Position Liquidation Manager

Provides automated liquidation capabilities for trading safety:
- Emergency liquidation triggers
- Position sizing controls
- Automated stop-loss execution
- Risk-based position management
"""

import asyncio
import logging
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Any, List, Optional, Callable, Tuple
from dataclasses import dataclass
from collections import deque

logger = logging.getLogger(__name__)


class LiquidationTrigger(Enum):
    """Types of liquidation triggers"""
    STOP_LOSS = "stop_loss"
    POSITION_LIMIT = "position_limit"
    PORTFOLIO_LOSS = "portfolio_loss"
    CORRELATION_BREAKDOWN = "correlation_breakdown"
    SYSTEM_EMERGENCY = "system_emergency"
    MANUAL = "manual"


class LiquidationStatus(Enum):
    """Status of liquidation operations"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class LiquidationOrder:
    """Liquidation order details"""
    id: str
    timestamp: datetime
    symbol: str
    quantity: float
    trigger: LiquidationTrigger
    reason: str
    status: LiquidationStatus
    target_price: Optional[float] = None
    executed_price: Optional[float] = None
    executed_quantity: Optional[float] = None
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class EmergencyLiquidation:
    """Emergency liquidation event"""
    timestamp: datetime
    trigger: LiquidationTrigger
    affected_positions: List[str]
    total_value: float
    reason: str
    severity: str
    orders: List[LiquidationOrder]
    metadata: Dict[str, Any]


@dataclass
class LiquidationConfig:
    """Configuration for liquidation manager"""
    name: str
    max_position_loss: float = 0.05  # 5% max loss per position
    max_portfolio_loss: float = 0.10  # 10% max portfolio loss
    emergency_threshold: float = 0.15  # 15% emergency liquidation
    stop_loss_buffer: float = 0.01  # 1% buffer for stop losses
    max_liquidation_rate: int = 10  # Max positions to liquidate per minute
    enable_auto_liquidation: bool = True
    require_confirmation: bool = False  # For emergency liquidations


class LiquidationManager:
    """
    Automated position liquidation manager for trading safety
    
    Manages automated liquidation of positions based on various
    risk triggers and emergency conditions.
    """
    
    def __init__(self, config: LiquidationConfig, ibkr_service=None):
        self.config = config
        self.ibkr_service = ibkr_service
        
        # Liquidation tracking
        self.pending_liquidations: Dict[str, LiquidationOrder] = {}
        self.liquidation_history = deque(maxlen=1000)
        self.emergency_events = deque(maxlen=100)
        
        # Position monitoring
        self.position_stop_losses: Dict[str, float] = {}
        self.position_entry_prices: Dict[str, float] = {}
        
        # Rate limiting
        self.liquidation_count = 0
        self.last_liquidation_reset = datetime.now()
        
        # Callbacks
        self.liquidation_callbacks: List[Callable] = []
        self.emergency_callbacks: List[Callable] = []
        
        # Statistics
        self.stats = {
            'total_liquidations': 0,
            'emergency_liquidations': 0,
            'successful_liquidations': 0,
            'failed_liquidations': 0,
            'total_value_liquidated': 0.0,
            'last_liquidation': None
        }
        
        logger.info(f"Liquidation manager '{config.name}' initialized")
    
    async def monitor_position(self, symbol: str, current_price: float, 
                             position_size: float, entry_price: Optional[float] = None):
        """
        Monitor a position for liquidation triggers
        
        Args:
            symbol: Trading symbol
            current_price: Current market price
            position_size: Position size (positive for long, negative for short)
            entry_price: Entry price for the position
        """
        # Update position tracking
        if entry_price is not None:
            self.position_entry_prices[symbol] = entry_price
        
        # Calculate current P&L
        entry = self.position_entry_prices.get(symbol, current_price)
        pnl_percent = self._calculate_pnl_percent(entry, current_price, position_size)
        
        # Check stop loss trigger
        if await self._check_stop_loss_trigger(symbol, pnl_percent):
            await self._trigger_liquidation(
                symbol, position_size, LiquidationTrigger.STOP_LOSS,
                f"Stop loss triggered: {pnl_percent:.2%} loss"
            )
        
        # Check position limit trigger
        if await self._check_position_limit_trigger(symbol, abs(position_size), current_price):
            await self._trigger_liquidation(
                symbol, position_size, LiquidationTrigger.POSITION_LIMIT,
                f"Position size limit exceeded"
            )
    
    async def monitor_portfolio(self, portfolio_value: float, total_pnl: float):
        """
        Monitor portfolio for emergency liquidation triggers
        
        Args:
            portfolio_value: Total portfolio value
            total_pnl: Total unrealized P&L
        """
        pnl_percent = total_pnl / portfolio_value if portfolio_value > 0 else 0
        
        # Check portfolio loss trigger
        if pnl_percent < -self.config.max_portfolio_loss:
            await self._trigger_emergency_liquidation(
                LiquidationTrigger.PORTFOLIO_LOSS,
                f"Portfolio loss {pnl_percent:.2%} exceeds limit {self.config.max_portfolio_loss:.2%}",
                portfolio_value
            )
        
        # Check emergency threshold
        elif pnl_percent < -self.config.emergency_threshold:
            await self._trigger_emergency_liquidation(
                LiquidationTrigger.SYSTEM_EMERGENCY,
                f"Emergency threshold breached: {pnl_percent:.2%}",
                portfolio_value
            )
    
    async def _check_stop_loss_trigger(self, symbol: str, pnl_percent: float) -> bool:
        """Check if stop loss should be triggered"""
        if not self.config.enable_auto_liquidation:
            return False
        
        # Check against configured max loss
        if pnl_percent < -self.config.max_position_loss:
            return True
        
        # Check against custom stop loss if set
        custom_stop = self.position_stop_losses.get(symbol)
        if custom_stop and pnl_percent < -custom_stop:
            return True
        
        return False
    
    async def _check_position_limit_trigger(self, symbol: str, position_size: float, 
                                          current_price: float) -> bool:
        """Check if position size limits are exceeded"""
        # This would integrate with portfolio management to check position limits
        # For now, implement basic size check
        position_value = position_size * current_price
        
        # Example: Check if position exceeds 20% of portfolio
        # This would need actual portfolio value from IBKR service
        max_position_value = 100000  # Placeholder
        
        return position_value > max_position_value * 0.2
    
    async def _trigger_liquidation(self, symbol: str, quantity: float, 
                                 trigger: LiquidationTrigger, reason: str):
        """Trigger liquidation of a specific position"""
        # Check rate limiting
        if not await self._check_rate_limit():
            logger.warning(f"Liquidation rate limit exceeded, queuing {symbol}")
            return
        
        # Create liquidation order
        order_id = f"LIQ_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        liquidation_order = LiquidationOrder(
            id=order_id,
            timestamp=datetime.now(),
            symbol=symbol,
            quantity=abs(quantity),  # Always liquidate full position
            trigger=trigger,
            reason=reason,
            status=LiquidationStatus.PENDING,
            metadata={
                'original_quantity': quantity,
                'liquidation_type': 'position'
            }
        )
        
        # Add to pending liquidations
        self.pending_liquidations[order_id] = liquidation_order
        
        # Execute liquidation
        await self._execute_liquidation(liquidation_order)
        
        # Trigger callbacks
        await self._trigger_liquidation_callbacks(liquidation_order)
        
        logger.warning(f"Liquidation triggered: {symbol} - {reason}")
    
    async def _trigger_emergency_liquidation(self, trigger: LiquidationTrigger, 
                                           reason: str, portfolio_value: float):
        """Trigger emergency liquidation of all positions"""
        if not self.config.enable_auto_liquidation:
            logger.critical(f"Emergency liquidation blocked (auto-liquidation disabled): {reason}")
            return
        
        if self.config.require_confirmation:
            logger.critical(f"Emergency liquidation requires confirmation: {reason}")
            # In a real implementation, this would trigger a confirmation mechanism
            return
        
        # Get all positions (this would come from IBKR service)
        positions = await self._get_all_positions()
        
        liquidation_orders = []
        for symbol, position_data in positions.items():
            order_id = f"EMRG_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            liquidation_order = LiquidationOrder(
                id=order_id,
                timestamp=datetime.now(),
                symbol=symbol,
                quantity=abs(position_data['quantity']),
                trigger=trigger,
                reason=f"Emergency: {reason}",
                status=LiquidationStatus.PENDING,
                metadata={
                    'original_quantity': position_data['quantity'],
                    'liquidation_type': 'emergency',
                    'market_value': position_data.get('market_value', 0)
                }
            )
            liquidation_orders.append(liquidation_order)
            self.pending_liquidations[order_id] = liquidation_order
        
        # Create emergency event
        emergency_event = EmergencyLiquidation(
            timestamp=datetime.now(),
            trigger=trigger,
            affected_positions=list(positions.keys()),
            total_value=portfolio_value,
            reason=reason,
            severity="CRITICAL",
            orders=liquidation_orders,
            metadata={'portfolio_value': portfolio_value}
        )
        
        self.emergency_events.append(emergency_event)
        self.stats['emergency_liquidations'] += 1
        
        # Execute all liquidations
        for order in liquidation_orders:
            await self._execute_liquidation(order)
        
        # Trigger emergency callbacks
        await self._trigger_emergency_callbacks(emergency_event)
        
        logger.critical(f"Emergency liquidation executed: {len(liquidation_orders)} positions - {reason}")
    
    async def _execute_liquidation(self, order: LiquidationOrder):
        """Execute a liquidation order"""
        order.status = LiquidationStatus.IN_PROGRESS
        
        try:
            if self.ibkr_service:
                # Execute through IBKR service
                # Determine order action (SELL for long positions, BUY for short positions)
                action = "SELL"  # Simplified - would need position direction
                
                # Place market order for immediate execution
                result = await self._place_liquidation_order(
                    order.symbol, action, order.quantity
                )
                
                if result.get('success'):
                    order.status = LiquidationStatus.COMPLETED
                    order.executed_quantity = order.quantity
                    order.executed_price = result.get('executed_price')
                    self.stats['successful_liquidations'] += 1
                else:
                    order.status = LiquidationStatus.FAILED
                    order.error_message = result.get('error', 'Unknown error')
                    self.stats['failed_liquidations'] += 1
            else:
                # Simulation mode
                order.status = LiquidationStatus.COMPLETED
                order.executed_quantity = order.quantity
                order.executed_price = 100.0  # Simulated price
                self.stats['successful_liquidations'] += 1
                logger.info(f"SIMULATED liquidation: {order.symbol} - {order.quantity} shares")
        
        except Exception as e:
            order.status = LiquidationStatus.FAILED
            order.error_message = str(e)
            self.stats['failed_liquidations'] += 1
            logger.error(f"Liquidation execution error: {e}")
        
        finally:
            # Move to history
            self.liquidation_history.append(order)
            if order.id in self.pending_liquidations:
                del self.pending_liquidations[order.id]
            
            # Update statistics
            self.stats['total_liquidations'] += 1
            self.stats['last_liquidation'] = datetime.now()
            
            if order.executed_price and order.executed_quantity:
                self.stats['total_value_liquidated'] += order.executed_price * order.executed_quantity
    
    async def _place_liquidation_order(self, symbol: str, action: str, quantity: float) -> Dict[str, Any]:
        """Place liquidation order through IBKR service"""
        try:
            # This would use the actual IBKR service to place orders
            # For now, return simulated result
            return {
                'success': True,
                'order_id': f"IBKR_{symbol}_{datetime.now().timestamp()}",
                'executed_price': 100.0,  # Simulated
                'executed_quantity': quantity
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _get_all_positions(self) -> Dict[str, Dict[str, Any]]:
        """Get all current positions"""
        # This would integrate with IBKR service to get actual positions
        # For now, return simulated positions
        return {
            'AAPL': {'quantity': 100, 'market_value': 15000},
            'GOOGL': {'quantity': 50, 'market_value': 12000}
        }
    
    def _calculate_pnl_percent(self, entry_price: float, current_price: float, 
                              position_size: float) -> float:
        """Calculate P&L percentage for a position"""
        if position_size > 0:  # Long position
            return (current_price - entry_price) / entry_price
        else:  # Short position
            return (entry_price - current_price) / entry_price
    
    async def _check_rate_limit(self) -> bool:
        """Check if liquidation rate limit allows new liquidation"""
        now = datetime.now()
        
        # Reset counter every minute
        if (now - self.last_liquidation_reset).total_seconds() >= 60:
            self.liquidation_count = 0
            self.last_liquidation_reset = now
        
        if self.liquidation_count >= self.config.max_liquidation_rate:
            return False
        
        self.liquidation_count += 1
        return True
    
    async def _trigger_liquidation_callbacks(self, order: LiquidationOrder):
        """Trigger callbacks for liquidation events"""
        for callback in self.liquidation_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(order)
                else:
                    callback(order)
            except Exception as e:
                logger.error(f"Liquidation callback error: {e}")
    
    async def _trigger_emergency_callbacks(self, event: EmergencyLiquidation):
        """Trigger callbacks for emergency liquidation events"""
        for callback in self.emergency_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(event)
                else:
                    callback(event)
            except Exception as e:
                logger.error(f"Emergency liquidation callback error: {e}")
    
    def set_position_stop_loss(self, symbol: str, stop_loss_percent: float):
        """Set custom stop loss for a position"""
        self.position_stop_losses[symbol] = stop_loss_percent
        logger.info(f"Stop loss set for {symbol}: {stop_loss_percent:.2%}")
    
    def add_liquidation_callback(self, callback: Callable):
        """Add callback for liquidation events"""
        self.liquidation_callbacks.append(callback)
    
    def add_emergency_callback(self, callback: Callable):
        """Add callback for emergency liquidation events"""
        self.emergency_callbacks.append(callback)
    
    def get_pending_liquidations(self) -> List[LiquidationOrder]:
        """Get all pending liquidations"""
        return list(self.pending_liquidations.values())
    
    def get_liquidation_history(self, limit: int = 50) -> List[LiquidationOrder]:
        """Get liquidation history"""
        return list(self.liquidation_history)[-limit:]
    
    def get_emergency_events(self, limit: int = 10) -> List[EmergencyLiquidation]:
        """Get emergency liquidation events"""
        return list(self.emergency_events)[-limit:]
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get liquidation statistics"""
        return {
            'config': {
                'name': self.config.name,
                'max_position_loss': self.config.max_position_loss,
                'max_portfolio_loss': self.config.max_portfolio_loss,
                'auto_liquidation_enabled': self.config.enable_auto_liquidation
            },
            'stats': self.stats.copy(),
            'pending_count': len(self.pending_liquidations),
            'history_count': len(self.liquidation_history),
            'emergency_count': len(self.emergency_events),
            'rate_limit': {
                'current_count': self.liquidation_count,
                'max_rate': self.config.max_liquidation_rate,
                'reset_time': self.last_liquidation_reset.isoformat()
            }
        }
    
    async def manual_liquidation(self, symbol: str, quantity: Optional[float] = None,
                               reason: str = "Manual liquidation") -> str:
        """Manually trigger liquidation of a position"""
        if quantity is None:
            # Get current position size
            positions = await self._get_all_positions()
            if symbol not in positions:
                raise ValueError(f"No position found for {symbol}")
            quantity = positions[symbol]['quantity']

        # Ensure quantity is not None before passing to _trigger_liquidation
        if quantity is None:
            raise ValueError(f"Could not determine quantity for {symbol}")

        await self._trigger_liquidation(
            symbol, quantity, LiquidationTrigger.MANUAL, reason
        )
        
        return f"Manual liquidation triggered for {symbol}: {quantity} shares"
    
    def disable_auto_liquidation(self):
        """Disable automatic liquidation"""
        self.config.enable_auto_liquidation = False
        logger.warning("Automatic liquidation disabled")
    
    def enable_auto_liquidation(self):
        """Enable automatic liquidation"""
        self.config.enable_auto_liquidation = True
        logger.info("Automatic liquidation enabled")
