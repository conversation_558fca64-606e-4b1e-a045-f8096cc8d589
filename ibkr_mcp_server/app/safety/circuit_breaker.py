"""
Circuit Breaker Implementation

Provides circuit breaker pattern for trading safety:
- Daily loss limits
- Position concentration limits  
- Correlation breakdown protection
- System overload protection
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass

logger = logging.getLogger(__name__)


class CircuitBreakerState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Circuit is open, blocking operations
    HALF_OPEN = "half_open"  # Testing if circuit can close


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker"""
    name: str
    failure_threshold: int = 5
    recovery_timeout: int = 60  # seconds
    success_threshold: int = 3  # for half-open state
    timeout_duration: int = 300  # seconds to keep circuit open


class CircuitBreaker:
    """
    Circuit breaker implementation for trading safety
    
    Prevents cascading failures by opening circuit when failure
    threshold is exceeded, then gradually allowing traffic back.
    """
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.last_success_time = None
        self.state_change_time = datetime.now()
        
        # Callbacks for state changes
        self.on_state_change: Optional[Callable] = None
        self.on_failure: Optional[Callable] = None
        self.on_success: Optional[Callable] = None
        
        logger.info(f"Circuit breaker '{self.config.name}' initialized")
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function through circuit breaker
        
        Args:
            func: Function to execute
            *args, **kwargs: Function arguments
            
        Returns:
            Function result if circuit allows execution
            
        Raises:
            CircuitBreakerOpenError: If circuit is open
        """
        if not self.can_execute():
            raise CircuitBreakerOpenError(
                f"Circuit breaker '{self.config.name}' is open"
            )
        
        try:
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            await self._on_success()
            return result
            
        except Exception as e:
            await self._on_failure(e)
            raise
    
    def can_execute(self) -> bool:
        """Check if circuit breaker allows execution"""
        current_time = datetime.now()
        
        if self.state == CircuitBreakerState.CLOSED:
            return True
        
        elif self.state == CircuitBreakerState.OPEN:
            # Check if timeout has passed
            if (current_time - self.state_change_time).total_seconds() >= self.config.timeout_duration:
                self._transition_to_half_open()
                return True
            return False
        
        elif self.state == CircuitBreakerState.HALF_OPEN:
            return True
        
        return False
    
    async def _on_success(self):
        """Handle successful execution"""
        self.last_success_time = datetime.now()
        
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self._transition_to_closed()
        
        if self.on_success:
            await self._safe_callback(self.on_success, success=True)
    
    async def _on_failure(self, exception: Exception):
        """Handle failed execution"""
        self.last_failure_time = datetime.now()
        self.failure_count += 1
        
        if self.state == CircuitBreakerState.CLOSED:
            if self.failure_count >= self.config.failure_threshold:
                self._transition_to_open()
        
        elif self.state == CircuitBreakerState.HALF_OPEN:
            self._transition_to_open()
        
        if self.on_failure:
            await self._safe_callback(self.on_failure, exception=exception)
    
    def _transition_to_open(self):
        """Transition circuit breaker to open state"""
        previous_state = self.state
        self.state = CircuitBreakerState.OPEN
        self.state_change_time = datetime.now()
        self.success_count = 0
        
        logger.warning(
            f"Circuit breaker '{self.config.name}' opened "
            f"(failures: {self.failure_count})"
        )
        
        if self.on_state_change:
            asyncio.create_task(self._safe_callback(
                self.on_state_change,
                previous_state=previous_state,
                new_state=self.state
            ))
    
    def _transition_to_half_open(self):
        """Transition circuit breaker to half-open state"""
        previous_state = self.state
        self.state = CircuitBreakerState.HALF_OPEN
        self.state_change_time = datetime.now()
        self.success_count = 0
        
        logger.info(f"Circuit breaker '{self.config.name}' half-opened")
        
        if self.on_state_change:
            asyncio.create_task(self._safe_callback(
                self.on_state_change,
                previous_state=previous_state,
                new_state=self.state
            ))
    
    def _transition_to_closed(self):
        """Transition circuit breaker to closed state"""
        previous_state = self.state
        self.state = CircuitBreakerState.CLOSED
        self.state_change_time = datetime.now()
        self.failure_count = 0
        self.success_count = 0
        
        logger.info(f"Circuit breaker '{self.config.name}' closed")
        
        if self.on_state_change:
            asyncio.create_task(self._safe_callback(
                self.on_state_change,
                previous_state=previous_state,
                new_state=self.state
            ))
    
    async def _safe_callback(self, callback: Callable, **kwargs):
        """Safely execute callback without affecting circuit breaker"""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(**kwargs)
            else:
                callback(**kwargs)
        except Exception as e:
            logger.error(f"Circuit breaker callback error: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get circuit breaker statistics"""
        return {
            "name": self.config.name,
            "state": self.state.value,
            "failure_count": self.failure_count,
            "success_count": self.success_count,
            "last_failure_time": self.last_failure_time.isoformat() if self.last_failure_time else None,
            "last_success_time": self.last_success_time.isoformat() if self.last_success_time else None,
            "state_change_time": self.state_change_time.isoformat(),
            "can_execute": self.can_execute()
        }
    
    def reset(self):
        """Reset circuit breaker to initial state"""
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.last_success_time = None
        self.state_change_time = datetime.now()
        
        logger.info(f"Circuit breaker '{self.config.name}' reset")


class CircuitBreakerOpenError(Exception):
    """Exception raised when circuit breaker is open"""
    pass


class CircuitBreakerManager:
    """
    Manages multiple circuit breakers for different trading operations
    """
    
    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.global_callbacks: List[Callable] = []
        
        # Initialize default trading circuit breakers
        self._initialize_default_breakers()
    
    def _initialize_default_breakers(self):
        """Initialize default circuit breakers for trading safety"""
        
        # Daily loss limit circuit breaker
        daily_loss_config = CircuitBreakerConfig(
            name="daily_loss",
            failure_threshold=3,  # 3 significant losses
            recovery_timeout=3600,  # 1 hour
            timeout_duration=86400  # 24 hours
        )
        self.add_circuit_breaker(daily_loss_config)
        
        # Position concentration circuit breaker
        concentration_config = CircuitBreakerConfig(
            name="position_concentration",
            failure_threshold=2,  # 2 concentration violations
            recovery_timeout=1800,  # 30 minutes
            timeout_duration=3600  # 1 hour
        )
        self.add_circuit_breaker(concentration_config)
        
        # Correlation breakdown circuit breaker
        correlation_config = CircuitBreakerConfig(
            name="correlation_breakdown",
            failure_threshold=5,  # 5 correlation breakdowns
            recovery_timeout=900,  # 15 minutes
            timeout_duration=1800  # 30 minutes
        )
        self.add_circuit_breaker(correlation_config)
        
        # System overload circuit breaker
        overload_config = CircuitBreakerConfig(
            name="system_overload",
            failure_threshold=3,  # 3 overload events
            recovery_timeout=300,  # 5 minutes
            timeout_duration=600  # 10 minutes
        )
        self.add_circuit_breaker(overload_config)
    
    def add_circuit_breaker(self, config: CircuitBreakerConfig) -> CircuitBreaker:
        """Add a new circuit breaker"""
        breaker = CircuitBreaker(config)
        
        # Set up global callbacks
        breaker.on_state_change = self._on_state_change
        
        self.circuit_breakers[config.name] = breaker
        logger.info(f"Added circuit breaker: {config.name}")
        
        return breaker
    
    def get_circuit_breaker(self, name: str) -> Optional[CircuitBreaker]:
        """Get circuit breaker by name"""
        return self.circuit_breakers.get(name)
    
    async def execute_with_breaker(self, breaker_name: str, func: Callable, *args, **kwargs) -> Any:
        """Execute function with specific circuit breaker"""
        breaker = self.get_circuit_breaker(breaker_name)
        if not breaker:
            raise ValueError(f"Circuit breaker '{breaker_name}' not found")
        
        return await breaker.call(func, *args, **kwargs)
    
    def check_all_breakers(self) -> Dict[str, bool]:
        """Check if all circuit breakers allow execution"""
        return {
            name: breaker.can_execute()
            for name, breaker in self.circuit_breakers.items()
        }
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all circuit breakers"""
        return {
            name: breaker.get_stats()
            for name, breaker in self.circuit_breakers.items()
        }
    
    async def _on_state_change(self, previous_state: CircuitBreakerState, new_state: CircuitBreakerState, **kwargs):
        """Handle circuit breaker state changes"""
        for callback in self.global_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(previous_state=previous_state, new_state=new_state, **kwargs)
                else:
                    callback(previous_state=previous_state, new_state=new_state, **kwargs)
            except Exception as e:
                logger.error(f"Global circuit breaker callback error: {e}")
    
    def add_global_callback(self, callback: Callable):
        """Add global callback for all circuit breaker state changes"""
        self.global_callbacks.append(callback)
    
    def reset_all(self):
        """Reset all circuit breakers"""
        for breaker in self.circuit_breakers.values():
            breaker.reset()
        
        logger.info("All circuit breakers reset")
