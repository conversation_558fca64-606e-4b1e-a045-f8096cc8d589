"""
Central Safety Manager

Coordinates all safety systems for comprehensive trading protection:
- Circuit breaker management
- Anomaly detection coordination
- Correlation monitoring integration
- System overload protection
- Automated liquidation management
"""

import asyncio
import logging
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass

from .circuit_breaker import Circuit<PERSON>reakerManager, CircuitBreakerConfig
from .anomaly_detector import AnomalyDetector, AnomalyDetectorConfig, AnomalyAlert
from .correlation_monitor import CorrelationMonitor, CorrelationMonitorConfig, CorrelationBreakdown
from .system_monitor import SystemMonitor, SystemMonitorConfig, SystemAlert
from .liquidation_manager import LiquidationManager, LiquidationConfig, LiquidationOrder, EmergencyLiquidation

logger = logging.getLogger(__name__)


class SafetyLevel(Enum):
    """Overall safety levels"""
    SAFE = "safe"
    CAUTION = "caution"
    WARNING = "warning"
    DANGER = "danger"
    CRITICAL = "critical"


@dataclass
class SafetyStatus:
    """Overall safety status"""
    timestamp: datetime
    level: SafetyLevel
    active_alerts: int
    circuit_breakers_open: int
    system_state: str
    correlation_breakdowns: int
    pending_liquidations: int
    description: str
    recommendations: List[str]


@dataclass
class SafetyManagerConfig:
    """Configuration for safety manager"""
    name: str = "TradingSafetyManager"
    enable_circuit_breakers: bool = True
    enable_anomaly_detection: bool = True
    enable_correlation_monitoring: bool = True
    enable_system_monitoring: bool = True
    enable_liquidation_management: bool = True
    safety_check_interval: float = 1.0  # seconds
    alert_aggregation_window: int = 60  # seconds


class SafetyManager:
    """
    Central safety manager coordinating all safety systems
    
    Provides unified interface for all trading safety features
    and coordinates responses to safety events.
    """
    
    def __init__(self, config: SafetyManagerConfig, ibkr_service=None):
        self.config = config
        self.ibkr_service = ibkr_service
        
        # Initialize safety components
        self.circuit_breaker_manager = None
        self.anomaly_detector = None
        self.correlation_monitor = None
        self.system_monitor = None
        self.liquidation_manager = None
        
        # Safety state
        self.current_safety_level = SafetyLevel.SAFE
        self.safety_history = []
        self.active_alerts = []
        
        # Monitoring
        self.monitoring_active = False
        self.monitor_task = None
        
        # Callbacks
        self.safety_callbacks: List[Callable] = []
        
        # Statistics
        self.stats = {
            'total_alerts': 0,
            'safety_level_changes': 0,
            'emergency_stops': 0,
            'last_safety_check': None
        }
        
        self._initialize_components()
        logger.info(f"Safety manager '{config.name}' initialized")
    
    def _initialize_components(self):
        """Initialize all safety components"""
        
        # Circuit breaker manager
        if self.config.enable_circuit_breakers:
            self.circuit_breaker_manager = CircuitBreakerManager()
            self.circuit_breaker_manager.add_global_callback(self._on_circuit_breaker_event)
        
        # Anomaly detector
        if self.config.enable_anomaly_detection:
            anomaly_config = AnomalyDetectorConfig(
                name="TradingAnomalyDetector",
                window_size=100,
                zscore_threshold=3.0
            )
            self.anomaly_detector = AnomalyDetector(anomaly_config)
            self.anomaly_detector.add_callback(self._on_anomaly_detected)
        
        # Correlation monitor
        if self.config.enable_correlation_monitoring:
            correlation_config = CorrelationMonitorConfig(
                name="TradingCorrelationMonitor",
                window_size=50,
                breakdown_threshold=0.3
            )
            self.correlation_monitor = CorrelationMonitor(correlation_config)
            self.correlation_monitor.add_callback(self._on_correlation_breakdown)
        
        # System monitor
        if self.config.enable_system_monitoring:
            system_config = SystemMonitorConfig(
                name="TradingSystemMonitor",
                cpu_threshold=80.0,
                memory_threshold=85.0
            )
            self.system_monitor = SystemMonitor(system_config)
            self.system_monitor.add_callback(self._on_system_alert)
        
        # Liquidation manager
        if self.config.enable_liquidation_management:
            liquidation_config = LiquidationConfig(
                name="TradingLiquidationManager",
                max_position_loss=0.05,
                max_portfolio_loss=0.10
            )
            self.liquidation_manager = LiquidationManager(liquidation_config, self.ibkr_service)
            self.liquidation_manager.add_liquidation_callback(self._on_liquidation_event)
            self.liquidation_manager.add_emergency_callback(self._on_emergency_liquidation)
    
    async def start_monitoring(self):
        """Start safety monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        
        # Start component monitoring
        if self.system_monitor:
            await self.system_monitor.start_monitoring()
        
        # Start safety monitoring loop
        self.monitor_task = asyncio.create_task(self._safety_monitoring_loop())
        
        logger.info("Safety monitoring started")
    
    async def stop_monitoring(self):
        """Stop safety monitoring"""
        self.monitoring_active = False
        
        # Stop component monitoring
        if self.system_monitor:
            await self.system_monitor.stop_monitoring()
        
        # Stop safety monitoring loop
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Safety monitoring stopped")
    
    async def _safety_monitoring_loop(self):
        """Main safety monitoring loop"""
        while self.monitoring_active:
            try:
                await self._perform_safety_check()
                await asyncio.sleep(self.config.safety_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Safety monitoring loop error: {e}")
                await asyncio.sleep(self.config.safety_check_interval)
    
    async def _perform_safety_check(self):
        """Perform comprehensive safety check"""
        self.stats['last_safety_check'] = datetime.now()
        
        # Collect safety status from all components
        safety_data = await self._collect_safety_data()
        
        # Determine overall safety level
        new_safety_level = self._calculate_safety_level(safety_data)
        
        # Check for safety level changes
        if new_safety_level != self.current_safety_level:
            await self._handle_safety_level_change(new_safety_level, safety_data)
        
        # Clean up old alerts
        self._cleanup_old_alerts()
    
    async def _collect_safety_data(self) -> Dict[str, Any]:
        """Collect safety data from all components"""
        data = {
            'timestamp': datetime.now(),
            'circuit_breakers': {},
            'anomalies': [],
            'correlations': {},
            'system': {},
            'liquidations': {}
        }
        
        # Circuit breaker status
        if self.circuit_breaker_manager:
            data['circuit_breakers'] = self.circuit_breaker_manager.get_all_stats()
        
        # Recent anomalies
        if self.anomaly_detector:
            data['anomalies'] = self.anomaly_detector.get_recent_anomalies(10)
        
        # Correlation status
        if self.correlation_monitor:
            data['correlations'] = {
                'recent_breakdowns': self.correlation_monitor.get_recent_breakdowns(5),
                'regime_status': self.correlation_monitor.get_regime_status()
            }
        
        # System status
        if self.system_monitor:
            data['system'] = {
                'current_state': self.system_monitor.get_current_state(),
                'resource_usage': self.system_monitor.get_resource_usage(),
                'recent_alerts': self.system_monitor.get_recent_alerts(5)
            }
        
        # Liquidation status
        if self.liquidation_manager:
            data['liquidations'] = {
                'pending': self.liquidation_manager.get_pending_liquidations(),
                'recent_history': self.liquidation_manager.get_liquidation_history(5),
                'emergency_events': self.liquidation_manager.get_emergency_events(3)
            }
        
        return data
    
    def _calculate_safety_level(self, safety_data: Dict[str, Any]) -> SafetyLevel:
        """Calculate overall safety level based on all components"""
        risk_score = 0
        
        # Circuit breaker risk
        circuit_breakers = safety_data.get('circuit_breakers', {})
        open_breakers = sum(1 for stats in circuit_breakers.values() 
                           if not stats.get('can_execute', True))
        risk_score += open_breakers * 20  # 20 points per open breaker
        
        # Anomaly risk
        anomalies = safety_data.get('anomalies', [])
        recent_anomalies = [a for a in anomalies 
                           if (datetime.now() - a.timestamp).total_seconds() < 300]  # 5 minutes
        risk_score += len(recent_anomalies) * 10  # 10 points per recent anomaly
        
        # Correlation risk
        correlations = safety_data.get('correlations', {})
        recent_breakdowns = correlations.get('recent_breakdowns', [])
        active_breakdowns = [b for b in recent_breakdowns 
                           if (datetime.now() - b.timestamp).total_seconds() < 600]  # 10 minutes
        risk_score += len(active_breakdowns) * 15  # 15 points per breakdown
        
        # System risk
        system = safety_data.get('system', {})
        system_state = system.get('current_state')
        if system_state:
            state_risk = {
                'optimal': 0,
                'degraded': 10,
                'overloaded': 25,
                'critical': 50
            }
            risk_score += state_risk.get(system_state.value if hasattr(system_state, 'value') else str(system_state), 0)
        
        # Liquidation risk
        liquidations = safety_data.get('liquidations', {})
        pending_liquidations = len(liquidations.get('pending', []))
        emergency_events = len(liquidations.get('emergency_events', []))
        risk_score += pending_liquidations * 5  # 5 points per pending liquidation
        risk_score += emergency_events * 30  # 30 points per emergency event
        
        # Determine safety level based on risk score
        if risk_score >= 80:
            return SafetyLevel.CRITICAL
        elif risk_score >= 60:
            return SafetyLevel.DANGER
        elif risk_score >= 40:
            return SafetyLevel.WARNING
        elif risk_score >= 20:
            return SafetyLevel.CAUTION
        else:
            return SafetyLevel.SAFE
    
    async def _handle_safety_level_change(self, new_level: SafetyLevel, safety_data: Dict[str, Any]):
        """Handle safety level changes"""
        previous_level = self.current_safety_level
        self.current_safety_level = new_level
        self.stats['safety_level_changes'] += 1
        
        # Create safety status
        status = SafetyStatus(
            timestamp=datetime.now(),
            level=new_level,
            active_alerts=len(self.active_alerts),
            circuit_breakers_open=sum(1 for stats in safety_data.get('circuit_breakers', {}).values() 
                                    if not stats.get('can_execute', True)),
            system_state=str(safety_data.get('system', {}).get('current_state', 'unknown')),
            correlation_breakdowns=len(safety_data.get('correlations', {}).get('recent_breakdowns', [])),
            pending_liquidations=len(safety_data.get('liquidations', {}).get('pending', [])),
            description=f"Safety level changed from {previous_level.value} to {new_level.value}",
            recommendations=self._generate_recommendations(new_level, safety_data)
        )
        
        self.safety_history.append(status)
        
        # Take action based on new safety level
        await self._take_safety_action(new_level, safety_data)
        
        # Trigger callbacks
        await self._trigger_safety_callbacks(status)
        
        logger.warning(f"Safety level changed: {previous_level.value} -> {new_level.value}")
    
    def _generate_recommendations(self, safety_level: SafetyLevel, 
                                safety_data: Dict[str, Any]) -> List[str]:
        """Generate safety recommendations based on current state"""
        recommendations = []
        
        if safety_level in [SafetyLevel.CRITICAL, SafetyLevel.DANGER]:
            recommendations.append("Consider emergency position liquidation")
            recommendations.append("Halt new trading operations")
            recommendations.append("Review system resources and performance")
        
        elif safety_level == SafetyLevel.WARNING:
            recommendations.append("Reduce position sizes")
            recommendations.append("Monitor correlations closely")
            recommendations.append("Check system performance")
        
        elif safety_level == SafetyLevel.CAUTION:
            recommendations.append("Exercise increased caution in trading")
            recommendations.append("Monitor risk metrics closely")
        
        # Specific recommendations based on components
        circuit_breakers = safety_data.get('circuit_breakers', {})
        open_breakers = [name for name, stats in circuit_breakers.items() 
                        if not stats.get('can_execute', True)]
        if open_breakers:
            recommendations.append(f"Circuit breakers open: {', '.join(open_breakers)}")
        
        system_state = safety_data.get('system', {}).get('current_state')
        if system_state and hasattr(system_state, 'value') and system_state.value in ['overloaded', 'critical']:
            recommendations.append("System resources critically low - consider scaling")
        
        return recommendations
    
    async def _take_safety_action(self, safety_level: SafetyLevel, safety_data: Dict[str, Any]):
        """Take automated safety actions based on safety level"""
        
        if safety_level == SafetyLevel.CRITICAL:
            # Emergency actions
            self.stats['emergency_stops'] += 1
            
            # Trigger emergency liquidation if configured
            if (self.liquidation_manager and 
                self.liquidation_manager.config.enable_auto_liquidation):
                
                logger.critical("CRITICAL safety level - triggering emergency protocols")
                # This would trigger emergency liquidation in a real scenario
        
        elif safety_level == SafetyLevel.DANGER:
            # Severe restriction actions
            logger.error("DANGER safety level - implementing severe restrictions")
        
        elif safety_level == SafetyLevel.WARNING:
            # Moderate restriction actions
            logger.warning("WARNING safety level - implementing moderate restrictions")
    
    async def _on_circuit_breaker_event(self, **kwargs):
        """Handle circuit breaker events"""
        self.stats['total_alerts'] += 1
        # Circuit breaker events are handled by the safety monitoring loop
    
    async def _on_anomaly_detected(self, alert: AnomalyAlert):
        """Handle anomaly detection alerts"""
        self.stats['total_alerts'] += 1
        self.active_alerts.append(alert)
        logger.warning(f"Anomaly detected: {alert.description}")
    
    async def _on_correlation_breakdown(self, breakdown: CorrelationBreakdown):
        """Handle correlation breakdown events"""
        self.stats['total_alerts'] += 1
        logger.warning(f"Correlation breakdown: {breakdown.description}")
    
    async def _on_system_alert(self, alert: SystemAlert):
        """Handle system alerts"""
        self.stats['total_alerts'] += 1
        self.active_alerts.append(alert)
        logger.warning(f"System alert: {alert.description}")
    
    async def _on_liquidation_event(self, order: LiquidationOrder):
        """Handle liquidation events"""
        logger.info(f"Liquidation event: {order.symbol} - {order.status.value}")
    
    async def _on_emergency_liquidation(self, event: EmergencyLiquidation):
        """Handle emergency liquidation events"""
        self.stats['emergency_stops'] += 1
        logger.critical(f"Emergency liquidation: {event.reason}")
    
    def _cleanup_old_alerts(self):
        """Clean up old alerts"""
        cutoff_time = datetime.now() - timedelta(minutes=30)
        self.active_alerts = [alert for alert in self.active_alerts 
                            if alert.timestamp > cutoff_time]
    
    async def _trigger_safety_callbacks(self, status: SafetyStatus):
        """Trigger safety callbacks"""
        for callback in self.safety_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(status)
                else:
                    callback(status)
            except Exception as e:
                logger.error(f"Safety callback error: {e}")
    
    def add_safety_callback(self, callback: Callable):
        """Add callback for safety events"""
        self.safety_callbacks.append(callback)
    
    def get_current_status(self) -> SafetyStatus:
        """Get current safety status"""
        return SafetyStatus(
            timestamp=datetime.now(),
            level=self.current_safety_level,
            active_alerts=len(self.active_alerts),
            circuit_breakers_open=0,  # Would calculate from actual data
            system_state="unknown",
            correlation_breakdowns=0,
            pending_liquidations=0,
            description=f"Current safety level: {self.current_safety_level.value}",
            recommendations=[]
        )
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get safety manager statistics"""
        component_stats = {}
        
        if self.circuit_breaker_manager:
            component_stats['circuit_breakers'] = self.circuit_breaker_manager.get_all_stats()
        
        if self.anomaly_detector:
            component_stats['anomaly_detector'] = self.anomaly_detector.get_statistics()
        
        if self.correlation_monitor:
            component_stats['correlation_monitor'] = self.correlation_monitor.get_statistics()
        
        if self.system_monitor:
            component_stats['system_monitor'] = self.system_monitor.get_statistics()
        
        if self.liquidation_manager:
            component_stats['liquidation_manager'] = self.liquidation_manager.get_statistics()
        
        return {
            'config': {
                'name': self.config.name,
                'components_enabled': {
                    'circuit_breakers': self.config.enable_circuit_breakers,
                    'anomaly_detection': self.config.enable_anomaly_detection,
                    'correlation_monitoring': self.config.enable_correlation_monitoring,
                    'system_monitoring': self.config.enable_system_monitoring,
                    'liquidation_management': self.config.enable_liquidation_management
                }
            },
            'current_status': {
                'safety_level': self.current_safety_level.value,
                'monitoring_active': self.monitoring_active,
                'active_alerts': len(self.active_alerts)
            },
            'stats': self.stats.copy(),
            'components': component_stats
        }
    
    async def emergency_stop(self, reason: str = "Manual emergency stop"):
        """Trigger emergency stop"""
        logger.critical(f"EMERGENCY STOP TRIGGERED: {reason}")
        
        # Set critical safety level
        self.current_safety_level = SafetyLevel.CRITICAL
        self.stats['emergency_stops'] += 1
        
        # Trigger emergency liquidation if available
        if self.liquidation_manager:
            # This would trigger actual emergency liquidation
            logger.critical("Emergency liquidation would be triggered here")
        
        # Trigger callbacks
        status = SafetyStatus(
            timestamp=datetime.now(),
            level=SafetyLevel.CRITICAL,
            active_alerts=len(self.active_alerts),
            circuit_breakers_open=0,
            system_state="emergency",
            correlation_breakdowns=0,
            pending_liquidations=0,
            description=f"EMERGENCY STOP: {reason}",
            recommendations=["All trading halted", "Review system immediately"]
        )
        
        await self._trigger_safety_callbacks(status)
