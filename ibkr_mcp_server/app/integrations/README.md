# IBKR MCP Server Integrations

This directory contains integration services that connect the IBKR MCP server with external systems and provide real-time event processing capabilities.

## Components

### 1. Event Stream Manager (`event_stream_manager.py`)

**Purpose:** Manages real-time event streams from Supabase and coordinates event processing across the system.

**Key Features:**
- Real-time subscription to database changes
- Event routing and processing
- Auto-execution of trading signals
- WebSocket integration for real-time updates

**Usage:**
```python
from integrations.event_stream_manager import EventStreamManager

# Initialize event stream manager
event_manager = EventStreamManager(supabase_client, ibkr_service)

# Start event processing
await event_manager.start()

# Subscribe to specific events
await event_manager.subscribe_to_signals(['strategy_1', 'strategy_2'])
```

### 2. Enhanced Event Stream Manager (`enhanced_event_stream_manager.py`)

**Purpose:** Advanced event processing with circuit breakers, retry logic, and enhanced error handling.

**Key Features:**
- Circuit breaker pattern for fault tolerance
- Exponential backoff retry mechanism
- Event batching and buffering
- Performance monitoring and metrics

### 3. Hybrid Event System (`hybrid_event_system.py`)

**Purpose:** Combines multiple event sources (Supabase realtime, polling, webhooks) into a unified event processing system.

**Key Features:**
- Multi-source event aggregation
- Event deduplication
- Priority-based event processing
- Fallback mechanisms

### 4. Supabase-IBKR Sync (`supabase_ibkr_sync.py`)

**Purpose:** Synchronizes data between IBKR and Supabase, ensuring data consistency and real-time updates.

**Key Features:**
- Bidirectional data synchronization
- Market data streaming
- Order status synchronization
- Position tracking
- Real-time portfolio updates

**Usage:**
```python
from integrations.supabase_ibkr_sync import SupabaseIBKRSync

# Initialize sync service
sync_service = SupabaseIBKRSync(supabase_client, ibkr_service)

# Start synchronization
await sync_service.start_sync()

# Sync specific data types
await sync_service.sync_market_data(['AAPL', 'TSLA'])
await sync_service.sync_orders()
await sync_service.sync_positions()
```

## Configuration

### Environment Variables

```bash
# Supabase Configuration
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_SERVICE_KEY="your-service-role-key"

# Event Processing Configuration
EVENT_BATCH_SIZE=100
EVENT_PROCESSING_INTERVAL=1000  # milliseconds
MAX_RETRY_ATTEMPTS=3
CIRCUIT_BREAKER_THRESHOLD=5

# Sync Configuration
SYNC_INTERVAL=5000  # milliseconds
ENABLE_AUTO_SYNC=true
SYNC_MARKET_DATA=true
SYNC_ORDERS=true
SYNC_POSITIONS=true
```

### Database Tables Required

The integrations require the following Supabase tables:
- `orders` - Trading orders
- `positions` - Portfolio positions
- `trading_signals` - Generated trading signals
- `market_data` - Real-time market data
- `instruments` - Trading instruments
- `audit_log` - System audit trail
- `risk_metrics` - Risk monitoring data

## Event Types

### Market Data Events
- `market_data.insert` - New market data received
- `market_data.update` - Market data updated

### Order Events
- `orders.insert` - New order created
- `orders.update` - Order status changed
- `orders.delete` - Order cancelled

### Signal Events
- `trading_signals.insert` - New trading signal generated
- `trading_signals.update` - Signal status changed

### Position Events
- `positions.insert` - New position opened
- `positions.update` - Position updated
- `positions.delete` - Position closed

## Error Handling

The integration services implement comprehensive error handling:

1. **Circuit Breaker Pattern:** Prevents cascade failures by temporarily disabling failing services
2. **Retry Logic:** Automatic retry with exponential backoff for transient failures
3. **Dead Letter Queue:** Failed events are stored for manual review and reprocessing
4. **Monitoring:** Real-time monitoring of event processing performance and error rates

## Performance Considerations

- **Event Batching:** Events are processed in batches to improve throughput
- **Connection Pooling:** Database connections are pooled and reused
- **Async Processing:** All operations are asynchronous for better performance
- **Memory Management:** Event buffers have size limits to prevent memory leaks

## Testing

See the main project's testing documentation for integration testing procedures. Key test scenarios include:

1. **Event Processing Tests:** Verify events are processed correctly
2. **Sync Tests:** Ensure data synchronization works bidirectionally
3. **Error Handling Tests:** Test circuit breakers and retry logic
4. **Performance Tests:** Load testing for high-volume event processing

## Monitoring

The integrations provide built-in monitoring capabilities:

- Event processing metrics
- Error rates and types
- Performance statistics
- Connection health status
- Circuit breaker status

Access monitoring data through the WebSocket endpoints or query the audit log table.
