"""
Enhanced Event Stream Manager

Provides advanced real-time event streaming capabilities using Supabase Realtime
and Google Cloud Pub/Sub for the IBKR MCP server. This enhanced version includes
real-time subscriptions, event filtering, and hybrid messaging support.
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional, Callable, Set, Union
from datetime import datetime, timezone
from uuid import UUID, uuid4
from dataclasses import dataclass
from enum import Enum

from app.database.connection import supabase_conn
from app.integrations.event_stream_manager import EventStreamManager

logger = logging.getLogger(__name__)


class EventPriority(Enum):
    """Event priority levels for processing order"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class RealtimeEvent:
    """Structured event data for realtime processing"""
    id: str
    event_type: str
    table: str
    record: Dict[str, Any]
    old_record: Optional[Dict[str, Any]]
    timestamp: datetime
    priority: EventPriority = EventPriority.MEDIUM
    metadata: Optional[Dict[str, Any]] = None


class EnhancedEventStreamManager(EventStreamManager):
    """
    Enhanced event stream manager with Supabase Realtime integration.
    
    Features:
    - Real-time database subscriptions
    - Event filtering and routing
    - Priority-based event processing
    - Hybrid messaging (Supabase + Pub/Sub)
    - Connection health monitoring
    - Event replay capabilities
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize enhanced event stream manager.
        
        Args:
            config: Configuration dictionary with realtime settings
        """
        super().__init__()
        self.config = config or {}
        self.supabase = supabase_conn
        
        # Realtime subscriptions
        self._realtime_channels: Dict[str, Any] = {}
        self._subscription_callbacks: Dict[str, List[Callable]] = {}
        
        # Event processing
        self._event_queue: asyncio.Queue = asyncio.Queue()
        self._priority_queues: Dict[EventPriority, asyncio.Queue] = {
            priority: asyncio.Queue() for priority in EventPriority
        }
        
        # Connection monitoring
        self._connection_healthy = False
        self._last_heartbeat = None
        self._reconnect_attempts = 0
        self._max_reconnect_attempts = 5
        
        # Event filtering
        self._event_filters: Dict[str, Callable] = {}
        self._table_subscriptions: Set[str] = set()
        
        # Statistics
        self._realtime_stats = {
            'events_received': 0,
            'events_processed': 0,
            'events_filtered': 0,
            'connection_drops': 0,
            'last_event_time': None
        }

    async def start(self, config: Optional[Dict[str, Any]] = None):
        """
        Start enhanced event stream manager with realtime subscriptions.
        
        Args:
            config: Additional configuration for realtime features
        """
        await super().start(config)
        
        # Merge configurations
        if config:
            self.config.update(config)
        
        logger.info("Starting Enhanced Event Stream Manager with Realtime")
        
        try:
            # Initialize realtime subscriptions
            await self._initialize_realtime_subscriptions()
            
            # Start enhanced processing tasks
            enhanced_tasks = [
                asyncio.create_task(self._process_priority_events_loop()),
                asyncio.create_task(self._monitor_realtime_connection_loop()),
                asyncio.create_task(self._heartbeat_loop()),
                asyncio.create_task(self._event_replay_loop())
            ]
            
            self._stream_tasks.extend(enhanced_tasks)
            
            logger.info(f"Started {len(enhanced_tasks)} enhanced event processing tasks")
            
        except Exception as e:
            logger.error(f"Failed to start Enhanced Event Stream Manager: {e}")
            raise

    async def stop(self):
        """Stop enhanced event stream manager and cleanup realtime connections"""
        logger.info("Stopping Enhanced Event Stream Manager")
        
        # Unsubscribe from all realtime channels
        await self._cleanup_realtime_subscriptions()
        
        # Call parent stop method
        await super().stop()
        
        logger.info("Enhanced Event Stream Manager stopped")

    async def subscribe_to_table(self, table_name: str, 
                                event_types: Optional[List[str]] = None,
                                filters: Optional[Dict[str, Any]] = None,
                                callback: Optional[Callable] = None) -> bool:
        """
        Subscribe to real-time events from a specific table.
        
        Args:
            table_name: Name of the table to subscribe to
            event_types: List of event types (INSERT, UPDATE, DELETE)
            filters: Optional filters for the subscription
            callback: Optional callback function for events
            
        Returns:
            True if subscription successful
        """
        try:
            if table_name in self._table_subscriptions:
                logger.warning(f"Already subscribed to table: {table_name}")
                return True
            
            # Default event types
            if event_types is None:
                event_types = ['INSERT', 'UPDATE', 'DELETE']
            
            # Create realtime channel - note: Supabase Python client has limitations
            channel_name = f"table_{table_name}"
            
            # For now, implement a polling-based approach instead of realtime subscriptions
            # since the Python Supabase client's realtime features are limited
            logger.warning(f"Supabase realtime not fully supported in Python client, using polling for {table_name}")
            
            # Create a polling task instead of realtime subscription
            polling_task = asyncio.create_task(
                self._poll_table_changes(table_name, event_types, filters, callback)
            )
            
            # Store the polling task instead of a channel
            self._realtime_channels[table_name] = {
                'type': 'polling',
                'task': polling_task,
                'table': table_name,
                'event_types': event_types
            }
            
            # Store channel and callback
            if callback:
                if table_name not in self._subscription_callbacks:
                    self._subscription_callbacks[table_name] = []
                self._subscription_callbacks[table_name].append(callback)
            
            self._table_subscriptions.add(table_name)
            
            logger.info(f"Subscribed to realtime events for table: {table_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to subscribe to table {table_name}: {e}")
            return False

    async def unsubscribe_from_table(self, table_name: str) -> bool:
        """
        Unsubscribe from real-time events for a table.
        
        Args:
            table_name: Name of the table to unsubscribe from
            
        Returns:
            True if unsubscription successful
        """
        try:
            if table_name not in self._table_subscriptions:
                logger.warning(f"Not subscribed to table: {table_name}")
                return True
            
            # Unsubscribe from channel or cancel polling task
            if table_name in self._realtime_channels:
                channel_data = self._realtime_channels[table_name]
                
                if isinstance(channel_data, dict) and channel_data.get('type') == 'polling':
                    # Cancel polling task
                    task = channel_data.get('task')
                    if task and not task.cancelled():
                        task.cancel()
                        try:
                            await task
                        except asyncio.CancelledError:
                            pass
                elif not isinstance(channel_data, dict) and hasattr(channel_data, 'unsubscribe') and callable(getattr(channel_data, 'unsubscribe', None)):
                    # Legacy channel unsubscribe (if we had working realtime)
                    try:
                        await channel_data.unsubscribe()
                    except Exception as e:
                        logger.warning(f"Error unsubscribing from {table_name}: {e}")
                else:
                    logger.warning(f"Channel data for {table_name} doesn't support unsubscribe")
                
                del self._realtime_channels[table_name]
            
            # Remove callbacks
            if table_name in self._subscription_callbacks:
                del self._subscription_callbacks[table_name]
            
            self._table_subscriptions.remove(table_name)
            
            logger.info(f"Unsubscribed from realtime events for table: {table_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unsubscribe from table {table_name}: {e}")
            return False

    def add_event_filter(self, table_name: str, filter_func: Callable[[Dict[str, Any]], bool]):
        """
        Add event filter for a specific table.
        
        Args:
            table_name: Name of the table
            filter_func: Function that returns True if event should be processed
        """
        self._event_filters[table_name] = filter_func
        logger.info(f"Added event filter for table: {table_name}")

    def remove_event_filter(self, table_name: str):
        """Remove event filter for a table"""
        if table_name in self._event_filters:
            del self._event_filters[table_name]
            logger.info(f"Removed event filter for table: {table_name}")

    async def emit_priority_event(self, event_type: str, event_data: Dict[str, Any], 
                                 priority: EventPriority = EventPriority.MEDIUM):
        """
        Emit an event with specific priority.
        
        Args:
            event_type: Type of event
            event_data: Event data
            priority: Event priority level
        """
        realtime_event = RealtimeEvent(
            id=str(uuid4()),
            event_type=event_type,
            table=event_data.get('table', 'unknown'),
            record=event_data,
            old_record=None,
            timestamp=datetime.now(timezone.utc),
            priority=priority
        )
        
        await self._priority_queues[priority].put(realtime_event)
        logger.debug(f"Emitted {priority.value} priority event: {event_type}")

    def get_realtime_statistics(self) -> Dict[str, Any]:
        """Get realtime event processing statistics"""
        return {
            **self.get_statistics(),
            'realtime_stats': self._realtime_stats,
            'active_subscriptions': len(self._table_subscriptions),
            'subscribed_tables': list(self._table_subscriptions),
            'connection_healthy': self._connection_healthy,
            'last_heartbeat': self._last_heartbeat.isoformat() if self._last_heartbeat else None,
            'queue_sizes': {
                priority.value: queue.qsize() 
                for priority, queue in self._priority_queues.items()
            }
        }

    async def _initialize_realtime_subscriptions(self):
        """Initialize default realtime subscriptions for key tables"""
        default_tables = [
            'market_data', 'trading_signals', 'positions', 
            'orders', 'risk_metrics', 'portfolios', 'executions'
        ]
        
        for table in default_tables:
            if self.config.get(f'subscribe_{table}', True):
                await self.subscribe_to_table(table)

    async def _cleanup_realtime_subscriptions(self):
        """Cleanup all realtime subscriptions"""
        for table_name in list(self._table_subscriptions):
            await self.unsubscribe_from_table(table_name)

    async def _handle_realtime_event(self, payload: Dict[str, Any], 
                                   event_type: str, table_name: str):
        """Handle incoming realtime event"""
        try:
            # Update statistics
            self._realtime_stats['events_received'] += 1
            self._realtime_stats['last_event_time'] = datetime.now(timezone.utc)
            
            # Apply filters
            if table_name in self._event_filters:
                if not self._event_filters[table_name](payload):
                    self._realtime_stats['events_filtered'] += 1
                    return
            
            # Create structured event
            realtime_event = RealtimeEvent(
                id=str(uuid4()),
                event_type=f"realtime_{event_type.lower()}",
                table=table_name,
                record=payload.get('new', {}),
                old_record=payload.get('old'),
                timestamp=datetime.now(timezone.utc),
                priority=self._determine_event_priority(table_name, payload)
            )
            
            # Queue event by priority
            await self._priority_queues[realtime_event.priority].put(realtime_event)
            
            # Call registered callbacks
            if table_name in self._subscription_callbacks:
                for callback in self._subscription_callbacks[table_name]:
                    try:
                        await callback(realtime_event)
                    except Exception as e:
                        logger.error(f"Error in callback for {table_name}: {e}")
            
            logger.debug(f"Processed realtime event: {event_type} on {table_name}")
            
        except Exception as e:
            logger.error(f"Error handling realtime event: {e}")

    def _determine_event_priority(self, table_name: str, payload: Dict[str, Any]) -> EventPriority:
        """Determine event priority based on table and content"""
        # Critical events
        if table_name == 'risk_metrics' and payload.get('new', {}).get('risk_level') == 'HIGH':
            return EventPriority.CRITICAL
        if table_name == 'executions':
            return EventPriority.HIGH
        
        # High priority events
        if table_name in ['orders', 'positions']:
            return EventPriority.HIGH
        
        # Medium priority events
        if table_name in ['trading_signals', 'portfolios']:
            return EventPriority.MEDIUM
        
        # Default to low priority
        return EventPriority.LOW

    def _build_filter_string(self, filters: Optional[Dict[str, Any]]) -> str:
        """Build filter string for Supabase realtime subscription"""
        if not filters:
            return ""
        
        filter_parts = []
        for key, value in filters.items():
            filter_parts.append(f"{key}=eq.{value}")
        
        return "&".join(filter_parts)

    async def _process_priority_events_loop(self):
        """Process events by priority order"""
        while self._running:
            try:
                # Process events in priority order
                for priority in [EventPriority.CRITICAL, EventPriority.HIGH, 
                               EventPriority.MEDIUM, EventPriority.LOW]:
                    queue = self._priority_queues[priority]
                    
                    if not queue.empty():
                        try:
                            event = await asyncio.wait_for(queue.get(), timeout=0.1)
                            await self._process_realtime_event(event)
                        except asyncio.TimeoutError:
                            continue
                
                await asyncio.sleep(0.1)  # Small delay to prevent busy waiting
                
            except asyncio.CancelledError:
                logger.info("Priority event processing loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in priority event processing: {e}")
                await asyncio.sleep(1)

    async def _process_realtime_event(self, event: RealtimeEvent):
        """Process a single realtime event"""
        try:
            # Emit to standard event handlers
            await self.emit_event(event.event_type, {
                'id': event.id,
                'table': event.table,
                'record': event.record,
                'old_record': event.old_record,
                'timestamp': event.timestamp.isoformat(),
                'priority': event.priority.value,
                'metadata': event.metadata
            })
            
            self._realtime_stats['events_processed'] += 1
            
        except Exception as e:
            logger.error(f"Error processing realtime event {event.id}: {e}")

    async def _monitor_realtime_connection_loop(self):
        """Monitor realtime connection health"""
        while self._running:
            try:
                await self._check_connection_health()
                await asyncio.sleep(30)  # Check every 30 seconds
            except asyncio.CancelledError:
                logger.info("Realtime connection monitoring loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error monitoring realtime connection: {e}")
                await asyncio.sleep(30)

    async def _check_connection_health(self):
        """Check if realtime connection is healthy"""
        try:
            # Simple health check - could be enhanced with actual ping
            current_time = datetime.now(timezone.utc)
            
            # Check if we've received events recently
            if self._realtime_stats['last_event_time']:
                time_since_last_event = current_time - self._realtime_stats['last_event_time']
                if time_since_last_event.total_seconds() > 300:  # 5 minutes
                    logger.warning("No realtime events received in 5 minutes")
                    self._connection_healthy = False
                else:
                    self._connection_healthy = True
            
        except Exception as e:
            logger.error(f"Error checking connection health: {e}")
            self._connection_healthy = False

    async def _heartbeat_loop(self):
        """Send periodic heartbeat to maintain connection"""
        while self._running:
            try:
                self._last_heartbeat = datetime.now(timezone.utc)
                await asyncio.sleep(60)  # Heartbeat every minute
            except asyncio.CancelledError:
                logger.info("Heartbeat loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in heartbeat loop: {e}")
                await asyncio.sleep(60)

    async def _event_replay_loop(self):
        """Handle event replay for missed events during disconnections"""
        while self._running:
            try:
                # Placeholder for event replay logic
                # This would check for missed events and replay them
                await asyncio.sleep(300)  # Check every 5 minutes
            except asyncio.CancelledError:
                logger.info("Event replay loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in event replay loop: {e}")
                await asyncio.sleep(300)

    async def _poll_table_changes(self, table_name: str, 
                                 event_types: List[str], 
                                 filters: Optional[Dict[str, Any]] = None,
                                 callback: Optional[Callable] = None):
        """
        Poll table for changes since Supabase Python client realtime is limited.
        
        Args:
            table_name: Table to monitor
            event_types: Event types to monitor (INSERT, UPDATE, DELETE)
            filters: Optional filters for the query
            callback: Optional callback for events
        """
        last_check = datetime.now(timezone.utc)
        
        while self._running and table_name in self._table_subscriptions:
            try:
                # For INSERT events, check for new records
                if 'INSERT' in event_types:
                    query = self.supabase.client.table(table_name).select("*")
                    query = query.gte("created_at", last_check.isoformat())
                    
                    # Apply filters if provided
                    if filters:
                        for key, value in filters.items():
                            query = query.eq(key, value)
                    
                    try:
                        result = query.execute()
                        if result.data:
                            for record in result.data:
                                await self._handle_realtime_event(
                                    {'new': record, 'old': None}, 
                                    'INSERT', 
                                    table_name
                                )
                                
                                # Call user callback if provided
                                if callback:
                                    try:
                                        await callback({
                                            'event_type': 'INSERT',
                                            'table': table_name,
                                            'new': record,
                                            'old': None
                                        })
                                    except Exception as e:
                                        logger.error(f"Callback error for {table_name}: {e}")
                    
                    except Exception as e:
                        logger.error(f"Error polling {table_name} for INSERT events: {e}")
                
                # For UPDATE events, check for recently updated records
                if 'UPDATE' in event_types:
                    query = self.supabase.client.table(table_name).select("*")
                    query = query.gte("updated_at", last_check.isoformat())
                    
                    # Apply filters if provided
                    if filters:
                        for key, value in filters.items():
                            query = query.eq(key, value)
                    
                    try:
                        result = query.execute()
                        if result.data:
                            for record in result.data:
                                await self._handle_realtime_event(
                                    {'new': record, 'old': None}, 
                                    'UPDATE', 
                                    table_name
                                )
                                
                                # Call user callback if provided
                                if callback:
                                    try:
                                        await callback({
                                            'event_type': 'UPDATE',
                                            'table': table_name,
                                            'new': record,
                                            'old': None
                                        })
                                    except Exception as e:
                                        logger.error(f"Callback error for {table_name}: {e}")
                    
                    except Exception as e:
                        logger.error(f"Error polling {table_name} for UPDATE events: {e}")
                
                # Update last check time
                last_check = datetime.now(timezone.utc)
                
                # Update statistics
                self._realtime_stats['events_received'] += 1
                self._realtime_stats['last_event_time'] = last_check.isoformat()
                
                # Wait before next poll (adjust based on table activity)
                await asyncio.sleep(1)  # Poll every second
                
            except asyncio.CancelledError:
                logger.info(f"Polling cancelled for table: {table_name}")
                break
            except Exception as e:
                logger.error(f"Error in polling loop for {table_name}: {e}")
                await asyncio.sleep(5)  # Wait longer on error
