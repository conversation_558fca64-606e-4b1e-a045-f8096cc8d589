"""
Event Stream Manager

Manages real-time event streams between Supabase and the MCP server,
coordinating event handlers and ensuring reliable event processing.
"""

import asyncio
from typing import Dict, Any, List, Optional, Callable, Set, Tuple
from datetime import datetime, timezone
from uuid import UUID
import logging

try:
    from database.events.processor import EventProcessor as _EventProcessor
    from database.repositories import (
        TradingSignalRepository as _TradingSignalRepository,
        OrderRepository as _OrderRepository,
        PositionRepository as _PositionRepository
    )
    from models.domain import Signal as _Signal, Order as _Order, Position as _Position

    # Assign to final names
    EventProcessor = _EventProcessor  # type: ignore
    TradingSignalRepository = _TradingSignalRepository  # type: ignore
    OrderRepository = _OrderRepository  # type: ignore
    PositionRepository = _PositionRepository  # type: ignore
    Signal = _Signal  # type: ignore
    Order = _Order  # type: ignore
    Position = _Position  # type: ignore

except ImportError:
    try:
        from app.database.events.processor import EventProcessor as _EventProcessor
        from app.database.repositories import (
            TradingSignalRepository as _TradingSignalRepository,
            OrderRepository as _OrderRepository,
            PositionRepository as _PositionRepository
        )
        from app.models.domain import Signal as _Signal, Order as _Order, Position as _Position

        # Assign to final names
        EventProcessor = _EventProcessor  # type: ignore
        TradingSignalRepository = _TradingSignalRepository  # type: ignore
        OrderRepository = _OrderRepository  # type: ignore
        PositionRepository = _PositionRepository  # type: ignore
        Signal = _Signal  # type: ignore
        Order = _Order  # type: ignore
        Position = _Position  # type: ignore

    except ImportError:
        # Fallback - create dummy classes to prevent import errors
        class EventProcessor:
            def __init__(self): pass
            async def start(self, config): pass
            async def stop(self): pass
            def register_callback(self, *args): pass
            def get_status(self): return {"status": "disabled"}

        class TradingSignalRepository:
            def __init__(self): pass
            async def create_order(self, data): return {"id": "dummy"}
            async def update(self, id, data): pass

        class OrderRepository:
            def __init__(self): pass
            async def create_order(self, data): return {"id": "dummy"}
            async def update(self, id, data): pass

        class PositionRepository:
            def __init__(self): pass
            async def find_all(self, **kwargs): return []
            async def close_position(self, *args): pass
            async def update(self, id, data): pass
            async def open_position(self, data): pass

        class Signal:
            def __init__(self, **kwargs): pass

        class Order:
            def __init__(self, **kwargs): pass

        class Position:
            def __init__(self, **kwargs): pass

logger = logging.getLogger(__name__)


class EventStreamManager:
    """
    Manages event streams and coordinates event processing.
    
    This manager acts as the central hub for all real-time events,
    routing them to appropriate handlers and maintaining event flow.
    """
    
    def __init__(self):
        """Initialize event stream manager"""
        self.event_processor = EventProcessor()
        self.signal_repo = TradingSignalRepository()
        self.order_repo = OrderRepository()
        self.position_repo = PositionRepository()
        
        # Event handlers registry
        self._handlers: Dict[str, List[Callable]] = {
            'market_data': [],
            'trading_signal': [],
            'order_execution': [],
            'position_update': [],
            'risk_alert': [],
            'correlation_change': []
        }
        
        # Active streams
        self._active_streams: Set[str] = set()
        
        # Lifecycle management
        self._running = False
        self._stream_tasks: List[asyncio.Task] = []

        # Event statistics
        self._event_stats = {
            'total_events': 0,
            'events_by_type': {},
            'errors': 0,
            'last_event_time': None
        }
    
    async def start(self, config: Optional[Dict[str, Any]] = None):
        """
        Start event stream manager.
        
        Args:
            config: Configuration for event streams
        """
        if self._running:
            logger.warning("Event stream manager already running")
            return
        
        logger.info("Starting EventStreamManager")
        self._running = True
        config = config or {}
        
        try:
            # Configure event processor
            processor_config = {
                'enable_market_data': config.get('enable_market_data', True),
                'enable_signals': config.get('enable_signals', True),
                'enable_orders': config.get('enable_orders', True),
                'enable_positions': config.get('enable_positions', True),
                'instrument_ids': config.get('instrument_ids', []),
                'strategy_ids': config.get('strategy_ids')
            }
            
            # Start event processor
            await self.event_processor.start(processor_config)
            
            # Register internal handlers
            await self._register_internal_handlers()
            
            # Start event processing tasks
            self._stream_tasks = [
                asyncio.create_task(self._process_events_loop()),
                asyncio.create_task(self._monitor_streams_loop()),
                asyncio.create_task(self._cleanup_expired_events_loop())
            ]
            
            logger.info(f"Started {len(self._stream_tasks)} event processing tasks")
            logger.info("Started event stream manager")
        
        except Exception as e:
            logger.error(f"Failed to start EventStreamManager: {e}")
            self._running = False
            raise

    async def stop(self):
        """Stop event stream manager"""
        if not self._running:
            return
        
        logger.info("Stopping EventStreamManager")
        self._running = False
        
        # Stop event processor
        await self.event_processor.stop()
        
        # Cancel all running tasks
        for task in self._stream_tasks:
            if not task.done():
                task.cancel()
        
        # Wait for tasks to complete cancellation
        if self._stream_tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*self._stream_tasks, return_exceptions=True),
                    timeout=5.0
                )
            except asyncio.TimeoutError:
                logger.warning("Timeout waiting for event stream tasks to cancel")
        
        self._stream_tasks.clear()
        self._active_streams.clear()
        logger.info("EventStreamManager stopped successfully")
    
    def register_handler(self, event_type: str, handler: Callable) -> bool:
        """
        Register an event handler.
        
        Args:
            event_type: Type of event to handle
            handler: Async callable to handle events
            
        Returns:
            True if registered successfully
        """
        if event_type not in self._handlers:
            logger.error(f"Unknown event type: {event_type}")
            return False
        
        self._handlers[event_type].append(handler)
        logger.info(f"Registered handler for {event_type}")
        return True
    
    def unregister_handler(self, event_type: str, handler: Callable) -> bool:
        """
        Unregister an event handler.
        
        Args:
            event_type: Type of event
            handler: Handler to remove
            
        Returns:
            True if unregistered successfully
        """
        if event_type in self._handlers and handler in self._handlers[event_type]:
            self._handlers[event_type].remove(handler)
            logger.info(f"Unregistered handler for {event_type}")
            return True
        return False
    
    async def emit_event(self, event_type: str, event_data: Dict[str, Any]):
        """
        Emit an event to all registered handlers.
        
        Args:
            event_type: Type of event
            event_data: Event data
        """
        if event_type not in self._handlers:
            logger.error(f"Unknown event type: {event_type}")
            return
        
        # Update statistics
        self._event_stats['total_events'] += 1
        self._event_stats['events_by_type'][event_type] = \
            self._event_stats['events_by_type'].get(event_type, 0) + 1
        self._event_stats['last_event_time'] = datetime.now(timezone.utc)
        
        # Call all handlers
        for handler in self._handlers[event_type]:
            try:
                await handler(event_data)
            except Exception as e:
                logger.error(f"Error in {event_type} handler: {e}")
                self._event_stats['errors'] += 1
    
    async def subscribe_to_instrument(self, instrument_id: UUID):
        """Subscribe to events for a specific instrument"""
        # Add to market data stream
        stream_id = f"instrument_{instrument_id}"
        if stream_id not in self._active_streams:
            # Configure processor to include this instrument
            # This would update the event processor's subscription
            self._active_streams.add(stream_id)
            logger.info(f"Subscribed to instrument {instrument_id}")
    
    async def unsubscribe_from_instrument(self, instrument_id: UUID):
        """Unsubscribe from events for a specific instrument"""
        stream_id = f"instrument_{instrument_id}"
        if stream_id in self._active_streams:
            self._active_streams.remove(stream_id)
            logger.info(f"Unsubscribed from instrument {instrument_id}")
    
    async def subscribe_to_strategy(self, strategy_id: UUID):
        """Subscribe to events for a specific strategy"""
        stream_id = f"strategy_{strategy_id}"
        if stream_id not in self._active_streams:
            self._active_streams.add(stream_id)
            logger.info(f"Subscribed to strategy {strategy_id}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get event processing statistics"""
        return {
            **self._event_stats,
            'active_streams': len(self._active_streams),
            'registered_handlers': {
                event_type: len(handlers)
                for event_type, handlers in self._handlers.items()
            },
            'processor_status': self.event_processor.get_status()
        }
    
    async def _register_internal_handlers(self):
        """Register internal event handlers"""
        # Market data handler
        async def handle_market_data(event_data: Dict[str, Any]):
            """Process market data updates"""
            await self.emit_event('market_data', event_data)
        
        self.event_processor.register_callback(
            'market_data',
            'market_data',
            handle_market_data
        )
        
        # Trading signal handler
        async def handle_trading_signal(signal_data: Dict[str, Any]):
            """Process new trading signals"""
            # Validate signal
            if signal_data.get('signal_strength', 0) < 0.3:
                logger.debug(f"Ignoring weak signal: {signal_data}")
                return
            
            # Emit to handlers
            await self.emit_event('trading_signal', signal_data)
            
            # Auto-execute if configured
            if signal_data.get('auto_execute', False):
                await self._auto_execute_signal(signal_data)
        
        self.event_processor.register_callback(
            'signals',
            'signal_processor',
            handle_trading_signal
        )
        
        # Order execution handler
        async def handle_order_execution(execution_data: Dict[str, Any]):
            """Process order executions"""
            await self.emit_event('order_execution', execution_data)
            
            # Update position if needed
            await self._update_position_from_execution(execution_data)
        
        self.event_processor.register_callback(
            'orders',
            'execution',
            handle_order_execution
        )
        
        # Position update handler
        async def handle_position_update(position_data: Dict[str, Any]):
            """Process position updates"""
            await self.emit_event('position_update', position_data)
            
            # Check risk limits
            await self._check_position_risk(position_data)
        
        self.event_processor.register_callback(
            'positions',
            'risk_manager',
            handle_position_update
        )
    
    async def _auto_execute_signal(self, signal_data: Dict[str, Any]):
        """Automatically execute a trading signal"""
        try:
            # Create order from signal
            order_data = {
                "signal_id": signal_data['id'],
                "instrument_id": signal_data['instrument_id'],
                "order_type": "LIMIT" if signal_data.get('target_price') else "MARKET",
                "side": signal_data['signal_type'],
                "quantity": signal_data.get('suggested_quantity', 0),
                "limit_price": signal_data.get('target_price'),
                "metadata": {
                    "auto_executed": True,
                    "signal_strength": signal_data['signal_strength']
                }
            }
            
            # Create order
            order = await self.order_repo.create_order(order_data)
            
            logger.info(f"Auto-executed signal {signal_data['id']} -> order {order['id']}")
            
        except Exception as e:
            logger.error(f"Failed to auto-execute signal: {e}")
    
    async def _update_position_from_execution(self, execution_data: Dict[str, Any]):
        """Update position based on order execution"""
        try:
            # Find or create position
            positions = await self.position_repo.find_all(
                filters={
                    "instrument_id": execution_data['instrument_id'],
                    "is_open": True
                },
                limit=1
            )
            
            if positions:
                # Update existing position
                position = positions[0]
                new_quantity = position['quantity']
                
                if execution_data['side'] == position['side']:
                    # Adding to position
                    new_quantity += execution_data['filled_quantity']
                else:
                    # Reducing position
                    new_quantity -= execution_data['filled_quantity']
                
                if new_quantity <= 0:
                    # Close position
                    await self.position_repo.close_position(
                        UUID(position['id']),
                        execution_data['average_fill_price'],
                        0  # Calculate realized P&L
                    )
                else:
                    # Update quantity
                    await self.position_repo.update(
                        UUID(position['id']),
                        {"quantity": new_quantity}
                    )
            else:
                # Create new position
                await self.position_repo.open_position({
                    "instrument_id": execution_data['instrument_id'],
                    "side": execution_data['side'],
                    "quantity": execution_data['filled_quantity'],
                    "entry_price": execution_data['average_fill_price']
                })
                
        except Exception as e:
            logger.error(f"Failed to update position from execution: {e}")
    
    async def _check_position_risk(self, position_data: Dict[str, Any]):
        """Check position risk limits"""
        try:
            # Check unrealized P&L
            unrealized_pnl = position_data.get('unrealized_pnl', 0)
            
            # Check against risk limits
            if unrealized_pnl < -10000:  # Example: $10k loss limit
                await self.emit_event('risk_alert', {
                    'type': 'position_loss_limit',
                    'severity': 'high',
                    'position_id': position_data['id'],
                    'unrealized_pnl': unrealized_pnl,
                    'message': f"Position loss exceeds limit: ${unrealized_pnl:.2f}",
                    'action_required': True
                })
            
            # Check position size limits
            position_value = position_data['quantity'] * position_data.get('current_price', 0)
            if position_value > 1000000:  # Example: $1M position limit
                await self.emit_event('risk_alert', {
                    'type': 'position_size_limit',
                    'severity': 'medium',
                    'position_id': position_data['id'],
                    'position_value': position_value,
                    'message': f"Position size exceeds limit: ${position_value:.2f}",
                    'action_required': False
                })
                
        except Exception as e:
            logger.error(f"Failed to check position risk: {e}")

    async def _process_events_loop(self):
        """Continuously process incoming events."""
        while self._running:
            try:
                # Process any pending events
                await self._process_pending_events()
                await asyncio.sleep(1)  # Process events every second
            except asyncio.CancelledError:
                logger.info("Event processing loop cancelled")
                break
            except Exception as e:
                logger.error(f"Event processing error: {e}")
                await asyncio.sleep(1)

    async def _monitor_streams_loop(self):
        """Monitor active streams for health and performance."""
        while self._running:
            try:
                await self._monitor_stream_health()
                await asyncio.sleep(30)  # Monitor every 30 seconds
            except asyncio.CancelledError:
                logger.info("Stream monitoring loop cancelled")
                break
            except Exception as e:
                logger.error(f"Stream monitoring error: {e}")
                await asyncio.sleep(30)

    async def _cleanup_expired_events_loop(self):
        """Clean up expired events and maintain cache health."""
        while self._running:
            try:
                await self._cleanup_expired_events()
                await asyncio.sleep(300)  # Cleanup every 5 minutes
            except asyncio.CancelledError:
                logger.info("Event cleanup loop cancelled")
                break
            except Exception as e:
                logger.error(f"Event cleanup error: {e}")
                await asyncio.sleep(300)

    async def _process_pending_events(self):
        """Process any pending events in the queue."""
        try:
            # Implementation would process events from the event processor
            # This is a placeholder for the actual event processing logic
            pass
        except Exception as e:
            logger.error(f"Error processing pending events: {e}")

    async def _monitor_stream_health(self):
        """Monitor the health of active streams."""
        try:
            # Monitor active streams and their performance
            logger.debug(f"Monitoring {len(self._active_streams)} active streams")
        except Exception as e:
            logger.error(f"Error monitoring stream health: {e}")

    async def _cleanup_expired_events(self):
        """Clean up expired events and maintain cache health."""
        try:
            # Clean up any expired or stale events
            logger.debug("Cleaning up expired events")
        except Exception as e:
            logger.error(f"Error cleaning up expired events: {e}")


class CorrelationMonitor:
    """
    Monitors correlation changes and triggers events.
    
    This component tracks correlation stability and alerts
    when significant changes occur.
    """
    
    def __init__(self, event_manager: EventStreamManager):
        """
        Initialize correlation monitor.
        
        Args:
            event_manager: Event stream manager instance
        """
        self.event_manager = event_manager
        self._correlation_cache: Dict[Tuple[UUID, UUID], float] = {}
        self._correlation_threshold = 0.3  # 30% change threshold
    
    async def update_correlation(self, instrument1_id: UUID, instrument2_id: UUID,
                               new_correlation: float):
        """Update correlation and check for significant changes"""
        key = (instrument1_id, instrument2_id)
        
        # Check if we have previous correlation
        if key in self._correlation_cache:
            old_correlation = self._correlation_cache[key]
            change = abs(new_correlation - old_correlation)
            
            if change > self._correlation_threshold:
                # Significant change detected
                await self.event_manager.emit_event('correlation_change', {
                    'instrument1_id': str(instrument1_id),
                    'instrument2_id': str(instrument2_id),
                    'old_correlation': old_correlation,
                    'new_correlation': new_correlation,
                    'change': change,
                    'timestamp': datetime.now(timezone.utc).isoformat()
                })
        
        # Update cache
        self._correlation_cache[key] = new_correlation
