"""
Edge Functions Client

Provides integration between the IBKR MCP Server and Supabase Edge Functions.
Handles communication with market-data-aggregator and signal-executor functions.
"""

import asyncio
import aiohttp
import json
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class EdgeFunctionAction(Enum):
    """Available actions for Edge Functions"""
    # Market Data Aggregator actions
    PROCESS_TICK = "process_tick"
    GENERATE_CANDLES = "generate_candles"
    CALCULATE_INDICATORS = "calculate_indicators"
    UPDATE_CORRELATIONS = "update_correlations"
    
    # Signal Executor actions
    PROCESS_SIGNALS = "process_signals"
    EXECUTE_SIGNAL = "execute_signal"
    VALIDATE_RISK = "validate_risk"
    MANAGE_POSITIONS = "manage_positions"

@dataclass
class MarketDataTick:
    """Market data tick structure"""
    pair_id: str
    symbol: str
    bid: float
    ask: float
    bid_volume: Optional[int] = None
    ask_volume: Optional[int] = None
    timestamp: Optional[str] = None
    source: str = "IBKR"

@dataclass
class EdgeFunctionResponse:
    """Response from Edge Function"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: Optional[str] = None

class EdgeFunctionsClient:
    """
    Client for interacting with Supabase Edge Functions.
    
    Provides methods to call market-data-aggregator and signal-executor
    functions with proper error handling and retry logic.
    """
    
    def __init__(self, supabase_url: str, supabase_anon_key: str, timeout: int = 30):
        """
        Initialize Edge Functions client.
        
        Args:
            supabase_url: Supabase project URL
            supabase_anon_key: Supabase anonymous key
            timeout: Request timeout in seconds
        """
        self.supabase_url = supabase_url.rstrip('/')
        self.supabase_anon_key = supabase_anon_key
        self.timeout = timeout
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Function URLs
        self.market_data_url = f"{self.supabase_url}/functions/v1/market-data-aggregator"
        self.signal_executor_url = f"{self.supabase_url}/functions/v1/signal-executor"
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def _ensure_session(self):
        """Ensure aiohttp session is created"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {self.supabase_anon_key}',
                    'apikey': self.supabase_anon_key
                }
            )
    
    async def close(self):
        """Close the aiohttp session"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def _make_request(self, url: str, payload: Dict[str, Any]) -> EdgeFunctionResponse:
        """
        Make HTTP request to Edge Function.

        Args:
            url: Function URL
            payload: Request payload

        Returns:
            EdgeFunctionResponse with result or error
        """
        await self._ensure_session()

        if self.session is None:
            return EdgeFunctionResponse(success=False, error="Session not initialized")

        try:
            async with self.session.post(url, json=payload) as response:
                response_data = await response.json()
                
                if response.status == 200:
                    return EdgeFunctionResponse(
                        success=response_data.get('success', True),
                        data=response_data.get('data'),
                        timestamp=response_data.get('timestamp')
                    )
                else:
                    error_msg = response_data.get('error', f'HTTP {response.status}')
                    logger.error(f"Edge Function error: {error_msg}")
                    return EdgeFunctionResponse(
                        success=False,
                        error=error_msg,
                        timestamp=response_data.get('timestamp')
                    )
                    
        except asyncio.TimeoutError:
            error_msg = f"Request timeout after {self.timeout}s"
            logger.error(error_msg)
            return EdgeFunctionResponse(success=False, error=error_msg)
            
        except Exception as e:
            error_msg = f"Request failed: {str(e)}"
            logger.error(error_msg)
            return EdgeFunctionResponse(success=False, error=error_msg)
    
    # Market Data Aggregator Methods
    
    async def process_tick(self, tick_data: MarketDataTick) -> EdgeFunctionResponse:
        """
        Process a market data tick.
        
        Args:
            tick_data: Market data tick to process
            
        Returns:
            EdgeFunctionResponse with processing result
        """
        payload = {
            "action": EdgeFunctionAction.PROCESS_TICK.value,
            "data": {
                "pair_id": tick_data.pair_id,
                "symbol": tick_data.symbol,
                "bid": tick_data.bid,
                "ask": tick_data.ask,
                "bid_volume": tick_data.bid_volume,
                "ask_volume": tick_data.ask_volume,
                "timestamp": tick_data.timestamp or datetime.now(timezone.utc).isoformat(),
                "source": tick_data.source
            }
        }
        
        return await self._make_request(self.market_data_url, payload)
    
    async def generate_candles(self, pair_ids: List[str], interval: str = "1min") -> EdgeFunctionResponse:
        """
        Generate OHLC candles for specified pairs.
        
        Args:
            pair_ids: List of currency pair IDs
            interval: Candle interval (1min, 5min, 15min, 1hour, 1day)
            
        Returns:
            EdgeFunctionResponse with generated candles
        """
        payload = {
            "action": EdgeFunctionAction.GENERATE_CANDLES.value,
            "pair_ids": pair_ids,
            "interval": interval
        }
        
        return await self._make_request(self.market_data_url, payload)
    
    async def calculate_indicators(self, pair_ids: List[str], lookback_periods: int = 50) -> EdgeFunctionResponse:
        """
        Calculate technical indicators for specified pairs.
        
        Args:
            pair_ids: List of currency pair IDs
            lookback_periods: Number of periods for calculation
            
        Returns:
            EdgeFunctionResponse with calculated indicators
        """
        payload = {
            "action": EdgeFunctionAction.CALCULATE_INDICATORS.value,
            "pair_ids": pair_ids,
            "lookback_periods": lookback_periods
        }
        
        return await self._make_request(self.market_data_url, payload)
    
    async def update_correlations(self, pair_ids: List[str], lookback_periods: int = 50) -> EdgeFunctionResponse:
        """
        Update correlation matrix for specified pairs.
        
        Args:
            pair_ids: List of currency pair IDs
            lookback_periods: Number of periods for correlation calculation
            
        Returns:
            EdgeFunctionResponse with correlation updates
        """
        payload = {
            "action": EdgeFunctionAction.UPDATE_CORRELATIONS.value,
            "pair_ids": pair_ids,
            "lookback_periods": lookback_periods
        }
        
        return await self._make_request(self.market_data_url, payload)
    
    # Signal Executor Methods
    
    async def process_signals(self, strategy_id: Optional[str] = None) -> EdgeFunctionResponse:
        """
        Process all active trading signals.
        
        Args:
            strategy_id: Optional strategy ID to filter signals
            
        Returns:
            EdgeFunctionResponse with processing results
        """
        payload = {
            "action": EdgeFunctionAction.PROCESS_SIGNALS.value
        }
        
        if strategy_id:
            payload["strategy_id"] = strategy_id
        
        return await self._make_request(self.signal_executor_url, payload)
    
    async def execute_signal(self, signal_id: str, force_execution: bool = False) -> EdgeFunctionResponse:
        """
        Execute a specific trading signal.
        
        Args:
            signal_id: ID of the signal to execute
            force_execution: Whether to bypass risk validation
            
        Returns:
            EdgeFunctionResponse with execution result
        """
        payload = {
            "action": EdgeFunctionAction.EXECUTE_SIGNAL.value,
            "signal_id": signal_id,
            "force_execution": force_execution
        }
        
        return await self._make_request(self.signal_executor_url, payload)
    
    async def validate_risk(self, signal_id: str) -> EdgeFunctionResponse:
        """
        Validate risk limits for a signal.
        
        Args:
            signal_id: ID of the signal to validate
            
        Returns:
            EdgeFunctionResponse with validation result
        """
        payload = {
            "action": EdgeFunctionAction.VALIDATE_RISK.value,
            "signal_id": signal_id
        }
        
        return await self._make_request(self.signal_executor_url, payload)
    
    async def manage_positions(self) -> EdgeFunctionResponse:
        """
        Manage open positions (stop-loss, take-profit monitoring).
        
        Returns:
            EdgeFunctionResponse with management results
        """
        payload = {
            "action": EdgeFunctionAction.MANAGE_POSITIONS.value
        }
        
        return await self._make_request(self.signal_executor_url, payload)
    
    # Batch Operations
    
    async def batch_process_ticks(self, ticks: List[MarketDataTick]) -> List[Union[EdgeFunctionResponse, BaseException]]:
        """
        Process multiple ticks in parallel.

        Args:
            ticks: List of market data ticks

        Returns:
            List of EdgeFunctionResponse objects or exceptions
        """
        tasks = [self.process_tick(tick) for tick in ticks]
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    async def health_check(self) -> Dict[str, bool]:
        """
        Check health of both Edge Functions.
        
        Returns:
            Dictionary with health status of each function
        """
        results = {}
        
        # Test market data aggregator
        try:
            response = await self._make_request(self.market_data_url, {"action": "health_check"})
            results["market_data_aggregator"] = response.success
        except Exception:
            results["market_data_aggregator"] = False
        
        # Test signal executor
        try:
            response = await self._make_request(self.signal_executor_url, {"action": "health_check"})
            results["signal_executor"] = response.success
        except Exception:
            results["signal_executor"] = False
        
        return results

# Convenience functions for common operations

async def process_ibkr_tick(client: EdgeFunctionsClient, pair_id: str, symbol: str, 
                           bid: float, ask: float, volume: Optional[int] = None) -> bool:
    """
    Convenience function to process an IBKR tick.
    
    Args:
        client: EdgeFunctionsClient instance
        pair_id: Currency pair ID
        symbol: Trading symbol
        bid: Bid price
        ask: Ask price
        volume: Optional volume
        
    Returns:
        True if successful, False otherwise
    """
    tick = MarketDataTick(
        pair_id=pair_id,
        symbol=symbol,
        bid=bid,
        ask=ask,
        bid_volume=volume,
        source="IBKR"
    )
    
    response = await client.process_tick(tick)
    return response.success

async def execute_trading_signal(client: EdgeFunctionsClient, signal_id: str, 
                                validate_risk: bool = True) -> Dict[str, Any]:
    """
    Convenience function to execute a trading signal with optional risk validation.
    
    Args:
        client: EdgeFunctionsClient instance
        signal_id: Signal ID to execute
        validate_risk: Whether to validate risk before execution
        
    Returns:
        Dictionary with execution results
    """
    if validate_risk:
        risk_response = await client.validate_risk(signal_id)
        if not risk_response.success:
            return {"success": False, "error": "Risk validation failed", "details": risk_response.error}
    
    execution_response = await client.execute_signal(signal_id)
    return {
        "success": execution_response.success,
        "data": execution_response.data,
        "error": execution_response.error
    }
