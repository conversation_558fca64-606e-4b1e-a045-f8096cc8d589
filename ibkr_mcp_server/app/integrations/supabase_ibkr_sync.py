"""
Supabase-IBKR Synchronization Service

Handles real-time synchronization between Interactive Brokers and Supabase,
ensuring data consistency and enabling event-driven trading strategies.
"""

import asyncio
from typing import Dict, Any, List
from datetime import datetime, timezone
from uuid import UUID
import logging
from enum import Enum

# Import required modules with fallback handling
try:
    from app.database.repositories.forex_repositories import (  # type: ignore
        MarketDataRepository as _MarketDataRepository,
        OrderRepository as _OrderRepository,
        PositionRepository as _PositionRepository,
        TradingSignalRepository as _TradingSignalRepository,
        PerformanceMetricsRepository as _PerformanceMetricsRepository
    )
    from app.database.repositories.multi_asset_repository import (  # type: ignore
        InstrumentRepository as _InstrumentRepository,
        StockDetailsRepository as _StockDetailsRepository,
        OptionDetailsRepository as _OptionDetailsRepository,
        FutureDetailsRepository as _FutureDetailsRepository,
        CryptoDetailsRepository as _CryptoDetailsRepository
    )
    from app.services.ibkr_service import IBKRService as _IBKRService  # type: ignore
    from app.models.order_models import OrderStatus as _OrderStatus  # type: ignore

    # Assign to final names
    MarketDataRepository = _MarketDataRepository  # type: ignore
    OrderRepository = _OrderRepository  # type: ignore
    PositionRepository = _PositionRepository  # type: ignore
    TradingSignalRepository = _TradingSignalRepository  # type: ignore
    PerformanceMetricsRepository = _PerformanceMetricsRepository  # type: ignore
    InstrumentRepository = _InstrumentRepository  # type: ignore
    StockDetailsRepository = _StockDetailsRepository  # type: ignore
    OptionDetailsRepository = _OptionDetailsRepository  # type: ignore
    FutureDetailsRepository = _FutureDetailsRepository  # type: ignore
    CryptoDetailsRepository = _CryptoDetailsRepository  # type: ignore
    IBKRService = _IBKRService  # type: ignore
    OrderStatus = _OrderStatus  # type: ignore

except ImportError:
    try:
        from ibkr_mcp_server.app.database.repositories.forex_repositories import (  # type: ignore
            MarketDataRepository as _MarketDataRepository,
            OrderRepository as _OrderRepository,
            PositionRepository as _PositionRepository,
            TradingSignalRepository as _TradingSignalRepository,
            PerformanceMetricsRepository as _PerformanceMetricsRepository
        )
        from ibkr_mcp_server.app.database.repositories.multi_asset_repository import (  # type: ignore
            InstrumentRepository as _InstrumentRepository,
            StockDetailsRepository as _StockDetailsRepository,
            OptionDetailsRepository as _OptionDetailsRepository,
            FutureDetailsRepository as _FutureDetailsRepository,
            CryptoDetailsRepository as _CryptoDetailsRepository
        )
        from ibkr_mcp_server.app.services.ibkr_service import IBKRService as _IBKRService  # type: ignore
        from ibkr_mcp_server.app.models.order_models import OrderStatus as _OrderStatus  # type: ignore

        # Assign to final names
        MarketDataRepository = _MarketDataRepository  # type: ignore
        OrderRepository = _OrderRepository  # type: ignore
        PositionRepository = _PositionRepository  # type: ignore
        TradingSignalRepository = _TradingSignalRepository  # type: ignore
        PerformanceMetricsRepository = _PerformanceMetricsRepository  # type: ignore
        InstrumentRepository = _InstrumentRepository  # type: ignore
        StockDetailsRepository = _StockDetailsRepository  # type: ignore
        OptionDetailsRepository = _OptionDetailsRepository  # type: ignore
        FutureDetailsRepository = _FutureDetailsRepository  # type: ignore
        CryptoDetailsRepository = _CryptoDetailsRepository  # type: ignore
        IBKRService = _IBKRService  # type: ignore
        OrderStatus = _OrderStatus  # type: ignore

    except ImportError:
        # Create fallback classes with all required methods
        class OrderStatus(Enum):  # type: ignore
            SUBMITTED = "Submitted"
            FILLED = "Filled"
            CANCELLED = "Cancelled"
            PENDING = "Pending"
            PENDING_SUBMIT = "PendingSubmit"
            PENDING_CANCEL = "PendingCancel"
            PRE_SUBMITTED = "PreSubmitted"
            API_CANCELLED = "ApiCancelled"
            API_PENDING = "ApiPending"
            INACTIVE = "Inactive"
            UNKNOWN = "Unknown"

        class MarketDataRepository:  # type: ignore
            def __init__(self): pass
            async def create(self, data): return {"id": "dummy"}
            async def update(self, id, data): return {"id": id}
            async def get_latest(self, symbol): return []
            async def insert_tick(self, **kwargs): return {"id": "dummy"}

        class OrderRepository:  # type: ignore
            def __init__(self): pass
            async def get_pending_orders(self): return []
            async def update_status(self, order_id, status): return {"id": order_id}
            async def create(self, data): return {"id": "dummy"}
            async def update(self, order_id, data): return {"id": order_id}
            async def create_order(self, **kwargs): return {"id": "dummy"}

        class PositionRepository:  # type: ignore
            def __init__(self): pass
            async def get_open_positions(self): return []
            async def update_position(self, symbol, data): return {"symbol": symbol}
            async def create(self, data): return {"id": "dummy"}
            async def update(self, position_id, data): return {"id": position_id}

        class TradingSignalRepository:  # type: ignore
            def __init__(self): pass
            async def get_active_signals(self): return []
            async def update_status(self, signal_id, status): return {"id": signal_id}
            async def update(self, signal_id, data): return {"id": signal_id}

        class PerformanceMetricsRepository:  # type: ignore
            def __init__(self): pass
            async def create(self, data): return {"id": "dummy"}
            async def get_latest(self): return {}

        class InstrumentRepository:  # type: ignore
            def __init__(self): pass
            async def search_instruments(self, query, limit=100): return []
            async def get_by_symbol_and_class(self, symbol, asset_class): return None

        class StockDetailsRepository:  # type: ignore
            def __init__(self): pass

        class OptionDetailsRepository:  # type: ignore
            def __init__(self): pass

        class FutureDetailsRepository:  # type: ignore
            def __init__(self): pass

        class CryptoDetailsRepository:  # type: ignore
            def __init__(self): pass

        class OrderManagementServiceDelegate:  # type: ignore
            async def get_active_orders(self): return {"status": "success", "orders": []}

        class IBKRService:  # type: ignore
            def __init__(self):
                self.order_management_service_delegate = OrderManagementServiceDelegate()
            async def get_portfolio(self): return []
            async def get_market_data(self, symbol): return {}  # type: ignore
            async def get_account_summary(self): return {}
            async def create_order(self, **kwargs): return {"status": "error", "message": "Dummy service"}  # type: ignore

# Use ib_async for IBKR integrations
from ib_async import Contract, Stock, Option, Future, Forex, Crypto

logger = logging.getLogger(__name__)

class SupabaseIBKRSync:
    """
    Synchronization service between Supabase database and Interactive Brokers.
    
    Features:
    - Real-time order status updates
    - Position synchronization
    - Market data synchronization
    - Trading signal execution
    - Performance metrics tracking
    """
    
    def __init__(self, ibkr_service: IBKRService):
        """
        Initialize the synchronization service.
        
        Args:
            ibkr_service: IBKR service instance for broker operations
        """
        self.ibkr = ibkr_service
        
        # Initialize repositories
        self.market_repo = MarketDataRepository()
        self.order_repo = OrderRepository()
        self.position_repo = PositionRepository()
        self.signal_repo = TradingSignalRepository()
        self.performance_repo = PerformanceMetricsRepository()
        
        # Multi-asset repositories
        self.instrument_repo = InstrumentRepository()
        self.stock_repo = StockDetailsRepository()
        self.option_repo = OptionDetailsRepository()
        self.future_repo = FutureDetailsRepository()
        self.crypto_repo = CryptoDetailsRepository()
        
        # Sync state tracking
        self._last_sync = {}
        self._sync_lock = asyncio.Lock()
        
        # Task management for lifecycle
        self._sync_tasks: List[asyncio.Task] = []
        self._running = False

    async def start(self):
        """
        Start the synchronization service with lifecycle management.
        """
        if self._running:
            logger.warning("SupabaseIBKRSync is already running")
            return
        
        logger.info("Starting SupabaseIBKRSync service")
        self._running = True
        
        try:
            # Start all sync tasks
            sync_interval = 30  # Default sync interval
            self._sync_tasks = [
                asyncio.create_task(self._sync_orders_loop(sync_interval)),
                asyncio.create_task(self._sync_positions_loop(sync_interval)),
                asyncio.create_task(self._sync_market_data_loop(sync_interval)),
                asyncio.create_task(self._execute_signals_loop(sync_interval)),
                asyncio.create_task(self._update_performance_loop(sync_interval * 2))
            ]
            
            logger.info(f"Started {len(self._sync_tasks)} sync tasks")
            
        except Exception as e:
            logger.error(f"Failed to start SupabaseIBKRSync: {e}")
            self._running = False
            raise

    async def stop(self):
        """
        Stop the synchronization service and cleanup resources.
        """
        if not self._running:
            logger.warning("SupabaseIBKRSync is not running")
            return
            
        logger.info("Stopping SupabaseIBKRSync service")
        self._running = False
        
        # Cancel all running tasks
        for task in self._sync_tasks:
            if not task.done():
                task.cancel()
        
        # Wait for tasks to complete cancellation
        if self._sync_tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*self._sync_tasks, return_exceptions=True),
                    timeout=5.0
                )
            except asyncio.TimeoutError:
                logger.warning("Timeout waiting for sync tasks to cancel")
        
        self._sync_tasks.clear()
        logger.info("SupabaseIBKRSync stopped successfully")

    async def start_sync(self, sync_interval: int = 30):
        """
        Start the continuous synchronization process.
        
        Args:
            sync_interval: Sync interval in seconds (default: 30)
        """
        logger.info("Starting Supabase-IBKR synchronization service")
        
        # Start sync tasks
        tasks = [
            asyncio.create_task(self._sync_orders_loop(sync_interval)),
            asyncio.create_task(self._sync_positions_loop(sync_interval)),
            asyncio.create_task(self._sync_market_data_loop(sync_interval)),
            asyncio.create_task(self._execute_signals_loop(sync_interval)),
            asyncio.create_task(self._update_performance_loop(sync_interval * 2))
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Sync service error: {e}")
            raise

    async def _sync_orders_loop(self, interval: int):
        """Continuously sync order statuses between IBKR and Supabase."""
        while self._running:
            try:
                async with self._sync_lock:
                    await self._sync_orders()
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                logger.info("Order sync loop cancelled")
                break
            except Exception as e:
                logger.error(f"Order sync error: {e}")
                await asyncio.sleep(interval)

    async def _sync_positions_loop(self, interval: int):
        """Continuously sync positions between IBKR and Supabase."""
        while self._running:
            try:
                async with self._sync_lock:
                    await self._sync_positions()
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                logger.info("Position sync loop cancelled")
                break
            except Exception as e:
                logger.error(f"Position sync error: {e}")
                await asyncio.sleep(interval)

    async def _sync_market_data_loop(self, interval: int):
        """Continuously sync market data from IBKR to Supabase."""
        while self._running:
            try:
                await self._sync_market_data()
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                logger.info("Market data sync loop cancelled")
                break
            except Exception as e:
                logger.error(f"Market data sync error: {e}")
                await asyncio.sleep(interval)

    async def _execute_signals_loop(self, interval: int):
        """Continuously check and execute trading signals."""
        while self._running:
            try:
                await self._execute_pending_signals()
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                logger.info("Signal execution loop cancelled")
                break
            except Exception as e:
                logger.error(f"Signal execution error: {e}")
                await asyncio.sleep(interval)

    async def _update_performance_loop(self, interval: int):
        """Continuously update performance metrics."""
        while self._running:
            try:
                await self._update_performance_metrics()
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                logger.info("Performance update loop cancelled")
                break
            except Exception as e:
                logger.error(f"Performance update error: {e}")
                await asyncio.sleep(interval)

    async def _sync_orders(self):
        """Sync order statuses between IBKR and Supabase."""
        try:
            # Get pending orders from Supabase
            pending_orders = await self.order_repo.get_pending_orders()
            
            if not pending_orders:
                return
            
            # Get open orders from IBKR using order management service
            if hasattr(self.ibkr, 'order_management_service_delegate') and self.ibkr.order_management_service_delegate:
                order_result = await self.ibkr.order_management_service_delegate.get_active_orders()
                if order_result.get("status") == "success":
                    ibkr_orders = order_result.get("orders", [])
                else:
                    logger.warning(f"Failed to get active orders: {order_result.get('message')}")
                    return
            else:
                logger.warning("Order management service not available")
                return
            
            # Update order statuses
            for db_order in pending_orders:
                if not db_order:  # Check for None
                    continue
                    
                ibkr_order_id = db_order.get("ibkr_order_id")
                if not ibkr_order_id:
                    continue
                
                # Find matching IBKR order
                ibkr_order = next(
                    (o for o in ibkr_orders if str(o.get("order_id")) == str(ibkr_order_id)),
                    None
                )
                
                if ibkr_order:
                    # Map IBKR status to our OrderStatus enum
                    new_status = self._map_ibkr_status(ibkr_order.get("status", "Unknown"))
                    
                    # Update if status changed
                    if db_order["status"] != new_status.value:
                        await self.order_repo.update(
                            UUID(db_order["id"]),
                            {
                                "status": new_status.value,
                                "filled_quantity": ibkr_order.get("filled", 0),
                                "average_fill_price": ibkr_order.get("avg_fill_price"),
                                "commission": None,  # Commission not available in active orders response
                                "last_updated": datetime.now(timezone.utc)
                            }
                        )
                        logger.info(f"Updated order {db_order['id']} status to {new_status.value}")
            
        except Exception as e:
            logger.error(f"Error syncing orders: {e}")
            raise

    async def _sync_positions(self):
        """Sync positions between IBKR and Supabase."""
        try:
            # Get positions from IBKR
            ibkr_positions = await self.ibkr.get_portfolio()
            
            # Get existing positions from Supabase
            db_positions = await self.position_repo.get_open_positions()
            
            # Create sets for comparison
            ibkr_symbols = {pos["symbol"] for pos in ibkr_positions if pos.get("position", 0) != 0}
            
            # Update existing positions
            for ibkr_pos in ibkr_positions:
                position_qty = ibkr_pos.get("position", 0)
                if position_qty == 0:
                    continue
                    
                position_data = {
                    "symbol": ibkr_pos.get("symbol", ""),
                    "side": "LONG" if position_qty > 0 else "SHORT",
                    "quantity": abs(float(position_qty)),
                    "entry_price": float(ibkr_pos.get("avgCost", 0)),
                    "current_price": float(ibkr_pos.get("marketPrice", 0)),
                    "unrealized_pnl": float(ibkr_pos.get("unrealizedPNL", 0)),
                    "realized_pnl": float(ibkr_pos.get("realizedPNL", 0)),
                    "last_updated": datetime.now(timezone.utc)
                }
                
                # Find existing position
                existing_pos = next(
                    (p for p in db_positions if p["symbol"] == ibkr_pos.get("symbol")),
                    None
                )
                
                if existing_pos:
                    await self.position_repo.update(
                        UUID(existing_pos["id"]),
                        position_data
                    )
                else:
                    await self.position_repo.create(position_data)
            
            # Close positions that no longer exist in IBKR
            for db_pos in db_positions:
                if db_pos["symbol"] not in ibkr_symbols and db_pos.get("status") == "OPEN":
                    await self.position_repo.update(
                        UUID(db_pos["id"]),
                        {
                            "status": "CLOSED",
                            "quantity": 0,
                            "last_updated": datetime.now(timezone.utc)
                        }
                    )
            
        except Exception as e:
            logger.error(f"Error syncing positions: {e}")
            raise

    async def _sync_market_data(self):
        """Sync market data from IBKR to Supabase."""
        try:
            # Get active instruments (get all types)
            instruments = await self.instrument_repo.search_instruments("", limit=100)
            
            for instrument in instruments:
                try:
                    # Create IBKR contract
                    contract = self._create_ibkr_contract(instrument)
                    
                    # Get market data
                    market_data = await self.ibkr.get_market_data(contract.symbol)
                    
                    if market_data:
                        # Store in Supabase
                        await self.market_repo.create({
                            "symbol": instrument["symbol"],
                            "price": float(market_data.get("price", 0)),
                            "bid": float(market_data.get("bid", 0)),
                            "ask": float(market_data.get("ask", 0)),
                            "volume": int(market_data.get("volume", 0)),
                            "timestamp": datetime.now(timezone.utc)
                        })
                        
                except Exception as e:
                    logger.warning(f"Failed to sync market data for {instrument['symbol']}: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"Error syncing market data: {e}")
            raise

    async def _execute_pending_signals(self):
        """Execute pending trading signals."""
        try:
            # Get pending signals
            pending_signals = await self.signal_repo.get_active_signals()
            
            for signal in pending_signals:
                try:
                    await self._execute_signal(signal)
                except Exception as e:
                    logger.error(f"Failed to execute signal {signal['id']}: {e}")
                    # Mark signal as failed
                    await self.signal_repo.update(
                        UUID(signal["id"]),
                        {
                            "status": "FAILED",
                            "error_message": str(e),
                            "last_updated": datetime.now(timezone.utc)
                        }
                    )
            
        except Exception as e:
            logger.error(f"Error executing signals: {e}")
            raise

    async def _execute_signal(self, signal: Dict[str, Any]):
        """Execute a single trading signal."""
        try:
            # Get instrument details
            instrument = await self.instrument_repo.get_by_symbol_and_class(
                signal["symbol"], 
                signal.get("asset_class", "STK")
            )
            if not instrument:
                raise ValueError(f"Instrument not found: {signal['symbol']}")
            
            # Create order data
            order_data = {
                "signal_id": signal["id"],
                "symbol": signal["symbol"],
                "side": signal["action"],  # BUY/SELL
                "order_type": signal.get("order_type", "MARKET"),
                "quantity": signal["quantity"],
                "price": signal.get("price"),
                "account": signal.get("account", ""),
                "status": "PENDING_SUBMIT"
            }
            
            # Create order in database
            db_order = await self.order_repo.create(order_data)
            if not db_order:
                raise Exception("Failed to create order in database")
            
            # Create IBKR contract and order
            # contract = self._create_ibkr_contract(instrument)  # Not used in current implementation

            # Place order using IBKRService
            order_result = await self.ibkr.create_order(
                order_type=order_data["order_type"],
                symbol=instrument["symbol"],
                action=order_data["side"],
                quantity=order_data["quantity"],
                account=order_data.get("account", ""),
                price=order_data.get("price")
            )
            
            if order_result.get("status") == "success":
                order_id = order_result.get("orderId")
                # Update with IBKR order ID
                await self.order_repo.update(
                    UUID(db_order["id"]),
                    {"ibkr_order_id": order_id}
                )
                
                # Mark signal as executed
                await self.signal_repo.update(
                    UUID(signal["id"]),
                    {
                        "status": "EXECUTED",
                        "order_id": db_order["id"],
                        "executed_at": datetime.now(timezone.utc)
                    }
                )
                
                logger.info(f"Executed signal {signal['id']} with order {order_id}")
            else:
                raise Exception(f"Order placement failed: {order_result.get('message')}")
            
        except Exception as e:
            logger.error(f"Error executing signal {signal['id']}: {e}")
            raise

    async def _update_performance_metrics(self):
        """Update performance metrics based on current positions and orders."""
        try:
            # Get account summary from IBKR
            account_summary = await self.ibkr.get_account_summary()
            
            if account_summary:
                # Calculate metrics
                metrics = {
                    "total_equity": float(account_summary.get("TotalCashValue", 0)),
                    "buying_power": float(account_summary.get("BuyingPower", 0)),
                    "unrealized_pnl": float(account_summary.get("UnrealizedPnL", 0)),
                    "realized_pnl": float(account_summary.get("RealizedPnL", 0)),
                    "timestamp": datetime.now(timezone.utc)
                }
                
                # Store metrics
                await self.performance_repo.create(metrics)
                
                logger.debug("Updated performance metrics")
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
            raise

    def _create_ibkr_contract(self, instrument: Dict[str, Any]) -> Contract:
        """Create IBKR contract from instrument data."""
        asset_type = instrument.get("asset_type", "STK").upper()
        
        if asset_type == "STK":
            return Stock(
                symbol=instrument["symbol"],
                exchange=instrument.get("exchange", "SMART"),
                currency=instrument.get("currency", "USD")
            )
        elif asset_type == "OPT":
            return Option(
                symbol=instrument["symbol"],
                lastTradeDateOrContractMonth=instrument.get("expiry") or "",
                strike=float(instrument.get("strike", 0)),
                right=instrument.get("right", "C"),
                exchange=instrument.get("exchange", "SMART"),
                currency=instrument.get("currency", "USD")
            )
        elif asset_type == "FUT":
            return Future(
                symbol=instrument["symbol"],
                lastTradeDateOrContractMonth=instrument.get("expiry") or "",
                exchange=instrument.get("exchange", ""),
                currency=instrument.get("currency", "USD")
            )
        elif asset_type == "CASH":
            return Forex(
                pair=instrument["symbol"],
                exchange=instrument.get("exchange", "IDEALPRO")
            )
        elif asset_type == "CRYPTO":
            return Crypto(
                symbol=instrument["symbol"],
                exchange=instrument.get("exchange", "PAXOS"),
                currency=instrument.get("currency", "USD")
            )
        else:
            # Default to stock
            return Stock(
                symbol=instrument["symbol"],
                exchange=instrument.get("exchange", "SMART"),
                currency=instrument.get("currency", "USD")
            )

    def _map_ibkr_status(self, ibkr_status: str) -> OrderStatus:
        """Map IBKR order status to our OrderStatus enum."""
        status_mapping = {
            "PendingSubmit": OrderStatus.PENDING_SUBMIT,
            "PendingCancel": OrderStatus.PENDING_CANCEL,
            "PreSubmitted": OrderStatus.PRE_SUBMITTED,
            "Submitted": OrderStatus.SUBMITTED,
            "ApiCancelled": OrderStatus.API_CANCELLED,
            "Cancelled": OrderStatus.CANCELLED,
            "Filled": OrderStatus.FILLED,
            "Inactive": OrderStatus.INACTIVE,
            "ApiPending": OrderStatus.API_PENDING
        }
        
        return status_mapping.get(ibkr_status, OrderStatus.UNKNOWN)

    async def stop_sync(self):
        """Stop the synchronization service."""
        logger.info("Stopping Supabase-IBKR synchronization service")
        await self.stop()  # Delegate to the new stop() method
