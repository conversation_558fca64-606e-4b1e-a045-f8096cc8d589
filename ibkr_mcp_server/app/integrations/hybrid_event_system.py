"""
Hybrid Event System

Combines Supabase Realtime with Google Cloud Pub/Sub for a robust, scalable
event-driven architecture. Provides failover capabilities, message persistence,
and cross-service communication for the IBKR trading platform.
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional, Callable, Union
from datetime import datetime, timezone
from uuid import uuid4
from dataclasses import dataclass, asdict
from enum import Enum
import os

from google.cloud import pubsub_v1
from google.auth import default
from concurrent.futures import ThreadPoolExecutor

from app.integrations.enhanced_event_stream_manager import EnhancedEventStreamManager, EventPriority

logger = logging.getLogger(__name__)


class MessageTransport(Enum):
    """Message transport types"""
    SUPABASE_REALTIME = "supabase_realtime"
    GOOGLE_PUBSUB = "google_pubsub"
    HYBRID = "hybrid"


@dataclass
class HybridMessage:
    """Structured message for hybrid event system"""
    id: str
    event_type: str
    payload: Dict[str, Any]
    timestamp: datetime
    priority: EventPriority
    source: str
    transport: MessageTransport
    retry_count: int = 0
    max_retries: int = 3
    metadata: Optional[Dict[str, Any]] = None


class PubSubProcessor:
    """Google Cloud Pub/Sub message processor"""
    
    def __init__(self, project_id: str, credentials_path: Optional[str] = None):
        """
        Initialize Pub/Sub processor.
        
        Args:
            project_id: Google Cloud project ID
            credentials_path: Path to service account credentials
        """
        self.project_id = project_id
        self.credentials_path = credentials_path
        
        # Initialize clients
        self.publisher: Optional[pubsub_v1.PublisherClient] = None
        self.subscriber: Optional[pubsub_v1.SubscriberClient] = None
        self.executor = ThreadPoolExecutor(max_workers=10)
        
        # Topic and subscription management
        self.topics: Dict[str, str] = {}
        self.subscriptions: Dict[str, str] = {}
        self.message_handlers: Dict[str, Callable] = {}
        
        # Statistics
        self.stats = {
            'messages_published': 0,
            'messages_received': 0,
            'publish_errors': 0,
            'receive_errors': 0
        }

    async def initialize(self):
        """Initialize Pub/Sub clients and create default topics"""
        try:
            # Set credentials if provided
            if self.credentials_path and os.path.exists(self.credentials_path):
                os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = self.credentials_path
            
            # Initialize clients
            self.publisher = pubsub_v1.PublisherClient()
            self.subscriber = pubsub_v1.SubscriberClient()
            
            # Create default topics
            await self._create_default_topics()
            
            logger.info("Pub/Sub processor initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Pub/Sub processor: {e}")
            raise

    async def _create_default_topics(self):
        """Create default topics for trading events"""
        default_topics = [
            'market-data-events',
            'trading-signals',
            'order-events',
            'position-updates',
            'risk-alerts',
            'system-events'
        ]
        
        for topic_name in default_topics:
            await self.create_topic(topic_name)

    async def create_topic(self, topic_name: str) -> bool:
        """Create a Pub/Sub topic"""
        try:
            if not self.publisher:
                logger.error("Publisher client not initialized. Call initialize() first.")
                return False
                
            topic_path = self.publisher.topic_path(self.project_id, topic_name)
            
            # Check if topic exists
            try:
                self.publisher.get_topic(request={"topic": topic_path})
                logger.info(f"Topic already exists: {topic_name}")
            except Exception:
                # Create topic
                self.publisher.create_topic(request={"name": topic_path})
                logger.info(f"Created topic: {topic_name}")
            
            self.topics[topic_name] = topic_path
            return True
            
        except Exception as e:
            logger.error(f"Failed to create topic {topic_name}: {e}")
            return False

    async def create_subscription(self, topic_name: str, subscription_name: str,
                                handler: Callable) -> bool:
        """Create a subscription for a topic"""
        try:
            if not self.subscriber:
                logger.error("Subscriber client not initialized. Call initialize() first.")
                return False
                
            if topic_name not in self.topics:
                logger.error(f"Topic not found: {topic_name}")
                return False
            
            topic_path = self.topics[topic_name]
            subscription_path = self.subscriber.subscription_path(
                self.project_id, subscription_name
            )
            
            # Check if subscription exists
            try:
                self.subscriber.get_subscription(request={"subscription": subscription_path})
                logger.info(f"Subscription already exists: {subscription_name}")
            except Exception:
                # Create subscription
                self.subscriber.create_subscription(
                    request={
                        "name": subscription_path,
                        "topic": topic_path,
                        "ack_deadline_seconds": 60
                    }
                )
                logger.info(f"Created subscription: {subscription_name}")
            
            self.subscriptions[subscription_name] = subscription_path
            self.message_handlers[subscription_name] = handler
            return True
            
        except Exception as e:
            logger.error(f"Failed to create subscription {subscription_name}: {e}")
            return False

    async def publish_message(self, topic_name: str, message: HybridMessage) -> bool:
        """Publish message to Pub/Sub topic"""
        try:
            if not self.publisher:
                logger.error("Publisher client not initialized. Call initialize() first.")
                return False
                
            if topic_name not in self.topics:
                logger.error(f"Topic not found: {topic_name}")
                return False
            
            topic_path = self.topics[topic_name]
            
            # Serialize message
            message_data = json.dumps(asdict(message), default=str).encode('utf-8')
            
            # Publish message
            future = self.publisher.publish(topic_path, message_data)
            message_id = future.result()
            
            self.stats['messages_published'] += 1
            logger.debug(f"Published message {message_id} to {topic_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to publish message to {topic_name}: {e}")
            self.stats['publish_errors'] += 1
            return False

    async def start_subscriber(self, subscription_name: str):
        """Start message subscriber for a subscription"""
        try:
            if not self.subscriber:
                logger.error("Subscriber client not initialized. Call initialize() first.")
                return
                
            if subscription_name not in self.subscriptions:
                logger.error(f"Subscription not found: {subscription_name}")
                return
            
            subscription_path = self.subscriptions[subscription_name]
            handler = self.message_handlers[subscription_name]
            
            # Create callback wrapper
            def callback(message):
                try:
                    # Decode message
                    message_data = json.loads(message.data.decode('utf-8'))
                    hybrid_message = HybridMessage(**message_data)
                    
                    # Process message asynchronously
                    asyncio.create_task(handler(hybrid_message))
                    
                    # Acknowledge message
                    message.ack()
                    self.stats['messages_received'] += 1
                    
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    message.nack()
                    self.stats['receive_errors'] += 1
            
            # Start subscriber
            streaming_pull_future = self.subscriber.subscribe(
                subscription_path, callback=callback
            )
            
            logger.info(f"Started subscriber for: {subscription_name}")
            
            # Keep subscriber running
            try:
                streaming_pull_future.result()
            except KeyboardInterrupt:
                streaming_pull_future.cancel()
                
        except Exception as e:
            logger.error(f"Failed to start subscriber {subscription_name}: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """Get Pub/Sub statistics"""
        return self.stats.copy()


class RealtimeProcessor:
    """Supabase Realtime message processor"""
    
    def __init__(self, event_stream_manager: EnhancedEventStreamManager):
        """
        Initialize Realtime processor.
        
        Args:
            event_stream_manager: Enhanced event stream manager instance
        """
        self.event_manager = event_stream_manager
        self.message_handlers: Dict[str, Callable] = {}
        
        # Statistics
        self.stats = {
            'messages_processed': 0,
            'processing_errors': 0,
            'subscriptions_active': 0
        }

    async def initialize(self):
        """Initialize Realtime processor"""
        try:
            # Register default handlers
            await self._register_default_handlers()
            logger.info("Realtime processor initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Realtime processor: {e}")
            raise

    async def _register_default_handlers(self):
        """Register default event handlers"""
        # Market data handler
        async def handle_market_data(event):
            await self._process_realtime_message('market_data', event)
        
        # Trading signals handler
        async def handle_trading_signals(event):
            await self._process_realtime_message('trading_signals', event)
        
        # Orders handler
        async def handle_orders(event):
            await self._process_realtime_message('orders', event)
        
        # Register handlers
        self.event_manager.register_handler('realtime_insert', handle_market_data)
        self.event_manager.register_handler('realtime_update', handle_trading_signals)
        self.event_manager.register_handler('realtime_delete', handle_orders)

    async def _process_realtime_message(self, event_type: str, event_data: Dict[str, Any]):
        """Process Realtime message"""
        try:
            # Create hybrid message
            hybrid_message = HybridMessage(
                id=str(uuid4()),
                event_type=event_type,
                payload=event_data,
                timestamp=datetime.now(timezone.utc),
                priority=EventPriority.MEDIUM,
                source='supabase_realtime',
                transport=MessageTransport.SUPABASE_REALTIME
            )
            
            # Process message
            if event_type in self.message_handlers:
                await self.message_handlers[event_type](hybrid_message)
            
            self.stats['messages_processed'] += 1
            
        except Exception as e:
            logger.error(f"Error processing realtime message: {e}")
            self.stats['processing_errors'] += 1

    def register_handler(self, event_type: str, handler: Callable):
        """Register message handler"""
        self.message_handlers[event_type] = handler
        logger.info(f"Registered handler for event type: {event_type}")

    def get_stats(self) -> Dict[str, Any]:
        """Get Realtime processor statistics"""
        return self.stats.copy()


class HybridEventSystem:
    """
    Hybrid event system combining Supabase Realtime and Google Cloud Pub/Sub.
    
    Features:
    - Dual transport support (Realtime + Pub/Sub)
    - Automatic failover between transports
    - Message persistence and replay
    - Priority-based routing
    - Cross-service communication
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize hybrid event system.
        
        Args:
            config: Configuration dictionary with transport settings
        """
        self.config = config
        
        # Initialize components
        self.event_manager = EnhancedEventStreamManager(config.get('realtime', {}))
        self.pubsub_processor = PubSubProcessor(
            project_id=config.get('pubsub', {}).get('project_id', 'gen-lang-client-**********'),
            credentials_path=config.get('pubsub', {}).get('credentials_path')
        )
        self.realtime_processor = RealtimeProcessor(self.event_manager)
        
        # System state
        self._running = False
        self._transport_health = {
            MessageTransport.SUPABASE_REALTIME: True,
            MessageTransport.GOOGLE_PUBSUB: True
        }
        
        # Message routing
        self._routing_rules: Dict[str, MessageTransport] = {}
        self._failover_enabled = config.get('failover_enabled', True)
        
        # Statistics
        self._system_stats = {
            'total_messages': 0,
            'transport_usage': {transport.value: 0 for transport in MessageTransport},
            'failover_events': 0,
            'errors': 0
        }

    async def start(self):
        """Start hybrid event system"""
        if self._running:
            logger.warning("Hybrid event system already running")
            return
        
        logger.info("Starting Hybrid Event System")
        self._running = True
        
        try:
            # Initialize components
            await self.event_manager.start()
            await self.pubsub_processor.initialize()
            await self.realtime_processor.initialize()
            
            # Setup default routing rules
            self._setup_default_routing()
            
            # Start monitoring tasks
            asyncio.create_task(self._monitor_transport_health())
            
            logger.info("Hybrid Event System started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start Hybrid Event System: {e}")
            self._running = False
            raise

    async def stop(self):
        """Stop hybrid event system"""
        if not self._running:
            return
        
        logger.info("Stopping Hybrid Event System")
        self._running = False
        
        # Stop components
        await self.event_manager.stop()
        
        logger.info("Hybrid Event System stopped")

    async def publish_event(self, event_type: str, payload: Dict[str, Any],
                          priority: EventPriority = EventPriority.MEDIUM,
                          transport: Optional[MessageTransport] = None) -> bool:
        """
        Publish event through hybrid system.
        
        Args:
            event_type: Type of event
            payload: Event payload
            priority: Event priority
            transport: Preferred transport (auto-selected if None)
            
        Returns:
            True if published successfully
        """
        try:
            # Create hybrid message
            message = HybridMessage(
                id=str(uuid4()),
                event_type=event_type,
                payload=payload,
                timestamp=datetime.now(timezone.utc),
                priority=priority,
                source='hybrid_system',
                transport=transport or self._select_transport(event_type)
            )
            
            # Route message
            success = await self._route_message(message)
            
            if success:
                self._system_stats['total_messages'] += 1
                self._system_stats['transport_usage'][message.transport.value] += 1
            else:
                self._system_stats['errors'] += 1
            
            return success
            
        except Exception as e:
            logger.error(f"Error publishing event {event_type}: {e}")
            self._system_stats['errors'] += 1
            return False

    def set_routing_rule(self, event_type: str, transport: MessageTransport):
        """Set routing rule for specific event type"""
        self._routing_rules[event_type] = transport
        logger.info(f"Set routing rule: {event_type} -> {transport.value}")

    def get_system_statistics(self) -> Dict[str, Any]:
        """Get comprehensive system statistics"""
        return {
            'system_stats': self._system_stats,
            'transport_health': self._transport_health,
            'event_manager_stats': self.event_manager.get_realtime_statistics(),
            'pubsub_stats': self.pubsub_processor.get_stats(),
            'realtime_stats': self.realtime_processor.get_stats(),
            'routing_rules': {k: v.value for k, v in self._routing_rules.items()}
        }

    def _setup_default_routing(self):
        """Setup default routing rules"""
        # High-frequency events to Realtime
        self.set_routing_rule('market_data', MessageTransport.SUPABASE_REALTIME)
        self.set_routing_rule('price_update', MessageTransport.SUPABASE_REALTIME)
        
        # Critical events to Pub/Sub for persistence
        self.set_routing_rule('risk_alert', MessageTransport.GOOGLE_PUBSUB)
        self.set_routing_rule('order_execution', MessageTransport.GOOGLE_PUBSUB)
        
        # System events to both (hybrid)
        self.set_routing_rule('system_event', MessageTransport.HYBRID)

    def _select_transport(self, event_type: str) -> MessageTransport:
        """Select appropriate transport for event type"""
        # Check routing rules
        if event_type in self._routing_rules:
            preferred = self._routing_rules[event_type]
            
            # Check if preferred transport is healthy
            if self._transport_health[preferred]:
                return preferred
            elif self._failover_enabled:
                # Failover to healthy transport
                for transport, healthy in self._transport_health.items():
                    if healthy and transport != preferred:
                        self._system_stats['failover_events'] += 1
                        logger.warning(f"Failover: {event_type} from {preferred.value} to {transport.value}")
                        return transport
        
        # Default to Realtime if healthy, otherwise Pub/Sub
        if self._transport_health[MessageTransport.SUPABASE_REALTIME]:
            return MessageTransport.SUPABASE_REALTIME
        else:
            return MessageTransport.GOOGLE_PUBSUB

    async def _route_message(self, message: HybridMessage) -> bool:
        """Route message to appropriate transport"""
        try:
            if message.transport == MessageTransport.SUPABASE_REALTIME:
                return await self._send_via_realtime(message)
            elif message.transport == MessageTransport.GOOGLE_PUBSUB:
                return await self._send_via_pubsub(message)
            elif message.transport == MessageTransport.HYBRID:
                # Send via both transports
                realtime_success = await self._send_via_realtime(message)
                pubsub_success = await self._send_via_pubsub(message)
                return realtime_success or pubsub_success
            
            return False
            
        except Exception as e:
            logger.error(f"Error routing message {message.id}: {e}")
            return False

    async def _send_via_realtime(self, message: HybridMessage) -> bool:
        """Send message via Supabase Realtime"""
        try:
            await self.event_manager.emit_priority_event(
                message.event_type,
                message.payload,
                message.priority
            )
            return True
        except Exception as e:
            logger.error(f"Failed to send via Realtime: {e}")
            return False

    async def _send_via_pubsub(self, message: HybridMessage) -> bool:
        """Send message via Google Cloud Pub/Sub"""
        try:
            # Determine topic based on event type
            topic_name = self._get_topic_for_event(message.event_type)
            return await self.pubsub_processor.publish_message(topic_name, message)
        except Exception as e:
            logger.error(f"Failed to send via Pub/Sub: {e}")
            return False

    def _get_topic_for_event(self, event_type: str) -> str:
        """Get Pub/Sub topic name for event type"""
        topic_mapping = {
            'market_data': 'market-data-events',
            'trading_signal': 'trading-signals',
            'order_execution': 'order-events',
            'position_update': 'position-updates',
            'risk_alert': 'risk-alerts'
        }
        return topic_mapping.get(event_type, 'system-events')

    async def _monitor_transport_health(self):
        """Monitor health of transport systems"""
        while self._running:
            try:
                # Check Realtime health
                realtime_stats = self.event_manager.get_realtime_statistics()
                self._transport_health[MessageTransport.SUPABASE_REALTIME] = \
                    realtime_stats.get('connection_healthy', False)
                
                # Check Pub/Sub health (simplified)
                pubsub_stats = self.pubsub_processor.get_stats()
                self._transport_health[MessageTransport.GOOGLE_PUBSUB] = \
                    pubsub_stats.get('publish_errors', 0) < 10  # Threshold
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error monitoring transport health: {e}")
                await asyncio.sleep(30)
