"""
Webhook Processor

Handles processing of database webhooks for order execution and risk alerts.
Integrates with external notification systems and Edge Functions.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timezone
from dataclasses import dataclass
from enum import Enum
import aiohttp
from urllib.parse import urljoin

from app.database.connection import supabase_conn
from app.integrations.edge_functions_client import EdgeFunctionsClient

logger = logging.getLogger(__name__)

class WebhookEventType(Enum):
    """Types of webhook events"""
    ORDER_EXECUTION = "order_execution"
    RISK_ALERT = "risk_alert"
    POSITION_UPDATE = "position_update"
    MARKET_DATA_UPDATE = "market_data_update"

class WebhookStatus(Enum):
    """Webhook processing status"""
    PENDING = "pending"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"
    FAILED_PERMANENT = "failed_permanent"

@dataclass
class WebhookEvent:
    """Webhook event data structure"""
    id: str
    event_type: str
    payload: Dict[str, Any]
    status: str
    priority: int
    retry_count: int
    max_retries: int
    created_at: str
    error_message: Optional[str] = None

@dataclass
class NotificationConfig:
    """Configuration for external notifications"""
    webhook_url: Optional[str] = None
    slack_webhook: Optional[str] = None
    email_endpoint: Optional[str] = None
    discord_webhook: Optional[str] = None
    telegram_bot_token: Optional[str] = None
    telegram_chat_id: Optional[str] = None

class WebhookProcessor:
    """
    Processes database webhooks and sends notifications to external systems.
    
    Features:
    - Batch processing of webhook events
    - Retry logic with exponential backoff
    - Multiple notification channels
    - Integration with Edge Functions
    - Real-time event streaming
    """
    
    def __init__(self, 
                 notification_config: Optional[NotificationConfig] = None,
                 edge_functions_client: Optional[EdgeFunctionsClient] = None,
                 batch_size: int = 10,
                 max_concurrent: int = 5):
        """
        Initialize webhook processor.
        
        Args:
            notification_config: Configuration for external notifications
            edge_functions_client: Client for Edge Functions integration
            batch_size: Number of events to process in each batch
            max_concurrent: Maximum concurrent webhook processing
        """
        self.notification_config = notification_config or NotificationConfig()
        self.edge_functions_client = edge_functions_client
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        self.session: Optional[aiohttp.ClientSession] = None
        self.is_running = False
        
        # Event handlers
        self.event_handlers: Dict[str, List[Callable]] = {
            WebhookEventType.ORDER_EXECUTION.value: [],
            WebhookEventType.RISK_ALERT.value: [],
            WebhookEventType.POSITION_UPDATE.value: [],
            WebhookEventType.MARKET_DATA_UPDATE.value: []
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.stop()
        await self.close()
    
    async def _ensure_session(self):
        """Ensure aiohttp session is created"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
    
    async def close(self):
        """Close the aiohttp session"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    def register_handler(self, event_type: str, handler: Callable):
        """
        Register a custom event handler.
        
        Args:
            event_type: Type of event to handle
            handler: Async function to handle the event
        """
        if event_type in self.event_handlers:
            self.event_handlers[event_type].append(handler)
        else:
            logger.warning(f"Unknown event type: {event_type}")
    
    async def start(self, poll_interval: int = 5):
        """
        Start the webhook processor.
        
        Args:
            poll_interval: Interval between polling for new events (seconds)
        """
        self.is_running = True
        logger.info("Starting webhook processor")
        
        while self.is_running:
            try:
                await self.process_pending_webhooks()
                await asyncio.sleep(poll_interval)
            except Exception as e:
                logger.error(f"Error in webhook processor loop: {e}")
                await asyncio.sleep(poll_interval)
    
    async def stop(self):
        """Stop the webhook processor"""
        self.is_running = False
        logger.info("Stopping webhook processor")
    
    async def process_pending_webhooks(self) -> Dict[str, int]:
        """
        Process pending webhook events.
        
        Returns:
            Dictionary with processing statistics
        """
        # Get pending webhooks
        pending_events = await self._get_pending_webhooks()
        
        if not pending_events:
            return {"processed": 0, "failed": 0, "pending": 0}
        
        logger.info(f"Processing {len(pending_events)} webhook events")
        
        # Process events in batches with concurrency control
        semaphore = asyncio.Semaphore(self.max_concurrent)
        tasks = []
        
        for event in pending_events:
            task = asyncio.create_task(self._process_webhook_event(event, semaphore))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count results
        processed = sum(1 for r in results if r is True)
        failed = sum(1 for r in results if r is False or isinstance(r, Exception))
        
        # Get remaining pending count
        remaining_pending = await self._get_pending_count()
        
        logger.info(f"Webhook processing complete: {processed} processed, {failed} failed, {remaining_pending} pending")
        
        return {
            "processed": processed,
            "failed": failed,
            "pending": remaining_pending
        }
    
    async def _get_pending_webhooks(self) -> List[WebhookEvent]:
        """Get pending webhook events from database"""
        try:
            result = await supabase_conn.execute_rpc('get_pending_webhooks', {
                'batch_size': self.batch_size
            })
            
            events = []
            for row in result or []:
                events.append(WebhookEvent(
                    id=row['id'],
                    event_type=row['event_type'],
                    payload=row['payload'],
                    status=row['status'],
                    priority=row['priority'],
                    retry_count=row['retry_count'],
                    max_retries=row['max_retries'],
                    created_at=row['created_at'],
                    error_message=row.get('error_message')
                ))
            
            return events
            
        except Exception as e:
            logger.error(f"Failed to get pending webhooks: {e}")
            return []
    
    async def _get_pending_count(self) -> int:
        """Get count of remaining pending webhooks"""
        try:
            result = supabase_conn.client.table('webhook_events')\
                .select('id')\
                .in_('status', ['pending', 'failed'])\
                .execute()
            return len(result.data) if result.data else 0
        except Exception as e:
            logger.error(f"Failed to get pending count: {e}")
            return 0
    
    async def _process_webhook_event(self, event: WebhookEvent, semaphore: asyncio.Semaphore) -> bool:
        """
        Process a single webhook event.
        
        Args:
            event: Webhook event to process
            semaphore: Concurrency control semaphore
            
        Returns:
            True if successful, False otherwise
        """
        async with semaphore:
            try:
                # Mark as processing
                await self._update_webhook_status(event.id, WebhookStatus.PROCESSING.value)
                
                # Process based on event type
                success = await self._handle_event_by_type(event)
                
                if success:
                    # Mark as processed
                    await self._update_webhook_status(event.id, WebhookStatus.PROCESSED.value)
                    logger.debug(f"Successfully processed webhook {event.id}")
                    return True
                else:
                    # Handle failure
                    await self._handle_webhook_failure(event)
                    return False
                    
            except Exception as e:
                logger.error(f"Error processing webhook {event.id}: {e}")
                await self._handle_webhook_failure(event, str(e))
                return False
    
    async def _handle_event_by_type(self, event: WebhookEvent) -> bool:
        """Handle event based on its type"""
        try:
            # Call registered handlers
            handlers = self.event_handlers.get(event.event_type, [])
            for handler in handlers:
                await handler(event)
            
            # Built-in handlers
            if event.event_type == WebhookEventType.ORDER_EXECUTION.value:
                return await self._handle_order_execution(event)
            elif event.event_type == WebhookEventType.RISK_ALERT.value:
                return await self._handle_risk_alert(event)
            elif event.event_type == WebhookEventType.POSITION_UPDATE.value:
                return await self._handle_position_update(event)
            elif event.event_type == WebhookEventType.MARKET_DATA_UPDATE.value:
                return await self._handle_market_data_update(event)
            else:
                logger.warning(f"Unknown event type: {event.event_type}")
                return True  # Mark as processed to avoid retry
                
        except Exception as e:
            logger.error(f"Error handling event type {event.event_type}: {e}")
            return False
    
    async def _handle_order_execution(self, event: WebhookEvent) -> bool:
        """Handle order execution webhook"""
        payload = event.payload
        
        # Send notifications
        notifications_sent = await self._send_notifications(
            title="Order Executed",
            message=f"Order {payload.get('order_id')} executed: {payload.get('side')} {payload.get('quantity')} {payload.get('pair_id')} at {payload.get('average_fill_price')}",
            payload=payload,
            priority=event.priority
        )
        
        # Trigger Edge Function for position management if needed
        if self.edge_functions_client:
            try:
                await self.edge_functions_client.manage_positions()
            except Exception as e:
                logger.error(f"Failed to trigger position management: {e}")
        
        return notifications_sent
    
    async def _handle_risk_alert(self, event: WebhookEvent) -> bool:
        """Handle risk alert webhook"""
        payload = event.payload
        severity = payload.get('severity', 'medium')
        
        # Send high-priority notifications for risk alerts
        notifications_sent = await self._send_notifications(
            title=f"Risk Alert - {severity.upper()}",
            message=f"Risk threshold exceeded: {payload.get('alert_type')}",
            payload=payload,
            priority=1,  # High priority
            urgent=severity in ['high', 'critical']
        )
        
        # Auto-execute risk management if critical
        if severity == 'critical' and self.edge_functions_client:
            try:
                # Validate all open positions
                await self.edge_functions_client.manage_positions()
                logger.info("Triggered emergency position management due to critical risk alert")
            except Exception as e:
                logger.error(f"Failed to trigger emergency position management: {e}")
        
        return notifications_sent
    
    async def _handle_position_update(self, event: WebhookEvent) -> bool:
        """Handle position update webhook"""
        # Send notifications for significant position changes
        return await self._send_notifications(
            title="Position Update",
            message="Position updated",
            payload=event.payload,
            priority=event.priority
        )
    
    async def _handle_market_data_update(self, event: WebhookEvent) -> bool:
        """Handle market data update webhook"""
        # Trigger technical analysis updates if needed
        if self.edge_functions_client:
            try:
                payload = event.payload
                pair_id = payload.get('pair_id')
                if pair_id and isinstance(pair_id, str):
                    pair_ids = [pair_id]
                    await self.edge_functions_client.calculate_indicators(pair_ids)
            except Exception as e:
                logger.error(f"Failed to trigger indicator calculation: {e}")
        
        return True
    
    async def _send_notifications(self, title: str, message: str, payload: Dict[str, Any], 
                                 priority: int = 5, urgent: bool = False) -> bool:
        """Send notifications to configured channels"""
        await self._ensure_session()
        success_count = 0
        total_channels = 0
        
        # Slack notification
        if self.notification_config.slack_webhook:
            total_channels += 1
            if await self._send_slack_notification(title, message, payload, urgent):
                success_count += 1
        
        # Discord notification
        if self.notification_config.discord_webhook:
            total_channels += 1
            if await self._send_discord_notification(title, message, payload, urgent):
                success_count += 1
        
        # Generic webhook
        if self.notification_config.webhook_url:
            total_channels += 1
            if await self._send_generic_webhook(title, message, payload, priority):
                success_count += 1
        
        # Telegram notification
        if self.notification_config.telegram_bot_token and self.notification_config.telegram_chat_id:
            total_channels += 1
            if await self._send_telegram_notification(title, message, payload, urgent):
                success_count += 1
        
        # Consider successful if at least one channel succeeded or no channels configured
        return success_count > 0 or total_channels == 0
    
    async def _send_slack_notification(self, title: str, message: str, payload: Dict[str, Any], urgent: bool) -> bool:
        """Send Slack notification"""
        try:
            if self.session is None:
                logger.error("Session not initialized for Slack notification")
                return False

            slack_payload = {
                "text": f"{'🚨 ' if urgent else ''}{title}",
                "attachments": [
                    {
                        "color": "danger" if urgent else "good",
                        "fields": [
                            {"title": "Message", "value": message, "short": False},
                            {"title": "Timestamp", "value": datetime.now(timezone.utc).isoformat(), "short": True}
                        ]
                    }
                ]
            }

            if not self.notification_config.slack_webhook:
                logger.error("Slack webhook URL not configured")
                return False

            async with self.session.post(self.notification_config.slack_webhook, json=slack_payload) as response:
                return response.status == 200
                
        except Exception as e:
            logger.error(f"Failed to send Slack notification: {e}")
            return False
    
    async def _send_discord_notification(self, title: str, message: str, payload: Dict[str, Any], urgent: bool) -> bool:
        """Send Discord notification"""
        try:
            if self.session is None:
                logger.error("Session not initialized for Discord notification")
                return False

            if not self.notification_config.discord_webhook:
                logger.error("Discord webhook URL not configured")
                return False

            discord_payload = {
                "content": f"{'🚨 ' if urgent else ''}{title}: {message}",
                "embeds": [
                    {
                        "title": title,
                        "description": message,
                        "color": 15158332 if urgent else 3066993,  # Red if urgent, green otherwise
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
                ]
            }

            async with self.session.post(self.notification_config.discord_webhook, json=discord_payload) as response:
                return response.status in [200, 204]
                
        except Exception as e:
            logger.error(f"Failed to send Discord notification: {e}")
            return False
    
    async def _send_generic_webhook(self, title: str, message: str, payload: Dict[str, Any], priority: int) -> bool:
        """Send generic webhook notification"""
        try:
            if self.session is None:
                logger.error("Session not initialized for generic webhook")
                return False

            if not self.notification_config.webhook_url:
                logger.error("Generic webhook URL not configured")
                return False

            webhook_payload = {
                "title": title,
                "message": message,
                "priority": priority,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "data": payload
            }

            async with self.session.post(self.notification_config.webhook_url, json=webhook_payload) as response:
                return response.status == 200
                
        except Exception as e:
            logger.error(f"Failed to send generic webhook: {e}")
            return False
    
    async def _send_telegram_notification(self, title: str, message: str, payload: Dict[str, Any], urgent: bool) -> bool:
        """Send Telegram notification"""
        try:
            if self.session is None:
                logger.error("Session not initialized for Telegram notification")
                return False

            if not self.notification_config.telegram_bot_token:
                logger.error("Telegram bot token not configured")
                return False

            telegram_message = f"{'🚨 ' if urgent else ''}{title}\n\n{message}"
            telegram_url = f"https://api.telegram.org/bot{self.notification_config.telegram_bot_token}/sendMessage"

            telegram_payload = {
                "chat_id": self.notification_config.telegram_chat_id,
                "text": telegram_message,
                "parse_mode": "Markdown"
            }

            async with self.session.post(telegram_url, json=telegram_payload) as response:
                return response.status == 200
                
        except Exception as e:
            logger.error(f"Failed to send Telegram notification: {e}")
            return False
    
    async def _update_webhook_status(self, webhook_id: str, status: str, error_message: Optional[str] = None):
        """Update webhook status in database"""
        try:
            updates = {"status": status, "updated_at": datetime.now(timezone.utc).isoformat()}
            if error_message:
                updates["error_message"] = error_message
            if status == WebhookStatus.PROCESSED.value:
                updates["processed_at"] = datetime.now(timezone.utc).isoformat()
            
            supabase_conn.client.table('webhook_events')\
                .update(updates)\
                .eq('id', webhook_id)\
                .execute()
                
        except Exception as e:
            logger.error(f"Failed to update webhook status: {e}")
    
    async def _handle_webhook_failure(self, event: WebhookEvent, error_message: Optional[str] = None):
        """Handle webhook processing failure"""
        new_retry_count = event.retry_count + 1
        
        if new_retry_count >= event.max_retries:
            # Mark as permanently failed
            await self._update_webhook_status(
                event.id, 
                WebhookStatus.FAILED_PERMANENT.value, 
                error_message or "Max retries exceeded"
            )
        else:
            # Schedule for retry with exponential backoff
            next_retry = datetime.now(timezone.utc)
            backoff_minutes = 2 ** new_retry_count  # Exponential backoff
            
            try:
                supabase_conn.client.table('webhook_events')\
                    .update({
                        "status": WebhookStatus.FAILED.value,
                        "retry_count": new_retry_count,
                        "next_retry_at": (next_retry.timestamp() + backoff_minutes * 60),
                        "error_message": error_message,
                        "updated_at": next_retry.isoformat()
                    })\
                    .eq('id', event.id)\
                    .execute()
            except Exception as e:
                logger.error(f"Failed to schedule webhook retry: {e}")
    
    async def get_webhook_stats(self, hours_back: int = 24) -> Dict[str, Any]:
        """Get webhook processing statistics"""
        try:
            result = await supabase_conn.execute_rpc('get_webhook_stats', {
                'hours_back': hours_back
            })
            return result or {}
        except Exception as e:
            logger.error(f"Failed to get webhook stats: {e}")
            return {}
    
    async def cleanup_old_webhooks(self, days_to_keep: int = 30) -> int:
        """Clean up old processed webhook events"""
        try:
            result = await supabase_conn.execute_rpc('cleanup_webhook_events', {
                'days_to_keep': days_to_keep
            })
            return result or 0
        except Exception as e:
            logger.error(f"Failed to cleanup webhooks: {e}")
            return 0
