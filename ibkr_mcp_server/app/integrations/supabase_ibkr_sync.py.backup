"""
Supabase-IBKR Synchronization Service

Handles real-time synchronization between Interactive Brokers and Supabase,
ensuring data consistency and enabling event-driven trading strategies.
"""

import asyncio
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from uuid import UUID
import logging
from decimal import Decimal

from ..database.repositories import (
    MarketDataRepo            # Create contract and order
            contract = self._create_ibkr_contract(instrument)
            ibkr_order = self._create_ibkr_order(order_data)
            
            # Place order using IBKRService create_order method
            order_result = await self.ibkr.create_order(
                order_type=order_data["order_type"],
                symbol=instrument["symbol"],
                action=order_data["side"],
                quantity=order_data["quantity"],
                account=order_data.get("account", ""),
                price=order_data.get("price")
            )
            
            if order_result.get("status") == "success":
                order_id = order_result.get("orderId")
                # Update with IBKR order ID
                await self.order_repo.update(
                    UUID(db_order["id"]),
                    {"ibkr_order_id": order_id}
                )
                logger.info(f"Executed signal {signal['id']} with order {order_id}")
            else:
                logger.error(f"Failed to place order for signal {signal['id']}: {order_result.get('message')}")
            Repository,
    PositionRepository,
    TradingSignalRepository,
    PerformanceMetricsRepository
)
from ..database.repositories.multi_asset_repository import (
    InstrumentRepository,
    StockDetailsRepository,
    OptionDetailsRepository,
    FutureDetailsRepository,
    CryptoDetailsRepository
)
from ..services.ibkr_service import IBKRService
from ..models.order_models import OrderStatus

logger = logging.getLogger(__name__)


class SupabaseIBKRSync:
    """
    Synchronizes IBKR data with Supabase in real-time.
    
    This service maintains bi-directional synchronization between
    IBKR's trading platform and Supabase database, handling:
    - Market data streaming
    - Order status updates
    - Position tracking
    - Performance metrics
    - Instrument details
    """
    
    def __init__(self, ibkr_service: IBKRService):
        """
        Initialize sync service.
        
        Args:
            ibkr_service: IBKR service instance
        """
        self.ibkr = ibkr_service
        
        # Initialize repositories
        self.market_repo = MarketDataRepository()
        self.order_repo = OrderRepository()
        self.position_repo = PositionRepository()
        self.signal_repo = TradingSignalRepository()
        self.performance_repo = PerformanceMetricsRepository()
        self.instrument_repo = InstrumentRepository()
        
        # Asset-specific repositories
        self.stock_repo = StockDetailsRepository()
        self.option_repo = OptionDetailsRepository()
        self.future_repo = FutureDetailsRepository()
        self.crypto_repo = CryptoDetailsRepository()
        
        # Sync state
        self._running = False
        self._tasks: List[asyncio.Task] = []
        self._subscribed_instruments: Set[UUID] = set()
        self._sync_intervals = {
            'market_data': 0.1,      # 100ms for tick data
            'orders': 0.5,           # 500ms for order updates
            'positions': 2.0,        # 2s for position updates
            'performance': 60.0,     # 1 minute for performance metrics
            'details': 300.0         # 5 minutes for instrument details
        }
    
    async def start(self, instrument_ids: Optional[List[UUID]] = None):
        """
        Start synchronization services.
        
        Args:
            instrument_ids: Optional list of instruments to sync
        """
        if self._running:
            logger.warning("Sync service already running")
            return
        
        self._running = True
        
        # Set initial instruments if provided
        if instrument_ids:
            self._subscribed_instruments.update(instrument_ids)
        
        # Start sync tasks
        self._tasks = [
            asyncio.create_task(self._sync_market_data()),
            asyncio.create_task(self._sync_orders()),
            asyncio.create_task(self._sync_positions()),
            asyncio.create_task(self._sync_performance()),
            asyncio.create_task(self._sync_instrument_details()),
            asyncio.create_task(self._process_signals())
        ]
        
        logger.info(f"Started Supabase-IBKR sync with {len(self._subscribed_instruments)} instruments")
    
    async def stop(self):
        """Stop synchronization services"""
        if not self._running:
            return
        
        self._running = False
        
        # Cancel all tasks
        for task in self._tasks:
            task.cancel()
        
        # Wait for tasks to complete
        if self._tasks:
            await asyncio.gather(*self._tasks, return_exceptions=True)
            self._tasks.clear()
        
        logger.info("Stopped Supabase-IBKR sync")
    
    async def add_instruments(self, instrument_ids: List[UUID]):
        """Add instruments to sync"""
        self._subscribed_instruments.update(instrument_ids)
        logger.info(f"Added {len(instrument_ids)} instruments to sync")
    
    async def remove_instruments(self, instrument_ids: List[UUID]):
        """Remove instruments from sync"""
        for inst_id in instrument_ids:
            self._subscribed_instruments.discard(inst_id)
        logger.info(f"Removed {len(instrument_ids)} instruments from sync")
    
    async def _sync_market_data(self):
        """Continuously sync market data to Supabase"""
        while self._running:
            try:
                if not self._subscribed_instruments:
                    await asyncio.sleep(1)
                    continue
                
                # Get market data for all subscribed instruments
                ticks = []
                
                for inst_id in self._subscribed_instruments:
                    # Get instrument details
                    instrument = await self.instrument_repo.get_by_id(inst_id)
                    if not instrument:
                        continue
                    
                    # Get market data from IBKR
                    ibkr_data = await self._get_ibkr_market_data(instrument)
                    if ibkr_data:
                        ticks.append({
                            "instrument_id": inst_id,
                            "bid": ibkr_data.get("bid", 0),
                            "ask": ibkr_data.get("ask", 0),
                            "last_price": ibkr_data.get("last", 0),
                            "bid_volume": ibkr_data.get("bid_size"),
                            "ask_volume": ibkr_data.get("ask_size"),
                            "volume": ibkr_data.get("volume"),
                            "timestamp": datetime.utcnow().isoformat()
                        })
                
                # Bulk insert market data
                if ticks:
                    await self.market_repo.bulk_insert_ticks(ticks)
                
                await asyncio.sleep(self._sync_intervals['market_data'])
                
            except Exception as e:
                logger.error(f"Market data sync error: {e}")
                await asyncio.sleep(1)
    
    async def _sync_orders(self):
        """Sync order status updates"""
        while self._running:
            try:
                # Get all open orders from IBKR - use OrderManagementService if available, or direct ib_async method
                if hasattr(self.ibkr, 'order_management_service_delegate') and self.ibkr.order_management_service_delegate:
                    order_result = await self.ibkr.order_management_service_delegate.get_active_orders()
                    ibkr_orders = order_result.get('orders', []) if order_result.get('status') == 'success' else []
                else:
                    # Direct ib_async access
                    ibkr_orders = await self.ibkr.ib.reqAllOpenOrdersAsync()
                
                # Get pending orders from database
                db_orders = await self.order_repo.get_pending_orders()
                
                # Update order statuses
                for db_order in db_orders:
                    ibkr_order_id = db_order.get("ibkr_order_id")
                    if not ibkr_order_id:
                        continue
                    
                    # Find matching IBKR order
                    ibkr_order = next(
                        (o for o in ibkr_orders if o.orderId == ibkr_order_id),
                        None
                    )
                    
                    if ibkr_order:
                        # Update status if changed
                        new_status = self._map_ibkr_status(ibkr_order.status)
                        if new_status != db_order["status"]:
                            await self.order_repo.update_order_status(
                                UUID(db_order["id"]),
                                new_status,
                                filled_quantity=ibkr_order.filled,
                                average_fill_price=ibkr_order.avgFillPrice,
                                commission=ibkr_order.commission
                            )
                    else:
                        # Order not found in IBKR, might be cancelled
                        if db_order["status"] == "PENDING":
                            await self.order_repo.update_order_status(
                                UUID(db_order["id"]),
                                "CANCELLED"
                            )
                
                await asyncio.sleep(self._sync_intervals['orders'])
                
            except Exception as e:
                logger.error(f"Order sync error: {e}")
                await asyncio.sleep(1)
    
    async def _sync_positions(self):
        """Sync position updates"""
        while self._running:
            try:
                # Get positions from IBKR using get_portfolio method
                portfolio_result = await self.ibkr.get_portfolio()
                ibkr_positions = portfolio_result if isinstance(portfolio_result, list) else []
                
                # Get open positions from database
                db_positions = await self.position_repo.get_open_positions()
                
                # Map IBKR positions to instruments
                for ibkr_pos in ibkr_positions:
                    # Find or create instrument
                    instrument = await self._ensure_instrument_exists(ibkr_pos)
                    if not instrument:
                        continue
                    
                    # Find matching DB position
                    db_pos = next(
                        (p for p in db_positions 
                         if p.get("instrument_id") == instrument["id"]),
                        None
                    )
                    
                    if db_pos:
                        # Update existing position
                        await self.position_repo.update_position_price(
                            UUID(db_pos["id"]),
                            float(ibkr_pos.marketPrice)
                        )
                    else:
                        # Create new position
                        await self.position_repo.open_position({
                            "instrument_id": instrument["id"],
                            "side": "LONG" if ibkr_pos.position > 0 else "SHORT",
                            "quantity": abs(float(ibkr_pos.position)),
                            "entry_price": float(ibkr_pos.averageCost),
                            "current_price": float(ibkr_pos.marketPrice)
                        })
                
                # Check for closed positions
                for db_pos in db_positions:
                    # Check if position still exists in IBKR
                    instrument = await self.instrument_repo.get_by_id(
                        UUID(db_pos["instrument_id"])
                    )
                    
                    if instrument:
                        ibkr_exists = any(
                            p.contract.symbol == instrument["symbol"] 
                            for p in ibkr_positions
                        )
                        
                        if not ibkr_exists:
                            # Position closed
                            await self.position_repo.close_position(
                                UUID(db_pos["id"]),
                                db_pos["current_price"],
                                db_pos["unrealized_pnl"]
                            )
                
                await asyncio.sleep(self._sync_intervals['positions'])
                
            except Exception as e:
                logger.error(f"Position sync error: {e}")
                await asyncio.sleep(2)
    
    async def _sync_performance(self):
        """Calculate and sync performance metrics"""
        while self._running:
            try:
                # Get account summary using IBKRService method
                account_summary = await self.ibkr.get_account_summary()
                
                # Calculate daily metrics
                today = datetime.utcnow().date()
                
                # Get today's trades
                closed_positions = await self.position_repo.find_all(
                    filters={
                        "is_open": False,
                        "closed_at": f">={today.isoformat()}"
                    }
                )
                
                # Calculate metrics
                total_trades = len(closed_positions)
                winning_trades = sum(1 for p in closed_positions if p["realized_pnl"] > 0)
                losing_trades = sum(1 for p in closed_positions if p["realized_pnl"] < 0)
                
                gross_profit = sum(p["realized_pnl"] for p in closed_positions if p["realized_pnl"] > 0)
                gross_loss = abs(sum(p["realized_pnl"] for p in closed_positions if p["realized_pnl"] < 0))
                
                metrics = {
                    "metric_date": today,
                    "total_trades": total_trades,
                    "winning_trades": winning_trades,
                    "losing_trades": losing_trades,
                    "gross_profit": gross_profit,
                    "gross_loss": gross_loss,
                    "average_win": gross_profit / winning_trades if winning_trades > 0 else 0,
                    "average_loss": gross_loss / losing_trades if losing_trades > 0 else 0
                }
                
                # Update metrics
                await self.performance_repo.update_daily_metrics(None, metrics)
                
                await asyncio.sleep(self._sync_intervals['performance'])
                
            except Exception as e:
                logger.error(f"Performance sync error: {e}")
                await asyncio.sleep(60)
    
    async def _sync_instrument_details(self):
        """Sync instrument-specific details"""
        while self._running:
            try:
                for inst_id in self._subscribed_instruments:
                    instrument = await self.instrument_repo.get_by_id(inst_id)
                    if not instrument:
                        continue
                    
                    # Update based on asset class
                    if instrument["asset_class"] == "STOCK":
                        await self._update_stock_details(instrument)
                    elif instrument["asset_class"] == "OPTION":
                        await self._update_option_details(instrument)
                    elif instrument["asset_class"] == "FUTURE":
                        await self._update_future_details(instrument)
                    elif instrument["asset_class"] == "CRYPTO":
                        await self._update_crypto_details(instrument)
                
                await asyncio.sleep(self._sync_intervals['details'])
                
            except Exception as e:
                logger.error(f"Instrument details sync error: {e}")
                await asyncio.sleep(300)
    
    async def _process_signals(self):
        """Process trading signals and execute orders"""
        while self._running:
            try:
                # Get active signals
                active_signals = await self.signal_repo.get_active_signals()
                
                for signal in active_signals:
                    # Check if signal already has an order
                    existing_orders = await self.order_repo.find_all(
                        filters={"signal_id": signal["id"]}
                    )
                    
                    if not existing_orders:
                        # Execute signal
                        await self._execute_signal(signal)
                
                await asyncio.sleep(1)  # Check signals every second
                
            except Exception as e:
                logger.error(f"Signal processing error: {e}")
                await asyncio.sleep(1)
    
    async def _execute_signal(self, signal: Dict[str, Any]):
        """Execute a trading signal"""
        try:
            # Get instrument details
            instrument = await self.instrument_repo.get_by_id(
                UUID(signal["instrument_id"])
            )
            
            if not instrument:
                logger.error(f"Instrument not found for signal {signal['id']}")
                return
            
            # Prepare order
            order_data = {
                "instrument_id": signal["instrument_id"],
                "signal_id": signal["id"],
                "order_type": "LIMIT" if signal.get("target_price") else "MARKET",
                "side": signal["signal_type"],
                "quantity": self._calculate_position_size(signal),
                "limit_price": signal.get("target_price"),
                "metadata": {
                    "signal_strength": signal["signal_strength"],
                    "strategy_id": signal.get("strategy_id")
                }
            }
            
            # Create order in database
            db_order = await self.order_repo.create_order(order_data)
            
            # Submit to IBKR
            contract = self._create_ibkr_contract(instrument)
            ibkr_order = self._create_ibkr_order(order_data)
            
            order_id = await self.ibkr.trading_client.place_order(
                contract,
                ibkr_order
            )
            
            # Update with IBKR order ID
            await self.order_repo.update(
                UUID(db_order["id"]),
                {"ibkr_order_id": order_id}
            )
            
            logger.info(f"Executed signal {signal['id']} with order {order_id}")
            
        except Exception as e:
            logger.error(f"Failed to execute signal {signal['id']}: {e}")
    
    async def _get_ibkr_market_data(self, instrument: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get market data from IBKR for an instrument"""
        try:
            contract = self._create_ibkr_contract(instrument)
            
            # Get market data
            ticker = await self.ibkr.market_data_client.get_ticker(contract)
            
            if ticker:
                return {
                    "bid": ticker.bid,
                    "ask": ticker.ask,
                    "last": ticker.last,
                    "bid_size": ticker.bidSize,
                    "ask_size": ticker.askSize,
                    "volume": ticker.volume
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get market data for {instrument['symbol']}: {e}")
            return None
    
    async def _ensure_instrument_exists(self, ibkr_position) -> Optional[Dict[str, Any]]:
        """Ensure instrument exists in database"""
        try:
            # Determine asset class from contract
            asset_class = self._determine_asset_class(ibkr_position.contract)
            
            # Check if instrument exists
            instrument = await self.instrument_repo.get_by_symbol_and_class(
                ibkr_position.contract.symbol,
                asset_class,
                ibkr_position.contract.exchange
            )
            
            if not instrument:
                # Create new instrument
                instrument = await self.instrument_repo.create_instrument(
                    symbol=ibkr_position.contract.symbol,
                    asset_class=asset_class,
                    exchange=ibkr_position.contract.exchange,
                    currency=ibkr_position.contract.currency,
                    contract_details={
                        "con_id": ibkr_position.contract.conId,
                        "sec_type": ibkr_position.contract.secType,
                        "multiplier": ibkr_position.contract.multiplier
                    }
                )
            
            return instrument
            
        except Exception as e:
            logger.error(f"Failed to ensure instrument exists: {e}")
            return None
    
    def _determine_asset_class(self, contract) -> str:
        """Determine asset class from IBKR contract"""
        sec_type_map = {
            "STK": "STOCK",
            "OPT": "OPTION",
            "FUT": "FUTURE",
            "CASH": "FOREX",
            "CRYPTO": "CRYPTO",
            "BOND": "BOND",
            "CMDTY": "COMMODITY"
        }
        return sec_type_map.get(contract.secType, "STOCK")
    
    def _create_ibkr_contract(self, instrument: Dict[str, Any]):
        """Create IBKR contract from instrument"""
        from ib_async import Contract, Stock, Option, Future, Forex, Crypto
        
        contract_details = instrument.get("contract_details", {})
        
        if instrument["asset_class"] == "STOCK":
            return Stock(
                symbol=instrument["symbol"],
                exchange=instrument.get("exchange", "SMART"),
                currency=instrument.get("currency", "USD")
            )
        elif instrument["asset_class"] == "FOREX":
            # Parse forex pair
            if "/" in instrument["symbol"]:
                base, quote = instrument["symbol"].split("/")
                return Forex(base + quote)
            return Forex(instrument["symbol"])
        elif instrument["asset_class"] == "OPTION":
            return Option(
                symbol=contract_details.get("underlying_symbol"),
                lastTradeDateOrContractMonth=contract_details.get("expiry"),
                strike=contract_details.get("strike"),
                right=contract_details.get("right", "C"),
                exchange=instrument.get("exchange", "SMART")
            )
        elif instrument["asset_class"] == "FUTURE":
            return Future(
                symbol=instrument["symbol"],
                lastTradeDateOrContractMonth=contract_details.get("expiry"),
                exchange=instrument.get("exchange")
            )
        else:
            # Generic contract
            contract = Contract()
            contract.symbol = instrument["symbol"]
            contract.secType = contract_details.get("sec_type", "STK")
            contract.exchange = instrument.get("exchange", "SMART")
            contract.currency = instrument.get("currency", "USD")
            return contract
    
    def _create_ibkr_order(self, order_data: Dict[str, Any]):
        """Create IBKR order from order data"""
        from ib_async import MarketOrder, LimitOrder, StopOrder
        
        quantity = order_data["quantity"]
        
        if order_data["order_type"] == "MARKET":
            return MarketOrder(
                action=order_data["side"],
                totalQuantity=quantity
            )
        elif order_data["order_type"] == "LIMIT":
            return LimitOrder(
                action=order_data["side"],
                totalQuantity=quantity,
                lmtPrice=float(order_data["limit_price"])
            )
        elif order_data["order_type"] == "STOP":
            return StopOrder(
                action=order_data["side"],
                totalQuantity=quantity,
                stopPrice=float(order_data["stop_price"])
            )
    
    def _map_ibkr_status(self, ibkr_status: str) -> str:
        """Map IBKR order status to our status"""
        status_map = {
            "PendingSubmit": "PENDING",
            "PendingCancel": "PENDING",
            "PreSubmitted": "PENDING",
            "Submitted": "PENDING",
            "Filled": "FILLED",
            "Cancelled": "CANCELLED",
            "Inactive": "CANCELLED"
        }
        return status_map.get(ibkr_status, "PENDING")
    
    def _calculate_position_size(self, signal: Dict[str, Any]) -> float:
        """Calculate position size based on signal and risk parameters"""
        # This is a simplified version
        # In production, this would consider:
        # - Account balance
        # - Risk per trade
        # - Signal strength
        # - Volatility
        # - Correlation with existing positions
        
        base_size = 100000  # Base position size
        signal_strength = signal.get("signal_strength", 0.5)
        
        # Adjust by signal strength
        return base_size * signal_strength
    
    async def _update_stock_details(self, instrument: Dict[str, Any]):
        """Update stock-specific details"""
        # In production, this would fetch from market data provider
        pass
    
    async def _update_option_details(self, instrument: Dict[str, Any]):
        """Update option Greeks and details"""
        # In production, this would calculate/fetch Greeks
        pass
    
    async def _update_future_details(self, instrument: Dict[str, Any]):
        """Update futures contract details"""
        # In production, this would fetch contract specifications
        pass
    
    async def _update_crypto_details(self, instrument: Dict[str, Any]):
        """Update cryptocurrency details"""
        # In production, this would fetch from crypto data provider
        pass
