"""
Service Manager for IBKR MCP Server

Provides centralized service lifecycle management and coordination
across all application services.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from contextlib import asynccontextmanager
from datetime import datetime, timezone

from .base_service import BaseService, ServiceStatus
from ..core.settings import settings

logger = logging.getLogger(__name__)


class ServiceManager:
    """
    Centralized service manager for coordinating all application services.
    
    Provides:
    - Service registration and discovery
    - Lifecycle management (start/stop/restart)
    - Health monitoring
    - Dependency management
    - Graceful shutdown
    """
    
    def __init__(self):
        self.services: Dict[str, BaseService] = {}
        self.service_dependencies: Dict[str, List[str]] = {}
        self.is_running = False
        self.startup_order: List[str] = []
        self.shutdown_order: List[str] = []
        
        # Initialize core services
        self._register_core_services()
    
    def _register_core_services(self):
        """Register core application services"""
        # Lazy import to avoid circular dependencies
        from .phase2_service import Phase2Service

        # Register Phase 2 service
        self.register_service("phase2", Phase2Service(), dependencies=[])

        # Define startup/shutdown order
        self.startup_order = ["phase2"]
        self.shutdown_order = list(reversed(self.startup_order))
    
    def register_service(self, name: str, service: BaseService, dependencies: Optional[List[str]] = None):
        """
        Register a service with the manager.
        
        Args:
            name: Unique service name
            service: Service instance
            dependencies: List of service names this service depends on
        """
        if name in self.services:
            raise ValueError(f"Service '{name}' is already registered")
        
        self.services[name] = service
        self.service_dependencies[name] = dependencies or []
        
        logger.info(f"Registered service: {name}")
    
    def unregister_service(self, name: str):
        """Unregister a service from the manager"""
        if name not in self.services:
            raise ValueError(f"Service '{name}' is not registered")
        
        # Check if other services depend on this one
        dependents = [svc for svc, deps in self.service_dependencies.items() if name in deps]
        if dependents:
            raise ValueError(f"Cannot unregister service '{name}': services {dependents} depend on it")
        
        del self.services[name]
        del self.service_dependencies[name]
        
        # Remove from startup/shutdown orders
        if name in self.startup_order:
            self.startup_order.remove(name)
        if name in self.shutdown_order:
            self.shutdown_order.remove(name)
        
        logger.info(f"Unregistered service: {name}")
    
    def get_service(self, name: str) -> Optional[BaseService]:
        """Get a service by name"""
        return self.services.get(name)
    
    def list_services(self) -> List[str]:
        """Get list of registered service names"""
        return list(self.services.keys())
    
    async def start_all(self):
        """Start all services in dependency order"""
        if self.is_running:
            logger.warning("Service manager is already running")
            return
        
        logger.info("Starting all services...")
        
        try:
            # Start services in dependency order
            for service_name in self.startup_order:
                if service_name in self.services:
                    await self._start_service_with_dependencies(service_name)
            
            self.is_running = True
            logger.info("All services started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start services: {e}")
            # Attempt to stop any services that were started
            await self.stop_all()
            raise
    
    async def stop_all(self):
        """Stop all services in reverse dependency order"""
        if not self.is_running:
            logger.warning("Service manager is not running")
            return
        
        logger.info("Stopping all services...")
        
        # Stop services in reverse dependency order
        for service_name in self.shutdown_order:
            if service_name in self.services:
                await self._stop_service_safely(service_name)
        
        self.is_running = False
        logger.info("All services stopped")
    
    async def restart_all(self):
        """Restart all services"""
        await self.stop_all()
        await self.start_all()
    
    async def start_service(self, name: str):
        """Start a specific service and its dependencies"""
        if name not in self.services:
            raise ValueError(f"Service '{name}' is not registered")
        
        await self._start_service_with_dependencies(name)
    
    async def stop_service(self, name: str):
        """Stop a specific service"""
        if name not in self.services:
            raise ValueError(f"Service '{name}' is not registered")
        
        await self._stop_service_safely(name)
    
    async def restart_service(self, name: str):
        """Restart a specific service"""
        await self.stop_service(name)
        await self.start_service(name)
    
    async def _start_service_with_dependencies(self, name: str):
        """Start a service after ensuring its dependencies are running"""
        service = self.services[name]
        
        if service.is_running():
            logger.debug(f"Service '{name}' is already running")
            return
        
        # Start dependencies first
        for dep_name in self.service_dependencies[name]:
            if dep_name in self.services:
                await self._start_service_with_dependencies(dep_name)
        
        # Start the service
        logger.info(f"Starting service: {name}")
        await service.start()
        logger.info(f"Service '{name}' started successfully")
    
    async def _stop_service_safely(self, name: str):
        """Stop a service with error handling"""
        service = self.services[name]
        
        if not service.is_running():
            logger.debug(f"Service '{name}' is not running")
            return
        
        try:
            logger.info(f"Stopping service: {name}")
            await service.stop()
            logger.info(f"Service '{name}' stopped successfully")
        except Exception as e:
            logger.error(f"Error stopping service '{name}': {e}")
    
    def get_service_status(self, name: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific service"""
        service = self.services.get(name)
        if not service:
            return None
        
        return service.get_status()
    
    def get_all_service_status(self) -> Dict[str, Any]:
        """Get status of all services"""
        status = {
            "manager_running": self.is_running,
            "total_services": len(self.services),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "services": {}
        }
        
        for name, service in self.services.items():
            status["services"][name] = service.get_status()
        
        return status
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get health summary of all services"""
        healthy_count = 0
        total_count = len(self.services)
        
        service_health = {}
        for name, service in self.services.items():
            is_healthy = service.is_healthy()
            service_health[name] = is_healthy
            if is_healthy:
                healthy_count += 1
        
        return {
            "overall_healthy": healthy_count == total_count,
            "healthy_services": healthy_count,
            "total_services": total_count,
            "health_percentage": (healthy_count / total_count * 100) if total_count > 0 else 0,
            "service_health": service_health,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    
    @asynccontextmanager
    async def lifespan(self):
        """Async context manager for service lifecycle"""
        await self.start_all()
        try:
            yield self
        finally:
            await self.stop_all()


# Global service manager instance
service_manager = ServiceManager()

# Convenience functions
async def start_services():
    """Start all services"""
    await service_manager.start_all()

async def stop_services():
    """Stop all services"""
    await service_manager.stop_all()

async def get_service_health():
    """Get service health summary"""
    return service_manager.get_health_summary()

def get_service(name: str) -> Optional[BaseService]:
    """Get a service by name"""
    return service_manager.get_service(name)
