"""
Phase 2 Integration Service

Main service that orchestrates database webhooks, Edge Functions,
and real-time processing for the IBKR trading platform.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
from contextlib import asynccontextmanager

try:
    from config.phase2_config import Phase2Config, default_config  # type: ignore
    from integrations.edge_functions_client import EdgeFunctionsClient, MarketDataTick  # type: ignore
    from integrations.webhook_processor import WebhookProcessor, NotificationConfig  # type: ignore
    from database.connection import supabase_conn  # type: ignore
except ImportError:
    try:
        from app.config.phase2_config import Phase2Config, default_config  # type: ignore
        from app.integrations.edge_functions_client import EdgeFunctionsClient, MarketDataTick  # type: ignore
        from app.integrations.webhook_processor import WebhookProcessor, NotificationConfig  # type: ignore
        from app.database.connection import supabase_conn  # type: ignore
    except ImportError:
        # Fallback - create dummy classes to prevent import errors
        class Phase2Config:  # type: ignore
            def __init__(self):
                # Add all required attributes
                # Create proper dummy config objects with all required attributes
                class EdgeFunctionsConfig:
                    def __init__(self):
                        self.supabase_url = ''
                        self.supabase_anon_key = ''
                        self.timeout = 30
                        self.indicator_calculation_interval = 60
                        self.signal_processing_interval = 30

                class NotificationConfig:
                    def __init__(self):
                        self.slack_webhook_url = None
                        self.discord_webhook_url = None
                        self.generic_webhook_url = None
                        self.telegram_bot_token = None
                        self.telegram_chat_id = None

                class WebhookConfig:
                    def __init__(self):
                        self.enabled = True
                        self.batch_size = 50
                        self.max_concurrent = 5
                        self.poll_interval = 5
                        self.cleanup_days = 7

                class RiskManagementConfig:
                    def __init__(self):
                        self.position_monitoring_interval = 30

                self.edge_functions = EdgeFunctionsConfig()
                self.notifications = NotificationConfig()
                self.webhooks = WebhookConfig()
                self.risk_management = RiskManagementConfig()
            def validate(self): return []

        class Result:  # type: ignore
            def __init__(self, success=False, data=None, error=None):
                self.success = success
                self.data = data or {}
                self.error = error

        class EdgeFunctionsClient:  # type: ignore
            def __init__(self, **kwargs): pass  # noqa: ARG002
            async def health_check(self): return {"available": False}
            async def close(self): pass
            async def calculate_indicators(self, *args, **kwargs): return {"success": False}  # noqa: ARG002
            async def update_correlations(self, *args, **kwargs): return {"success": False}  # noqa: ARG002
            async def process_signals(self): return Result(success=False, data={})
            async def manage_positions(self): return Result(success=False, data={})
            async def process_tick(self, tick): return Result(success=False)  # noqa: ARG002
            async def execute_signal(self, signal_id, force=False): return Result(success=False, data={}, error='Dummy service')  # noqa: ARG002

        class MarketDataTick:  # type: ignore
            def __init__(self, **kwargs): pass  # noqa: ARG002

        class WebhookProcessor:  # type: ignore
            def __init__(self, **kwargs): pass  # noqa: ARG002
            async def stop(self): pass
            async def close(self): pass
            def register_handler(self, *args): pass  # noqa: ARG002
            async def start(self, *args): pass  # noqa: ARG002
            async def cleanup_old_webhooks(self, days): return 0  # noqa: ARG002
            async def get_webhook_stats(self, hours): return {}  # noqa: ARG002
            async def process_pending_webhooks(self): return {"processed": 0}

        class NotificationConfig:  # type: ignore
            def __init__(self, **kwargs): pass  # noqa: ARG002

        class SupabaseConn:  # type: ignore
            def __init__(self):
                class Client:
                    def table(self, name):  # noqa: ARG002
                        class Table:
                            def select(self, fields):  # noqa: ARG002
                                class Query:
                                    def eq(self, field, value):  # noqa: ARG002
                                        class FilteredQuery:
                                            def execute(self):
                                                class QueryResult:
                                                    def __init__(self):
                                                        self.data = []
                                                return QueryResult()
                                        return FilteredQuery()
                                return Query()
                        return Table()
                self.client = Client()

        default_config = Phase2Config()
        supabase_conn = SupabaseConn()
from .base_service import BaseService

logger = logging.getLogger(__name__)

class Phase2Service(BaseService):
    """
    Main service for Phase 2 functionality.
    
    Coordinates:
    - Database webhook processing
    - Edge Functions integration
    - Real-time market data processing
    - Risk monitoring and alerts
    - Notification systems
    """
    
    def __init__(self, config: Optional[Phase2Config] = None):
        """
        Initialize Phase 2 service.

        Args:
            config: Configuration object (uses default if None)
        """
        # Initialize base service
        super().__init__("phase2", {})

        self.phase2_config = config or default_config

        # Initialize components
        self.edge_functions_client: Optional[EdgeFunctionsClient] = None
        self.webhook_processor: Optional[WebhookProcessor] = None

        # Background tasks (in addition to base service tasks)
        self.background_tasks: List[asyncio.Task] = []

        logger.info("Phase 2 service initialized")

    async def _start_service(self) -> None:
        """Service-specific startup logic"""
        # Validate configuration
        config_errors = self.phase2_config.validate()
        if config_errors:
            raise ValueError(f"Configuration validation failed: {config_errors}")

        # Initialize Edge Functions client
        await self._initialize_edge_functions_client()

        # Initialize webhook processor
        await self._initialize_webhook_processor()

        # Start background tasks
        await self._start_background_tasks()

    async def _stop_service(self) -> None:
        """Service-specific shutdown logic"""
        # Cancel background tasks
        for task in self.background_tasks:
            if not task.done():
                task.cancel()

        # Wait for tasks to complete
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)

        # Stop webhook processor
        if self.webhook_processor:
            await self.webhook_processor.stop()
            await self.webhook_processor.close()

        # Close Edge Functions client
        if self.edge_functions_client:
            await self.edge_functions_client.close()

    # The start() and stop() methods are inherited from BaseService
    
    @asynccontextmanager
    async def lifespan(self):
        """Async context manager for service lifecycle"""
        await self.start()
        try:
            yield self
        finally:
            await self.stop()
    
    async def _initialize_edge_functions_client(self):
        """Initialize Edge Functions client"""
        self.edge_functions_client = EdgeFunctionsClient(
            supabase_url=self.phase2_config.edge_functions.supabase_url,
            supabase_anon_key=self.phase2_config.edge_functions.supabase_anon_key,
            timeout=self.phase2_config.edge_functions.timeout
        )
        
        # Test connection
        health = await self.edge_functions_client.health_check()
        logger.info(f"Edge Functions health check: {health}")
    
    async def _initialize_webhook_processor(self):
        """Initialize webhook processor"""
        notification_config = NotificationConfig(
            slack_webhook=self.phase2_config.notifications.slack_webhook_url,
            discord_webhook=self.phase2_config.notifications.discord_webhook_url,
            webhook_url=self.phase2_config.notifications.generic_webhook_url,
            telegram_bot_token=self.phase2_config.notifications.telegram_bot_token,
            telegram_chat_id=self.phase2_config.notifications.telegram_chat_id
        )
        
        self.webhook_processor = WebhookProcessor(
            notification_config=notification_config,
            edge_functions_client=self.edge_functions_client,
            batch_size=self.phase2_config.webhooks.batch_size,
            max_concurrent=self.phase2_config.webhooks.max_concurrent
        )
        
        # Register custom event handlers
        self._register_event_handlers()
    
    def _register_event_handlers(self):
        """Register custom event handlers"""
        if not self.webhook_processor:
            return
        
        # Register order execution handler
        async def order_execution_handler(event):
            logger.info(f"Custom order execution handler: {event.payload.get('order_id')}")
            # Add custom logic here
        
        # Register risk alert handler
        async def risk_alert_handler(event):
            severity = event.payload.get('severity', 'medium')
            logger.warning(f"Custom risk alert handler: {severity} - {event.payload.get('alert_type')}")
            # Add custom logic here
        
        self.webhook_processor.register_handler("order_execution", order_execution_handler)
        self.webhook_processor.register_handler("risk_alert", risk_alert_handler)
    
    async def _start_background_tasks(self):
        """Start background processing tasks"""
        # Webhook processing task
        if self.phase2_config.webhooks.enabled and self.webhook_processor:
            webhook_task = asyncio.create_task(
                self.webhook_processor.start(self.phase2_config.webhooks.poll_interval)
            )
            self.background_tasks.append(webhook_task)
        
        # Market data processing task
        market_data_task = asyncio.create_task(
            self._market_data_processing_loop()
        )
        self.background_tasks.append(market_data_task)
        
        # Signal processing task
        signal_processing_task = asyncio.create_task(
            self._signal_processing_loop()
        )
        self.background_tasks.append(signal_processing_task)
        
        # Risk monitoring task
        risk_monitoring_task = asyncio.create_task(
            self._risk_monitoring_loop()
        )
        self.background_tasks.append(risk_monitoring_task)
        
        # Cleanup task
        cleanup_task = asyncio.create_task(
            self._cleanup_loop()
        )
        self.background_tasks.append(cleanup_task)
    
    async def _market_data_processing_loop(self):
        """Background task for market data processing"""
        logger.info("Starting market data processing loop")
        
        while self.is_running():
            try:
                # Get active currency pairs
                active_pairs = await self._get_active_currency_pairs()
                
                if active_pairs and self.edge_functions_client:
                    # Calculate technical indicators
                    await self.edge_functions_client.calculate_indicators(
                        active_pairs, 
                        lookback_periods=50
                    )
                    
                    # Update correlations
                    await self.edge_functions_client.update_correlations(
                        active_pairs,
                        lookback_periods=50
                    )
                
                await asyncio.sleep(self.phase2_config.edge_functions.indicator_calculation_interval)
                
            except Exception as e:
                logger.error(f"Error in market data processing loop: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _signal_processing_loop(self):
        """Background task for signal processing"""
        logger.info("Starting signal processing loop")
        
        while self.is_running():
            try:
                if self.edge_functions_client:
                    # Process active signals
                    result = await self.edge_functions_client.process_signals()
                    
                    if result.success and result.data:
                        processed_count = result.data.get('processed_signals', 0)
                        if processed_count > 0:
                            logger.info(f"Processed {processed_count} trading signals")
                
                await asyncio.sleep(self.phase2_config.edge_functions.signal_processing_interval)
                
            except Exception as e:
                logger.error(f"Error in signal processing loop: {e}")
                await asyncio.sleep(30)  # Wait before retrying
    
    async def _risk_monitoring_loop(self):
        """Background task for risk monitoring"""
        logger.info("Starting risk monitoring loop")
        
        while self.is_running():
            try:
                if self.edge_functions_client:
                    # Manage open positions (check stop-loss, take-profit)
                    result = await self.edge_functions_client.manage_positions()
                    
                    if result.success and result.data:
                        actions = result.data.get('actions', [])
                        if actions:
                            logger.info(f"Risk management actions taken: {len(actions)}")
                
                await asyncio.sleep(self.phase2_config.risk_management.position_monitoring_interval)
                
            except Exception as e:
                logger.error(f"Error in risk monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _cleanup_loop(self):
        """Background task for cleanup operations"""
        logger.info("Starting cleanup loop")
        
        while self.is_running():
            try:
                # Clean up old webhook events
                if self.webhook_processor:
                    deleted_count = await self.webhook_processor.cleanup_old_webhooks(
                        self.phase2_config.webhooks.cleanup_days
                    )
                    if deleted_count > 0:
                        logger.info(f"Cleaned up {deleted_count} old webhook events")
                
                # Sleep for 24 hours
                await asyncio.sleep(86400)
                
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(3600)  # Wait 1 hour before retrying
    
    async def _get_active_currency_pairs(self) -> List[str]:
        """Get list of active currency pair IDs"""
        try:
            result = supabase_conn.client.table('currency_pairs')\
                .select('id')\
                .eq('is_active', True)\
                .execute()
            
            return [row['id'] for row in result.data] if result.data else []
            
        except Exception as e:
            logger.error(f"Failed to get active currency pairs: {e}")
            return []
    
    # Public API methods
    
    async def process_market_tick(self, pair_id: str, symbol: str, bid: float, ask: float, 
                                 volume: Optional[int] = None) -> bool:
        """
        Process a market data tick.
        
        Args:
            pair_id: Currency pair ID
            symbol: Trading symbol
            bid: Bid price
            ask: Ask price
            volume: Optional volume
            
        Returns:
            True if successful, False otherwise
        """
        if not self.edge_functions_client:
            logger.error("Edge Functions client not initialized")
            return False
        
        try:
            tick = MarketDataTick(
                pair_id=pair_id,
                symbol=symbol,
                bid=bid,
                ask=ask,
                bid_volume=volume,
                source="IBKR"
            )
            
            response = await self.edge_functions_client.process_tick(tick)
            return response.success
            
        except Exception as e:
            logger.error(f"Failed to process market tick: {e}")
            return False
    
    async def execute_trading_signal(self, signal_id: str, force_execution: bool = False) -> Dict[str, Any]:
        """
        Execute a trading signal.
        
        Args:
            signal_id: Signal ID to execute
            force_execution: Whether to bypass risk validation
            
        Returns:
            Dictionary with execution results
        """
        if not self.edge_functions_client:
            return {"success": False, "error": "Edge Functions client not initialized"}
        
        try:
            response = await self.edge_functions_client.execute_signal(signal_id, force_execution)
            return {
                "success": response.success,
                "data": response.data,
                "error": response.error
            }
            
        except Exception as e:
            logger.error(f"Failed to execute trading signal: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_system_status(self) -> Dict[str, Any]:
        """
        Get comprehensive system status.
        
        Returns:
            Dictionary with system status information
        """
        status = {
            "service_running": self.is_running(),
            "edge_functions": {"available": False},
            "webhooks": {"enabled": self.phase2_config.webhooks.enabled, "processing": False},
            "background_tasks": len(self.background_tasks),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        # Check Edge Functions health
        if self.edge_functions_client:
            try:
                health = await self.edge_functions_client.health_check()
                status["edge_functions"] = health
            except Exception as e:
                status["edge_functions"]["error"] = str(e)
        
        # Check webhook processor status
        if self.webhook_processor:
            try:
                webhook_stats = await self.webhook_processor.get_webhook_stats(24)
                status["webhooks"]["stats"] = webhook_stats
                status["webhooks"]["processing"] = True
            except Exception as e:
                status["webhooks"]["error"] = str(e)
        
        return status
    
    async def trigger_manual_processing(self) -> Dict[str, Any]:
        """
        Trigger manual processing of signals and webhooks.
        
        Returns:
            Dictionary with processing results
        """
        results = {}
        
        # Process webhooks
        if self.webhook_processor:
            try:
                webhook_results = await self.webhook_processor.process_pending_webhooks()
                results["webhooks"] = webhook_results
            except Exception as e:
                results["webhooks"] = {"error": str(e)}
        
        # Process signals
        if self.edge_functions_client:
            try:
                signal_results = await self.edge_functions_client.process_signals()
                results["signals"] = {
                    "success": signal_results.success,
                    "data": signal_results.data,
                    "error": signal_results.error
                }
            except Exception as e:
                results["signals"] = {"error": str(e)}
        
        return results

# Global service instance
phase2_service = Phase2Service()

# Convenience functions
async def start_phase2_service():
    """Start the Phase 2 service"""
    await phase2_service.start()

async def stop_phase2_service():
    """Stop the Phase 2 service"""
    await phase2_service.stop()

async def get_phase2_status():
    """Get Phase 2 service status"""
    return await phase2_service.get_system_status()
