"""
Market Data Streaming Service

This service provides high-frequency market data streaming capabilities
with support for multiple data types and real-time updates.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable, Set, Union
from datetime import datetime
from decimal import Decimal
from enum import Enum
from dataclasses import dataclass, field
import weakref

from services.ibkr_service import IBKRService
from ib_async import Contract, Ticker, BarDataList, RealTimeBarList
from ib_async.wrapper import Wrapper

logger = logging.getLogger('market-data-streaming')


class DataType(Enum):
    """Supported market data types"""
    TRADES = "TRADES"
    BID_ASK = "BID_ASK"
    MID_POINT = "MIDPOINT"
    HISTORICAL = "HISTORICAL"
    OPTION_COMPUTATION = "OPTION_COMPUTATION"
    OPTION_GREEKS = "OPTION_GREEKS"
    REAL_TIME_BARS = "RTBars"
    TICK_BY_TICK = "TICK_BY_TICK"
    NEWS = "NEWS"
    FUNDAMENTAL = "FUNDAMENTAL"


@dataclass
class MarketDataSnapshot:
    """Snapshot of market data at a point in time"""
    symbol: str
    timestamp: datetime
    bid: Optional[float] = None
    bid_size: Optional[int] = None
    ask: Optional[float] = None
    ask_size: Optional[int] = None
    last: Optional[float] = None
    last_size: Optional[int] = None
    volume: Optional[int] = None
    high: Optional[float] = None
    low: Optional[float] = None
    close: Optional[float] = None
    open: Optional[float] = None
    vwap: Optional[float] = None
    implied_volatility: Optional[float] = None
    delta: Optional[float] = None
    gamma: Optional[float] = None
    theta: Optional[float] = None
    vega: Optional[float] = None
    custom_fields: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StreamSubscription:
    """Represents a market data stream subscription"""
    subscription_id: str
    symbol: str
    contract: Contract
    data_types: List[DataType]
    callback: Optional[Callable[[MarketDataSnapshot], None]]
    req_id: Optional[int] = None
    active: bool = True
    error_count: int = 0
    last_update: Optional[datetime] = None


class MarketDataStreamingService:
    """
    High-frequency market data streaming service
    
    Provides real-time market data streaming with support for multiple
    data types, tick-by-tick updates, and historical data streaming.
    """
    
    def __init__(self, ibkr_service: IBKRService):
        self.ibkr_service = ibkr_service
        self._subscriptions: Dict[str, StreamSubscription] = {}
        self._ticker_cache: Dict[int, Any] = {}  # req_id -> Ticker or RealTimeBarList
        self._callbacks: Dict[str, List[Callable]] = {}  # symbol -> callbacks
        self._req_id_counter = 1000
        self._streaming_tasks: Dict[str, asyncio.Task] = {}
        
        # Register event handlers
        self._register_handlers()
        
    def _register_handlers(self):
        """Register handlers for market data events"""
        if self.ibkr_service.ib:
            # These would be the actual ib_async event handlers
            # For now, we'll structure it for future implementation
            pass
    
    async def subscribe_market_data(
        self,
        symbol: str,
        data_types: List[DataType],
        callback: Optional[Callable[[MarketDataSnapshot], None]] = None,
        generic_tick_list: str = "",
        snapshot: bool = False,
        regulatory_snapshot: bool = False
    ) -> Dict[str, Any]:
        """
        Subscribe to real-time market data for a symbol
        
        Args:
            symbol: The ticker symbol
            data_types: List of data types to subscribe to
            callback: Optional callback function for data updates
            generic_tick_list: IB generic tick list (e.g., "233" for RTVolume)
            snapshot: If True, return snapshot and cancel
            regulatory_snapshot: If True, request regulatory snapshot
            
        Returns:
            Subscription details including subscription_id
        """
        try:
            # Create contract
            contract = await self.ibkr_service.create_contract(symbol)
            
            # Generate subscription ID
            subscription_id = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S%f')}"
            
            # Get request ID
            req_id = self._get_next_req_id()
            
            # Create subscription
            subscription = StreamSubscription(
                subscription_id=subscription_id,
                symbol=symbol,
                contract=contract,
                data_types=data_types,
                callback=callback,
                req_id=req_id
            )
            
            # Store subscription
            self._subscriptions[subscription_id] = subscription
            
            # Add callback to symbol callbacks
            if callback:
                if symbol not in self._callbacks:
                    self._callbacks[symbol] = []
                self._callbacks[symbol].append(callback)
            
            # Request market data based on data types
            if DataType.REAL_TIME_BARS in data_types:
                # Request real-time bars (5-second bars)
                bars = self.ibkr_service.ib.reqRealTimeBars(
                    contract=contract,
                    barSize=5,
                    whatToShow="TRADES",
                    useRTH=False
                )
                # Store the bars object for updates
                self._ticker_cache[req_id] = bars
            else:
                # Request tick data
                ticker = self.ibkr_service.ib.reqMktData(
                    contract=contract,
                    genericTickList=generic_tick_list,
                    snapshot=snapshot,
                    regulatorySnapshot=regulatory_snapshot
                )
                self._ticker_cache[req_id] = ticker
            
            # Start streaming task
            task = asyncio.create_task(
                self._stream_market_data(subscription_id)
            )
            self._streaming_tasks[subscription_id] = task
            
            logger.info(f"Subscribed to market data for {symbol}: {subscription_id}")
            
            return {
                "status": "success",
                "subscription_id": subscription_id,
                "symbol": symbol,
                "data_types": [dt.value for dt in data_types],
                "req_id": req_id
            }
            
        except Exception as e:
            logger.error(f"Failed to subscribe to market data for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def unsubscribe_market_data(self, subscription_id: str) -> Dict[str, Any]:
        """Unsubscribe from market data stream"""
        try:
            if subscription_id not in self._subscriptions:
                return {
                    "status": "error",
                    "message": f"Subscription {subscription_id} not found"
                }
            
            subscription = self._subscriptions[subscription_id]
            
            # Cancel streaming task
            if subscription_id in self._streaming_tasks:
                task = self._streaming_tasks[subscription_id]
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                del self._streaming_tasks[subscription_id]
            
            # Cancel market data request
            if subscription.req_id is not None and subscription.req_id in self._ticker_cache:
                ticker = self._ticker_cache[subscription.req_id]
                if hasattr(ticker, 'contract') and ticker.contract is not None:
                    self.ibkr_service.ib.cancelMktData(ticker.contract)
                del self._ticker_cache[subscription.req_id]
            
            # Remove callback
            if subscription.callback and subscription.symbol in self._callbacks:
                self._callbacks[subscription.symbol].remove(subscription.callback)
                if not self._callbacks[subscription.symbol]:
                    del self._callbacks[subscription.symbol]
            
            # Remove subscription
            del self._subscriptions[subscription_id]
            
            logger.info(f"Unsubscribed from market data: {subscription_id}")
            
            return {
                "status": "success",
                "message": f"Unsubscribed from {subscription_id}"
            }
            
        except Exception as e:
            logger.error(f"Failed to unsubscribe {subscription_id}: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def get_tick_by_tick_data(
        self,
        symbol: str,
        tick_type: str = "Last",
        number_of_ticks: int = 0,
        ignore_size: bool = False
    ) -> Dict[str, Any]:
        """
        Get tick-by-tick data for a symbol
        
        Args:
            symbol: The ticker symbol
            tick_type: "Last", "AllLast", "BidAsk", or "MidPoint"
            number_of_ticks: Number of ticks to retrieve (0 for streaming)
            ignore_size: Ignore size parameter in tick data
            
        Returns:
            Tick data or streaming subscription
        """
        try:
            contract = await self.ibkr_service.create_contract(symbol)
            
            # Request tick-by-tick data
            ticker = self.ibkr_service.ib.reqTickByTickData(
                contract=contract,
                tickType=tick_type,
                numberOfTicks=number_of_ticks,
                ignoreSize=ignore_size
            )
            
            # Format tick data based on the appropriate ticker attributes
            tick_data = []
            if ticker:
                # Add relevant ticker data based on available attributes
                tick_info = {
                    "tickType": tick_type,
                    "timestamp": datetime.now().isoformat()
                }
                
                # Add any available standard attributes that might be on the ticker
                for attr in ['time', 'price', 'size', 'bid', 'ask', 'last', 'volume']:
                    if hasattr(ticker, attr):
                        tick_info[attr] = getattr(ticker, attr)
                
                tick_data.append(tick_info)
            
            return {
                "status": "success",
                "symbol": symbol,
                "tickType": tick_type,
                "data": tick_data
            }
            
        except Exception as e:
            logger.error(f"Failed to get tick-by-tick data for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def stream_historical_data(
        self,
        symbol: str,
        duration: str = "1 D",
        bar_size: str = "1 min",
        what_to_show: str = "TRADES",
        use_rth: bool = True,
        callback: Optional[Callable[[List[Dict]], None]] = None
    ) -> Dict[str, Any]:
        """
        Stream historical data with updates
        
        Args:
            symbol: The ticker symbol
            duration: Time duration (e.g., "1 D", "1 W", "1 M")
            bar_size: Bar size (e.g., "1 min", "5 mins", "1 hour")
            what_to_show: Data type to show
            use_rth: Use regular trading hours only
            callback: Callback for data updates
            
        Returns:
            Streaming subscription details
        """
        try:
            contract = await self.ibkr_service.create_contract(symbol)
            
            # Request streaming historical data
            bars = await self.ibkr_service.ib.reqHistoricalDataAsync(
                contract=contract,
                endDateTime='',
                durationStr=duration,
                barSizeSetting=bar_size,
                whatToShow=what_to_show,
                useRTH=use_rth,
                keepUpToDate=True  # This enables streaming updates
            )
            
            # Generate subscription ID
            subscription_id = f"hist_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S%f')}"
            
            # Create task to handle updates
            if callback:
                task = asyncio.create_task(
                    self._handle_historical_updates(bars, callback)
                )
                self._streaming_tasks[subscription_id] = task
            
            return {
                "status": "success",
                "subscription_id": subscription_id,
                "symbol": symbol,
                "duration": duration,
                "barSize": bar_size
            }
            
        except Exception as e:
            logger.error(f"Failed to stream historical data for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def get_market_depth(
        self,
        symbol: str,
        num_rows: int = 5
    ) -> Dict[str, Any]:
        """
        Get Level II market depth data
        
        Args:
            symbol: The ticker symbol
            num_rows: Number of depth levels to retrieve
            
        Returns:
            Market depth data
        """
        try:
            contract = await self.ibkr_service.create_contract(symbol)
            
            # Request market depth
            depth_ticker = self.ibkr_service.ib.reqMktDepth(
                contract=contract,
                numRows=num_rows
            )
            
            # Format depth data
            bids = []
            asks = []
            
            # Since this is a Ticker object and not a depth object with bids/asks lists,
            # we need to extract depth information differently
            if hasattr(depth_ticker, 'domBids') and depth_ticker.domBids:
                for i, dom_bid in enumerate(depth_ticker.domBids[:num_rows]):
                    bids.append({
                        "price": dom_bid.price if hasattr(dom_bid, 'price') else None,
                        "size": dom_bid.size if hasattr(dom_bid, 'size') else None,
                        "marketMaker": dom_bid.marketMaker if hasattr(dom_bid, 'marketMaker') else None
                    })
            
            if hasattr(depth_ticker, 'domAsks') and depth_ticker.domAsks:
                for i, dom_ask in enumerate(depth_ticker.domAsks[:num_rows]):
                    asks.append({
                        "price": dom_ask.price if hasattr(dom_ask, 'price') else None,
                        "size": dom_ask.size if hasattr(dom_ask, 'size') else None,
                        "marketMaker": dom_ask.marketMaker if hasattr(dom_ask, 'marketMaker') else None
                    })
            
            return {
                "status": "success",
                "symbol": symbol,
                "bids": bids,
                "asks": asks,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get market depth for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def get_option_chain(
        self,
        underlying_symbol: str,
        exchange: str = "SMART"
    ) -> Dict[str, Any]:
        """
        Get option chain for an underlying symbol
        
        Args:
            underlying_symbol: The underlying ticker symbol
            exchange: Exchange to query
            
        Returns:
            Option chain data
        """
        try:
            # Create underlying contract
            underlying = await self.ibkr_service.create_contract(
                underlying_symbol,
                exchange=exchange
            )
            
            # Request option chain
            chains = await self.ibkr_service.ib.reqSecDefOptParamsAsync(
                underlyingSymbol=underlying_symbol,
                futFopExchange="",
                underlyingSecType=underlying.secType,
                underlyingConId=underlying.conId
            )
            
            # Format option chain data
            option_chains = []
            for chain in chains:
                option_chains.append({
                    "exchange": chain.exchange,
                    "underlyingConId": chain.underlyingConId,
                    "tradingClass": chain.tradingClass,
                    "multiplier": chain.multiplier,
                    "expirations": chain.expirations,
                    "strikes": chain.strikes
                })
            
            return {
                "status": "success",
                "underlying": underlying_symbol,
                "chains": option_chains
            }
            
        except Exception as e:
            logger.error(f"Failed to get option chain for {underlying_symbol}: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def _stream_market_data(self, subscription_id: str):
        """Internal method to stream market data"""
        subscription = self._subscriptions.get(subscription_id)
        if not subscription:
            return
        
        try:
            while subscription.active:
                # Get ticker from cache
                if subscription.req_id is None:
                    await asyncio.sleep(0.1)
                    continue

                ticker = self._ticker_cache.get(subscription.req_id)
                if not ticker:
                    await asyncio.sleep(0.1)
                    continue
                
                # Create snapshot from ticker data
                snapshot = self._create_snapshot_from_ticker(
                    subscription.symbol,
                    ticker
                )
                
                # Update last update time
                subscription.last_update = datetime.now()
                
                # Call callbacks
                if subscription.callback:
                    try:
                        subscription.callback(snapshot)
                    except Exception as e:
                        logger.error(f"Callback error for {subscription_id}: {str(e)}")
                        subscription.error_count += 1
                
                # Check error threshold
                if subscription.error_count > 10:
                    logger.error(f"Too many errors for {subscription_id}, stopping stream")
                    subscription.active = False
                    break
                
                # Small delay to prevent overwhelming
                await asyncio.sleep(0.1)
                
        except asyncio.CancelledError:
            logger.info(f"Stream cancelled for {subscription_id}")
        except Exception as e:
            logger.error(f"Stream error for {subscription_id}: {str(e)}")
        finally:
            subscription.active = False
    
    async def _handle_historical_updates(
        self,
        bars: BarDataList,
        callback: Callable[[List[Dict]], None]
    ):
        """Handle streaming historical data updates"""
        try:
            while True:
                # Check for new bars
                if bars:
                    # Format bars
                    formatted_bars = []
                    for bar in bars:
                        formatted_bars.append({
                            "date": bar.date,
                            "open": bar.open,
                            "high": bar.high,
                            "low": bar.low,
                            "close": bar.close,
                            "volume": bar.volume,
                            "average": bar.average,
                            "barCount": bar.barCount
                        })
                    
                    # Call callback with formatted data
                    if callback:
                        callback(formatted_bars)
                
                # Wait for updates
                await asyncio.sleep(1)
                
        except asyncio.CancelledError:
            logger.info("Historical data stream cancelled")
        except Exception as e:
            logger.error(f"Historical stream error: {str(e)}")
    
    def _create_snapshot_from_ticker(
        self,
        symbol: str,
        ticker: Any
    ) -> MarketDataSnapshot:
        """Create market data snapshot from ticker"""
        # Helper function to safely convert to int or keep as float if needed
        def safe_int_convert(value):
            if value is None:
                return None
            try:
                return int(value)
            except (ValueError, TypeError):
                return value
        
        return MarketDataSnapshot(
            symbol=symbol,
            timestamp=datetime.now(),
            bid=ticker.bid if hasattr(ticker, 'bid') else None,
            bid_size=safe_int_convert(ticker.bidSize if hasattr(ticker, 'bidSize') else None),
            ask=ticker.ask if hasattr(ticker, 'ask') else None,
            ask_size=safe_int_convert(ticker.askSize if hasattr(ticker, 'askSize') else None),
            last=ticker.last if hasattr(ticker, 'last') else None,
            last_size=safe_int_convert(ticker.lastSize if hasattr(ticker, 'lastSize') else None),
            volume=safe_int_convert(ticker.volume if hasattr(ticker, 'volume') else None),
            high=ticker.high if hasattr(ticker, 'high') else None,
            low=ticker.low if hasattr(ticker, 'low') else None,
            close=ticker.close if hasattr(ticker, 'close') else None,
            open=ticker.open if hasattr(ticker, 'open') else None,
            vwap=ticker.vwap if hasattr(ticker, 'vwap') else None
        )
    
    def _get_next_req_id(self) -> int:
        """Get next request ID"""
        self._req_id_counter += 1
        return self._req_id_counter
    
    async def get_active_subscriptions(self) -> List[Dict[str, Any]]:
        """Get list of active subscriptions"""
        active = []
        for sub_id, subscription in self._subscriptions.items():
            active.append({
                "subscription_id": sub_id,
                "symbol": subscription.symbol,
                "data_types": [dt.value for dt in subscription.data_types],
                "active": subscription.active,
                "last_update": subscription.last_update.isoformat() if subscription.last_update else None,
                "error_count": subscription.error_count
            })
        return active
    
    async def cleanup(self):
        """Clean up all subscriptions and resources"""
        # Cancel all streaming tasks
        for task in self._streaming_tasks.values():
            task.cancel()
        
        # Wait for tasks to complete
        if self._streaming_tasks:
            await asyncio.gather(
                *self._streaming_tasks.values(),
                return_exceptions=True
            )
        
        # Clear all data
        self._subscriptions.clear()
        self._ticker_cache.clear()
        self._callbacks.clear()
        self._streaming_tasks.clear()
        
        logger.info("Market data streaming service cleaned up")
