"""
Portfolio Rebalancing Service

This service provides automated portfolio rebalancing capabilities with
support for multiple rebalancing strategies and risk management.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
from dataclasses import dataclass, field
import numpy as np

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from services.ibkr_service import IBKRService
    from services.order_management_service import OrderManagementService
    from services.algo.algorithmic_trading_service import AlgorithmicTradingService

logger = logging.getLogger('portfolio-rebalancing')


class RebalancingStrategy(Enum):
    """Portfolio rebalancing strategies"""
    CALENDAR = "calendar"  # Time-based rebalancing
    THRESHOLD = "threshold"  # Deviation-based rebalancing
    TACTICAL = "tactical"  # Market conditions-based
    CONSTANT_PROPORTION = "constant_proportion"  # CPPI strategy
    RISK_PARITY = "risk_parity"  # Equal risk contribution
    MINIMUM_VARIANCE = "minimum_variance"  # Minimize portfolio variance
    MAXIMUM_SHARPE = "maximum_sharpe"  # Maximize Sharpe ratio
    BLACK_LITTERMAN = "black_litterman"  # Black-Litterman optimization


@dataclass
class AssetAllocation:
    """Target allocation for an asset"""
    symbol: str
    target_weight: Decimal
    min_weight: Optional[Decimal] = None
    max_weight: Optional[Decimal] = None
    asset_class: Optional[str] = None
    constraints: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RebalancingConfig:
    """Configuration for portfolio rebalancing"""
    strategy: RebalancingStrategy
    target_allocations: List[AssetAllocation]
    account: str
    rebalance_threshold: Decimal = Decimal("0.05")  # 5% deviation
    min_trade_size: Decimal = Decimal("100")  # Minimum trade value
    max_trade_size: Optional[Decimal] = None
    frequency: Optional[str] = None  # For calendar rebalancing
    risk_limit: Optional[Decimal] = None
    transaction_cost: Decimal = Decimal("0.001")  # 0.1% transaction cost
    tax_aware: bool = False
    custom_params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RebalancingResult:
    """Result of portfolio rebalancing"""
    timestamp: datetime
    strategy: RebalancingStrategy
    trades_executed: List[Dict[str, Any]]
    pre_rebalance_weights: Dict[str, Decimal]
    post_rebalance_weights: Dict[str, Decimal]
    total_value: Decimal
    transaction_costs: Decimal
    tracking_error: Optional[Decimal] = None
    metrics: Dict[str, Any] = field(default_factory=dict)


class PortfolioRebalancingService:
    """
    Automated portfolio rebalancing service
    
    Provides sophisticated portfolio rebalancing with multiple strategies,
    risk management, and tax-aware optimization.
    """
    
    def __init__(self, ibkr_service: "IBKRService", oms: "OrderManagementService",
                 algo_service: "AlgorithmicTradingService"):
        self.ibkr_service = ibkr_service
        self.oms = oms
        self.algo_service = algo_service
        self._rebalancing_tasks: Dict[str, asyncio.Task] = {}
        self._rebalancing_history: List[RebalancingResult] = []
        
    async def rebalance_portfolio(
        self,
        config: RebalancingConfig,
        force: bool = False
    ) -> RebalancingResult:
        """
        Rebalance portfolio according to configuration
        
        Args:
            config: Rebalancing configuration
            force: Force rebalancing even if within threshold
            
        Returns:
            RebalancingResult with execution details
        """
        try:
            # Get current portfolio
            portfolio = await self._get_portfolio_positions(config.account)
            
            # Calculate current weights
            current_weights = await self._calculate_current_weights(portfolio)
            
            # Check if rebalancing is needed
            if not force and not self._needs_rebalancing(current_weights, config):
                logger.info("Portfolio within threshold, no rebalancing needed")
                return RebalancingResult(
                    timestamp=datetime.now(),
                    strategy=config.strategy,
                    trades_executed=[],
                    pre_rebalance_weights=current_weights,
                    post_rebalance_weights=current_weights,
                    total_value=Decimal(str(sum(p["marketValue"] for p in portfolio.values()))),
                    transaction_costs=Decimal("0"),
                    tracking_error=None,
                    metrics={}
                )
            
            # Calculate target trades based on strategy
            trades = await self._calculate_rebalancing_trades(
                portfolio, current_weights, config
            )
            
            # Execute trades
            executed_trades = await self._execute_rebalancing_trades(trades, config)
            
            # Get post-rebalance portfolio
            await asyncio.sleep(2)  # Allow time for trades to settle
            post_portfolio = await self._get_portfolio_positions(config.account)
            post_weights = await self._calculate_current_weights(post_portfolio)
            
            # Calculate metrics
            total_value = Decimal(str(sum(p["marketValue"] for p in post_portfolio.values())))
            transaction_costs = self._calculate_transaction_costs(executed_trades, config)
            
            result = RebalancingResult(
                timestamp=datetime.now(),
                strategy=config.strategy,
                trades_executed=executed_trades,
                pre_rebalance_weights=current_weights,
                post_rebalance_weights=post_weights,
                total_value=total_value,
                transaction_costs=transaction_costs,
                tracking_error=None,
                metrics=self._calculate_rebalancing_metrics(
                    current_weights, post_weights, config
                )
            )
            
            # Store in history
            self._rebalancing_history.append(result)
            
            return result
            
        except Exception as e:
            logger.error(f"Portfolio rebalancing error: {str(e)}")
            raise
    
    async def schedule_rebalancing(
        self,
        config: RebalancingConfig,
        start_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Schedule automated portfolio rebalancing
        
        Args:
            config: Rebalancing configuration
            start_time: When to start rebalancing schedule
            
        Returns:
            Schedule details
        """
        schedule_id = f"rebal_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            # Create rebalancing task
            task = asyncio.create_task(
                self._run_scheduled_rebalancing(schedule_id, config, start_time)
            )
            self._rebalancing_tasks[schedule_id] = task
            
            return {
                "status": "success",
                "schedule_id": schedule_id,
                "strategy": config.strategy.value,
                "frequency": config.frequency,
                "start_time": start_time.isoformat() if start_time else "immediate"
            }
            
        except Exception as e:
            logger.error(f"Failed to schedule rebalancing: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def stop_scheduled_rebalancing(self, schedule_id: str) -> Dict[str, Any]:
        """Stop scheduled rebalancing"""
        if schedule_id in self._rebalancing_tasks:
            task = self._rebalancing_tasks[schedule_id]
            task.cancel()
            
            try:
                await task
            except asyncio.CancelledError:
                logger.info(f"Rebalancing schedule {schedule_id} cancelled")
            
            del self._rebalancing_tasks[schedule_id]
            
            return {
                "status": "success",
                "message": f"Stopped rebalancing schedule {schedule_id}"
            }
        else:
            return {
                "status": "error",
                "message": f"Schedule {schedule_id} not found"
            }
    
    async def optimize_portfolio(
        self,
        symbols: List[str],
        optimization_method: str = "maximum_sharpe",
        constraints: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Optimize portfolio allocation
        
        Args:
            symbols: List of symbols to include
            optimization_method: Optimization method to use
            constraints: Portfolio constraints
            
        Returns:
            Optimal allocations
        """
        try:
            # Get historical data for symbols
            returns_data = {}
            for symbol in symbols:
                hist_data = await self.ibkr_service.get_historical_data(symbol, duration="1 Y")
                if hist_data:
                    returns = self._calculate_returns(hist_data)
                    returns_data[symbol] = returns
            
            # Calculate optimal weights based on method
            if optimization_method == "maximum_sharpe":
                weights = self._optimize_sharpe_ratio(returns_data, constraints)
            elif optimization_method == "minimum_variance":
                weights = self._optimize_minimum_variance(returns_data, constraints)
            elif optimization_method == "risk_parity":
                weights = self._optimize_risk_parity(returns_data, constraints)
            else:
                raise ValueError(f"Unknown optimization method: {optimization_method}")
            
            # Create allocations
            allocations = []
            for symbol, weight in weights.items():
                allocations.append({
                    "symbol": symbol,
                    "weight": float(weight),
                    "percentage": float(weight * 100)
                })
            
            return {
                "status": "success",
                "method": optimization_method,
                "allocations": allocations,
                "metrics": self._calculate_portfolio_metrics(weights, returns_data)
            }
            
        except Exception as e:
            logger.error(f"Portfolio optimization error: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def analyze_rebalancing_impact(
        self,
        config: RebalancingConfig
    ) -> Dict[str, Any]:
        """
        Analyze the impact of rebalancing before execution
        
        Args:
            config: Rebalancing configuration
            
        Returns:
            Analysis results including costs, risks, and expected outcomes
        """
        try:
            # Get current portfolio
            portfolio = await self._get_portfolio_positions(config.account)
            current_weights = await self._calculate_current_weights(portfolio)
            
            # Calculate proposed trades
            trades = await self._calculate_rebalancing_trades(
                portfolio, current_weights, config
            )
            
            # Estimate costs
            estimated_costs = self._estimate_transaction_costs(trades, config)
            
            # Calculate risk metrics
            risk_metrics = await self._calculate_risk_metrics(
                current_weights, config.target_allocations
            )
            
            return {
                "status": "success",
                "current_weights": {k: float(v) for k, v in current_weights.items()},
                "target_weights": {
                    a.symbol: float(a.target_weight) 
                    for a in config.target_allocations
                },
                "proposed_trades": trades,
                "estimated_costs": float(estimated_costs),
                "risk_metrics": risk_metrics,
                "needs_rebalancing": self._needs_rebalancing(current_weights, config)
            }
            
        except Exception as e:
            logger.error(f"Rebalancing analysis error: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    # Private helper methods
    
    async def _get_portfolio_positions(self, account: str) -> Dict[str, Dict]:
        """Get current portfolio positions"""
        portfolio_data = await self.ibkr_service.get_portfolio()
        
        # Convert to dictionary by symbol
        positions = {}
        for position in portfolio_data:
            symbol = position.get("symbol")
            if symbol:
                positions[symbol] = position
        
        return positions
    
    async def _calculate_current_weights(
        self,
        portfolio: Dict[str, Dict]
    ) -> Dict[str, Decimal]:
        """Calculate current portfolio weights"""
        total_value = sum(
            Decimal(str(p.get("marketValue", 0))) 
            for p in portfolio.values()
        )
        
        weights = {}
        if total_value > 0:
            for symbol, position in portfolio.items():
                market_value = Decimal(str(position.get("marketValue", 0)))
                weights[symbol] = market_value / total_value
        
        return weights
    
    def _needs_rebalancing(
        self,
        current_weights: Dict[str, Decimal],
        config: RebalancingConfig
    ) -> bool:
        """Check if portfolio needs rebalancing"""
        # Create target weights dict
        target_weights = {
            a.symbol: a.target_weight 
            for a in config.target_allocations
        }
        
        # Check each position
        for symbol, target_weight in target_weights.items():
            current_weight = current_weights.get(symbol, Decimal("0"))
            deviation = abs(current_weight - target_weight)
            
            # Check if deviation exceeds threshold
            if deviation > config.rebalance_threshold:
                return True
        
        # Check for positions that should be sold
        for symbol, current_weight in current_weights.items():
            if symbol not in target_weights and current_weight > 0:
                return True
        
        return False
    
    async def _calculate_rebalancing_trades(
        self,
        portfolio: Dict[str, Dict],
        current_weights: Dict[str, Decimal],
        config: RebalancingConfig
    ) -> List[Dict[str, Any]]:
        """Calculate trades needed for rebalancing"""
        trades = []
        
        # Get total portfolio value
        total_value = sum(
            Decimal(str(p.get("marketValue", 0))) 
            for p in portfolio.values()
        )
        
        # Calculate trades for each target allocation
        for allocation in config.target_allocations:
            symbol = allocation.symbol
            target_weight = allocation.target_weight
            current_weight = current_weights.get(symbol, Decimal("0"))
            
            # Calculate weight difference
            weight_diff = target_weight - current_weight
            
            # Skip if within threshold
            if abs(weight_diff) < config.rebalance_threshold / 2:
                continue
            
            # Calculate trade value
            trade_value = weight_diff * total_value
            
            # Skip if below minimum trade size
            if abs(trade_value) < config.min_trade_size:
                continue
            
            # Apply max trade size limit
            if config.max_trade_size and abs(trade_value) > config.max_trade_size:
                trade_value = config.max_trade_size if trade_value > 0 else -config.max_trade_size
            
            # Get current price
            market_data = await self.ibkr_service.get_market_data(symbol)
            if market_data["status"] == "success":
                current_price = market_data["data"]["data"].get("last", 0)
                
                if current_price > 0:
                    # Calculate quantity
                    quantity = int(trade_value / Decimal(str(current_price)))
                    
                    if quantity != 0:
                        trades.append({
                            "symbol": symbol,
                            "action": "BUY" if quantity > 0 else "SELL",
                            "quantity": abs(quantity),
                            "price": current_price,
                            "value": float(abs(trade_value)),
                            "weight_diff": float(weight_diff)
                        })
        
        # Handle positions to be sold completely
        for symbol, position in portfolio.items():
            if symbol not in [a.symbol for a in config.target_allocations]:
                position_size = position.get("position", 0)
                if position_size > 0:
                    market_data = await self.ibkr_service.get_market_data(symbol)
                    if market_data["status"] == "success":
                        current_price = market_data["data"]["data"].get("last", 0)
                        
                        trades.append({
                            "symbol": symbol,
                            "action": "SELL",
                            "quantity": abs(position_size),
                            "price": current_price,
                            "value": float(abs(position_size * current_price)),
                            "weight_diff": float(-current_weights.get(symbol, 0))
                        })
        
        return trades
    
    async def _execute_rebalancing_trades(
        self,
        trades: List[Dict[str, Any]],
        config: RebalancingConfig
    ) -> List[Dict[str, Any]]:
        """Execute rebalancing trades"""
        executed_trades = []
        
        # Sort trades - sells first to free up capital
        sorted_trades = sorted(trades, key=lambda x: x["action"], reverse=True)
        
        for trade in sorted_trades:
            try:
                # Use adaptive order for better execution
                order_result = await self.ibkr_service.place_adaptive_order(
                    symbol=trade["symbol"],
                    action=trade["action"],
                    quantity=trade["quantity"],
                    price=trade["price"] * (1.001 if trade["action"] == "BUY" else 0.999),
                    account=config.account,
                    priority="Patient"  # Use patient priority for rebalancing
                )
                
                if order_result["status"] == "success":
                    executed_trades.append({
                        **trade,
                        "order_id": order_result["orderId"],
                        "status": "submitted"
                    })
                else:
                    logger.error(f"Failed to place order for {trade['symbol']}: {order_result}")
                    
            except Exception as e:
                logger.error(f"Error executing trade for {trade['symbol']}: {str(e)}")
        
        return executed_trades
    
    async def _run_scheduled_rebalancing(
        self,
        schedule_id: str,
        config: RebalancingConfig,
        start_time: Optional[datetime]
    ):
        """Run scheduled rebalancing"""
        try:
            # Wait for start time if specified
            if start_time and datetime.now() < start_time:
                wait_seconds = (start_time - datetime.now()).total_seconds()
                await asyncio.sleep(wait_seconds)
            
            while True:
                # Execute rebalancing
                result = await self.rebalance_portfolio(config)
                logger.info(f"Scheduled rebalancing {schedule_id} completed: "
                          f"{len(result.trades_executed)} trades executed")
                
                # Calculate next rebalancing time
                if config.frequency == "daily":
                    next_run = datetime.now() + timedelta(days=1)
                elif config.frequency == "weekly":
                    next_run = datetime.now() + timedelta(weeks=1)
                elif config.frequency == "monthly":
                    next_run = datetime.now() + timedelta(days=30)
                elif config.frequency == "quarterly":
                    next_run = datetime.now() + timedelta(days=90)
                else:
                    # Default to monthly
                    next_run = datetime.now() + timedelta(days=30)
                
                # Wait until next run
                wait_seconds = (next_run - datetime.now()).total_seconds()
                await asyncio.sleep(wait_seconds)
                
        except asyncio.CancelledError:
            logger.info(f"Scheduled rebalancing {schedule_id} cancelled")
        except Exception as e:
            logger.error(f"Scheduled rebalancing error: {str(e)}")
    
    def _calculate_returns(self, historical_data: List[Dict]) -> np.ndarray:
        """Calculate returns from historical data"""
        prices = [bar.get("close", 0) for bar in historical_data]
        returns = []
        
        for i in range(1, len(prices)):
            if prices[i-1] > 0:
                ret = (prices[i] - prices[i-1]) / prices[i-1]
                returns.append(ret)
        
        return np.array(returns)
    
    def _optimize_sharpe_ratio(
        self,
        returns_data: Dict[str, np.ndarray],
        constraints: Optional[Dict[str, Any]]
    ) -> Dict[str, Decimal]:
        """Optimize for maximum Sharpe ratio"""
        # Simplified implementation - would use scipy.optimize in production
        n_assets = len(returns_data)
        equal_weight = Decimal("1") / Decimal(str(n_assets))
        
        return {
            symbol: equal_weight 
            for symbol in returns_data.keys()
        }
    
    def _optimize_minimum_variance(
        self,
        returns_data: Dict[str, np.ndarray],
        constraints: Optional[Dict[str, Any]]
    ) -> Dict[str, Decimal]:
        """Optimize for minimum variance"""
        # Simplified implementation
        n_assets = len(returns_data)
        equal_weight = Decimal("1") / Decimal(str(n_assets))
        
        return {
            symbol: equal_weight 
            for symbol in returns_data.keys()
        }
    
    def _optimize_risk_parity(
        self,
        returns_data: Dict[str, np.ndarray],
        constraints: Optional[Dict[str, Any]]
    ) -> Dict[str, Decimal]:
        """Optimize for equal risk contribution"""
        # Simplified implementation
        n_assets = len(returns_data)
        equal_weight = Decimal("1") / Decimal(str(n_assets))
        
        return {
            symbol: equal_weight 
            for symbol in returns_data.keys()
        }
    
    def _calculate_transaction_costs(
        self,
        executed_trades: List[Dict[str, Any]],
        config: RebalancingConfig
    ) -> Decimal:
        """Calculate total transaction costs"""
        total_cost = Decimal("0")
        
        for trade in executed_trades:
            trade_value = Decimal(str(trade.get("value", 0)))
            total_cost += trade_value * config.transaction_cost
        
        return total_cost
    
    def _estimate_transaction_costs(
        self,
        trades: List[Dict[str, Any]],
        config: RebalancingConfig
    ) -> Decimal:
        """Estimate transaction costs before execution"""
        return self._calculate_transaction_costs(trades, config)
    
    def _calculate_rebalancing_metrics(
        self,
        pre_weights: Dict[str, Decimal],
        post_weights: Dict[str, Decimal],
        config: RebalancingConfig
    ) -> Dict[str, Any]:
        """Calculate rebalancing metrics"""
        # Calculate tracking error
        target_weights = {
            a.symbol: a.target_weight 
            for a in config.target_allocations
        }
        
        tracking_error = Decimal("0")
        for symbol, target_weight in target_weights.items():
            post_weight = post_weights.get(symbol, Decimal("0"))
            tracking_error += (post_weight - target_weight) ** 2
        
        tracking_error = tracking_error.sqrt() if tracking_error > 0 else Decimal("0")
        
        return {
            "tracking_error": float(tracking_error),
            "turnover": float(sum(
                abs(post_weights.get(s, Decimal("0")) - pre_weights.get(s, Decimal("0")))
                for s in set(list(pre_weights.keys()) + list(post_weights.keys()))
            )),
            "positions_changed": len([
                s for s in set(list(pre_weights.keys()) + list(post_weights.keys()))
                if pre_weights.get(s, Decimal("0")) != post_weights.get(s, Decimal("0"))
            ])
        }
    
    def _calculate_portfolio_metrics(
        self,
        weights: Dict[str, Decimal],
        returns_data: Dict[str, np.ndarray]
    ) -> Dict[str, Any]:
        """Calculate portfolio performance metrics"""
        # Simplified metrics calculation
        return {
            "expected_return": 0.08,  # Placeholder
            "volatility": 0.15,  # Placeholder
            "sharpe_ratio": 0.53,  # Placeholder
            "max_drawdown": -0.20  # Placeholder
        }
    
    async def _calculate_risk_metrics(
        self,
        current_weights: Dict[str, Decimal],
        target_allocations: List[AssetAllocation]
    ) -> Dict[str, Any]:
        """Calculate risk metrics for rebalancing"""
        return {
            "portfolio_volatility": 0.15,  # Placeholder
            "expected_tracking_error": 0.02,  # Placeholder
            "concentration_risk": max(current_weights.values()) if current_weights else 0,
            "diversification_ratio": len(current_weights)
        }
