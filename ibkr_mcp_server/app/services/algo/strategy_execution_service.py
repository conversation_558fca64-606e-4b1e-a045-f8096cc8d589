"""
Strategy Execution Service

This service provides a framework for executing complex trading strategies
with proper lifecycle management, risk controls, and performance tracking.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable, Type
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
from dataclasses import dataclass, field
from abc import ABC, abstractmethod

from services.ibkr_service import IBKRService
from services.order_management_service import OrderManagementService
from services.algo.algorithmic_trading_service import AlgorithmicTradingService
from services.algo.market_data_streaming_service import MarketDataStreamingService

logger = logging.getLogger('strategy-execution')


class StrategyState(Enum):
    """Strategy lifecycle states"""
    INITIALIZED = "initialized"
    STARTING = "starting"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class StrategyContext:
    """Context passed to strategy implementations"""
    strategy_id: str
    account: str
    symbols: List[str]
    parameters: Dict[str, Any]
    risk_limits: Dict[str, Decimal]
    start_time: datetime
    ibkr_service: IBKRService
    oms: OrderManagementService
    algo_service: AlgorithmicTradingService
    market_data_service: MarketDataStreamingService


@dataclass
class StrategyPerformance:
    """Strategy performance metrics"""
    total_pnl: Decimal
    realized_pnl: Decimal
    unrealized_pnl: Decimal
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_win: Decimal
    avg_loss: Decimal
    max_drawdown: Decimal
    sharpe_ratio: float
    win_rate: float
    profit_factor: Decimal
    metrics: Dict[str, Any] = field(default_factory=dict)


class TradingStrategy(ABC):
    """Base class for all trading strategies"""
    
    def __init__(self, context: StrategyContext):
        self.context = context
        self.state = StrategyState.INITIALIZED
        self._positions: Dict[str, Dict] = {}
        self._orders: List[int] = []
        self._performance = StrategyPerformance(
            total_pnl=Decimal("0"),
            realized_pnl=Decimal("0"),
            unrealized_pnl=Decimal("0"),
            total_trades=0,
            winning_trades=0,
            losing_trades=0,
            avg_win=Decimal("0"),
            avg_loss=Decimal("0"),
            max_drawdown=Decimal("0"),
            sharpe_ratio=0.0,
            win_rate=0.0,
            profit_factor=Decimal("0")
        )
    
    @abstractmethod
    async def on_start(self):
        """Called when strategy starts"""
        pass
    
    @abstractmethod
    async def on_market_data(self, symbol: str, data: Dict[str, Any]):
        """Called on market data update"""
        pass
    
    @abstractmethod
    async def on_position_update(self, symbol: str, position: Dict[str, Any]):
        """Called on position update"""
        pass
    
    @abstractmethod
    async def on_order_update(self, order_id: int, status: Dict[str, Any]):
        """Called on order status update"""
        pass
    
    @abstractmethod
    async def on_stop(self):
        """Called when strategy stops"""
        pass
    
    async def place_order(
        self,
        symbol: str,
        action: str,
        quantity: int,
        order_type: str = "LMT",
        price: Optional[float] = None
    ) -> Optional[int]:
        """Place an order with risk checks"""
        # Check risk limits
        if not self._check_risk_limits(symbol, action, quantity, price):
            logger.warning(f"Order rejected by risk limits: {symbol} {action} {quantity}")
            return None

        # Place order
        try:
            if order_type == "LMT" and price:
                # Use adaptive order for limit orders
                result = await self.context.ibkr_service.place_adaptive_order(
                    symbol=symbol,
                    action=action,
                    quantity=quantity,
                    price=price,
                    account=self.context.account
                )
            elif order_type == "MKT":
                # Market order
                result = await self.context.ibkr_service.create_order(
                    order_type="MKT",
                    symbol=symbol,
                    action=action,
                    quantity=quantity,
                    account=self.context.account
                )
            else:
                # Use create_order for other order types
                result = await self.context.ibkr_service.create_order(
                    order_type=order_type,
                    symbol=symbol,
                    action=action,
                    quantity=quantity,
                    account=self.context.account,
                    price=price
                )

            if result["status"] == "success":
                order_id = result["orderId"]
                self._orders.append(order_id)
                return order_id

        except Exception as e:
            logger.error(f"Error placing order: {str(e)}")

        return None
    
    def _check_risk_limits(
        self,
        symbol: str,
        action: str,
        quantity: int,
        price: Optional[float]
    ) -> bool:
        """Check if order passes risk limits"""
        # Check max position size
        max_position = self.context.risk_limits.get("max_position_size", Decimal("100000"))
        if price:
            order_value = Decimal(str(quantity * price))
            if order_value > max_position:
                return False
        
        # Check max loss
        max_loss = self.context.risk_limits.get("max_loss", Decimal("10000"))
        if self._performance.total_pnl < -max_loss:
            return False
        
        # Check max orders
        max_orders = self.context.risk_limits.get("max_orders", 100)
        if len(self._orders) >= max_orders:
            return False
        
        return True
    
    def update_performance(self, trade_result: Dict[str, Any]):
        """Update performance metrics"""
        pnl = Decimal(str(trade_result.get("pnl", 0)))
        
        self._performance.total_trades += 1
        self._performance.total_pnl += pnl
        
        if pnl > 0:
            self._performance.winning_trades += 1
            self._performance.avg_win = (
                (self._performance.avg_win * (self._performance.winning_trades - 1) + pnl) /
                self._performance.winning_trades
            )
        else:
            self._performance.losing_trades += 1
            self._performance.avg_loss = (
                (self._performance.avg_loss * (self._performance.losing_trades - 1) + abs(pnl)) /
                self._performance.losing_trades
            )
        
        # Update other metrics
        if self._performance.total_trades > 0:
            self._performance.win_rate = (
                self._performance.winning_trades / self._performance.total_trades
            )
        
        if self._performance.avg_loss > 0:
            self._performance.profit_factor = (
                self._performance.avg_win / self._performance.avg_loss
            )


class StrategyExecutionService:
    """
    Strategy execution service
    
    Manages the lifecycle of trading strategies, handles execution,
    and tracks performance.
    """
    
    def __init__(
        self,
        ibkr_service: IBKRService,
        oms: OrderManagementService,
        algo_service: AlgorithmicTradingService,
        market_data_service: MarketDataStreamingService
    ):
        self.ibkr_service = ibkr_service
        self.oms = oms
        self.algo_service = algo_service
        self.market_data_service = market_data_service
        self._strategies: Dict[str, TradingStrategy] = {}
        self._strategy_tasks: Dict[str, asyncio.Task] = {}
        self._strategy_registry: Dict[str, Type[TradingStrategy]] = {}
        
        # Register built-in strategies
        self._register_builtin_strategies()
    
    def register_strategy(self, name: str, strategy_class: Type[TradingStrategy]):
        """Register a strategy class"""
        self._strategy_registry[name] = strategy_class
        logger.info(f"Registered strategy: {name}")
    
    async def start_strategy(
        self,
        strategy_name: str,
        strategy_id: str,
        account: str,
        symbols: List[str],
        parameters: Dict[str, Any],
        risk_limits: Dict[str, Decimal]
    ) -> Dict[str, Any]:
        """Start a trading strategy"""
        try:
            # Get strategy class
            strategy_class = self._strategy_registry.get(strategy_name)
            if not strategy_class:
                raise ValueError(f"Unknown strategy: {strategy_name}")
            
            # Create context
            context = StrategyContext(
                strategy_id=strategy_id,
                account=account,
                symbols=symbols,
                parameters=parameters,
                risk_limits=risk_limits,
                start_time=datetime.now(),
                ibkr_service=self.ibkr_service,
                oms=self.oms,
                algo_service=self.algo_service,
                market_data_service=self.market_data_service
            )
            
            # Create strategy instance
            strategy = strategy_class(context)
            self._strategies[strategy_id] = strategy
            
            # Start strategy task
            task = asyncio.create_task(self._run_strategy(strategy))
            self._strategy_tasks[strategy_id] = task
            
            logger.info(f"Started strategy: {strategy_id} ({strategy_name})")
            
            return {
                "status": "success",
                "strategy_id": strategy_id,
                "strategy_name": strategy_name,
                "state": strategy.state.value
            }
            
        except Exception as e:
            logger.error(f"Failed to start strategy: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def stop_strategy(self, strategy_id: str) -> Dict[str, Any]:
        """Stop a running strategy"""
        try:
            if strategy_id not in self._strategies:
                return {
                    "status": "error",
                    "message": f"Strategy {strategy_id} not found"
                }
            
            strategy = self._strategies[strategy_id]
            strategy.state = StrategyState.STOPPING
            
            # Cancel strategy task
            if strategy_id in self._strategy_tasks:
                task = self._strategy_tasks[strategy_id]
                task.cancel()
                
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
            # Call strategy cleanup
            await strategy.on_stop()
            
            # Remove from active strategies
            del self._strategies[strategy_id]
            if strategy_id in self._strategy_tasks:
                del self._strategy_tasks[strategy_id]
            
            logger.info(f"Stopped strategy: {strategy_id}")
            
            return {
                "status": "success",
                "strategy_id": strategy_id,
                "performance": self._format_performance(strategy._performance)
            }
            
        except Exception as e:
            logger.error(f"Failed to stop strategy: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def pause_strategy(self, strategy_id: str) -> Dict[str, Any]:
        """Pause a running strategy"""
        if strategy_id in self._strategies:
            strategy = self._strategies[strategy_id]
            strategy.state = StrategyState.PAUSED
            
            return {
                "status": "success",
                "strategy_id": strategy_id,
                "state": strategy.state.value
            }
        else:
            return {
                "status": "error",
                "message": f"Strategy {strategy_id} not found"
            }
    
    async def resume_strategy(self, strategy_id: str) -> Dict[str, Any]:
        """Resume a paused strategy"""
        if strategy_id in self._strategies:
            strategy = self._strategies[strategy_id]
            if strategy.state == StrategyState.PAUSED:
                strategy.state = StrategyState.RUNNING
                
                return {
                    "status": "success",
                    "strategy_id": strategy_id,
                    "state": strategy.state.value
                }
            else:
                return {
                    "status": "error",
                    "message": f"Strategy {strategy_id} is not paused"
                }
        else:
            return {
                "status": "error",
                "message": f"Strategy {strategy_id} not found"
            }
    
    async def get_strategy_status(self, strategy_id: str) -> Dict[str, Any]:
        """Get strategy status and performance"""
        if strategy_id in self._strategies:
            strategy = self._strategies[strategy_id]
            
            return {
                "status": "success",
                "strategy_id": strategy_id,
                "state": strategy.state.value,
                "symbols": strategy.context.symbols,
                "start_time": strategy.context.start_time.isoformat(),
                "positions": strategy._positions,
                "orders": len(strategy._orders),
                "performance": self._format_performance(strategy._performance)
            }
        else:
            return {
                "status": "error",
                "message": f"Strategy {strategy_id} not found"
            }
    
    async def get_active_strategies(self) -> List[Dict[str, Any]]:
        """Get list of active strategies"""
        active = []
        
        for strategy_id, strategy in self._strategies.items():
            active.append({
                "strategy_id": strategy_id,
                "state": strategy.state.value,
                "symbols": strategy.context.symbols,
                "start_time": strategy.context.start_time.isoformat(),
                "performance": self._format_performance(strategy._performance)
            })
        
        return active
    
    async def _run_strategy(self, strategy: TradingStrategy):
        """Run strategy execution loop"""
        try:
            # Initialize strategy
            strategy.state = StrategyState.STARTING
            await strategy.on_start()
            strategy.state = StrategyState.RUNNING
            
            # Subscribe to market data
            subscriptions = []
            for symbol in strategy.context.symbols:
                # Create a closure to capture the current symbol value
                def create_callback(current_symbol):
                    # Convert MarketDataSnapshot to Dict for on_market_data
                    def callback_handler(data):
                        # Convert MarketDataSnapshot to dictionary
                        data_dict = {}
                        if data:
                            data_dict = {
                                "symbol": data.symbol,
                                "timestamp": data.timestamp,
                                "bid": data.bid,
                                "bid_size": data.bid_size,
                                "ask": data.ask,
                                "ask_size": data.ask_size,
                                "last": data.last,
                                "last_size": data.last_size,
                                "volume": data.volume,
                                "high": data.high,
                                "low": data.low,
                                "close": data.close
                            }
                            # Create a task to handle the async call
                            asyncio.create_task(strategy.on_market_data(current_symbol, data_dict))
                    return callback_handler
                
                sub = await self.market_data_service.subscribe_market_data(
                    symbol=symbol,
                    data_types=[],  # Default data types
                    callback=create_callback(symbol)
                )
                if sub["status"] == "success":
                    subscriptions.append(sub["subscription_id"])
            
            # Main execution loop
            while strategy.state in [StrategyState.RUNNING, StrategyState.PAUSED]:
                if strategy.state == StrategyState.PAUSED:
                    await asyncio.sleep(1)
                    continue
                
                # Check positions
                portfolio = await self.ibkr_service.get_portfolio()
                for position in portfolio:
                    symbol = position.get("symbol")
                    if symbol in strategy.context.symbols:
                        await strategy.on_position_update(symbol, position)
                
                # Check orders
                for order_id in strategy._orders:
                    order_status = await self.oms.get_order_status(order_id)
                    if order_status["status"] == "success":
                        await strategy.on_order_update(order_id, order_status)
                
                # Sleep before next iteration
                await asyncio.sleep(1)
            
            # Cleanup subscriptions
            for sub_id in subscriptions:
                await self.market_data_service.unsubscribe_market_data(sub_id)
            
        except asyncio.CancelledError:
            logger.info(f"Strategy {strategy.context.strategy_id} cancelled")
        except Exception as e:
            logger.error(f"Strategy error: {str(e)}")
            strategy.state = StrategyState.ERROR
        finally:
            strategy.state = StrategyState.STOPPED
    
    def _register_builtin_strategies(self):
        """Register built-in strategy implementations"""
        # Register example strategies
        self.register_strategy("simple_momentum", SimpleMomentumStrategy)
        self.register_strategy("mean_reversion", SimpleMeanReversionStrategy)
        self.register_strategy("pairs_trading", SimplePairsTradingStrategy)
    
    def _format_performance(self, performance: StrategyPerformance) -> Dict[str, Any]:
        """Format performance metrics for output"""
        return {
            "total_pnl": float(performance.total_pnl),
            "realized_pnl": float(performance.realized_pnl),
            "unrealized_pnl": float(performance.unrealized_pnl),
            "total_trades": performance.total_trades,
            "winning_trades": performance.winning_trades,
            "losing_trades": performance.losing_trades,
            "win_rate": performance.win_rate,
            "avg_win": float(performance.avg_win),
            "avg_loss": float(performance.avg_loss),
            "profit_factor": performance.profit_factor,
            "max_drawdown": float(performance.max_drawdown),
            "sharpe_ratio": performance.sharpe_ratio
        }


# Example Strategy Implementations

class SimpleMomentumStrategy(TradingStrategy):
    """Simple momentum trading strategy"""
    
    async def on_start(self):
        """Initialize strategy"""
        self.lookback_period = self.context.parameters.get("lookback_period", 20)
        self.momentum_threshold = self.context.parameters.get("momentum_threshold", 0.02)
        self.position_size = self.context.parameters.get("position_size", 100)
        self.price_history = {symbol: [] for symbol in self.context.symbols}
        logger.info(f"Momentum strategy started with lookback={self.lookback_period}")
    
    async def on_market_data(self, symbol: str, data: Dict[str, Any]):
        """Process market data"""
        if self.state != StrategyState.RUNNING:
            return
        
        # Store price history
        if symbol in self.price_history:
            price = data.get("last")
            if price:
                self.price_history[symbol].append(price)
                
                # Keep only lookback period
                if len(self.price_history[symbol]) > self.lookback_period:
                    self.price_history[symbol].pop(0)
                
                # Check for signal
                if len(self.price_history[symbol]) >= self.lookback_period:
                    await self._check_momentum_signal(symbol)
    
    async def _check_momentum_signal(self, symbol: str):
        """Check for momentum signal"""
        prices = self.price_history[symbol]
        momentum = (prices[-1] - prices[0]) / prices[0]
        
        current_position = self._positions.get(symbol, {}).get("position", 0)
        
        if momentum > self.momentum_threshold and current_position <= 0:
            # Buy signal
            await self.place_order(
                symbol=symbol,
                action="BUY",
                quantity=self.position_size,
                order_type="LMT",
                price=prices[-1] * 1.001
            )
        elif momentum < -self.momentum_threshold and current_position >= 0:
            # Sell signal
            await self.place_order(
                symbol=symbol,
                action="SELL",
                quantity=self.position_size,
                order_type="LMT",
                price=prices[-1] * 0.999
            )
    
    async def on_position_update(self, symbol: str, position: Dict[str, Any]):
        """Update position tracking"""
        self._positions[symbol] = position
    
    async def on_order_update(self, order_id: int, status: Dict[str, Any]):
        """Handle order updates"""
        if status.get("order_status") == "FILLED":
            # Update performance
            self.update_performance({
                "pnl": 0  # Would calculate actual P&L
            })
    
    async def on_stop(self):
        """Clean up strategy"""
        logger.info(f"Momentum strategy stopped")


class SimpleMeanReversionStrategy(TradingStrategy):
    """Simple mean reversion strategy"""
    
    async def on_start(self):
        """Initialize strategy"""
        self.ma_period = self.context.parameters.get("ma_period", 20)
        self.std_multiplier = self.context.parameters.get("std_multiplier", 2.0)
        self.position_size = self.context.parameters.get("position_size", 100)
        self.price_history = {symbol: [] for symbol in self.context.symbols}
        logger.info(f"Mean reversion strategy started with MA period={self.ma_period}")
    
    async def on_market_data(self, symbol: str, data: Dict[str, Any]):
        """Process market data"""
        if self.state != StrategyState.RUNNING:
            return
        
        # Implementation similar to momentum but looking for reversion signals
        pass
    
    async def on_position_update(self, symbol: str, position: Dict[str, Any]):
        """Update position tracking"""
        self._positions[symbol] = position
    
    async def on_order_update(self, order_id: int, status: Dict[str, Any]):
        """Handle order updates"""
        pass
    
    async def on_stop(self):
        """Clean up strategy"""
        logger.info(f"Mean reversion strategy stopped")


class SimplePairsTradingStrategy(TradingStrategy):
    """Simple pairs trading strategy"""
    
    async def on_start(self):
        """Initialize strategy"""
        if len(self.context.symbols) < 2:
            raise ValueError("Pairs trading requires at least 2 symbols")
        
        self.pair = (self.context.symbols[0], self.context.symbols[1])
        self.lookback = self.context.parameters.get("lookback", 50)
        self.entry_threshold = self.context.parameters.get("entry_threshold", 2.0)
        self.position_size = self.context.parameters.get("position_size", 100)
        self.price_history = {symbol: [] for symbol in self.context.symbols}
        logger.info(f"Pairs trading strategy started with pair={self.pair}")
    
    async def on_market_data(self, symbol: str, data: Dict[str, Any]):
        """Process market data"""
        if self.state != StrategyState.RUNNING:
            return
        
        # Implementation for pairs trading signals
        pass
    
    async def on_position_update(self, symbol: str, position: Dict[str, Any]):
        """Update position tracking"""
        self._positions[symbol] = position
    
    async def on_order_update(self, order_id: int, status: Dict[str, Any]):
        """Handle order updates"""
        pass
    
    async def on_stop(self):
        """Clean up strategy"""
        logger.info(f"Pairs trading strategy stopped")
