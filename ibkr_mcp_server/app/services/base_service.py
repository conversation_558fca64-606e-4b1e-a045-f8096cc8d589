"""
Base Service Module for IBKR MCP Server

Provides abstract base classes and common functionality
for all service implementations.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from datetime import datetime, timezone
from contextlib import asynccontextmanager
from enum import Enum


class ServiceStatus(Enum):
    """Service status enumeration"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


class BaseService(ABC):
    """
    Abstract base class for all services in the IBKR MCP Server.
    
    Provides common functionality including:
    - Service lifecycle management
    - Health monitoring
    - Error handling
    - Logging
    - Configuration management
    """
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        """
        Initialize base service.
        
        Args:
            name: Service name for identification
            config: Service configuration dictionary
        """
        self.name = name
        self.config = config or {}
        self.status = ServiceStatus.STOPPED
        self.logger = logging.getLogger(f"{__name__}.{name}")
        
        # Service state
        self._start_time: Optional[datetime] = None
        self._stop_time: Optional[datetime] = None
        self._error_count = 0
        self._last_error: Optional[Exception] = None
        
        # Background tasks
        self._background_tasks: List[asyncio.Task] = []
        self._shutdown_event = asyncio.Event()
        
        self.logger.info(f"Service {self.name} initialized")
    
    @abstractmethod
    async def _start_service(self) -> None:
        """
        Service-specific startup logic.
        Must be implemented by subclasses.
        """
        pass
    
    @abstractmethod
    async def _stop_service(self) -> None:
        """
        Service-specific shutdown logic.
        Must be implemented by subclasses.
        """
        pass
    
    async def start(self) -> None:
        """Start the service"""
        if self.status != ServiceStatus.STOPPED:
            raise RuntimeError(f"Service {self.name} is not in STOPPED state")
        
        try:
            self.status = ServiceStatus.STARTING
            self._start_time = datetime.now(timezone.utc)
            self._shutdown_event.clear()
            
            self.logger.info(f"Starting service {self.name}")
            await self._start_service()
            
            self.status = ServiceStatus.RUNNING
            self.logger.info(f"Service {self.name} started successfully")
            
        except Exception as e:
            self.status = ServiceStatus.ERROR
            self._last_error = e
            self._error_count += 1
            self.logger.error(f"Failed to start service {self.name}: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the service"""
        if self.status not in [ServiceStatus.RUNNING, ServiceStatus.ERROR]:
            self.logger.warning(f"Service {self.name} is not running")
            return
        
        try:
            self.status = ServiceStatus.STOPPING
            self._shutdown_event.set()
            
            self.logger.info(f"Stopping service {self.name}")
            
            # Cancel background tasks
            for task in self._background_tasks:
                if not task.done():
                    task.cancel()
            
            # Wait for tasks to complete
            if self._background_tasks:
                await asyncio.gather(*self._background_tasks, return_exceptions=True)
            
            # Service-specific cleanup
            await self._stop_service()
            
            self.status = ServiceStatus.STOPPED
            self._stop_time = datetime.now(timezone.utc)
            self.logger.info(f"Service {self.name} stopped successfully")
            
        except Exception as e:
            self.status = ServiceStatus.ERROR
            self._last_error = e
            self._error_count += 1
            self.logger.error(f"Error stopping service {self.name}: {e}")
            raise
    
    async def restart(self) -> None:
        """Restart the service"""
        await self.stop()
        await self.start()
    
    def is_running(self) -> bool:
        """Check if service is running"""
        return self.status == ServiceStatus.RUNNING
    
    def is_healthy(self) -> bool:
        """
        Check if service is healthy.
        Can be overridden by subclasses for custom health checks.
        """
        return self.status == ServiceStatus.RUNNING
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive service status"""
        uptime = None
        if self._start_time and self.status == ServiceStatus.RUNNING:
            uptime = (datetime.now(timezone.utc) - self._start_time).total_seconds()
        
        return {
            "name": self.name,
            "status": self.status.value,
            "is_running": self.is_running(),
            "is_healthy": self.is_healthy(),
            "start_time": self._start_time.isoformat() if self._start_time else None,
            "stop_time": self._stop_time.isoformat() if self._stop_time else None,
            "uptime_seconds": uptime,
            "error_count": self._error_count,
            "last_error": str(self._last_error) if self._last_error else None,
            "background_tasks": len(self._background_tasks)
        }
    
    def add_background_task(self, coro) -> asyncio.Task:
        """Add a background task to the service"""
        task = asyncio.create_task(coro)
        self._background_tasks.append(task)
        
        # Clean up completed tasks
        self._background_tasks = [t for t in self._background_tasks if not t.done()]
        
        return task
    
    @asynccontextmanager
    async def error_handler(self, operation: str):
        """Context manager for error handling"""
        try:
            yield
        except Exception as e:
            self._error_count += 1
            self._last_error = e
            self.logger.error(f"Error in {operation}: {e}")
            raise
    
    async def wait_for_shutdown(self) -> None:
        """Wait for shutdown signal"""
        await self._shutdown_event.wait()
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        return self.config.get(key, default)
    
    def set_config(self, key: str, value: Any) -> None:
        """Set configuration value"""
        self.config[key] = value


class AsyncService(BaseService):
    """
    Base class for asynchronous services that run continuously.
    
    Provides additional functionality for services that need to
    run background loops or handle continuous processing.
    """
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self._main_loop_task: Optional[asyncio.Task] = None
    
    @abstractmethod
    async def _main_loop(self) -> None:
        """
        Main service loop.
        Must be implemented by subclasses.
        """
        pass
    
    async def _start_service(self) -> None:
        """Start the async service with main loop"""
        self._main_loop_task = self.add_background_task(self._main_loop())
    
    async def _stop_service(self) -> None:
        """Stop the async service"""
        if self._main_loop_task and not self._main_loop_task.done():
            self._main_loop_task.cancel()
            try:
                await self._main_loop_task
            except asyncio.CancelledError:
                pass


# Export base classes
__all__ = [
    'ServiceStatus',
    'BaseService',
    'AsyncService'
]
