import os
import time
import logging
import asyncio
import nest_asyncio

# Debug: ibkr_service.py module is being loaded/reloaded
from datetime import datetime
from typing import List, Dict, Optional, Any, Union, TYPE_CHECKING
from fastapi import HTTPException

# Use TYPE_CHECKING to avoid circular imports
if TYPE_CHECKING:
    from order_management_service import OrderManagementService

from decimal import Decimal
from dotenv import load_dotenv

# Import ib_async components
from ib_async import IB, Contract, Stock, Option, TagValue, Trade, OrderState
from ib_async.order import Order, LimitOrder, MarketOrder, StopOrder
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type


try:
    nest_asyncio.apply()
except ValueError as e:
    pass  # nest_asyncio.apply() skipped

# Configure logging
logger = logging.getLogger('ibkr-service')

load_dotenv(override=False)  # Don't override environment variables set by command line

class IBKRConnectionError(Exception):
    """Exception raised for IBKR connection issues"""
    pass

class IBKRTimeoutError(Exception):
    """Exception raised for IBKR timeout issues"""
    pass

class IBKRDataError(Exception):
    """Exception raised for IBKR data issues"""
    pass

class IBKROrderError(Exception):
    """Exception raised for IBKR order issues"""
    pass

class IBKRService:
    def __init__(self):
        self._ib = None  # Lazy initialization
        self.order_management_service_delegate: Optional['OrderManagementService'] = None # Delegate for order management events
        self.host = os.getenv("IBKR_HOST", "127.0.0.1")

        # Smart port detection - use configured port if available, otherwise auto-detect
        configured_port = os.getenv("IBKR_PORT")
        if configured_port:
            self.port = int(configured_port.split()[0])
            self.use_smart_detection = False
            logger.info(f"Using configured port: {self.port}")
        else:
            self.port = None  # Will be set by smart detection
            self.use_smart_detection = True
            logger.info("No port configured - will use smart port detection")
        self.auto_detected_port = None

        self.client_id = int(os.getenv("IBKR_CLIENT_ID", "1"))
        self.connected = False
        self.connection_time = None
        self.connection_attempts = 0
        self.max_connection_attempts = 3
        self.reconnect_delay = 5  # seconds
        self.last_error = None
        self.readonly = True # Default to readonly
        self._next_order_id = 1
        self._event_handlers_registered = False

    @property
    def ib(self):
        """Lazy initialization of IB object"""
        if self._ib is None:
            self._ib = IB()
            logger.debug(f"Created IB object: {type(self._ib)}")
            logger.debug(f"IB object has placeOrder: {hasattr(self._ib, 'placeOrder')}")
            logger.debug(f"IB object has placeOrderAsync: {hasattr(self._ib, 'placeOrderAsync')}")
            if not self._event_handlers_registered:
                self._register_event_handlers()
                self._event_handlers_registered = True
        return self._ib

    async def initialize(self):
        """Initialize the service without connecting to TWS"""
        logger.info("Initializing IBKR service")
        # Reset connection state
        self.connection_attempts = 0
        self.last_error = None
        # Initialize IB object if not already done
        _ = self.ib  # This will trigger lazy initialization
        return True

    def set_order_management_service(self, oms_delegate: 'OrderManagementService'):
        self.order_management_service_delegate = oms_delegate

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type(IBKRConnectionError)
    ) # The client_id_override parameter was removed as it's set on the instance by the tool
    def detect_available_ports(self) -> Dict:
        """Detect which TWS/Gateway ports are available"""
        import socket

        # Common TWS/Gateway ports
        ports_to_check = [
            (7497, "TWS Paper Trading"),
            (7496, "TWS Live Trading / IB Gateway"),
            (4002, "Alternative TWS"),
            (4001, "Alternative Gateway")
        ]

        available_ports = []

        for port, description in ports_to_check:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex((self.host, port))
                sock.close()

                if result == 0:
                    available_ports.append({
                        "port": port,
                        "description": description,
                        "is_configured": self.port is not None and port == self.port
                    })
            except:
                pass

        return {
            "configured_port": self.port,
            "available_ports": available_ports,
            "recommendations": self._get_port_recommendations(available_ports)
        }

    def _get_port_recommendations(self, available_ports: list) -> list:
        """Generate recommendations based on available ports"""
        recommendations = []

        if not available_ports:
            recommendations.extend([
                "❌ No TWS/Gateway detected on any standard port",
                "🔧 Start TWS or IB Gateway",
                "🔧 Enable API access in TWS settings"
            ])
        elif len(available_ports) == 1:
            port_info = available_ports[0]
            if port_info["is_configured"]:
                recommendations.append(f"✅ Using {port_info['description']} on port {port_info['port']}")
            else:
                recommendations.extend([
                    f"🔧 TWS/Gateway detected on port {port_info['port']} ({port_info['description']})",
                    f"🔧 Consider updating configuration to use port {port_info['port']}"
                ])
        else:
            recommendations.append("⚠️  Multiple TWS/Gateway instances detected:")
            for port_info in available_ports:
                status = "✅ CONFIGURED" if port_info["is_configured"] else "⚪ AVAILABLE"
                recommendations.append(f"   Port {port_info['port']}: {port_info['description']} ({status})")

        return recommendations

    async def auto_detect_and_connect(self) -> bool:
        """Try to auto-detect the correct port and connect"""
        port_info = self.detect_available_ports()

        # If configured port is available, use it
        if self.port is not None:
            configured_available = any(p["port"] == self.port for p in port_info["available_ports"])
            if configured_available:
                logger.info(f"Using configured port {self.port}")
                return await self._attempt_connection(self.port)

        # If configured port not available, try auto-detection
        if port_info["available_ports"]:
            # Prefer paper trading (7497) over live (7496) for safety
            preferred_order = [7497, 7496, 4002, 4001]

            for preferred_port in preferred_order:
                for port_info_item in port_info["available_ports"]:
                    if port_info_item["port"] == preferred_port:
                        logger.info(f"Auto-detected and switching to port {preferred_port} ({port_info_item['description']})")
                        self.auto_detected_port = preferred_port
                        self.port = preferred_port  # Update the port
                        return await self._attempt_connection(preferred_port)

            # If no preferred port, use the first available
            first_available = port_info["available_ports"][0]
            logger.info(f"Using first available port {first_available['port']} ({first_available['description']})")
            self.auto_detected_port = first_available["port"]
            self.port = first_available["port"]
            return await self._attempt_connection(first_available["port"])

        return False

    async def _attempt_connection(self, port: int) -> bool:
        """Attempt connection to specific port"""
        try:
            await self.ib.connectAsync(
                host=self.host,
                port=port,
                clientId=self.client_id,
                readonly=self.readonly,
                timeout=20
            )

            # Verify connection is working
            await self.ib.reqCurrentTimeAsync()
            
            # CRITICAL FIX: Update connection status
            self.connected = True
            self.connection_time = datetime.now()
            logger.info(f"Successfully connected to IBKR at {self.host}:{port}")
            
            return True

        except Exception as e:
            logger.debug(f"Connection attempt to port {port} failed: {str(e)}")
            self.connected = False
            return False

    async def diagnose_connection(self) -> Dict:
        """Diagnose connection issues with TWS/Gateway"""
        import socket

        diagnosis = {
            "host": self.host,
            "port": self.port,
            "client_id": self.client_id,
            "socket_test": False,
            "socket_error": None,
            "tws_accessible": False,
            "recommendations": []
        }

        # If no port is configured, we can't test a specific port
        if self.port is None:
            diagnosis["socket_error"] = "No port configured for testing"
            diagnosis["recommendations"].extend([
                "⚠️  No specific port configured - using smart detection",
                "🔧 Smart detection will scan common TWS/Gateway ports",
                "🔧 Ensure TWS or IB Gateway is running",
                "🔧 Enable API access in TWS settings"
            ])
            return diagnosis

        # Test basic socket connectivity
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((self.host, self.port))
            sock.close()

            if result == 0:
                diagnosis["socket_test"] = True
                diagnosis["tws_accessible"] = True
                diagnosis["recommendations"].append("✅ TWS/Gateway is running and accessible")
            else:
                diagnosis["socket_error"] = f"Connection refused (error code: {result})"
                diagnosis["recommendations"].extend([
                    "❌ TWS/Gateway is not accessible",
                    f"🔧 Check if TWS or IB Gateway is running on port {self.port}",
                    "🔧 Verify API settings in TWS: Configure > API > Settings",
                    "🔧 Ensure 'Enable ActiveX and Socket Clients' is checked",
                    f"🔧 Verify Socket Port is set to {self.port}",
                    "🔧 Check if another application is using the same client ID"
                ])

        except Exception as e:
            diagnosis["socket_error"] = str(e)
            diagnosis["recommendations"].extend([
                f"❌ Socket test failed: {str(e)}",
                "🔧 Check network connectivity",
                "🔧 Verify firewall settings"
            ])

        return diagnosis

    async def connect(self, force: bool = False, readonly: Optional[bool] = None): # Added readonly parameter
        """
        Connect to IBKR TWS or Gateway with retry logic

        Args:
            force: If True, force reconnection even if already connected
        """
        # If already connected and not forcing reconnection, return
        if self.connected and not force:
            logger.debug("Already connected to IBKR")
            return True

        # If forcing reconnection, disconnect first
        if self.connected and force:
            await self.disconnect()

        # Update readonly setting if provided
        if readonly is not None:
            self.readonly = readonly

        self.connection_attempts += 1

        # Run smart port detection and connection on first attempt
        if self.connection_attempts == 1:
            logger.info("Running smart port detection and connection...")

            # Try auto-detection and connection
            if await self.auto_detect_and_connect():
                # Connection status already updated in _attempt_connection
                self.connection_attempts = 0  # Reset counter on success

                port_desc = "auto-detected" if self.auto_detected_port else "configured"
                logger.info(f"Connection established via {port_desc} port")
                return True
            else:
                # If auto-detection fails, run diagnostics
                diagnosis = await self.diagnose_connection()
                port_info = self.detect_available_ports()

                error_msg = f"Failed to connect to any available TWS/Gateway port"
                logger.error(error_msg)
                logger.error(f"Port detection results: {port_info}")

                for rec in diagnosis["recommendations"]:
                    logger.error(rec)

                raise IBKRConnectionError(f"{error_msg}. {diagnosis.get('socket_error', '')}")

        # Fallback: try the configured port directly (for retry attempts)
        if self.port is not None:
            try:
                logger.info(f"Connecting to IBKR at {self.host}:{self.port} (attempt {self.connection_attempts})")
                await self.ib.connectAsync(
                    host=self.host,
                    port=self.port,
                    clientId=self.client_id,
                    readonly=self.readonly,
                    timeout=20
                )

                # Verify connection is working by making a simple request
                await self.ib.reqCurrentTimeAsync()

                # Update connection status
                self.connected = True
                self.connection_time = datetime.now()
                self.connection_attempts = 0  # Reset counter on success
                logger.info(f"Successfully connected to IBKR at {self.host}:{self.port}")
                return True
            except Exception as e:
                # If fallback fails, ensure status is updated
                self.connected = False
                logger.error(f"Fallback connection attempt failed: {str(e)}")
                pass

        # If no port is configured, we can't do a fallback connection
        logger.error("No port configured and auto-detection failed")
        return False

    def _register_event_handlers(self):
        """Register handlers for ib_async events"""
        self.ib.orderStatusEvent += self._on_order_status
        self.ib.openOrderEvent += self._on_open_order # Correct: _on_open_order handles individual open orders
        # self.ib.openOrderEndEvent is not available in ib_async 1.0.3 as per docs and error.
        # The _on_open_order_end method and corresponding handle_open_order_end in OrderManagementService
        # will not be called by this event. reqAllOpenOrdersAsync() completion implies all orders are fetched.
        # Add other relevant handlers here, e.g., self.ib.execDetailsEvent += self._on_exec_details
        logger.debug("IBKRService event handlers registered")

    def _on_order_status(self, trade: Trade):
        """Handle order status updates from ib_async and delegate"""
        logger.debug(f"Received orderStatusEvent for OrderID {trade.order.orderId}, Status: {trade.orderStatus.status}")
        if self.order_management_service_delegate:
            self.order_management_service_delegate.handle_order_status_update(
                order_id=trade.order.orderId,
                status_str=trade.orderStatus.status,
                filled=float(trade.orderStatus.filled), # ib_async uses Decimal, convert to float
                remaining=float(trade.orderStatus.remaining), # ib_async uses Decimal, convert to float
                avg_fill_price=trade.orderStatus.avgFillPrice,
                perm_id=trade.order.permId,
                parent_id=trade.order.parentId,
                last_fill_price=trade.orderStatus.lastFillPrice,
                client_id=trade.order.clientId,
                why_held=trade.orderStatus.whyHeld,
                mkt_cap_price=getattr(trade.orderStatus, 'mktCapPrice', None) # mktCapPrice might not always be present
            )

    def _on_open_order(self, trade: Trade):
        """Handle open order information from ib_async and delegate"""
        logger.debug(f"Received openOrderEvent for OrderID {trade.order.orderId}, Symbol: {trade.contract.symbol}")
        if self.order_management_service_delegate:
            self.order_management_service_delegate.handle_open_order(trade.order.orderId, trade.contract, trade.order, trade.orderStatus)

    def _on_open_order_end(self):
        """Handle end of open order list from ib_async and delegate"""
        # This method is unlikely to be called if openOrderEndEvent is not available
        # in the ib_async version being used.
        logger.debug("Received _on_open_order_end call (potentially unused if no openOrderEndEvent)")
        if self.order_management_service_delegate and hasattr(self.order_management_service_delegate, 'handle_open_order_end'):
             self.order_management_service_delegate.handle_open_order_end()

    async def disconnect(self):
        """Disconnect from IBKR asynchronously"""
        if self.connected:
            logger.info("Disconnecting from IBKR")
            # Use asyncio.to_thread for potentially blocking operations
            await asyncio.to_thread(self.ib.disconnect)
            self.connected = False
            logger.info("Disconnected from IBKR")
            return True
        return False

    async def check_connection(self):
        """Check if connection is still valid and reconnect if needed with timeout handling"""
        if not self.connected:
            logger.info("Not connected to IBKR, attempting to connect")
            return await self.connect()

        try:
            # Try a simple request to verify connection with timeout
            timeout_duration = 10  # 10 seconds timeout

            # Check if ib_async connection is active
            if hasattr(self.ib, 'isConnected') and not self.ib.isConnected():
                logger.warning("IBKR connection lost (isConnected returned False)")
                self.connected = False
                return await self.connect(force=True)

            # Try to get current time as a simple connection test
            async def test_connection():
                return await asyncio.to_thread(self.ib.reqCurrentTime)

            try:
                await asyncio.wait_for(test_connection(), timeout=timeout_duration)
                logger.debug("Connection to IBKR is valid and responsive")
                # Ensure connected flag is true after successful check
                self.connected = True
                return True
            except asyncio.TimeoutError:
                logger.warning("Connection test timed out, reconnecting...")
                self.connected = False
                return await self.connect(force=True)

        except Exception as e:
            logger.warning(f"Connection check failed: {str(e)}, reconnecting...")
            self.connected = False
            return await self.connect(force=True)

    async def ensure_connected(self):
        """Ensure connection is established and stable"""
        return await self.check_connection()

    async def get_portfolio(self) -> List[Dict]:
        """Get portfolio positions with connection management"""
        try:
            await self.check_connection()

            logger.info("Fetching portfolio positions")
            positions = await asyncio.to_thread(self.ib.positions)

            # Format portfolio data
            portfolio_data = []
            for pos in positions:
                position_data = {
                    'symbol': pos.contract.symbol,
                    'secType': pos.contract.secType,
                    'exchange': pos.contract.exchange,
                    'currency': pos.contract.currency,
                    'position': pos.position,
                    'avgCost': pos.avgCost,
                    'marketPrice': getattr(pos, 'marketPrice', 0),
                    'marketValue': getattr(pos, 'marketValue', 0),
                    'unrealizedPNL': getattr(pos, 'unrealizedPNL', 0),
                    'realizedPNL': getattr(pos, 'realizedPNL', 0)
                }
                portfolio_data.append(position_data)

            logger.info(f"Retrieved {len(portfolio_data)} portfolio positions")
            return portfolio_data

        except Exception as e:
            logger.error(f"Failed to get portfolio: {str(e)}")
            raise IBKRDataError(f"Failed to get portfolio: {str(e)}")

    async def get_accounts(self) -> Dict:
        """Get managed accounts information"""
        try:
            await self.check_connection()

            logger.info("Fetching managed accounts")
            managed_accounts = self.ib.managedAccounts()

            # Format accounts data
            accounts_list = []
            for account_id in managed_accounts:
                accounts_list.append({
                    'accountId': account_id,
                    'accountType': 'INDIVIDUAL'  # Default type, could be enhanced
                })

            logger.info(f"Retrieved {len(accounts_list)} managed accounts")
            return {
                'status': 'success',
                'accounts': accounts_list,
                'default_account': managed_accounts[0] if managed_accounts else None
            }

        except Exception as e:
            logger.error(f"Failed to get accounts: {str(e)}")
            raise IBKRDataError(f"Failed to get accounts: {str(e)}")

    async def get_account_summary(self, tags: Optional[List[str]] = None) -> Dict:
        """Get account summary with connection management"""
        try:
            await self.check_connection()

            if tags is None:
                tags = ["NetLiquidation", "TotalCashValue", "SettledCash",
                        "AccruedCash", "BuyingPower", "EquityWithLoanValue",
                        "PreviousDayEquityWithLoanValue", "GrossPositionValue",
                        "RegTMargin", "InitMarginReq", "MaintMarginReq",
                        "AvailableFunds", "ExcessLiquidity", "Cushion",
                        "FullInitMarginReq", "FullMaintMarginReq", "FullAvailableFunds",
                        "FullExcessLiquidity", "LookAheadNextChange",
                        "LookAheadInitMarginReq", "LookAheadMaintMarginReq",
                        "LookAheadAvailableFunds", "LookAheadExcessLiquidity",
                        "HighestSeverity", "DayTradesRemaining", "Leverage"]

            logger.info("Fetching account summary")
            account_summary = await self.ib.accountSummaryAsync()

            # Convert to dictionary format
            summary_dict = {}
            for item in account_summary:
                summary_dict[item.tag] = {
                    'value': item.value,
                    'currency': item.currency
                }

            logger.info("Retrieved account summary")
            return summary_dict

        except Exception as e:
            logger.error(f"Failed to get account summary: {str(e)}")
            raise IBKRDataError(f"Failed to get account summary: {str(e)}")

    async def fetch_portfolio_details(self) -> Dict:
        """Fetch portfolio details from IBKR (legacy method)"""
        try:
            portfolio_data = await self.get_portfolio()
            account_summary = await self.get_account_summary()

            return {
                'positions': portfolio_data,
                'account_summary': account_summary
            }

        except Exception as e:
            logger.error(f"Failed to fetch portfolio details: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to fetch portfolio: {str(e)}"
            )

    async def create_contract(self, symbol: str, sec_type: str = "STK", 
                             exchange: str = "SMART", currency: str = "USD", **kwargs) -> Contract:
        """
        Create a contract object for trading
        
        Args:
            symbol (str): The ticker symbol
            sec_type (str): Security type (STK, OPT, FUT, CASH, etc.)
            exchange (str): Exchange (default: SMART)
            currency (str): Currency (default: USD)
            **kwargs: Additional contract parameters
                - For options: expiry, strike, right
                - For futures: expiry, multiplier
                
        Returns:
            Contract: IB contract object
        """
        try:
            contract = Contract()
            contract.symbol = symbol
            contract.secType = sec_type
            contract.exchange = exchange
            contract.currency = currency
            
            # Add security-specific parameters
            if sec_type == "OPT":
                if not all(k in kwargs for k in ['expiry', 'strike', 'right']):
                    raise ValueError("Options require expiry, strike, and right parameters")
                contract.lastTradeDateOrContractMonth = kwargs['expiry']
                contract.strike = kwargs['strike']
                contract.right = kwargs['right']
            
            elif sec_type == "FUT":
                if 'expiry' not in kwargs:
                    raise ValueError("Futures require expiry parameter")
                contract.lastTradeDateOrContractMonth = kwargs['expiry']
                if 'multiplier' in kwargs:
                    contract.multiplier = kwargs['multiplier']
            
            # Add any other parameters
            for key, value in kwargs.items():
                if key not in ['expiry', 'strike', 'right', 'multiplier'] and hasattr(contract, key):
                    setattr(contract, key, value)
            
            logger.debug(f"Created contract: {contract}")
            return contract
        
        except Exception as e:
            logger.error(f"Failed to create contract: {str(e)}")
            raise IBKROrderError(f"Failed to create contract: {str(e)}")

    async def create_order(self, order_type: str, symbol: str, action: str,
                         quantity: Union[int, float, Decimal], account: str, # Added account parameter
                         price: Optional[float] = None, **kwargs) -> Dict:
        """
        Creates and places an order with Interactive Brokers TWS.

        Args:
            order_type (str): Type of order ("MKT", "LMT", "STP", "auction", "adaptive", "bracket")
            symbol (str): The ticker symbol
            action (str): "BUY" or "SELL"
            quantity: Number of shares/contracts
            account (str): The account ID to place the order in
            price: Primary price point for the order
            **kwargs: Additional parameters based on order type
                - For limit: price (required)
                - For stop: stop_price (required)
                - For adaptive: priority ("Urgent", "Normal", "Patient")
                - For bracket: profit_price, stop_price
                - For all: exchange (defaults to "SMART"), time_in_force

        Returns:
            dict: Order status information including order ID

        Raises:
            ConnectionError: If not connected to TWS
            ValueError: If invalid parameters are provided
            IBKROrderError: For TWS API specific errors
        """
        try:
            # Check connection
            await self.check_connection()

            # Create contract
            exchange = kwargs.get("exchange", "SMART")
            contract = await self.create_contract(symbol, exchange=exchange)

            # Convert quantity to Decimal if it's not already
            if not isinstance(quantity, Decimal):
                quantity = Decimal(str(quantity))

            # Handle different order types
            if order_type.upper() == "MKT":
                order = Order()
                order.action = action
                order.orderType = "MKT"
                order.totalQuantity = float(quantity)
                order.account = str(account)

                # Set time in force if provided
                time_in_force = kwargs.get("time_in_force", "DAY")
                order.tif = time_in_force

                # Place the order
                trade = self.ib.placeOrder(contract, order)
                logger.info(f"Placed market order: {trade.order.orderId} for {symbol}")

                return {
                    "status": "success",
                    "orderId": trade.order.orderId,
                    "message": f"Market order placed for {symbol}"
                }

            elif order_type.upper() == "LMT":
                if price is None:
                    raise ValueError("Price is required for limit orders")

                order = Order()
                order.action = action
                order.orderType = "LMT"
                order.totalQuantity = float(quantity)
                order.lmtPrice = float(price)
                order.account = str(account)

                # Set time in force if provided
                time_in_force = kwargs.get("time_in_force", "DAY")
                order.tif = time_in_force

                # Place the order
                trade = self.ib.placeOrder(contract, order)
                logger.info(f"Placed limit order: {trade.order.orderId} for {symbol} @ {price}")

                return {
                    "status": "success",
                    "orderId": trade.order.orderId,
                    "message": f"Limit order placed for {symbol} @ {price}"
                }

            elif order_type.upper() == "ADAPTIVE":
                if price is None:
                    raise ValueError("Price is required for adaptive orders")

                order = Order()
                order.action = action
                order.orderType = "LMT"
                order.totalQuantity = float(quantity)
                order.lmtPrice = float(price)
                order.account = str(account)

                # Set adaptive algorithm
                order.algoStrategy = "Adaptive"
                order.algoParams = []
                priority = kwargs.get("priority", "Normal")
                order.algoParams.append(TagValue("adaptivePriority", priority))

                # Set time in force if provided
                time_in_force = kwargs.get("time_in_force", "DAY")
                order.tif = time_in_force

                # Place the order
                trade = self.ib.placeOrder(contract, order)
                logger.info(f"Placed adaptive order: {trade.order.orderId} for {symbol} @ {price} (priority: {priority})")

                return {
                    "status": "success",
                    "orderId": trade.order.orderId,
                    "message": f"Adaptive order placed for {symbol} @ {price}"
                }

            elif order_type.upper() == "AUCTION":
                if price is None:
                    raise ValueError("Price is required for auction orders")

                order = Order()
                order.action = action
                order.orderType = "LMT"
                order.totalQuantity = float(quantity)
                order.lmtPrice = float(price)
                order.account = str(account)

                # Set auction order parameters
                order.tif = "AUC"  # Auction time in force

                # Place the order
                trade = self.ib.placeOrder(contract, order)
                logger.info(f"Placed auction order: {trade.order.orderId} for {symbol} @ {price}")

                return {
                    "status": "success",
                    "orderId": trade.order.orderId,
                    "message": f"Auction order placed for {symbol} @ {price}"
                }

            else:
                raise ValueError(f"Unknown order type: {order_type}")

        except Exception as e:
            logger.error(f"Failed to create order: {str(e)}")
            raise IBKROrderError(f"Failed to create order: {str(e)}")

    async def place_adaptive_order(self, symbol: str, action: str, quantity: Union[int, float, Decimal],
                                 price: float, account: str, priority: str = "Normal",
                                 exchange: str = "SMART") -> Dict:
        """
        Place an Adaptive algorithm order

        Args:
            symbol: The ticker symbol
            action: "BUY" or "SELL"
            quantity: Number of shares/contracts
            price: Limit price
            account: The account ID to place the order in
            priority: Priority setting ("Urgent", "Normal", "Patient")
            exchange: Exchange (default: SMART)

        Returns:
            dict: Order status information
        """
        logger.info(f"Placing adaptive order for {symbol}: {action} {quantity} @ {price} (priority: {priority})")
        return await self.create_order("adaptive", symbol, action, quantity, account, price,
                                     priority=priority, exchange=exchange)

    async def place_bracket_order(
        self,
        symbol: str,
        action: str,
        quantity: Union[int, float, Decimal],
        entry_price: float,
        profit_price: float,
        stop_price: float,
        account: str,
        exchange: str = "SMART"
    ) -> Dict:
        """
        Place a bracket order with entry, profit target, and stop loss

        Args:
            symbol: The ticker symbol
            action: "BUY" or "SELL"
            quantity: Number of shares/contracts
            entry_price: Entry order price
            profit_price: Profit target price
            stop_price: Stop loss price
            account: The account ID to place the order in
            exchange: Exchange (default: SMART)

        Returns:
            dict: Order status information with all order IDs
        """
        try:
            await self.check_connection()

            # Create contract
            contract = await self.create_contract(symbol, exchange=exchange)

            # Convert quantity to float for ib_async
            quantity_float = float(quantity)

            # Create bracket order using ib_async helper
            logger.info(f"Creating bracket order for {symbol}: {action} {quantity_float} @ entry={entry_price}, profit={profit_price}, stop={stop_price}")

            bracket = self.ib.bracketOrder(
                action=action,
                quantity=quantity_float,
                limitPrice=entry_price,
                takeProfitPrice=profit_price,
                stopLossPrice=stop_price
            )

            # Set account for all orders in the bracket
            for order in bracket:
                order.account = str(account)

            # Place all orders in the bracket
            order_ids = []
            trades = []

            for order in bracket:
                trade = self.ib.placeOrder(contract, order)
                trades.append(trade)
                order_ids.append(trade.order.orderId)
                logger.info(f"Placed bracket order component: {trade.order.orderId} ({order.orderType})")

            logger.info(f"Successfully placed bracket order for {symbol}: parent={order_ids[0]}, profit={order_ids[1]}, stop={order_ids[2]}")

            return {
                "status": "success",
                "orderId": order_ids[0],  # Return parent order ID as primary
                "parentOrderId": order_ids[0],
                "profitOrderId": order_ids[1],
                "stopOrderId": order_ids[2],
                "allOrderIds": order_ids,
                "message": f"Bracket order placed for {symbol}: entry @ {entry_price}, profit @ {profit_price}, stop @ {stop_price}"
            }

        except Exception as e:
            logger.error(f"Failed to place bracket order for {symbol}: {str(e)}")
            raise IBKROrderError(f"Failed to place bracket order: {str(e)}")

    async def place_auction_order(self, symbol: str, action: str, quantity: Union[int, float, Decimal],
                               price: float, account: str, exchange: str = "SMART") -> Dict:
        """
        Place an Auction order

        Args:
            symbol: The ticker symbol
            action: "BUY" or "SELL"
            quantity: Number of shares/contracts
            price: Limit price
            account: The account ID to place the order in
            exchange: Exchange (default: SMART)

        Returns:
            dict: Order status information
        """
        logger.info(f"Placing auction order for {symbol}: {action} {quantity} @ {price}")
        return await self.create_order("auction", symbol, action, quantity, account, price, exchange=exchange)

    async def get_historical_data(
        self,
        symbol: str,
        duration: str = "1 Y",
        bar_size: str = "1 day",
        what_to_show: str = "TRADES",
        use_rth: bool = True,
        exchange: str = "SMART"
    ) -> List[Dict]:
        """
        Get historical market data for a symbol

        Args:
            symbol: The ticker symbol
            duration: Duration string (e.g., "1 Y", "6 M", "1 D")
            bar_size: Bar size (e.g., "1 day", "1 hour", "5 mins")
            what_to_show: Data type ("TRADES", "MIDPOINT", "BID", "ASK")
            use_rth: Use regular trading hours only
            exchange: Exchange (default: SMART)

        Returns:
            List of historical bars
        """
        try:
            await self.check_connection()

            # Create contract
            contract = await self.create_contract(symbol, exchange=exchange)

            # Request historical data
            logger.info(f"Requesting historical data for {symbol}: {duration}, {bar_size}")
            bars = await self.ib.reqHistoricalDataAsync(
                contract=contract,
                endDateTime='',  # Use current time
                durationStr=duration,
                barSizeSetting=bar_size,
                whatToShow=what_to_show,
                useRTH=use_rth,
                formatDate=1
            )

            # Convert to list of dictionaries
            historical_data = []
            for bar in bars:
                bar_data = {
                    "date": bar.date.strftime("%Y-%m-%d %H:%M:%S") if hasattr(bar.date, 'strftime') else str(bar.date),
                    "open": float(bar.open),
                    "high": float(bar.high),
                    "low": float(bar.low),
                    "close": float(bar.close),
                    "volume": int(bar.volume),
                    "average": float(bar.average) if bar.average else 0.0,
                    "barCount": int(bar.barCount) if bar.barCount else 0
                }
                historical_data.append(bar_data)

            logger.info(f"Retrieved {len(historical_data)} historical bars for {symbol}")
            return historical_data

        except Exception as e:
            logger.error(f"Failed to get historical data for {symbol}: {str(e)}")
            raise IBKRDataError(f"Failed to get historical data: {str(e)}")

    async def get_market_data(self, symbol: str, exchange: str = "SMART") -> Dict:
        """
        Get current market data for a symbol

        Args:
            symbol: The ticker symbol
            exchange: Exchange (default: SMART)

        Returns:
            Market data dictionary
        """
        try:
            await self.check_connection()

            # Create contract
            contract = await self.create_contract(symbol, exchange=exchange)

            # Request market data snapshot
            logger.info(f"Requesting market data for {symbol}")
            ticker = self.ib.reqMktData(contract, snapshot=True)

            # Wait for data to be populated
            await asyncio.sleep(2)

            # Extract market data
            market_data = {
                "symbol": symbol,
                "bid": float(ticker.bid) if ticker.bid and ticker.bid > 0 else 0.0,
                "ask": float(ticker.ask) if ticker.ask and ticker.ask > 0 else 0.0,
                "last": float(ticker.last) if ticker.last and ticker.last > 0 else 0.0,
                "close": float(ticker.close) if ticker.close and ticker.close > 0 else 0.0,
                "bid_size": int(ticker.bidSize) if ticker.bidSize else 0,
                "ask_size": int(ticker.askSize) if ticker.askSize else 0,
                "last_size": int(ticker.lastSize) if ticker.lastSize else 0,
                "volume": int(ticker.volume) if ticker.volume else 0,
                "high": float(ticker.high) if ticker.high and ticker.high > 0 else 0.0,
                "low": float(ticker.low) if ticker.low and ticker.low > 0 else 0.0
            }

            # Cancel the market data subscription
            self.ib.cancelMktData(contract)

            logger.info(f"Retrieved market data for {symbol}: last={market_data['last']}")
            return {
                "status": "success",
                "data": {
                    "data": market_data
                }
            }

        except Exception as e:
            logger.error(f"Failed to get market data for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }

# Lazy singleton instance
_ibkr_service_instance = None

def get_ibkr_service():
    """Get the singleton IBKR service instance (lazy initialization)"""
    global _ibkr_service_instance
    if _ibkr_service_instance is None:
        _ibkr_service_instance = IBKRService()
    return _ibkr_service_instance

# For backward compatibility - call the function to get the instance
ibkr_service = get_ibkr_service()