"""
IBKR MCP Server Application Package

Interactive Brokers Model Context Protocol Server
Provides trading capabilities, market data processing, and portfolio management.
"""

__version__ = "1.0.0"
__author__ = "IBKR MCP Server Team"
__description__ = "Interactive Brokers Model Context Protocol Server"

# Import core components for easy access
from .core.settings import settings

# Import main service classes
from .services.base_service import BaseService, AsyncService

# Import database components
from .database.connection import supabase_conn
from .models.database_models import (
    Base,
    MarketDataModel,
    TradingSignalModel,
    OrderModel,
    PositionModel
)

# Lazy imports to avoid circular dependencies
def get_phase2_service():
    """Lazy import for Phase2Service to avoid circular dependencies"""
    from .services.phase2_service import Phase2Service
    return Phase2Service

def get_config():
    """Lazy import for Config to avoid circular dependencies"""
    from .core.config import Config
    return Config

__all__ = [
    # Core
    'settings',
    'get_config',

    # Services
    'BaseService',
    'AsyncService',
    'get_phase2_service',

    # Database
    'supabase_conn',
    'Base',
    'MarketDataModel',
    'TradingSignalModel',
    'OrderModel',
    'PositionModel',

    # Metadata
    '__version__',
    '__author__',
    '__description__'
]