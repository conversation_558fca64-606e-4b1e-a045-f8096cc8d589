"""
Enhanced FastAPI Application with Google Cloud Pub/Sub Integration

Provides REST API endpoints, WebSocket real-time streaming, and enterprise-grade
message bus capabilities using Google Cloud Pub/Sub for the IBKR MCP trading system.
"""

import sys
import os
from pathlib import Path

# Add the app directory to Python path for absolute imports
app_dir = Path(__file__).parent
if str(app_dir) not in sys.path:
    sys.path.insert(0, str(app_dir))

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging
import uvicorn
from typing import Dict, Any

# Import WebSocket components
from websocket import websocket_router, connection_manager, broadcaster

# Import Pub/Sub components
from pubsub import PubSubManager, PubSubConfig, IBKRPubSubBridge, pubsub_manager
from pubsub.ibkr_pubsub_bridge import ibkr_pubsub_bridge

# Import services
from services.ibkr_service import IBKRService
from integrations.event_stream_manager import EventStreamManager

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage FastAPI application lifespan with Pub/Sub integration"""
    logger.info("🚀 Starting IBKR MCP Server with Google Cloud Pub/Sub + WebSocket...")
    
    # Initialize services
    try:
        # Initialize IBKR service
        ibkr_service = IBKRService()
        await ibkr_service.connect()
        
        # Initialize event manager
        event_manager = EventStreamManager()
        await event_manager.start()
        
        # Initialize Google Cloud Pub/Sub
        global pubsub_manager, ibkr_pubsub_bridge
        
        # Configure Pub/Sub
        pubsub_config = PubSubConfig()
        pubsub_config.project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'your-gcp-project-id')
        pubsub_config.credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        
        if pubsub_config.project_id != 'your-gcp-project-id':
            # Initialize Pub/Sub manager
            pubsub_manager = PubSubManager(pubsub_config)
            await pubsub_manager.initialize()
            
            # Initialize IBKR-Pub/Sub bridge
            ibkr_pubsub_bridge = IBKRPubSubBridge(ibkr_service, pubsub_manager)
            
            # Start Pub/Sub subscribers
            await pubsub_manager.start_subscriber('ibkr-websocket-broadcaster')
            
            logger.info("✅ Google Cloud Pub/Sub integration initialized")
        else:
            logger.warning("⚠️  Google Cloud Pub/Sub not configured - using WebSocket only")
            pubsub_manager = None
            ibkr_pubsub_bridge = None
        
        logger.info("✅ All services initialized successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize services: {e}")
        raise
    
    yield
    
    # Cleanup
    logger.info("🛑 Shutting down IBKR MCP Server...")
    try:
        if ibkr_service:
            await ibkr_service.disconnect()
        if event_manager:
            await event_manager.stop()
        if pubsub_manager:
            await pubsub_manager.shutdown()
    except Exception as e:
        logger.error(f"❌ Error during shutdown: {e}")


# Create FastAPI app with Pub/Sub + WebSocket support
app = FastAPI(
    title="IBKR MCP Server with Pub/Sub + WebSocket Streaming",
    description="Interactive Brokers MCP Server with Google Cloud Pub/Sub and real-time WebSocket streaming",
    version="3.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include WebSocket router
app.include_router(websocket_router, prefix="/api/v1")


# Enhanced health check endpoints
@app.get("/health")
async def health_check():
    """Enhanced health check endpoint"""
    return {
        "status": "healthy",
        "service": "IBKR MCP Server",
        "version": "3.0.0",
        "features": {
            "websocket_enabled": True,
            "pubsub_enabled": pubsub_manager is not None,
            "ibkr_bridge_enabled": ibkr_pubsub_bridge is not None
        }
    }


@app.get("/api/v1/status")
async def get_system_status():
    """Get comprehensive system status including Pub/Sub"""
    try:
        # Get WebSocket statistics
        ws_stats = connection_manager.get_statistics()
        
        # Get Pub/Sub statistics
        pubsub_stats = {}
        if pubsub_manager:
            pubsub_stats = pubsub_manager.get_statistics()
        
        # Get bridge statistics
        bridge_stats = {}
        if ibkr_pubsub_bridge:
            bridge_stats = ibkr_pubsub_bridge.get_statistics()
        
        return {
            "system": {
                "status": "operational",
                "timestamp": "2024-12-01T00:00:00Z",
                "version": "3.0.0"
            },
            "websocket": {
                "enabled": True,
                "active_connections": ws_stats.get('active_connections', 0),
                "total_connections": ws_stats.get('total_connections', 0),
                "messages_sent": ws_stats.get('messages_sent', 0),
                "channels": ws_stats.get('channels', {})
            },
            "pubsub": {
                "enabled": pubsub_manager is not None,
                **pubsub_stats
            },
            "ibkr_bridge": {
                "enabled": ibkr_pubsub_bridge is not None,
                **bridge_stats
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Pub/Sub specific endpoints
@app.get("/api/v1/pubsub/topics")
async def get_pubsub_topics():
    """Get list of Pub/Sub topics"""
    if not pubsub_manager:
        raise HTTPException(status_code=503, detail="Pub/Sub not enabled")
    
    return {
        "topics": list(pubsub_manager.config.topics.values()),
        "topic_mapping": pubsub_manager.config.topics
    }


@app.get("/api/v1/pubsub/subscriptions")
async def get_pubsub_subscriptions():
    """Get list of Pub/Sub subscriptions"""
    if not pubsub_manager:
        raise HTTPException(status_code=503, detail="Pub/Sub not enabled")
    
    return {
        "subscriptions": list(pubsub_manager.config.subscriptions.values()),
        "subscription_mapping": pubsub_manager.config.subscriptions
    }


@app.get("/api/v1/pubsub/stats")
async def get_pubsub_statistics():
    """Get detailed Pub/Sub statistics"""
    if not pubsub_manager:
        raise HTTPException(status_code=503, detail="Pub/Sub not enabled")
    
    return pubsub_manager.get_statistics()


@app.post("/api/v1/pubsub/publish/signal")
async def publish_trading_signal(request: Dict[str, Any]):
    """Manually publish a trading signal to Pub/Sub"""
    if not ibkr_pubsub_bridge:
        raise HTTPException(status_code=503, detail="Pub/Sub bridge not enabled")
    
    try:
        strategy_id = request.get('strategy_id')
        signal_data = request.get('signal_data', {})
        
        if not strategy_id or not signal_data:
            raise HTTPException(status_code=400, detail="strategy_id and signal_data required")
        
        await ibkr_pubsub_bridge.publish_trading_signal(strategy_id, signal_data)
        
        return {
            'success': True,
            'message': f'Trading signal published for strategy {strategy_id}',
            'strategy_id': strategy_id
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/pubsub/publish/risk-alert")
async def publish_risk_alert(request: Dict[str, Any]):
    """Manually publish a risk alert to Pub/Sub"""
    if not ibkr_pubsub_bridge:
        raise HTTPException(status_code=503, detail="Pub/Sub bridge not enabled")
    
    try:
        alert_data = request.get('alert_data', {})
        
        if not alert_data:
            raise HTTPException(status_code=400, detail="alert_data required")
        
        await ibkr_pubsub_bridge.publish_risk_alert(alert_data)
        
        return {
            'success': True,
            'message': 'Risk alert published',
            'alert_type': alert_data.get('type', 'unknown')
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Enhanced market data subscription with Pub/Sub
@app.post("/api/v1/market-data/subscribe")
async def subscribe_market_data(request: Dict[str, Any]):
    """Subscribe to real-time market data (WebSocket + Pub/Sub)"""
    try:
        symbols = request.get('symbols', [])
        data_types = request.get('data_types', ['LAST', 'BID', 'ASK'])
        
        if not symbols:
            raise HTTPException(status_code=400, detail="Symbols required")
        
        results = {}
        
        # Subscribe via IBKR bridge (publishes to Pub/Sub automatically)
        if ibkr_pubsub_bridge:
            for symbol in symbols:
                # Create contract and request market data through IBKR service
                try:
                    contract = await ibkr_pubsub_bridge.ibkr_service.create_contract(symbol)
                    # Request market data from IBKR (this will trigger the event handlers)
                    ticker = ibkr_pubsub_bridge.ibkr_service.ib.reqMktData(
                        contract=contract,
                        genericTickList='',
                        snapshot=False,
                        regulatorySnapshot=False
                    )
                    success = True
                except Exception as e:
                    logger.error(f"Failed to subscribe to market data for {symbol}: {e}")
                    success = False

                results[symbol] = {
                    'subscribed': success,
                    'data_types': data_types,
                    'pubsub_topic': 'ibkr-market-data',
                    'websocket_channel': f"market_data:{symbol}"
                }
        else:
            raise HTTPException(status_code=503, detail="IBKR bridge not available")
        
        return {
            'success': True,
            'subscriptions': results,
            'message': f'Subscribed to {len(symbols)} symbols via Pub/Sub + WebSocket'
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Configuration endpoint
@app.get("/api/v1/config")
async def get_configuration():
    """Get current system configuration"""
    config = {
        "websocket": {
            "enabled": True,
            "max_connections": 1000,
            "ping_interval": 30
        },
        "pubsub": {
            "enabled": pubsub_manager is not None,
            "project_id": os.getenv('GOOGLE_CLOUD_PROJECT', 'not-configured'),
            "credentials_configured": bool(os.getenv('GOOGLE_APPLICATION_CREDENTIALS'))
        },
        "ibkr": {
            "host": os.getenv('IBKR_HOST', '127.0.0.1'),
            "port": int(os.getenv('IBKR_PORT', '7497')),
            "client_id": int(os.getenv('IBKR_CLIENT_ID', '1'))
        }
    }
    
    return config


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s | %(levelname)s | %(name)s | %(message)s'
    )
    
    # Run the server
    uvicorn.run(
        "fastapi_pubsub_app:app",
        host="0.0.0.0",
        port=8002,
        reload=True,
        log_level="info"
    )
