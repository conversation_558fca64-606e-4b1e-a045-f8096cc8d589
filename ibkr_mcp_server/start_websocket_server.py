#!/usr/bin/env python3
"""
IBKR MCP Server with WebSocket Real-Time Streaming

Startup script for the enhanced IBKR MCP server with true real-time
push-based data streaming via WebSockets.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add the app directory to Python path
app_dir = Path(__file__).parent / "app"
sys.path.insert(0, str(app_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ibkr_websocket_server.log')
    ]
)

logger = logging.getLogger(__name__)


def main():
    """Main entry point for WebSocket-enabled IBKR MCP server"""
    
    logger.info("=" * 60)
    logger.info("🚀 IBKR MCP Server with Real-Time WebSocket Streaming")
    logger.info("=" * 60)
    
    try:
        # Import and run FastAPI app
        import uvicorn
        from app.fastapi_app import app
        
        logger.info("Starting FastAPI server with WebSocket support...")
        logger.info("📡 WebSocket endpoints available at:")
        logger.info("   - ws://localhost:8001/api/v1/ws (General)")
        logger.info("   - ws://localhost:8001/api/v1/ws/market-data (Market Data)")
        logger.info("   - ws://localhost:8001/api/v1/ws/orders (Orders)")
        logger.info("   - ws://localhost:8001/api/v1/ws/signals (Trading Signals)")
        logger.info("")
        logger.info("🌐 REST API endpoints available at:")
        logger.info("   - http://localhost:8001/health")
        logger.info("   - http://localhost:8001/api/v1/status")
        logger.info("   - http://localhost:8001/api/v1/ws/stats")
        logger.info("   - http://localhost:8001/docs (API Documentation)")
        logger.info("")
        
        # Run the server
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8001,
            log_level="info",
            access_log=True,
            reload=False  # Set to True for development
        )
        
    except KeyboardInterrupt:
        logger.info("Server shutdown requested by user")
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
