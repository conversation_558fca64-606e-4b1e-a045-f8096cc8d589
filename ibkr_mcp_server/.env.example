# .env.example
# Copy this file to .env and fill in your actual values.
# Do NOT commit the .env file to version control.

# -- Supabase Configuration --
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_SERVICE_KEY="your-supabase-service-role-key"
SUPABASE_ANON_KEY="your-supabase-anon-key"

# -- IBKR Configuration --
IBKR_HOST="127.0.0.1"
IBKR_PORT="7497"  # 7497 for paper trading, 7496 for live trading
IBKR_CLIENT_ID="1"
IBKR_READONLY="true"

# -- Webhook Secrets --
# A secret string used to verify that incoming webhooks are legitimate.
WEBHOOK_SECRET="generate-a-strong-random-secret"

# -- Notification Service Endpoints --
# The webhook URLs for your notification channels.
SLACK_WEBHOOK_URL="https://hooks.slack.com/services/..."
DISCORD_WEBHOOK_URL="https://discord.com/api/webhooks/..."
TELEGRAM_BOT_TOKEN="your-telegram-bot-token"
TELEGRAM_CHAT_ID="your-telegram-chat-id"

# -- Google Cloud Pub/Sub Configuration --
GCP_PROJECT_ID="your-gcp-project-id"
GCP_SERVICE_ACCOUNT_KEY_PATH="path/to/service-account-key.json"
PUBSUB_TOPIC_PREFIX="ibkr"

# -- Risk Management Parameters --
# Default values can be overridden in the 'risk_parameters' database table.
DEFAULT_MAX_PORTFOLIO_EXPOSURE="100000"
DEFAULT_MAX_POSITION_SIZE="25000"
DEFAULT_MIN_SIGNAL_STRENGTH="0.3"
DEFAULT_MAX_DAILY_LOSS="5000"

# -- Event Processing Configuration --
EVENT_BATCH_SIZE="100"
EVENT_PROCESSING_INTERVAL="1000"  # milliseconds
MAX_RETRY_ATTEMPTS="3"
CIRCUIT_BREAKER_THRESHOLD="5"

# -- Sync Configuration --
SYNC_INTERVAL="5000"  # milliseconds
ENABLE_AUTO_SYNC="true"
SYNC_MARKET_DATA="true"
SYNC_ORDERS="true"
SYNC_POSITIONS="true"

# -- WebSocket Configuration --
WEBSOCKET_PORT="8765"
WEBSOCKET_HOST="0.0.0.0"
ENABLE_WEBSOCKET="true"

# -- Logging Configuration --
LOG_LEVEL="INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FILE_PATH="logs/ibkr_mcp_server.log"
ENABLE_FILE_LOGGING="true"

# -- Development/Testing Configuration --
ENABLE_MOCK_DATA="false"
MOCK_MARKET_DATA="false"
TEST_MODE="false"

# -- Security Configuration --
ENABLE_API_KEY_AUTH="false"
API_KEY="your-api-key-for-external-access"
CORS_ORIGINS="http://localhost:3000,http://localhost:8080"

# -- Performance Configuration --
MAX_CONCURRENT_REQUESTS="100"
REQUEST_TIMEOUT="30"  # seconds
DATABASE_POOL_SIZE="10"
CACHE_TTL="300"  # seconds
