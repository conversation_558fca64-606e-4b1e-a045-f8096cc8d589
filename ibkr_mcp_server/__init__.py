"""
IBKR MCP Server Package

This package provides MCP (Model Context Protocol) server functionality
for Interactive Brokers TWS API integration.
"""

import sys
import os
from pathlib import Path

# Allow importing services directly without the full MCP server
__all__ = ['mcp']

def get_mcp_server():
    """Get the MCP server instance (lazy loading)"""
    import logging
    logger = logging.getLogger(__name__)

    try:
        logger.info("Loading simplified IBKR MCP Server...")

        # Try to import the simplified server from the root directory
        project_root = Path(__file__).parent.parent
        simple_server_path = project_root / "ibkr_mcp_server_simple.py"

        if simple_server_path.exists():
            logger.info(f"Using simplified server from: {simple_server_path}")
            import importlib.util

            spec = importlib.util.spec_from_file_location("ibkr_mcp_server_simple", simple_server_path)
            if spec is None or spec.loader is None:
                raise ImportError(f"Could not load spec from {simple_server_path}")

            logger.info("Creating simplified MCP module...")
            mcp_module = importlib.util.module_from_spec(spec)

            logger.info("Executing simplified MCP module...")
            spec.loader.exec_module(mcp_module)

            if hasattr(mcp_module, 'mcp'):
                logger.info("Simplified MCP server loaded successfully")
                return mcp_module.mcp
            else:
                raise ImportError("Simplified MCP module loaded but 'mcp' attribute not found")
        else:
            raise ImportError(f"Simplified server not found at: {simple_server_path}")

    except Exception as e:
        logger.error(f"Error loading simplified MCP server: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

        # Create a minimal fallback MCP server
        logger.info("Creating fallback MCP server...")
        try:
            from mcp.server import FastMCP

            # Create a minimal MCP server with basic functionality
            fallback_mcp = FastMCP("ibkr-fallback")

            @fallback_mcp.tool()
            def get_server_status() -> str:
                """Get the status of the IBKR MCP server"""
                return "IBKR MCP Server is running in fallback mode due to import errors. Please check the logs for details."

            @fallback_mcp.tool()
            def get_error_info() -> str:
                """Get information about the import error"""
                return f"Import error: {str(e)}"

            logger.info("Fallback MCP server created successfully")
            return fallback_mcp

        except Exception as fallback_error:
            logger.error(f"Failed to create fallback MCP server: {fallback_error}")
            # Provide a more helpful error message
            raise ImportError(
                f"Failed to import MCP server: {e}\n"
                f"Fallback also failed: {fallback_error}\n"
                "Make sure all dependencies are installed by running:\n"
                "pip install -r ibkr_mcp_server/requirements.txt\n"
                f"Current working directory: {os.getcwd()}\n"
                f"Python path: {sys.path[:3]}..."
            ) from e

# Lazy loading - only import when explicitly requested
mcp = None

def __getattr__(name):
    """Lazy loading of MCP server"""
    global mcp
    if name == 'mcp':
        if mcp is None:
            mcp = get_mcp_server()
        return mcp
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")
