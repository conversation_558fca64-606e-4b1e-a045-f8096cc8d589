#!/bin/bash

# IBKR MCP Server Startup Script
# This script ensures the server runs with the correct virtual environment

# Get the directory where the project is located (corrected path)
PROJECT_DIR="/Users/<USER>/IBKR/b-team"

# Path to the virtual environment
VENV_PATH="$PROJECT_DIR/ibkr_mcp_server/source/venv"
VENV_PYTHON="$VENV_PATH/bin/python3"

# Check if virtual environment exists
if [ ! -f "$VENV_PYTHON" ]; then
    echo "Error: Virtual environment not found at $VENV_PATH"
    echo "Please ensure the virtual environment is set up correctly."
    exit 1
fi

# Check if MCP package is installed in the virtual environment
if ! "$VENV_PYTHON" -c "import mcp.server" 2>/dev/null; then
    echo "Error: MCP package not found in virtual environment"
    echo "Installing missing dependencies..."
    "$VENV_PATH/bin/pip" install mcp==1.8.0
    if [ $? -ne 0 ]; then
        echo "Failed to install MCP package"
        exit 1
    fi
fi

# Run the MCP server with the virtual environment Python
echo "Starting IBKR MCP Server with virtual environment..."
exec "$VENV_PYTHON" "$PROJECT_DIR/mcp_server_main.py" "$@"
