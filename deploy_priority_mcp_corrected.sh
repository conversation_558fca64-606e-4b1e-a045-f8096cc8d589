#!/bin/bash

# 🚀 Priority MCP Server Deployment Script (Corrected)
# Deploys Memory, SQLite, Everything, and Puppeteer servers to <PERSON>

set -e  # Exit on any error

echo "🚀 Starting Priority MCP Server Deployment (Corrected)..."
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if <PERSON> is running
check_claude_running() {
    if pgrep -f "<PERSON>" > /dev/null; then
        print_warning "<PERSON> is currently running"
        echo "Please quit <PERSON> Desktop (Cmd+Q) before continuing."
        echo "Press Enter when Claude Desktop is closed..."
        read
    else
        print_success "Claude Desktop is not running - good to proceed"
    fi
}

# Create backup of current configuration
backup_config() {
    print_status "Creating backup of current Claude Desktop configuration..."
    
    BACKUP_FILE="$HOME/.config/claude_desktop/config.json.backup.$(date +%Y%m%d_%H%M%S)"
    
    if [ -f "$HOME/.config/claude_desktop/config.json" ]; then
        cp "$HOME/.config/claude_desktop/config.json" "$BACKUP_FILE"
        print_success "Backup created: $BACKUP_FILE"
    else
        print_warning "No existing config.json found - this might be a fresh installation"
    fi
}

# Install MCP server packages
install_packages() {
    print_status "Installing available priority MCP server packages..."
    
    # Check if npm is available
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install Node.js and npm first."
        exit 1
    fi
    
    print_status "Installing @modelcontextprotocol/server-memory..."
    npm install -g @modelcontextprotocol/server-memory
    
    print_status "Installing mcp-sqlite (SQLite server)..."
    npm install -g mcp-sqlite
    
    print_status "Installing @modelcontextprotocol/server-everything..."
    npm install -g @modelcontextprotocol/server-everything
    
    print_status "Installing @modelcontextprotocol/server-puppeteer..."
    npm install -g @modelcontextprotocol/server-puppeteer
    
    print_success "All available MCP server packages installed successfully"
}

# Create corrected configuration
create_corrected_config() {
    print_status "Creating corrected configuration with available servers..."
    
    cat > "/Users/<USER>/IBKR/b-team/claude_desktop_config_corrected.json" << 'EOF'
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/Users/<USER>/IBKR/b-team"
      ]
    },
    "brave-search": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-brave-search"
      ],
      "env": {
        "BRAVE_API_KEY": "BSAjQpxPzjay5DrCA-uy3QkB27tTouN"
      },
      "disabled": false,
      "autoApprove": []
    },
    "supabase-bteam": {
      "command": "npx",
      "args": [
        "-y",
        "@supabase/mcp-server-supabase@latest",
        "--access-token",
        "********************************************"
      ],
      "disabled": false,
      "autoApprove": ["*"]
    },
    "supabase-billions": {
      "command": "npx",
      "args": [
        "-y",
        "@supabase/mcp-server-supabase@latest",
        "--access-token",
        "********************************************"
      ],
      "disabled": false,
      "autoApprove": ["*"]
    },
    "ibkr-trading": {
      "command": "/Users/<USER>/IBKR/b-team/ibkr_server.sh",
      "args": [
        "--host",
        "127.0.0.1",
        "--client-id",
        "1"
      ],
      "env": {
        "IBKR_AUTO_CONNECT": "false",
        "IBKR_DEFAULT_EXCHANGE": "SMART",
        "IBKR_DEFAULT_CURRENCY": "USD",
        "IBKR_DEFAULT_TIME_IN_FORCE": "DAY"
      },
      "disabled": false,
      "autoApprove": ["*"]
    },
    "filesystem-trusts-converted": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/Volumes/MREL I/Transcend/Americana Transcendence  - GGTMW/Master Maur/State National - Jay Dolphin/SN/Trusts/Trusts Converted"
      ],
      "disabled": false,
      "autoApprove": []
    },
    "mcp-pandoc": {
      "command": "uvx",
      "args": ["mcp-pandoc"],
      "disabled": false,
      "autoApprove": []
    },
    "dalle-mcp": {
      "command": "node",
      "args": [
        "/Users/<USER>/Documents/Cline/MCP/dalle-mcp-server/build/index.js"
      ],
      "env": {
        "OPENAI_API_KEY": "********************************************************************************************************************************************************************",
        "SAVE_DIR": "/Users/<USER>/adverp/docs/dall-e"
      },
      "disabled": false,
      "autoApprove": [
        "/Users/<USER>/adverp/docs/dall-e"
      ]
    },
    "fundraise-server": {
      "command": "node",
      "args": [
        "/Users/<USER>/Documents/Cline/MCP/fundraise-server/build/index.js"
      ]
    },
    "github-mcp-server": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-github"
      ],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"
      },
      "disabled": false,
      "autoApprove": []
    },
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "disabled": false,
      "autoApprove": []
    },
    "sqlite": {
      "command": "npx",
      "args": ["-y", "mcp-sqlite"],
      "disabled": false,
      "autoApprove": []
    },
    "everything": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-everything"],
      "disabled": false,
      "autoApprove": []
    },
    "puppeteer": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-puppeteer"],
      "disabled": false,
      "autoApprove": []
    }
  }
}
EOF
    
    print_success "Corrected configuration created"
}

# Deploy new configuration
deploy_config() {
    print_status "Deploying corrected Claude Desktop configuration..."
    
    # Ensure config directory exists
    mkdir -p "$HOME/.config/claude_desktop"
    
    # Copy the corrected configuration
    cp "/Users/<USER>/IBKR/b-team/claude_desktop_config_corrected.json" "$HOME/.config/claude_desktop/config.json"
    
    print_success "Corrected configuration deployed successfully"
}

# Verify configuration
verify_config() {
    print_status "Verifying configuration..."
    
    if [ -f "$HOME/.config/claude_desktop/config.json" ]; then
        # Check if jq is available for JSON validation
        if command -v jq &> /dev/null; then
            if jq empty "$HOME/.config/claude_desktop/config.json" 2>/dev/null; then
                print_success "Configuration file is valid JSON"
                
                # Count servers
                SERVER_COUNT=$(jq '.mcpServers | keys | length' "$HOME/.config/claude_desktop/config.json")
                print_success "Configuration contains $SERVER_COUNT MCP servers"
                
                # List new servers
                echo "New servers added:"
                jq -r '.mcpServers | keys[]' "$HOME/.config/claude_desktop/config.json" | grep -E "(memory|sqlite|everything|puppeteer)" | sed 's/^/  - /'
            else
                print_error "Configuration file contains invalid JSON"
                exit 1
            fi
        else
            print_warning "jq not available - skipping JSON validation"
        fi
    else
        print_error "Configuration file not found after deployment"
        exit 1
    fi
}

# Main deployment function
main() {
    echo "This script will:"
    echo "1. Backup your current Claude Desktop configuration"
    echo "2. Install 4 available MCP servers (memory, sqlite, everything, puppeteer)"
    echo "3. Deploy the corrected configuration"
    echo "4. Verify the installation"
    echo ""
    echo "Note: Using available servers instead of non-existent ones"
    echo "- Memory: @modelcontextprotocol/server-memory ✅"
    echo "- SQLite: mcp-sqlite ✅"
    echo "- Everything: @modelcontextprotocol/server-everything ✅"
    echo "- Puppeteer: @modelcontextprotocol/server-puppeteer ✅"
    echo ""
    echo "After completion, you'll need to restart Claude Desktop manually."
    echo ""
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Deployment cancelled by user"
        exit 0
    fi
    
    echo ""
    print_status "Starting corrected deployment process..."
    
    # Step 1: Check if Claude Desktop is running
    check_claude_running
    
    # Step 2: Backup current configuration
    backup_config
    
    # Step 3: Install MCP packages
    install_packages
    
    # Step 4: Create corrected configuration
    create_corrected_config
    
    # Step 5: Deploy new configuration
    deploy_config
    
    # Step 6: Verify configuration
    verify_config
    
    echo ""
    echo "================================================"
    print_success "Priority MCP Server deployment completed successfully!"
    echo ""
    echo "New servers added:"
    echo "✅ Memory Server - Persistent context across sessions"
    echo "✅ SQLite Server - Local database operations"
    echo "✅ Everything Server - Fast system-wide file search"
    echo "✅ Puppeteer Server - Web scraping and automation"
    echo ""
    echo "Next steps:"
    echo "1. Start Claude Desktop"
    echo "2. Wait for initialization (may take 30-60 seconds)"
    echo "3. Test the new servers:"
    echo "   - Memory: Ask Claude to remember something"
    echo "   - SQLite: Ask to create a simple database"
    echo "   - Everything: Ask to search for files on your system"
    echo "   - Puppeteer: Ask to scrape a webpage"
    echo ""
    echo "If you encounter issues, restore backup with:"
    echo "cp ~/.config/claude_desktop/config.json.backup.* ~/.config/claude_desktop/config.json"
    echo ""
    print_success "Deployment complete! 🎉"
}

# Run main function
main "$@"
