/**
 * Market Data Aggregator Edge Function
 * 
 * This function aggregates real-time market data from multiple sources,
 * processes it for trading signals, and stores it in the database.
 * 
 * Features:
 * - Real-time data aggregation from IBKR and external sources
 * - OHLC candle generation
 * - Technical indicator calculations
 * - Data validation and quality checks
 * - Correlation analysis updates
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Types
interface MarketDataTick {
  pair_id: string;
  symbol: string;
  bid: number;
  ask: number;
  bid_volume?: number;
  ask_volume?: number;
  timestamp: string;
  source: string;
}

interface OHLCCandle {
  pair_id: string;
  interval: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  timestamp: string;
}

interface TechnicalIndicators {
  sma_20: number;
  sma_50: number;
  rsi_14: number;
  macd_line: number;
  macd_signal: number;
  bollinger_upper: number;
  bollinger_lower: number;
}

interface AggregationRequest {
  action: 'process_tick' | 'generate_candles' | 'calculate_indicators' | 'update_correlations';
  data?: any;
  pair_ids?: string[];
  interval?: string;
  lookback_periods?: number;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

serve(async (req) => {
  try {
    // Handle CORS
    if (req.method === 'OPTIONS') {
      return new Response('ok', {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        },
      });
    }

    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const requestData: AggregationRequest = await req.json();
    let result: any;

    switch (requestData.action) {
      case 'process_tick':
        result = await processTick(requestData.data);
        break;
      case 'generate_candles':
        result = await generateCandles(requestData.pair_ids!, requestData.interval!);
        break;
      case 'calculate_indicators':
        result = await calculateIndicators(requestData.pair_ids!, requestData.lookback_periods!);
        break;
      case 'update_correlations':
        result = await updateCorrelations(requestData.pair_ids!, requestData.lookback_periods!);
        break;
      default:
        throw new Error(`Unknown action: ${requestData.action}`);
    }

    return new Response(JSON.stringify({ success: true, data: result }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });

  } catch (error) {
    console.error('Market Data Aggregator Error:', error);
    return new Response(JSON.stringify({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
});

/**
 * Process incoming market data tick
 */
async function processTick(tickData: MarketDataTick): Promise<any> {
  // Validate tick data
  if (!tickData.pair_id || !tickData.bid || !tickData.ask) {
    throw new Error('Invalid tick data: missing required fields');
  }

  // Calculate mid price and spread
  const mid = (tickData.bid + tickData.ask) / 2;
  const spread = tickData.ask - tickData.bid;
  const spread_pct = (spread / mid) * 100;

  // Data quality checks
  if (spread_pct > 1.0) { // Spread > 1%
    console.warn(`High spread detected for ${tickData.symbol}: ${spread_pct.toFixed(4)}%`);
  }

  if (tickData.bid >= tickData.ask) {
    throw new Error(`Invalid bid/ask: bid (${tickData.bid}) >= ask (${tickData.ask})`);
  }

  // Insert tick data
  const { data, error } = await supabase
    .from('market_data')
    .insert({
      pair_id: tickData.pair_id,
      bid: tickData.bid,
      ask: tickData.ask,
      mid: mid,
      spread: spread,
      spread_pct: spread_pct,
      bid_volume: tickData.bid_volume,
      ask_volume: tickData.ask_volume,
      source: tickData.source,
      timestamp: tickData.timestamp || new Date().toISOString()
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to insert tick data: ${error.message}`);
  }

  // Trigger real-time updates
  await supabase.realtime.channel('market_data')
    .send({
      type: 'broadcast',
      event: 'tick_update',
      payload: {
        pair_id: tickData.pair_id,
        symbol: tickData.symbol,
        mid: mid,
        spread_pct: spread_pct,
        timestamp: data.timestamp
      }
    });

  return { tick_id: data.id, mid, spread_pct };
}

/**
 * Generate OHLC candles for specified intervals
 */
async function generateCandles(pairIds: string[], interval: string): Promise<OHLCCandle[]> {
  const candles: OHLCCandle[] = [];

  for (const pairId of pairIds) {
    // Call stored procedure to generate OHLC data
    const { data, error } = await supabase.rpc('generate_ohlc_candles', {
      p_pair_id: pairId,
      p_interval: interval,
      p_lookback_hours: 24
    });

    if (error) {
      console.error(`Failed to generate candles for ${pairId}:`, error);
      continue;
    }

    if (data && data.length > 0) {
      candles.push(...data);
    }
  }

  return candles;
}

/**
 * Calculate technical indicators
 */
async function calculateIndicators(pairIds: string[], lookbackPeriods: number): Promise<any[]> {
  const indicators: any[] = [];

  for (const pairId of pairIds) {
    try {
      // Get recent price data
      const { data: priceData, error } = await supabase
        .from('market_data')
        .select('mid, timestamp')
        .eq('pair_id', pairId)
        .order('timestamp', { ascending: false })
        .limit(lookbackPeriods);

      if (error || !priceData || priceData.length < 20) {
        console.warn(`Insufficient data for indicators calculation: ${pairId}`);
        continue;
      }

      const prices = priceData.map(d => d.mid).reverse(); // Oldest first
      
      // Calculate indicators
      const sma20 = calculateSMA(prices, 20);
      const sma50 = calculateSMA(prices, 50);
      const rsi14 = calculateRSI(prices, 14);
      const macd = calculateMACD(prices);
      const bollinger = calculateBollingerBands(prices, 20, 2);

      const indicatorData: TechnicalIndicators = {
        sma_20: sma20,
        sma_50: sma50,
        rsi_14: rsi14,
        macd_line: macd.line,
        macd_signal: macd.signal,
        bollinger_upper: bollinger.upper,
        bollinger_lower: bollinger.lower
      };

      // Store indicators
      const { data: savedIndicator, error: saveError } = await supabase
        .from('technical_indicators')
        .upsert({
          pair_id: pairId,
          ...indicatorData,
          timestamp: new Date().toISOString()
        })
        .select()
        .single();

      if (saveError) {
        console.error(`Failed to save indicators for ${pairId}:`, saveError);
        continue;
      }

      indicators.push(savedIndicator);

    } catch (error) {
      console.error(`Error calculating indicators for ${pairId}:`, error);
    }
  }

  return indicators;
}

/**
 * Update correlation matrix
 */
async function updateCorrelations(pairIds: string[], lookbackPeriods: number): Promise<any[]> {
  const correlations: any[] = [];

  // Calculate correlations for all pair combinations
  for (let i = 0; i < pairIds.length; i++) {
    for (let j = i + 1; j < pairIds.length; j++) {
      const pair1 = pairIds[i];
      const pair2 = pairIds[j];

      try {
        // Get price data for both pairs
        const [data1, data2] = await Promise.all([
          getPriceData(pair1, lookbackPeriods),
          getPriceData(pair2, lookbackPeriods)
        ]);

        if (data1.length < 10 || data2.length < 10) {
          continue; // Insufficient data
        }

        // Calculate correlation
        const correlation = calculateCorrelation(data1, data2);
        
        // Save correlation
        const { data: savedCorr, error } = await supabase
          .from('correlation_matrix')
          .upsert({
            pair1_id: pair1,
            pair2_id: pair2,
            correlation_coefficient: correlation,
            lookback_period: lookbackPeriods,
            calculation_method: 'pearson',
            timestamp: new Date().toISOString()
          })
          .select()
          .single();

        if (!error && savedCorr) {
          correlations.push(savedCorr);
        }

      } catch (error) {
        console.error(`Error calculating correlation for ${pair1}-${pair2}:`, error);
      }
    }
  }

  return correlations;
}

// Helper functions for technical analysis
function calculateSMA(prices: number[], period: number): number {
  if (prices.length < period) return 0;
  const sum = prices.slice(-period).reduce((a, b) => a + b, 0);
  return sum / period;
}

function calculateRSI(prices: number[], period: number): number {
  if (prices.length < period + 1) return 50;
  
  let gains = 0;
  let losses = 0;
  
  for (let i = prices.length - period; i < prices.length; i++) {
    const change = prices[i] - prices[i - 1];
    if (change > 0) gains += change;
    else losses -= change;
  }
  
  const avgGain = gains / period;
  const avgLoss = losses / period;
  const rs = avgGain / avgLoss;
  
  return 100 - (100 / (1 + rs));
}

function calculateMACD(prices: number[]): { line: number; signal: number } {
  const ema12 = calculateEMA(prices, 12);
  const ema26 = calculateEMA(prices, 26);
  const macdLine = ema12 - ema26;
  
  // Simplified signal line calculation
  const signalLine = macdLine * 0.9; // Placeholder
  
  return { line: macdLine, signal: signalLine };
}

function calculateEMA(prices: number[], period: number): number {
  if (prices.length < period) return prices[prices.length - 1] || 0;
  
  const multiplier = 2 / (period + 1);
  let ema = prices[0];
  
  for (let i = 1; i < prices.length; i++) {
    ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
  }
  
  return ema;
}

function calculateBollingerBands(prices: number[], period: number, stdDev: number): { upper: number; lower: number } {
  const sma = calculateSMA(prices, period);
  const recentPrices = prices.slice(-period);
  
  const variance = recentPrices.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period;
  const standardDeviation = Math.sqrt(variance);
  
  return {
    upper: sma + (standardDeviation * stdDev),
    lower: sma - (standardDeviation * stdDev)
  };
}

function calculateCorrelation(data1: number[], data2: number[]): number {
  const n = Math.min(data1.length, data2.length);
  if (n < 2) return 0;
  
  const mean1 = data1.slice(0, n).reduce((a, b) => a + b) / n;
  const mean2 = data2.slice(0, n).reduce((a, b) => a + b) / n;
  
  let numerator = 0;
  let sum1 = 0;
  let sum2 = 0;
  
  for (let i = 0; i < n; i++) {
    const diff1 = data1[i] - mean1;
    const diff2 = data2[i] - mean2;
    numerator += diff1 * diff2;
    sum1 += diff1 * diff1;
    sum2 += diff2 * diff2;
  }
  
  const denominator = Math.sqrt(sum1 * sum2);
  return denominator === 0 ? 0 : numerator / denominator;
}

async function getPriceData(pairId: string, limit: number): Promise<number[]> {
  const { data, error } = await supabase
    .from('market_data')
    .select('mid')
    .eq('pair_id', pairId)
    .order('timestamp', { ascending: false })
    .limit(limit);
  
  if (error || !data) return [];
  return data.map(d => d.mid).reverse();
}
