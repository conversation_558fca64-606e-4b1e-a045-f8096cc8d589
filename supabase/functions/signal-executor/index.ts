/**
 * Signal Executor Edge Function
 * 
 * This function processes trading signals and executes trades automatically
 * based on predefined strategies and risk management rules.
 * 
 * Features:
 * - Automated signal processing
 * - Risk validation before execution
 * - Order placement via IBKR integration
 * - Position sizing calculations
 * - Stop-loss and take-profit management
 * - Real-time execution monitoring
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Types
interface TradingSignal {
  id: string;
  strategy_id: string;
  pair_id: string;
  signal_type: 'BUY' | 'SELL' | 'CLOSE';
  signal_strength: number;
  target_price?: number;
  stop_loss?: number;
  take_profit?: number;
  expires_at: string;
  metadata?: any;
}

interface RiskParameters {
  max_total_exposure: number;
  max_position_size: number;
  max_drawdown_pct: number;
  max_daily_loss: number;
  position_size_pct: number;
  stop_loss_pct: number;
  take_profit_pct: number;
}

interface ExecutionRequest {
  action: 'process_signals' | 'execute_signal' | 'validate_risk' | 'manage_positions';
  signal_id?: string;
  strategy_id?: string;
  force_execution?: boolean;
}

interface OrderRequest {
  pair_id: string;
  side: 'BUY' | 'SELL';
  quantity: number;
  order_type: 'MARKET' | 'LIMIT' | 'STOP';
  limit_price?: number;
  stop_price?: number;
  time_in_force: 'DAY' | 'GTC' | 'IOC' | 'FOK';
  signal_id?: string;
}

interface PositionSizing {
  recommended_quantity: number;
  risk_amount: number;
  position_value: number;
  leverage_ratio: number;
  confidence_adjustment: number;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

serve(async (req) => {
  try {
    // Handle CORS
    if (req.method === 'OPTIONS') {
      return new Response('ok', {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        },
      });
    }

    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const requestData: ExecutionRequest = await req.json();
    let result: any;

    switch (requestData.action) {
      case 'process_signals':
        result = await processActiveSignals(requestData.strategy_id);
        break;
      case 'execute_signal':
        result = await executeSignal(requestData.signal_id!, requestData.force_execution);
        break;
      case 'validate_risk':
        result = await validateRiskLimits(requestData.signal_id!);
        break;
      case 'manage_positions':
        result = await manageOpenPositions();
        break;
      default:
        throw new Error(`Unknown action: ${requestData.action}`);
    }

    return new Response(JSON.stringify({ success: true, data: result }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });

  } catch (error) {
    console.error('Signal Executor Error:', error);
    return new Response(JSON.stringify({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
});

/**
 * Process all active trading signals
 */
async function processActiveSignals(strategyId?: string): Promise<any> {
  // Get active signals
  let query = supabase
    .from('trading_signals')
    .select('*')
    .eq('is_active', true)
    .gt('expires_at', new Date().toISOString())
    .order('signal_strength', { ascending: false });

  if (strategyId) {
    query = query.eq('strategy_id', strategyId);
  }

  const { data: signals, error } = await query;

  if (error) {
    throw new Error(`Failed to fetch signals: ${error.message}`);
  }

  const results = [];
  
  for (const signal of signals || []) {
    try {
      const result = await executeSignal(signal.id, false);
      results.push({ signal_id: signal.id, result });
    } catch (error) {
      console.error(`Failed to execute signal ${signal.id}:`, error);
      results.push({ signal_id: signal.id, error: error.message });
    }
  }

  return { processed_signals: results.length, results };
}

/**
 * Execute a specific trading signal
 */
async function executeSignal(signalId: string, forceExecution: boolean = false): Promise<any> {
  // Get signal details
  const { data: signal, error: signalError } = await supabase
    .from('trading_signals')
    .select('*')
    .eq('id', signalId)
    .single();

  if (signalError || !signal) {
    throw new Error(`Signal not found: ${signalId}`);
  }

  // Check if signal is still valid
  if (!signal.is_active || new Date(signal.expires_at) < new Date()) {
    throw new Error(`Signal expired or inactive: ${signalId}`);
  }

  // Validate risk limits unless forced
  if (!forceExecution) {
    const riskValidation = await validateRiskLimits(signalId);
    if (!riskValidation.approved) {
      throw new Error(`Risk validation failed: ${riskValidation.reason}`);
    }
  }

  // Calculate position sizing
  const positionSizing = await calculatePositionSize(signal);

  // Get current market price
  const currentPrice = await getCurrentPrice(signal.pair_id);

  // Create order request
  const orderRequest: OrderRequest = {
    pair_id: signal.pair_id,
    side: signal.signal_type as 'BUY' | 'SELL',
    quantity: positionSizing.recommended_quantity,
    order_type: signal.target_price ? 'LIMIT' : 'MARKET',
    limit_price: signal.target_price,
    time_in_force: 'DAY',
    signal_id: signalId
  };

  // Execute the order
  const orderResult = await placeOrder(orderRequest);

  // Update signal status
  await supabase
    .from('trading_signals')
    .update({
      is_active: false,
      executed_at: new Date().toISOString(),
      execution_price: currentPrice,
      order_id: orderResult.order_id
    })
    .eq('id', signalId);

  // Create position record if order is filled
  if (orderResult.status === 'FILLED') {
    await createPosition(signal, orderResult, positionSizing);
  }

  return {
    signal_id: signalId,
    order_id: orderResult.order_id,
    execution_price: currentPrice,
    quantity: positionSizing.recommended_quantity,
    position_value: positionSizing.position_value,
    status: orderResult.status
  };
}

/**
 * Validate risk limits before execution
 */
async function validateRiskLimits(signalId: string): Promise<{ approved: boolean; reason?: string }> {
  // Get risk parameters
  const { data: riskParams, error } = await supabase
    .from('risk_parameters')
    .select('*')
    .eq('is_active', true)
    .order('created_at', { ascending: false })
    .limit(1)
    .single();

  if (error || !riskParams) {
    return { approved: false, reason: 'Risk parameters not found' };
  }

  // Get signal details
  const { data: signal } = await supabase
    .from('trading_signals')
    .select('*')
    .eq('id', signalId)
    .single();

  if (!signal) {
    return { approved: false, reason: 'Signal not found' };
  }

  // Calculate current exposure
  const { data: positions } = await supabase
    .from('positions')
    .select('quantity, current_price, side')
    .eq('is_open', true);

  const totalExposure = positions?.reduce((sum, pos) => {
    const value = pos.quantity * pos.current_price;
    return sum + (pos.side === 'LONG' ? value : -value);
  }, 0) || 0;

  // Calculate proposed position size
  const positionSizing = await calculatePositionSize(signal);
  const newExposure = totalExposure + positionSizing.position_value;

  // Check exposure limits
  if (Math.abs(newExposure) > riskParams.max_total_exposure) {
    return { 
      approved: false, 
      reason: `Total exposure limit exceeded: ${Math.abs(newExposure)} > ${riskParams.max_total_exposure}` 
    };
  }

  // Check position size limits
  if (positionSizing.position_value > riskParams.max_position_size) {
    return { 
      approved: false, 
      reason: `Position size limit exceeded: ${positionSizing.position_value} > ${riskParams.max_position_size}` 
    };
  }

  // Check daily loss limits
  const todayStart = new Date();
  todayStart.setHours(0, 0, 0, 0);
  
  const { data: todayPnL } = await supabase
    .from('positions')
    .select('realized_pnl')
    .gte('closed_at', todayStart.toISOString())
    .not('realized_pnl', 'is', null);

  const dailyPnL = todayPnL?.reduce((sum, pos) => sum + (pos.realized_pnl || 0), 0) || 0;
  
  if (dailyPnL < -riskParams.max_daily_loss) {
    return { 
      approved: false, 
      reason: `Daily loss limit exceeded: ${Math.abs(dailyPnL)} > ${riskParams.max_daily_loss}` 
    };
  }

  return { approved: true };
}

/**
 * Calculate position size based on risk parameters
 */
async function calculatePositionSize(signal: TradingSignal): Promise<PositionSizing> {
  // Get risk parameters
  const { data: riskParams } = await supabase
    .from('risk_parameters')
    .select('*')
    .eq('is_active', true)
    .order('created_at', { ascending: false })
    .limit(1)
    .single();

  if (!riskParams) {
    throw new Error('Risk parameters not found');
  }

  // Get current price
  const currentPrice = await getCurrentPrice(signal.pair_id);
  
  // Calculate base position size (percentage of total capital)
  const baseCapital = 100000; // TODO: Get from account info
  const basePositionValue = baseCapital * (riskParams.position_size_pct / 100);
  
  // Adjust for signal strength (0.1 to 1.0)
  const strengthAdjustment = Math.max(0.1, Math.min(1.0, signal.signal_strength));
  const adjustedPositionValue = basePositionValue * strengthAdjustment;
  
  // Calculate quantity
  const quantity = adjustedPositionValue / currentPrice;
  
  // Calculate risk amount (for stop-loss)
  const stopLossDistance = signal.stop_loss ? 
    Math.abs(currentPrice - signal.stop_loss) : 
    currentPrice * (riskParams.stop_loss_pct / 100);
  
  const riskAmount = quantity * stopLossDistance;

  return {
    recommended_quantity: Math.round(quantity * 100) / 100, // Round to 2 decimals
    risk_amount: riskAmount,
    position_value: adjustedPositionValue,
    leverage_ratio: 1.0, // No leverage for now
    confidence_adjustment: strengthAdjustment
  };
}

/**
 * Get current market price for a pair
 */
async function getCurrentPrice(pairId: string): Promise<number> {
  const { data, error } = await supabase
    .from('market_data')
    .select('mid')
    .eq('pair_id', pairId)
    .order('timestamp', { ascending: false })
    .limit(1)
    .single();

  if (error || !data) {
    throw new Error(`Failed to get current price for ${pairId}`);
  }

  return data.mid;
}

/**
 * Place order (simulated - in real implementation would call IBKR API)
 */
async function placeOrder(orderRequest: OrderRequest): Promise<any> {
  // Insert order record
  const { data: order, error } = await supabase
    .from('orders')
    .insert({
      pair_id: orderRequest.pair_id,
      side: orderRequest.side,
      quantity: orderRequest.quantity,
      order_type: orderRequest.order_type,
      limit_price: orderRequest.limit_price,
      stop_price: orderRequest.stop_price,
      time_in_force: orderRequest.time_in_force,
      signal_id: orderRequest.signal_id,
      status: 'PENDING',
      created_at: new Date().toISOString()
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create order: ${error.message}`);
  }

  // Simulate immediate fill for market orders
  if (orderRequest.order_type === 'MARKET') {
    const currentPrice = await getCurrentPrice(orderRequest.pair_id);
    
    await supabase
      .from('orders')
      .update({
        status: 'FILLED',
        filled_quantity: orderRequest.quantity,
        average_fill_price: currentPrice,
        filled_at: new Date().toISOString()
      })
      .eq('id', order.id);

    return { order_id: order.id, status: 'FILLED', fill_price: currentPrice };
  }

  return { order_id: order.id, status: 'PENDING' };
}

/**
 * Create position record after order execution
 */
async function createPosition(signal: TradingSignal, orderResult: any, sizing: PositionSizing): Promise<void> {
  const { error } = await supabase
    .from('positions')
    .insert({
      pair_id: signal.pair_id,
      strategy_id: signal.strategy_id,
      side: signal.signal_type === 'BUY' ? 'LONG' : 'SHORT',
      quantity: sizing.recommended_quantity,
      entry_price: orderResult.fill_price,
      current_price: orderResult.fill_price,
      stop_loss: signal.stop_loss,
      take_profit: signal.take_profit,
      is_open: true,
      opened_at: new Date().toISOString(),
      signal_id: signal.id,
      order_id: orderResult.order_id
    });

  if (error) {
    console.error('Failed to create position:', error);
  }
}

/**
 * Manage open positions (stop-loss, take-profit monitoring)
 */
async function manageOpenPositions(): Promise<any> {
  const { data: positions, error } = await supabase
    .from('positions')
    .select('*')
    .eq('is_open', true);

  if (error || !positions) {
    return { managed_positions: 0 };
  }

  const results = [];

  for (const position of positions) {
    try {
      const currentPrice = await getCurrentPrice(position.pair_id);
      
      // Update current price
      await supabase
        .from('positions')
        .update({ current_price: currentPrice })
        .eq('id', position.id);

      // Check stop-loss
      if (position.stop_loss) {
        const shouldClose = (position.side === 'LONG' && currentPrice <= position.stop_loss) ||
                           (position.side === 'SHORT' && currentPrice >= position.stop_loss);
        
        if (shouldClose) {
          await closePosition(position.id, currentPrice, 'stop_loss');
          results.push({ position_id: position.id, action: 'stop_loss_triggered' });
          continue;
        }
      }

      // Check take-profit
      if (position.take_profit) {
        const shouldClose = (position.side === 'LONG' && currentPrice >= position.take_profit) ||
                           (position.side === 'SHORT' && currentPrice <= position.take_profit);
        
        if (shouldClose) {
          await closePosition(position.id, currentPrice, 'take_profit');
          results.push({ position_id: position.id, action: 'take_profit_triggered' });
        }
      }

    } catch (error) {
      console.error(`Error managing position ${position.id}:`, error);
      results.push({ position_id: position.id, error: error.message });
    }
  }

  return { managed_positions: positions.length, actions: results };
}

/**
 * Close a position
 */
async function closePosition(positionId: string, exitPrice: number, reason: string): Promise<void> {
  // Get position details
  const { data: position } = await supabase
    .from('positions')
    .select('*')
    .eq('id', positionId)
    .single();

  if (!position) return;

  // Calculate P&L
  const pnlMultiplier = position.side === 'LONG' ? 1 : -1;
  const realizedPnL = (exitPrice - position.entry_price) * position.quantity * pnlMultiplier;

  // Update position
  await supabase
    .from('positions')
    .update({
      is_open: false,
      current_price: exitPrice,
      realized_pnl: realizedPnL,
      closed_at: new Date().toISOString(),
      close_reason: reason
    })
    .eq('id', positionId);

  // Create closing order record
  await supabase
    .from('orders')
    .insert({
      pair_id: position.pair_id,
      side: position.side === 'LONG' ? 'SELL' : 'BUY',
      quantity: position.quantity,
      order_type: 'MARKET',
      status: 'FILLED',
      filled_quantity: position.quantity,
      average_fill_price: exitPrice,
      filled_at: new Date().toISOString(),
      created_at: new Date().toISOString()
    });
}
