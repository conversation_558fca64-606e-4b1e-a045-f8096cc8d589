# Supabase Edge Functions for IBKR Trading Platform

This directory contains Supabase Edge Functions that provide serverless computing capabilities for the IBKR trading platform. These functions handle real-time data processing, signal execution, and automated trading operations.

## Functions Overview

### 1. Market Data Aggregator (`market-data-aggregator/`)

**Purpose**: Processes and aggregates real-time market data from multiple sources.

**Features**:
- Real-time tick data processing
- OHLC candle generation
- Technical indicator calculations (SMA, RSI, MACD, Bollinger Bands)
- Correlation matrix updates
- Data validation and quality checks

**API Endpoints**:
- `POST /market-data-aggregator` with action `process_tick`
- `POST /market-data-aggregator` with action `generate_candles`
- `POST /market-data-aggregator` with action `calculate_indicators`
- `POST /market-data-aggregator` with action `update_correlations`

**Example Usage**:
```javascript
// Process a new market tick
const response = await fetch('/functions/v1/market-data-aggregator', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'process_tick',
    data: {
      pair_id: 'uuid-here',
      symbol: 'EUR/USD',
      bid: 1.0850,
      ask: 1.0852,
      timestamp: '2024-12-19T10:30:00Z',
      source: 'IBKR'
    }
  })
});
```

### 2. Signal Executor (`signal-executor/`)

**Purpose**: Executes trading signals automatically with risk management.

**Features**:
- Automated signal processing
- Risk validation before execution
- Position sizing calculations
- Order placement and management
- Stop-loss and take-profit monitoring
- Real-time position management

**API Endpoints**:
- `POST /signal-executor` with action `process_signals`
- `POST /signal-executor` with action `execute_signal`
- `POST /signal-executor` with action `validate_risk`
- `POST /signal-executor` with action `manage_positions`

**Example Usage**:
```javascript
// Execute a specific trading signal
const response = await fetch('/functions/v1/signal-executor', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'execute_signal',
    signal_id: 'signal-uuid-here',
    force_execution: false
  })
});
```

## Database Integration

Both functions integrate with the following database tables:

### Core Tables
- `market_data` - Real-time tick data
- `trading_signals` - Generated trading signals
- `orders` - Order management
- `positions` - Position tracking
- `risk_parameters` - Risk management settings

### New Tables (Added in Migration 002)
- `webhook_events` - Webhook event queue
- `technical_indicators` - Technical analysis data
- `ohlc_candles` - OHLC candlestick data
- `audit_log` - System audit trail

## Webhook Integration

The functions work with database webhooks to provide real-time event processing:

### Order Execution Webhook
- Triggered when orders are filled
- Sends notifications to external systems
- Updates performance metrics
- Creates audit trail

### Risk Alert Webhook
- Monitors exposure limits
- Checks position sizes
- Tracks drawdown levels
- Sends immediate alerts for threshold breaches

## Environment Variables

Required environment variables for both functions:

```bash
SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

## Deployment

### Using Supabase CLI

1. Install Supabase CLI:
```bash
npm install -g supabase
```

2. Login to Supabase:
```bash
supabase login
```

3. Deploy functions:
```bash
supabase functions deploy market-data-aggregator
supabase functions deploy signal-executor
```

### Manual Deployment

1. Navigate to your Supabase dashboard
2. Go to Edge Functions section
3. Create new function
4. Copy the TypeScript code from `index.ts`
5. Set environment variables
6. Deploy

## Testing

### Local Testing

```bash
# Start Supabase local development
supabase start

# Serve functions locally
supabase functions serve market-data-aggregator --env-file .env.local
supabase functions serve signal-executor --env-file .env.local
```

### Test Requests

```bash
# Test market data processing
curl -X POST http://localhost:54321/functions/v1/market-data-aggregator \
  -H "Content-Type: application/json" \
  -d '{"action": "process_tick", "data": {...}}'

# Test signal execution
curl -X POST http://localhost:54321/functions/v1/signal-executor \
  -H "Content-Type: application/json" \
  -d '{"action": "process_signals"}'
```

## Monitoring and Logging

### Function Logs
- Access logs through Supabase dashboard
- Monitor function performance and errors
- Set up alerts for critical failures

### Database Monitoring
- Monitor webhook event processing
- Track function execution metrics
- Review audit logs for system activity

## Security Considerations

1. **Service Role Key**: Functions use service role key for database access
2. **CORS**: Configured for specific origins in production
3. **Input Validation**: All inputs are validated before processing
4. **Error Handling**: Comprehensive error handling with logging
5. **Rate Limiting**: Consider implementing rate limiting for production

## Performance Optimization

1. **Batch Processing**: Functions support batch operations
2. **Database Indexing**: Proper indexes on frequently queried columns
3. **Connection Pooling**: Supabase handles connection pooling automatically
4. **Caching**: Consider implementing caching for frequently accessed data

## Integration with IBKR MCP Server

These Edge Functions complement the IBKR MCP Server by:

1. **Real-time Processing**: Handle high-frequency data processing
2. **Scalability**: Serverless scaling for variable workloads
3. **Event-driven**: React to database changes via webhooks
4. **Microservices**: Modular architecture for specific functions

## Troubleshooting

### Common Issues

1. **Environment Variables**: Ensure all required env vars are set
2. **Database Permissions**: Verify service role has necessary permissions
3. **CORS Errors**: Check CORS configuration for your domain
4. **Timeout Issues**: Monitor function execution time limits

### Debug Mode

Enable debug logging by setting:
```bash
DEBUG=true
```

## Future Enhancements

1. **Machine Learning Integration**: Add ML-based signal generation
2. **Multi-Asset Support**: Extend beyond forex to stocks, options, futures
3. **Advanced Risk Models**: Implement sophisticated risk management
4. **Real-time Dashboards**: WebSocket-based real-time updates
5. **Backtesting Integration**: Historical signal testing capabilities
