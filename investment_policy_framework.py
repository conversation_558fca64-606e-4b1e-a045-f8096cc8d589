"""
Multi-Asset Investment Policy Framework
A comprehensive system for managing objectives, goals, and policies across different security types
"""

# Database Schema Design for Supabase

## 1. SECURITY TYPES TABLE
"""
CREATE TABLE security_types (
    id SERIAL PRIMARY KEY,
    type_code VARCHAR(20) UNIQUE NOT NULL,  -- STOCK, OPTION, FUTURE, CRYPTO, ETF, FOREX
    type_name VARCHAR(100) NOT NULL,
    description TEXT,
    market_hours JSONB,  -- Trading hours per market
    tick_size DECIMAL(10,6),
    contract_multiplier DECIMAL(10,2),
    margin_requirements JSONB,  -- Initial, maintenance, day trade
    settlement_type VARCHAR(20),  -- T+2, T+1, T+0, instant
    regulatory_rules JSONB,  -- PDT, uptick rule, etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
"""

## 2. ASSET CLASSES TABLE
"""
CREATE TABLE asset_classes (
    id SERIAL PRIMARY KEY,
    class_code VARCHAR(20) UNIQUE NOT NULL,  -- EQUITY, FIXED_INCOME, COMMODITY, CURRENCY, ALTERNATIVE
    class_name VARCHAR(100) NOT NULL,
    risk_score INTEGER CHECK (risk_score BETWEEN 1 AND 10),
    liquidity_score INTEGER CHECK (liquidity_score BETWEEN 1 AND 10),
    correlation_matrix JSONB,  -- Correlation with other asset classes
    characteristics JSONB
);
"""

## 3. INVESTMENT OBJECTIVES TEMPLATE TABLE
"""
CREATE TABLE objective_templates (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL,
    objective_type VARCHAR(50),  -- GROWTH, INCOME, PRESERVATION, SPECULATION, HEDGING
    description TEXT,
    default_parameters JSONB,
    suitable_for JSONB,  -- Risk profiles, time horizons
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
"""

## 4. SECURITY-SPECIFIC OBJECTIVES TABLE
"""
CREATE TABLE security_objectives (
    id SERIAL PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id),
    security_type_id INTEGER REFERENCES security_types(id),
    objective_name VARCHAR(255) NOT NULL,
    objective_template_id INTEGER REFERENCES objective_templates(id),
    
    -- Financial Goals
    target_return DECIMAL(5,2),  -- Percentage
    return_period VARCHAR(20),  -- DAILY, WEEKLY, MONTHLY, ANNUAL
    risk_tolerance VARCHAR(20),  -- CONSERVATIVE, MODERATE, AGGRESSIVE
    
    -- Capital Allocation
    allocated_capital DECIMAL(15,2),
    max_position_size DECIMAL(15,2),
    max_portfolio_allocation DECIMAL(5,2),  -- Percentage of total portfolio
    
    -- Time Parameters
    time_horizon VARCHAR(20),  -- INTRADAY, SHORT, MEDIUM, LONG
    holding_period_min INTEGER,  -- Minutes
    holding_period_max INTEGER,  -- Minutes
    
    -- Risk Limits
    max_drawdown DECIMAL(5,2),
    var_limit DECIMAL(15,2),  -- Value at Risk
    sharpe_ratio_target DECIMAL(5,2),
    
    -- Constraints
    constraints JSONB,
    /*
    {
        "excluded_symbols": [],
        "allowed_sectors": [],
        "min_market_cap": 1000000000,
        "min_volume": 1000000,
        "max_volatility": 0.5,
        "geographic_limits": ["US", "EU"],
        "esg_requirements": {"min_score": 70}
    }
    */
    
    active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
"""

## 5. TRADING STRATEGIES BY SECURITY TABLE
"""
CREATE TABLE security_strategies (
    id SERIAL PRIMARY KEY,
    security_type_id INTEGER REFERENCES security_types(id),
    strategy_name VARCHAR(255) NOT NULL,
    strategy_category VARCHAR(50),  -- MOMENTUM, MEAN_REVERSION, ARBITRAGE, MARKET_MAKING, etc.
    
    -- Strategy Parameters
    parameters JSONB,
    /*
    {
        "indicators": ["SMA", "RSI", "MACD"],
        "entry_rules": {},
        "exit_rules": {},
        "position_sizing": "kelly_criterion",
        "signal_strength_threshold": 0.7
    }
    */
    
    -- Performance Metrics
    backtest_results JSONB,
    expected_win_rate DECIMAL(5,2),
    expected_profit_factor DECIMAL(5,2),
    
    -- Applicability
    market_conditions JSONB,  -- Trending, ranging, volatile
    suitable_timeframes TEXT[],
    min_capital_required DECIMAL(15,2),
    
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
"""

## 6. RISK POLICIES BY SECURITY TABLE
"""
CREATE TABLE security_risk_policies (
    id SERIAL PRIMARY KEY,
    security_type_id INTEGER REFERENCES security_types(id),
    policy_name VARCHAR(255) NOT NULL,
    
    -- Position Limits
    max_single_position DECIMAL(15,2),
    max_total_exposure DECIMAL(15,2),
    max_positions_count INTEGER,
    concentration_limit DECIMAL(5,2),  -- Max % in single position
    
    -- Loss Limits
    daily_loss_limit DECIMAL(15,2),
    weekly_loss_limit DECIMAL(15,2),
    monthly_loss_limit DECIMAL(15,2),
    max_consecutive_losses INTEGER,
    
    -- Trade Controls
    max_trades_per_day INTEGER,
    min_trade_interval INTEGER,  -- Seconds between trades
    max_order_size JSONB,  -- By security type
    
    -- Risk Metrics
    stop_loss_rules JSONB,
    /*
    {
        "type": "PERCENTAGE",  -- or FIXED, ATR, VOLATILITY
        "value": 0.02,
        "trailing": true,
        "trail_activation": 0.01,
        "time_stop": 3600  -- seconds
    }
    */
    
    take_profit_rules JSONB,
    position_sizing_rules JSONB,
    
    -- Market Conditions
    volatility_filters JSONB,
    correlation_limits JSONB,
    
    active BOOLEAN DEFAULT true,
    enforcement_level VARCHAR(20),  -- STRICT, FLEXIBLE, WARNING_ONLY
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
"""

## 7. GOAL TRACKING TABLE
"""
CREATE TABLE investment_goals (
    id SERIAL PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id),
    security_objective_id INTEGER REFERENCES security_objectives(id),
    
    goal_name VARCHAR(255) NOT NULL,
    goal_type VARCHAR(50),  -- RETURN, INCOME, GROWTH, PRESERVATION
    
    -- Target Metrics
    target_value DECIMAL(15,2),
    target_date DATE,
    measurement_frequency VARCHAR(20),  -- DAILY, WEEKLY, MONTHLY
    
    -- Progress Tracking
    current_value DECIMAL(15,2),
    progress_percentage DECIMAL(5,2),
    on_track BOOLEAN,
    
    -- Milestones
    milestones JSONB,
    /*
    [
        {"date": "2025-03-01", "target": 1000, "achieved": false},
        {"date": "2025-06-01", "target": 2500, "achieved": false}
    ]
    */
    
    status VARCHAR(20),  -- ACTIVE, ACHIEVED, MISSED, PAUSED
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    achieved_at TIMESTAMP
);
"""

## 8. POLICY INHERITANCE TABLE
"""
CREATE TABLE policy_inheritance (
    id SERIAL PRIMARY KEY,
    parent_type VARCHAR(50),  -- GLOBAL, ASSET_CLASS, SECURITY_TYPE
    parent_id INTEGER,
    child_type VARCHAR(50),
    child_id INTEGER,
    
    inheritance_rules JSONB,
    /*
    {
        "inherit_risk_limits": true,
        "inherit_objectives": false,
        "override_allowed": true,
        "merge_strategy": "CHILD_PRIORITY"  -- or PARENT_PRIORITY, MERGE
    }
    */
    
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
"""

## 9. DYNAMIC ADJUSTMENT RULES TABLE
"""
CREATE TABLE dynamic_adjustments (
    id SERIAL PRIMARY KEY,
    security_type_id INTEGER REFERENCES security_types(id),
    adjustment_name VARCHAR(255),
    
    -- Trigger Conditions
    trigger_conditions JSONB,
    /*
    {
        "market_volatility": {"operator": ">", "value": 30},
        "account_drawdown": {"operator": ">", "value": 0.05},
        "win_rate_rolling": {"operator": "<", "value": 0.4, "period": 20}
    }
    */
    
    -- Adjustments
    adjustments JSONB,
    /*
    {
        "reduce_position_size": 0.5,
        "tighten_stops": 0.01,
        "pause_new_trades": true,
        "switch_strategy": "defensive"
    }
    */
    
    -- Reset Conditions
    reset_conditions JSONB,
    duration_minutes INTEGER,
    
    active BOOLEAN DEFAULT true,
    auto_apply BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
"""

## 10. PERFORMANCE BENCHMARKS TABLE
"""
CREATE TABLE security_benchmarks (
    id SERIAL PRIMARY KEY,
    security_type_id INTEGER REFERENCES security_types(id),
    benchmark_name VARCHAR(255),
    benchmark_symbol VARCHAR(20),  -- SPY, BTC, TLT, etc.
    
    -- Comparison Metrics
    comparison_metrics JSONB,
    /*
    {
        "alpha": true,
        "beta": true,
        "sharpe_ratio": true,
        "sortino_ratio": true,
        "max_drawdown": true,
        "correlation": true
    }
    */
    
    -- Performance Targets
    outperformance_target DECIMAL(5,2),  -- Percentage
    tracking_error_limit DECIMAL(5,2),
    
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
"""

## Example Usage Functions

def create_comprehensive_policy(user_id, security_type, risk_profile):
    """
    Creates a complete investment policy for a security type
    """
    # 1. Select appropriate objective template
    # 2. Create security-specific objective
    # 3. Assign risk policies based on profile
    # 4. Set up trading strategies
    # 5. Configure goals and benchmarks
    # 6. Apply inheritance rules
    # 7. Set dynamic adjustments
    
def get_active_policies_for_trade(symbol, security_type):
    """
    Retrieves all applicable policies for a trade
    """
    # 1. Get security type policies
    # 2. Check inheritance from asset class
    # 3. Apply global overrides
    # 4. Check dynamic adjustments
    # 5. Return consolidated policy
    
def evaluate_trade_against_policies(trade_params, policies):
    """
    Validates a trade against all active policies
    """
    # 1. Check objective alignment
    # 2. Validate risk limits
    # 3. Verify strategy rules
    # 4. Apply dynamic adjustments
    # 5. Return approval/rejection with reasons
