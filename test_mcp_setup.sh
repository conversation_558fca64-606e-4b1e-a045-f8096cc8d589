#!/bin/bash

echo "🔍 Testing IBKR MCP Setup"
echo "========================="

# Test 1: Check if configuration file exists
echo "1. Checking Claude Desktop configuration..."
if [ -f ~/.config/claude/claude_desktop_config.json ]; then
    echo "   ✅ Configuration file exists"
    echo "   📍 Location: ~/.config/claude/claude_desktop_config.json"
else
    echo "   ❌ Configuration file missing"
    exit 1
fi

# Test 2: Validate JSON syntax
echo "2. Validating JSON syntax..."
if python3 -m json.tool ~/.config/claude/claude_desktop_config.json > /dev/null 2>&1; then
    echo "   ✅ JSON syntax is valid"
else
    echo "   ❌ JSON syntax error"
    exit 1
fi

# Test 3: Check if IBKR server script exists and is executable
echo "3. Checking IBKR server script..."
IBKR_SCRIPT="/Users/<USER>/IBKR/b-team/ibkr_server.sh"
if [ -f "$IBKR_SCRIPT" ]; then
    echo "   ✅ IBKR server script exists"
    if [ -x "$IBKR_SCRIPT" ]; then
        echo "   ✅ Script is executable"
    else
        echo "   ❌ Script is not executable"
        echo "   🔧 Fix: chmod +x $IBKR_SCRIPT"
    fi
else
    echo "   ❌ IBKR server script missing"
    exit 1
fi

# Test 4: Check virtual environment
echo "4. Checking virtual environment..."
VENV_PYTHON="/Users/<USER>/IBKR/b-team/ibkr_mcp_server/source/.venv/bin/python3"
if [ -f "$VENV_PYTHON" ]; then
    echo "   ✅ Virtual environment exists"
    
    # Test MCP package
    if "$VENV_PYTHON" -c "import mcp.server" 2>/dev/null; then
        echo "   ✅ MCP package is installed"
    else
        echo "   ❌ MCP package missing"
        echo "   🔧 Fix: $VENV_PYTHON -m pip install mcp"
    fi
else
    echo "   ❌ Virtual environment missing"
    exit 1
fi

# Test 5: Check simple server file
echo "5. Checking simple MCP server..."
SIMPLE_SERVER="/Users/<USER>/IBKR/b-team/simple_ibkr_mcp_server.py"
if [ -f "$SIMPLE_SERVER" ]; then
    echo "   ✅ Simple MCP server exists"
    
    # Test syntax
    if "$VENV_PYTHON" -m py_compile "$SIMPLE_SERVER" 2>/dev/null; then
        echo "   ✅ Server syntax is valid"
    else
        echo "   ❌ Server syntax error"
    fi
else
    echo "   ❌ Simple MCP server missing"
    exit 1
fi

# Test 6: Count configured MCP servers
echo "6. Checking configured MCP servers..."
SERVER_COUNT=$(python3 -c "
import json
with open('$HOME/.config/claude/claude_desktop_config.json', 'r') as f:
    config = json.load(f)
print(len(config.get('mcpServers', {})))
")

echo "   📊 Found $SERVER_COUNT MCP servers configured:"
python3 -c "
import json
with open('$HOME/.config/claude/claude_desktop_config.json', 'r') as f:
    config = json.load(f)
for name in config.get('mcpServers', {}):
    print(f'     - {name}')
"

echo ""
echo "🎉 Setup Verification Complete!"
echo ""
echo "📋 Next Steps:"
echo "   1. Restart Claude Desktop application"
echo "   2. The following MCP servers should be available:"
echo "      • filesystem (file operations in your project)"
echo "      • brave-search (web search)"
echo "      • supabase (database operations)"
echo "      • ibkr-trading (Interactive Brokers tools)"
echo ""
echo "🧪 Test by asking Claude: 'What IBKR trading tools are available?'"
echo ""
echo "📝 If you encounter issues, check logs with:"
echo "   tail -f ~/Library/Logs/Claude/mcp*.log"
