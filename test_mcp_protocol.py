#!/usr/bin/env python3
"""
Test MCP Protocol Communication
Tests if our IBKR MCP server responds correctly to MCP protocol messages
"""

import json
import subprocess
import sys
import time
import threading
from queue import Queue, Empty

def test_mcp_server():
    """Test the MCP server with actual MCP protocol messages"""
    
    print("🧪 Testing IBKR MCP Server Protocol...")
    
    # Start the server process
    server_path = "/Users/<USER>/IBKR/b-team/ibkr_server.sh"
    
    try:
        # Start server process
        process = subprocess.Popen(
            [server_path],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0
        )
        
        print("✅ Server process started")
        
        # Give server time to start
        time.sleep(2)
        
        # Test 1: Send initialize message
        initialize_msg = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        print("📤 Sending initialize message...")
        process.stdin.write(json.dumps(initialize_msg) + "\n")
        process.stdin.flush()
        
        # Read response with timeout
        def read_output(process, queue):
            try:
                line = process.stdout.readline()
                if line:
                    queue.put(line.strip())
            except:
                pass
        
        output_queue = Queue()
        read_thread = threading.Thread(target=read_output, args=(process, output_queue))
        read_thread.daemon = True
        read_thread.start()
        
        try:
            response = output_queue.get(timeout=5)
            print(f"📥 Received response: {response[:100]}...")
            
            # Try to parse as JSON
            try:
                response_data = json.loads(response)
                if "result" in response_data:
                    print("✅ Valid MCP initialize response received")
                else:
                    print("❌ Invalid MCP response format")
            except json.JSONDecodeError:
                print("❌ Response is not valid JSON")
                
        except Empty:
            print("❌ No response received within timeout")
        
        # Test 2: Send tools/list message
        tools_msg = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list"
        }
        
        print("📤 Sending tools/list message...")
        process.stdin.write(json.dumps(tools_msg) + "\n")
        process.stdin.flush()
        
        try:
            response = output_queue.get(timeout=5)
            print(f"📥 Received tools response: {response[:100]}...")
            
            try:
                response_data = json.loads(response)
                if "result" in response_data and "tools" in response_data["result"]:
                    tool_count = len(response_data["result"]["tools"])
                    print(f"✅ Found {tool_count} tools in response")
                else:
                    print("❌ Invalid tools response format")
            except json.JSONDecodeError:
                print("❌ Tools response is not valid JSON")
                
        except Empty:
            print("❌ No tools response received within timeout")
        
    except Exception as e:
        print(f"❌ Error testing server: {e}")
    
    finally:
        # Clean up
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            process.kill()
        print("🧹 Server process terminated")

if __name__ == "__main__":
    test_mcp_server()
