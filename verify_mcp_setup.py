#!/usr/bin/env python3
"""
MCP Setup Verification Script
Verifies that all MCP server components are properly configured
"""

import os
import sys
import json
import subprocess
from pathlib import Path

def check_file_exists(file_path, description):
    """Check if a file exists and report status"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (NOT FOUND)")
        return False

def check_directory_exists(dir_path, description):
    """Check if a directory exists and report status"""
    if os.path.isdir(dir_path):
        print(f"✅ {description}: {dir_path}")
        return True
    else:
        print(f"❌ {description}: {dir_path} (NOT FOUND)")
        return False

def check_python_import(module_name, python_path=None):
    """Check if a Python module can be imported"""
    if python_path is None:
        python_path = sys.executable
    
    try:
        result = subprocess.run([python_path, '-c', f'import {module_name}'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Python module '{module_name}' can be imported")
            return True
        else:
            print(f"❌ Python module '{module_name}' import failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error checking Python module '{module_name}': {e}")
        return False

def verify_json_config(config_path):
    """Verify JSON configuration file is valid"""
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        print(f"✅ JSON config is valid: {config_path}")
        return True, config
    except Exception as e:
        print(f"❌ JSON config error in {config_path}: {e}")
        return False, None

def main():
    print("🔍 MCP Setup Verification")
    print("=" * 50)
    
    base_dir = "/Users/<USER>/IBKR/b-team"
    issues = []
    
    # Check base directory
    if not check_directory_exists(base_dir, "Base directory"):
        issues.append("Base directory missing")
        return
    
    # Check key files
    key_files = [
        (f"{base_dir}/mcp_server_main.py", "Main MCP server"),
        (f"{base_dir}/ibkr_server.sh", "IBKR server script"),
        (f"{base_dir}/claude_desktop_config_fixed.json", "Fixed config file"),
    ]
    
    for file_path, description in key_files:
        if not check_file_exists(file_path, description):
            issues.append(f"Missing {description}")
    
    # Check virtual environment
    venv_path = f"{base_dir}/ibkr_mcp_server/source/.venv"
    venv_python = f"{venv_path}/bin/python3"
    
    if check_directory_exists(venv_path, "Virtual environment"):
        if check_file_exists(venv_python, "Python executable"):
            # Check required packages
            packages = ['mcp.server', 'ib_insync', 'fastapi']
            for package in packages:
                if not check_python_import(package, venv_python):
                    issues.append(f"Missing Python package: {package}")
    else:
        issues.append("Virtual environment missing")
    
    # Check configuration files
    config_files = [
        f"{base_dir}/claude_desktop_config.json",
        f"{base_dir}/claude_desktop_config_fixed.json",
        f"{base_dir}/claude_desktop_config_final.json"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            valid, config = verify_json_config(config_file)
            if valid and 'mcpServers' in config:
                print(f"  📋 Found {len(config['mcpServers'])} MCP servers configured")
                for server_name in config['mcpServers'].keys():
                    print(f"    - {server_name}")
    
    # Check script permissions
    script_path = f"{base_dir}/ibkr_server.sh"
    if os.path.exists(script_path):
        if os.access(script_path, os.X_OK):
            print(f"✅ Script is executable: {script_path}")
        else:
            print(f"❌ Script is not executable: {script_path}")
            issues.append("Script not executable")
    
    # Summary
    print("\n" + "=" * 50)
    if issues:
        print(f"❌ Found {len(issues)} issues:")
        for issue in issues:
            print(f"  - {issue}")
        print("\n🔧 Run the fix script to resolve these issues.")
    else:
        print("✅ All checks passed! MCP setup looks good.")
        print("\n🚀 You can now use the fixed configuration:")
        print(f"   cp {base_dir}/claude_desktop_config_fixed.json ~/.config/claude/claude_desktop_config.json")

if __name__ == "__main__":
    main()
