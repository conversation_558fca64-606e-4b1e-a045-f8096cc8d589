<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IBKR Trading Platform - Interactive Architecture Diagram</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a1f36 100%);
            color: #e8eaed;
            overflow-x: auto;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: rgba(26, 31, 54, 0.8);
            border-radius: 10px;
            border: 1px solid #4bffb5;
        }

        .header h1 {
            color: #4bffb5;
            margin: 0 0 10px 0;
            font-size: 2.5em;
            text-shadow: 0 0 10px rgba(75, 255, 181, 0.3);
        }

        .header p {
            color: #8892b0;
            margin: 5px 0;
            font-size: 1.1em;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #64ffda 0%, #4bffb5 100%);
            color: #0a192f;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(75, 255, 181, 0.4);
        }

        .btn.active {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
        }

        .visualization {
            flex: 1;
            display: flex;
            gap: 20px;
            min-height: 800px;
        }

        @media (max-width: 1200px) {
            .visualization {
                flex-direction: column;
                gap: 15px;
            }
            
            .network-panel, .dendro-panel {
                min-height: 600px;
                max-height: 70vh;
            }
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
                margin: 2px 0;
            }
        }

        .network-panel, .dendro-panel {
            flex: 1;
            background: rgba(26, 31, 54, 0.6);
            border-radius: 10px;
            border: 1px solid #3d4663;
            padding: 15px;
            min-height: 800px;
            max-height: 95vh;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        #network-diagram, #dendrogram {
            flex: 1;
            width: 100%;
            overflow: hidden;
        }

        .panel-title {
            color: #4bffb5;
            font-size: 1.4em;
            margin-bottom: 15px;
            text-align: center;
            font-weight: bold;
        }

        .tooltip {
            position: absolute;
            background: rgba(15, 20, 25, 0.95);
            color: #e8eaed;
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #4bffb5;
            font-size: 13px;
            pointer-events: none;
            z-index: 1000;
            max-width: 300px;
            line-height: 1.4;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        }

        .tooltip .title {
            color: #4bffb5;
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .tooltip .type {
            color: #64ffda;
            font-style: italic;
            margin-bottom: 8px;
        }

        .tooltip .description {
            color: #ccd6f6;
            font-size: 12px;
        }

        .legend {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(15, 20, 25, 0.9);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #4bffb5;
            z-index: 1001;
            min-width: 200px;
        }

        .legend h3 {
            color: #4bffb5;
            margin: 0 0 10px 0;
            font-size: 16px;
        }

        .zoom-controls {
            position: absolute;
            top: 50px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 5px;
            z-index: 1000;
        }

        .zoom-btn {
            width: 35px;
            height: 35px;
            background: rgba(75, 255, 181, 0.2);
            border: 1px solid #4bffb5;
            border-radius: 50%;
            color: #4bffb5;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .zoom-btn:hover {
            background: rgba(75, 255, 181, 0.4);
            transform: scale(1.1);
        }

        .reset-btn {
            width: 60px;
            height: 25px;
            font-size: 10px;
            border-radius: 4px;
            padding: 0;
        }

        .pan-indicator {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: #8892b0;
            font-size: 11px;
            background: rgba(26, 31, 54, 0.8);
            padding: 5px 10px;
            border-radius: 4px;
            border: 1px solid #3d4663;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
            font-size: 12px;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .legend-color.frontend { background: #4bffb5; }
        .legend-color.api { background: #64ffda; }
        .legend-color.backend { background: #ffd93d; }
        .legend-color.database { background: #ff6b6b; }
        .legend-color.infrastructure { background: #a8e6cf; }
        .legend-color.external { background: #ff8c94; }
        .legend-color.event { background: #9b59b6; }

        .gap-analysis {
            margin-top: 20px;
            padding: 20px;
            background: rgba(26, 31, 54, 0.8);
            border-radius: 10px;
            border: 1px solid #ff6b6b;
        }

        .gap-analysis h2 {
            color: #ff6b6b;
            margin-bottom: 15px;
        }

        .gap-item {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 107, 107, 0.1);
            border-radius: 5px;
            border-left: 3px solid #ff6b6b;
        }

        .recommendations {
            margin-top: 20px;
            padding: 20px;
            background: rgba(26, 31, 54, 0.8);
            border-radius: 10px;
            border: 1px solid #4bffb5;
        }

        .recommendations h2 {
            color: #4bffb5;
            margin-bottom: 15px;
        }

        .rec-item {
            margin: 10px 0;
            padding: 10px;
            background: rgba(75, 255, 181, 0.1);
            border-radius: 5px;
            border-left: 3px solid #4bffb5;
        }

        .link {
            stroke: #3d4663;
            stroke-width: 1;
            opacity: 0.6;
        }

        .link.active {
            stroke: #4bffb5;
            stroke-width: 2;
            opacity: 1;
        }

        .node circle {
            stroke: #3d4663;
            stroke-width: 2;
            cursor: pointer;
        }

        .node text {
            font-size: 11px;
            font-weight: bold;
            text-anchor: middle;
            pointer-events: none;
            fill: #e8eaed;
        }

        .node:hover circle {
            stroke: #4bffb5;
            stroke-width: 3;
        }

        .dendro-node circle {
            stroke: #3d4663;
            stroke-width: 1.5;
            cursor: pointer;
        }

        .dendro-node text {
            font-size: 10px;
            fill: #ccd6f6;
        }

        .dendro-link {
            fill: none;
            stroke: #3d4663;
            stroke-width: 1.5;
        }

        @media (max-width: 1200px) {
            .visualization {
                flex-direction: column;
            }
            
            .legend {
                position: relative;
                top: auto;
                right: auto;
                margin: 20px auto;
                width: fit-content;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ IBKR Trading Platform Architecture</h1>
            <p>Interactive Network Graph & Dendrogram Visualization</p>
            <p><strong>Comprehensive Forensic Analysis</strong> • D3.js Interactive Exploration</p>
        </div>

        <div class="controls">
            <button class="btn active" onclick="showAll()">Show All Components</button>
            <button class="btn" onclick="filterByLayer('frontend')">Frontend Layer</button>
            <button class="btn" onclick="filterByLayer('backend')">Backend Services</button>
            <button class="btn" onclick="filterByLayer('database')">Database Layer</button>
            <button class="btn" onclick="filterByLayer('infrastructure')">Infrastructure</button>
            <button class="btn" onclick="highlightCriticalPath()">Critical Path</button>
            <button class="btn" onclick="resetVisualization()">Reset View</button>
        </div>

        <div class="visualization">
            <div class="network-panel">
                <div class="panel-title">🌐 Interactive Network Graph</div>
                <div class="zoom-controls">
                    <button class="zoom-btn" id="zoom-in" title="Zoom In">+</button>
                    <button class="zoom-btn" id="zoom-out" title="Zoom Out">−</button>
                    <button class="zoom-btn reset-btn" id="reset-zoom" title="Reset View">⌂</button>
                </div>
                <div class="pan-indicator">
                    💡 Click + drag to pan • Scroll to zoom • Drag nodes to rearrange • Double-click to fit
                </div>
                <div id="network-diagram"></div>
            </div>
            <div class="dendro-panel">
                <div class="panel-title">🌳 Hierarchical Dendrogram</div>
                <div class="zoom-controls">
                    <button class="zoom-btn" id="dendro-zoom-in" title="Zoom In">+</button>
                    <button class="zoom-btn" id="dendro-zoom-out" title="Zoom Out">−</button>
                    <button class="zoom-btn reset-btn" id="dendro-reset-zoom" title="Reset View">⌂</button>
                </div>
                <div class="pan-indicator">
                    💡 Click + drag to pan • Scroll to zoom • Click nodes to expand/collapse • Double-click to fit
                </div>
                <div id="dendrogram"></div>
            </div>
        </div>

        <div class="legend">
            <h3>Component Types</h3>
            <div class="legend-item">
                <div class="legend-color frontend"></div>
                Frontend Components
            </div>
            <div class="legend-item">
                <div class="legend-color api"></div>
                API Services
            </div>
            <div class="legend-item">
                <div class="legend-color backend"></div>
                Backend Services
            </div>
            <div class="legend-item">
                <div class="legend-color database"></div>
                Database Layer
            </div>
            <div class="legend-item">
                <div class="legend-color infrastructure"></div>
                Infrastructure
            </div>
            <div class="legend-item">
                <div class="legend-color external"></div>
                External Systems
            </div>
            <div class="legend-item">
                <div class="legend-color event"></div>
                Event Processing
            </div>
        </div>
    </div>

    <div class="gap-analysis">
        <h2>🔍 Gap Analysis</h2>
        <div class="gap-item">
            <strong>Critical:</strong> Pub/Sub Error Recovery - Google Pub/Sub integration needs comprehensive error handling and dead letter queue management
        </div>
        <div class="gap-item">
            <strong>High:</strong> Webhook Security - Webhook processor requires authentication, rate limiting, and request validation
        </div>
        <div class="gap-item">
            <strong>High:</strong> Alert System Scaling - Alert system needs message deduplication and priority queuing for high-frequency events
        </div>
        <div class="gap-item">
            <strong>Medium:</strong> Event System Monitoring - Missing comprehensive monitoring and metrics for event processing pipeline
        </div>
        <div class="gap-item">
            <strong>Medium:</strong> Trigger Engine Reliability - Trigger execution needs retry logic and failure recovery mechanisms
        </div>
        <div class="gap-item">
            <strong>Low:</strong> Integration Adapter Configuration - Dynamic configuration management for third-party integrations
        </div>
    </div>

    <div class="recommendations">
        <h2>💡 Architecture Recommendations</h2>
        <div class="rec-item">
            <strong>Immediate:</strong> Implement Pub/Sub dead letter queues and error recovery patterns for event processing reliability
        </div>
        <div class="rec-item">
            <strong>Short-term:</strong> Add webhook authentication and rate limiting to secure external API endpoints
        </div>
        <div class="rec-item">
            <strong>Medium-term:</strong> Implement event sourcing patterns for audit trails and system state reconstruction
        </div>
        <div class="rec-item">
            <strong>Long-term:</strong> Create unified event schema registry for cross-system event compatibility
        </div>
        <div class="rec-item">
            <strong>Ongoing:</strong> Add comprehensive monitoring for Pub/Sub topics, webhook deliveries, and trigger executions
        </div>
    </div>

    <script>
        // Architecture data based on forensic analysis
        const architectureData = {
            nodes: [
                // Frontend Layer
                { id: "App.tsx", layer: "frontend", type: "frontend", size: 20, description: "Main React application container with navigation and routing" },
                { id: "TradingDashboard", layer: "frontend", type: "frontend", size: 18, description: "Primary trading interface with real-time market data" },
                { id: "BacktestingSuite", layer: "frontend", type: "frontend", size: 16, description: "Backtesting analysis and visualization suite" },
                { id: "SystemMonitoring", layer: "frontend", type: "frontend", size: 14, description: "System health and performance monitoring dashboard" },
                { id: "DXChart", layer: "frontend", type: "frontend", size: 15, description: "Advanced charting component using DXCharts library" },
                { id: "TVChart", layer: "frontend", type: "frontend", size: 12, description: "Legacy TradingView-style chart component" },
                
                // API Services Layer
                { id: "trading.ts", layer: "api", type: "api", size: 16, description: "Frontend API client for trading operations" },
                { id: "websocket.ts", layer: "api", type: "api", size: 14, description: "WebSocket client for real-time data streaming" },
                { id: "backtesting.ts", layer: "api", type: "api", size: 12, description: "Backtesting API client and data management" },
                
                // Backend Services Layer
                { id: "FastAPI App", layer: "backend", type: "backend", size: 22, description: "Main FastAPI application server with REST endpoints" },
                { id: "IBKR Service", layer: "backend", type: "backend", size: 20, description: "Core IBKR API integration and connection management" },
                { id: "Order Management", layer: "backend", type: "backend", size: 18, description: "Order execution and lifecycle management service" },
                { id: "MCP Server", layer: "backend", type: "backend", size: 16, description: "Model Context Protocol server for AI integration" },
                { id: "WebSocket Server", layer: "backend", type: "backend", size: 15, description: "Real-time data streaming and event broadcasting" },
                { id: "Phase2 Service", layer: "backend", type: "backend", size: 14, description: "Advanced trading features and algorithm execution" },
                { id: "Connection Manager", layer: "backend", type: "backend", size: 12, description: "Auto-reconnection and connection health management" },
                
                // Infrastructure Layer
                { id: "Service Manager", layer: "infrastructure", type: "infrastructure", size: 16, description: "Service lifecycle and dependency management" },
                { id: "Domain Manager", layer: "infrastructure", type: "infrastructure", size: 14, description: "Multi-layer architecture coordination" },
                { id: "Event System", layer: "infrastructure", type: "infrastructure", size: 12, description: "Event processing and message routing" },
                { id: "Safety Manager", layer: "infrastructure", type: "infrastructure", size: 14, description: "Risk management and safety controls" },
                { id: "Circuit Breaker", layer: "infrastructure", type: "infrastructure", size: 10, description: "Fault tolerance and error recovery" },
                
                // Database Layer
                { id: "Supabase DB", layer: "database", type: "database", size: 20, description: "Primary PostgreSQL database with real-time features" },
                { id: "Base Repository", layer: "database", type: "database", size: 14, description: "Common CRUD operations and data access patterns" },
                { id: "Forex Repositories", layer: "database", type: "database", size: 16, description: "FOREX-specific data repositories and models" },
                { id: "Backtesting Repos", layer: "database", type: "database", size: 12, description: "Backtesting data management and storage" },
                { id: "Database Models", layer: "database", type: "database", size: 12, description: "SQLAlchemy-style models and data structures" },
                
                // External Systems
                { id: "TWS/Gateway", layer: "external", type: "external", size: 18, description: "Interactive Brokers Trader Workstation" },
                { id: "IBKR API", layer: "external", type: "external", size: 16, description: "Interactive Brokers API for trading operations" },
                { id: "Supabase Edge", layer: "external", type: "external", size: 12, description: "Serverless edge functions for data processing" },
                { id: "Google PubSub", layer: "external", type: "external", size: 14, description: "Google Cloud Pub/Sub messaging service for event streaming" },
                
                // Event Processing Layer
                { id: "PubSub Manager", layer: "backend", type: "backend", size: 14, description: "Core Google Pub/Sub integration and message management" },
                { id: "Hybrid Event System", layer: "infrastructure", type: "infrastructure", size: 16, description: "Unified event processing system combining multiple event sources" },
                { id: "Webhook Processor", layer: "infrastructure", type: "infrastructure", size: 12, description: "Webhook processing and HTTP callback management" },
                { id: "Alert System", layer: "infrastructure", type: "infrastructure", size: 13, description: "Real-time alert generation and notification system" },
                { id: "Trigger Engine", layer: "infrastructure", type: "infrastructure", size: 11, description: "Event-driven trigger processing and execution" },
                { id: "Enhanced Manager", layer: "backend", type: "backend", size: 12, description: "Enhanced Pub/Sub management with reliability features" },
                { id: "IBKR PubSub Bridge", layer: "backend", type: "backend", size: 13, description: "Bridge connecting IBKR data streams to Pub/Sub topics" },
                { id: "Integration Adapter", layer: "backend", type: "backend", size: 10, description: "Adapter for third-party system integration via Pub/Sub" }
            ],
            
            links: [
                // Frontend to API connections
                { source: "App.tsx", target: "TradingDashboard", type: "contains" },
                { source: "App.tsx", target: "BacktestingSuite", type: "contains" },
                { source: "App.tsx", target: "SystemMonitoring", type: "contains" },
                { source: "TradingDashboard", target: "DXChart", type: "uses" },
                { source: "TradingDashboard", target: "TVChart", type: "uses" },
                { source: "TradingDashboard", target: "trading.ts", type: "calls" },
                { source: "TradingDashboard", target: "websocket.ts", type: "calls" },
                { source: "BacktestingSuite", target: "backtesting.ts", type: "calls" },
                { source: "SystemMonitoring", target: "trading.ts", type: "calls" },
                
                // API to Backend connections
                { source: "trading.ts", target: "FastAPI App", type: "http" },
                { source: "websocket.ts", target: "WebSocket Server", type: "websocket" },
                { source: "backtesting.ts", target: "FastAPI App", type: "http" },
                
                // Backend service interconnections
                { source: "FastAPI App", target: "IBKR Service", type: "depends" },
                { source: "FastAPI App", target: "Order Management", type: "depends" },
                { source: "FastAPI App", target: "WebSocket Server", type: "integrates" },
                { source: "IBKR Service", target: "Order Management", type: "provides" },
                { source: "IBKR Service", target: "Connection Manager", type: "uses" },
                { source: "MCP Server", target: "IBKR Service", type: "controls" },
                { source: "MCP Server", target: "Phase2 Service", type: "manages" },
                { source: "Phase2 Service", target: "Service Manager", type: "managed_by" },
                
                // Infrastructure connections
                { source: "Service Manager", target: "Domain Manager", type: "coordinates" },
                { source: "Domain Manager", target: "Event System", type: "uses" },
                { source: "Safety Manager", target: "Circuit Breaker", type: "implements" },
                { source: "Safety Manager", target: "IBKR Service", type: "monitors" },
                
                // Database connections
                { source: "IBKR Service", target: "Supabase DB", type: "writes" },
                { source: "Order Management", target: "Forex Repositories", type: "uses" },
                { source: "Phase2 Service", target: "Base Repository", type: "extends" },
                { source: "Forex Repositories", target: "Base Repository", type: "inherits" },
                { source: "Backtesting Repos", target: "Base Repository", type: "inherits" },
                { source: "Base Repository", target: "Supabase DB", type: "queries" },
                { source: "Database Models", target: "Supabase DB", type: "maps" },
                
                // External system connections
                { source: "IBKR Service", target: "TWS/Gateway", type: "connects" },
                { source: "Connection Manager", target: "TWS/Gateway", type: "manages" },
                { source: "TWS/Gateway", target: "IBKR API", type: "exposes" },
                { source: "Supabase DB", target: "Supabase Edge", type: "triggers" },
                
                // Google Pub/Sub and Event System connections
                { source: "PubSub Manager", target: "Google PubSub", type: "publishes" },
                { source: "Enhanced Manager", target: "PubSub Manager", type: "enhances" },
                { source: "IBKR PubSub Bridge", target: "PubSub Manager", type: "uses" },
                { source: "IBKR Service", target: "IBKR PubSub Bridge", type: "feeds" },
                { source: "Integration Adapter", target: "PubSub Manager", type: "integrates" },
                { source: "FastAPI App", target: "PubSub Manager", type: "depends" },
                
                // Hybrid Event System connections
                { source: "Hybrid Event System", target: "Event System", type: "extends" },
                { source: "Hybrid Event System", target: "PubSub Manager", type: "coordinates" },
                { source: "Hybrid Event System", target: "Webhook Processor", type: "manages" },
                { source: "Hybrid Event System", target: "Alert System", type: "triggers" },
                { source: "Hybrid Event System", target: "Trigger Engine", type: "controls" },
                
                // Webhook, Alert, and Trigger System connections
                { source: "Webhook Processor", target: "Google PubSub", type: "subscribes" },
                { source: "Alert System", target: "Webhook Processor", type: "uses" },
                { source: "Trigger Engine", target: "Alert System", type: "activates" },
                { source: "Google PubSub", target: "Alert System", type: "notifies" },
                { source: "Google PubSub", target: "Trigger Engine", type: "triggers" },
                { source: "Safety Manager", target: "Alert System", type: "monitors" },
                { source: "Order Management", target: "Trigger Engine", type: "responds_to" },
                
                // WebSocket integration with Pub/Sub
                { source: "WebSocket Server", target: "PubSub Manager", type: "broadcasts" },
                { source: "Google PubSub", target: "WebSocket Server", type: "feeds" }
            ]
        };

        // Color scheme for different component types
        const colors = {
            frontend: "#4bffb5",
            api: "#64ffda", 
            backend: "#ffd93d",
            infrastructure: "#a8e6cf",
            database: "#ff6b6b",
            external: "#ff8c94",
            event: "#9b59b6"
        };

        // Initialize network diagram
        function initNetworkDiagram() {
            const container = document.getElementById('network-diagram');
            const width = container.clientWidth;
            const height = 750; // Increased height

            const svg = d3.select("#network-diagram")
                .append("svg")
                .attr("width", width)
                .attr("height", height)
                .style("background", "rgba(15, 20, 25, 0.3)")
                .style("border-radius", "8px");

            // Create zoom behavior
            const zoom = d3.zoom()
                .scaleExtent([0.1, 4])
                .on("zoom", function(event) {
                    g.attr("transform", event.transform);
                    // Update zoom indicator
                    updateZoomIndicator(event.transform.k);
                });

            svg.call(zoom)
                .on("dblclick.zoom", function() {
                    // Double-click to fit content
                    svg.transition().duration(500).call(
                        zoom.transform,
                        d3.zoomIdentity.scale(0.8).translate(50, 50)
                    );
                });

            // Add zoom level indicator
            const zoomIndicator = d3.select(container)
                .append("div")
                .style("position", "absolute")
                .style("top", "10px")
                .style("left", "10px")
                .style("background", "rgba(26, 31, 54, 0.9)")
                .style("padding", "5px 10px")
                .style("border-radius", "4px")
                .style("font-size", "11px")
                .style("color", "#8892b0")
                .style("border", "1px solid #3d4663")
                .text("Zoom: 100%");

            function updateZoomIndicator(scale) {
                zoomIndicator.text(`Zoom: ${Math.round(scale * 100)}%`);
            }

            // Create main group for zoomable content
            const g = svg.append("g");

            // Create tooltip
            const tooltip = d3.select("body").append("div")
                .attr("class", "tooltip")
                .style("opacity", 0);

            // Create simulation with better positioning
            const simulation = d3.forceSimulation(architectureData.nodes)
                .force("link", d3.forceLink(architectureData.links).id(d => d.id).distance(100))
                .force("charge", d3.forceManyBody().strength(-400))
                .force("center", d3.forceCenter(width / 2, height / 2))
                .force("collision", d3.forceCollide().radius(d => d.size + 8))
                .force("x", d3.forceX(width / 2).strength(0.05))
                .force("y", d3.forceY(height / 2).strength(0.05));

            // Create links
            const link = g.append("g")
                .selectAll("line")
                .data(architectureData.links)
                .enter().append("line")
                .attr("class", "link")
                .style("stroke-width", d => d.type === "critical" ? 3 : 1.5)
                .style("stroke", "#3d4663")
                .style("opacity", 0.6);

            // Create nodes
            const node = g.append("g")
                .selectAll("g")
                .data(architectureData.nodes)
                .enter().append("g")
                .attr("class", "node")
                .call(d3.drag()
                    .on("start", dragstarted)
                    .on("drag", dragged)
                    .on("end", dragended));

            // Add circles to nodes
            node.append("circle")
                .attr("r", d => d.size)
                .style("fill", d => colors[d.type])
                .style("opacity", 0.9)
                .style("stroke", "#2a3441")
                .style("stroke-width", 2);

            // Add labels to nodes
            node.append("text")
                .text(d => d.id)
                .attr("dy", d => d.size + 18)
                .style("font-size", "11px")
                .style("fill", "#e8eaed")
                .style("text-anchor", "middle")
                .style("font-weight", "bold");

            // Zoom control event handlers
            d3.select("#zoom-in").on("click", function() {
                svg.transition().duration(300).call(
                    zoom.scaleBy, 1.3
                );
            });

            d3.select("#zoom-out").on("click", function() {
                svg.transition().duration(300).call(
                    zoom.scaleBy, 1 / 1.3
                );
            });

            d3.select("#reset-zoom").on("click", function() {
                svg.transition().duration(500).call(
                    zoom.transform,
                    d3.zoomIdentity
                );
            });

            // Add hover events
            node.on("mouseover", function(event, d) {
                tooltip.transition().duration(200).style("opacity", .9);
                tooltip.html(`
                    <div class="title">${d.id}</div>
                    <div class="type">${d.type.toUpperCase()} Component</div>
                    <div class="description">${d.description}</div>
                `)
                .style("left", (event.pageX + 10) + "px")
                .style("top", (event.pageY - 28) + "px");
                
                // Highlight connected nodes
                highlightConnected(d);
            })
            .on("mouseout", function(d) {
                tooltip.transition().duration(500).style("opacity", 0);
                resetHighlight();
            });

            // Update simulation
            simulation.on("tick", () => {
                link
                    .attr("x1", d => d.source.x)
                    .attr("y1", d => d.source.y)
                    .attr("x2", d => d.target.x)
                    .attr("y2", d => d.target.y);

                node.attr("transform", d => `translate(${d.x},${d.y})`);
            });

            // Drag functions
            function dragstarted(event) {
                if (!event.active) simulation.alphaTarget(0.3).restart();
                event.subject.fx = event.subject.x;
                event.subject.fy = event.subject.y;
            }

            function dragged(event) {
                event.subject.fx = event.x;
                event.subject.fy = event.y;
            }

            function dragended(event) {
                if (!event.active) simulation.alphaTarget(0);
                event.subject.fx = null;
                event.subject.fy = null;
            }

            // Highlight connected nodes
            function highlightConnected(selectedNode) {
                const connectedNodes = new Set();
                connectedNodes.add(selectedNode.id);

                architectureData.links.forEach(link => {
                    if (link.source.id === selectedNode.id) {
                        connectedNodes.add(link.target.id);
                    }
                    if (link.target.id === selectedNode.id) {
                        connectedNodes.add(link.source.id);
                    }
                });

                node.style("opacity", d => connectedNodes.has(d.id) ? 1 : 0.3);
                link.style("opacity", d => 
                    d.source.id === selectedNode.id || d.target.id === selectedNode.id ? 1 : 0.1
                ).classed("active", d => 
                    d.source.id === selectedNode.id || d.target.id === selectedNode.id
                );
            }

            function resetHighlight() {
                node.style("opacity", 1);
                link.style("opacity", 0.6).classed("active", false);
            }

            // Store references for global access
            window.networkElements = { svg, node, link, simulation };
        }

        // Initialize dendrogram
        function initDendrogram() {
            const container = document.getElementById('dendrogram');
            const width = container.clientWidth;
            const height = 750; // Increased height

            // Create hierarchical data structure
            const hierarchyData = {
                name: "IBKR Trading Platform",
                children: [
                    {
                        name: "Frontend Layer",
                        children: architectureData.nodes.filter(n => n.layer === "frontend").map(n => ({
                            name: n.id,
                            description: n.description,
                            type: n.type
                        }))
                    },
                    {
                        name: "API Services",
                        children: architectureData.nodes.filter(n => n.layer === "api").map(n => ({
                            name: n.id,
                            description: n.description,
                            type: n.type
                        }))
                    },
                    {
                        name: "Backend Services",
                        children: architectureData.nodes.filter(n => n.layer === "backend").map(n => ({
                            name: n.id,
                            description: n.description,
                            type: n.type
                        }))
                    },
                    {
                        name: "Infrastructure",
                        children: architectureData.nodes.filter(n => n.layer === "infrastructure").map(n => ({
                            name: n.id,
                            description: n.description,
                            type: n.type
                        }))
                    },
                    {
                        name: "Database Layer",
                        children: architectureData.nodes.filter(n => n.layer === "database").map(n => ({
                            name: n.id,
                            description: n.description,
                            type: n.type
                        }))
                    },
                    {
                        name: "External Systems",
                        children: architectureData.nodes.filter(n => n.layer === "external").map(n => ({
                            name: n.id,
                            description: n.description,
                            type: n.type
                        }))
                    }
                ]
            };

            const svg = d3.select("#dendrogram")
                .append("svg")
                .attr("width", width)
                .attr("height", height)
                .style("background", "rgba(15, 20, 25, 0.3)")
                .style("border-radius", "8px");

            // Create zoom behavior for dendrogram
            const dendroZoom = d3.zoom()
                .scaleExtent([0.1, 3])
                .on("zoom", function(event) {
                    dendroG.attr("transform", event.transform);
                    // Update zoom indicator
                    updateDendroZoomIndicator(event.transform.k);
                });

            svg.call(dendroZoom)
                .on("dblclick.zoom", function() {
                    // Double-click to fit content
                    svg.transition().duration(500).call(
                        dendroZoom.transform,
                        d3.zoomIdentity.scale(0.9).translate(25, 25)
                    );
                });

            // Add zoom level indicator for dendrogram
            const dendroZoomIndicator = d3.select(container)
                .append("div")
                .style("position", "absolute")
                .style("top", "10px")
                .style("left", "10px")
                .style("background", "rgba(26, 31, 54, 0.9)")
                .style("padding", "5px 10px")
                .style("border-radius", "4px")
                .style("font-size", "11px")
                .style("color", "#8892b0")
                .style("border", "1px solid #3d4663")
                .text("Zoom: 100%");

            function updateDendroZoomIndicator(scale) {
                dendroZoomIndicator.text(`Zoom: ${Math.round(scale * 100)}%`);
            }

            // Create main group for zoomable content
            const dendroG = svg.append("g");

            const root = d3.hierarchy(hierarchyData);
            const treeLayout = d3.tree().size([height - 150, width - 250]);
            treeLayout(root);

            // Create tooltip
            const tooltip = d3.select("body").append("div")
                .attr("class", "tooltip")
                .style("opacity", 0);

            // Draw links
            dendroG.selectAll('.dendro-link')
                .data(root.links())
                .enter()
                .append('path')
                .attr('class', 'dendro-link')
                .attr('d', d3.linkHorizontal()
                    .x(d => d.y + 150)
                    .y(d => d.x + 75))
                .style('stroke', '#3d4663')
                .style('stroke-width', '2px')
                .style('fill', 'none')
                .style('opacity', 0.6);

            // Draw nodes
            const nodes = dendroG.selectAll('.dendro-node')
                .data(root.descendants())
                .enter()
                .append('g')
                .attr('class', 'dendro-node')
                .attr('transform', d => `translate(${d.y + 150},${d.x + 75})`);

            nodes.append('circle')
                .attr('r', d => d.children ? 10 : 6)
                .style('fill', d => d.data.type ? colors[d.data.type] : '#64ffda')
                .style('opacity', 0.9)
                .style('stroke', '#2a3441')
                .style('stroke-width', '2px');

            // Dendrogram zoom control event handlers
            d3.select("#dendro-zoom-in").on("click", function() {
                svg.transition().duration(300).call(
                    dendroZoom.scaleBy, 1.3
                );
            });

            d3.select("#dendro-zoom-out").on("click", function() {
                svg.transition().duration(300).call(
                    dendroZoom.scaleBy, 1 / 1.3
                );
            });

            d3.select("#dendro-reset-zoom").on("click", function() {
                svg.transition().duration(500).call(
                    dendroZoom.transform,
                    d3.zoomIdentity
                );
            });

            nodes.append('text')
                .attr('dy', 3)
                .attr('x', d => d.children ? -12 : 12)
                .style('text-anchor', d => d.children ? 'end' : 'start')
                .text(d => d.data.name)
                .style('font-size', d => d.children ? '12px' : '10px')
                .style('font-weight', d => d.children ? 'bold' : 'normal');

            // Add hover events
            nodes.on("mouseover", function(event, d) {
                if (d.data.description) {
                    tooltip.transition().duration(200).style("opacity", .9);
                    tooltip.html(`
                        <div class="title">${d.data.name}</div>
                        <div class="type">${d.data.type ? d.data.type.toUpperCase() : 'LAYER'}</div>
                        <div class="description">${d.data.description || 'System layer grouping'}</div>
                    `)
                    .style("left", (event.pageX + 10) + "px")
                    .style("top", (event.pageY - 28) + "px");
                }
            })
            .on("mouseout", function(d) {
                tooltip.transition().duration(500).style("opacity", 0);
            });
        }

        // Control functions
        function showAll() {
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            if (window.networkElements) {
                window.networkElements.node.style('opacity', 1);
                window.networkElements.link.style('opacity', 0.6);
            }
        }

        function filterByLayer(layer) {
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            if (window.networkElements) {
                window.networkElements.node.style('opacity', d => d.layer === layer ? 1 : 0.2);
                window.networkElements.link.style('opacity', d => 
                    d.source.layer === layer || d.target.layer === layer ? 0.6 : 0.1
                );
            }
        }

        function highlightCriticalPath() {
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            const criticalComponents = [
                "TradingDashboard", "trading.ts", "FastAPI App", 
                "IBKR Service", "TWS/Gateway", "Supabase DB"
            ];
            
            if (window.networkElements) {
                window.networkElements.node.style('opacity', d => 
                    criticalComponents.includes(d.id) ? 1 : 0.3
                );
                window.networkElements.link.style('opacity', d => 
                    criticalComponents.includes(d.source.id) && criticalComponents.includes(d.target.id) ? 1 : 0.1
                );
            }
        }

        function resetVisualization() {
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            document.querySelector('.btn').classList.add('active');
            
            if (window.networkElements) {
                window.networkElements.simulation.alpha(0.3).restart();
                showAll();
            }
        }

        // Initialize visualizations when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initNetworkDiagram();
            initDendrogram();
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            // Reinitialize on resize for responsiveness
            d3.select("#network-diagram").select("svg").remove();
            d3.select("#dendrogram").select("svg").remove();
            setTimeout(() => {
                initNetworkDiagram();
                initDendrogram();
            }, 100);
        });
    </script>
</body>
</html>
