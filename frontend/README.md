# IBKR Trading Platform - Frontend Suite

A comprehensive React/TypeScript frontend for the IBKR Trading Platform, providing both real-time trading capabilities and advanced backtesting analytics.

## 🚀 Platform Overview

This frontend suite consists of two main applications:

1. **Live Trading Cockpit** - Real-time market data visualization and trading interface
2. **Backtesting Analytics Suite** - Comprehensive strategy analysis and visualization

## 🎯 Core Features

### Live Trading Cockpit
- **Real-time Chart**: TradingView-style candlestick charts with live price updates
- **Market Data Streaming**: WebSocket-based real-time market data
- **Trade Execution**: Live trade fills visualization with markers on charts
- **Portfolio Management**: Real-time position and P&L tracking
- **Order Management**: View and manage open orders
- **Multi-Symbol Support**: Switch between different trading symbols

### Backtesting Analytics Suite
- **Visual Replay Chart**: Interactive TradingView-powered chart with trade execution visualization
- **Equity Curve Chart**: Comprehensive equity and drawdown analysis with Recharts
- **Stats Dashboard**: Grid-based KPI display with performance metrics
- **Trade Log Table**: Sortable, filterable data table with export functionality

## 🏗️ Architecture

### Live Trading Components
- `TradingDashboard`: Main cockpit interface
- `TVChart`: TradingView-style chart component using lightweight-charts
- Service layer for API and WebSocket communication

### Backtesting Components
- `BacktestingSuite`: Main orchestrator component
- `VisualReplayChart`: TradingView chart with replay functionality
- `EquityCurveChart`: Equity and drawdown visualization
- `StatsDashboard`: Performance metrics grid
- `TradeLogTable`: Interactive trade table

### Services & Infrastructure
- `apiService`: REST API client for backend communication
- `websocketService`: WebSocket client for real-time data streaming
- Custom React hooks for state management
- Comprehensive TypeScript type definitions

## 🛠️ Technology Stack

- **React 18** with TypeScript for type-safe development
- **Tailwind CSS** for utility-first styling and dark theme
- **lightweight-charts** for professional financial charting
- **Recharts** for equity curve and analytics visualization
- **Axios** for HTTP API requests
- **WebSocket API** for real-time communication
- **React Table** for advanced table functionality

## 🚀 Getting Started

### Prerequisites
- Node.js 16+ and npm/yarn
- IBKR MCP Server running on `http://localhost:8000` (API) and `http://localhost:8001` (WebSocket)

### Installation

```bash
# Navigate to the trading-ui directory
cd trading-ui

# Install dependencies
npm install

# Start the development server
npm start
```

The application will open at `http://localhost:3000`.

### Environment Configuration

Create a `.env` file in the trading-ui directory:

```env
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8001/ws
```

## 🔌 Backend Integration

### FastAPI Server (Port 8000)
- `/health` - Health check
- `/api/v1/klines` - Historical candlestick data
- `/api/v1/market-data/subscribe` - Subscribe to market data
- `/api/v1/positions` - Get portfolio positions
- `/api/v1/orders/open` - Get open orders
- `/api/v1/backtests` - Backtest management endpoints

### WebSocket Server (Port 8001)
- `/ws` - WebSocket endpoint for real-time data
- Channels:
  - `market_data:{symbol}` - Real-time price updates
  - `trade_fills` - Trade execution events
  - `order_updates` - Order status changes
  - `portfolio_updates` - Portfolio changes

## 📊 Application Features

### Live Trading Cockpit Features

#### Real-time Data Flow
1. **Market Data**: Subscribe to symbol-specific channels for live price updates
2. **Trade Fills**: Listen for `trade_fill` events to display trade markers on charts
3. **Order Updates**: Real-time order status changes
4. **Portfolio Updates**: Live position and P&L updates

#### Dashboard Layout
- Multi-panel dashboard layout
- Real-time price display with percentage changes
- Connection status indicators
- Responsive design for different screen sizes

#### Chart Features
- Candlestick charts with volume
- Real-time price updates
- Trade execution markers
- Customizable intervals and symbols

### Backtesting Analytics Features

#### Performance Metrics
- Total Return, Annual Return, Sharpe Ratio
- Maximum Drawdown, Volatility, Calmar Ratio
- Risk-adjusted returns and statistical analysis

#### Trading Statistics
- Win Rate, Profit Factor, Average Win/Loss
- Trade Duration Analysis, Largest Win/Loss
- Commission tracking and P&L analysis

#### Visual Analytics
- Animated playback of strategy execution
- Trade entry/exit markers with P&L information
- Playback speed controls (0.5x to 4x)
- Equity curve and drawdown visualization
- Interactive charts with zoom, pan, and hover

## 🏗️ Project Structure

```
src/
├── components/          # Live trading components
│   ├── TradingDashboard.tsx
│   └── TVChart.tsx
├── views/              # Backtesting components
│   ├── BacktestingSuite.tsx
│   └── BacktestingSuite/
│       ├── VisualReplayChart.tsx
│       ├── EquityCurveChart.tsx
│       ├── StatsDashboard.tsx
│       └── TradeLogTable.tsx
├── services/           # API and WebSocket services
│   ├── api.ts
│   └── websocket.ts
├── hooks/              # Custom React hooks
│   ├── useWebSocket.ts
│   └── useMarketData.ts
├── types/              # TypeScript definitions
│   ├── trading.ts
│   └── backtesting.ts
└── utils/              # Utility functions
```

## 🎮 Usage Guide

### Live Trading Cockpit
1. **Symbol Selection**: Use the dropdown to select trading symbols
2. **Chart Interaction**: View real-time candlestick charts with trade markers
3. **Live Data**: Monitor positions, orders, and recent trades in the side panels
4. **Connection Status**: Check WebSocket connection status in the header

### Backtesting Analytics
1. **Select Backtest**: Choose from available backtest runs
2. **Visual Replay**: Watch animated strategy execution
3. **Analyze Performance**: Review comprehensive metrics and statistics
4. **Export Data**: Download trade logs for further analysis

## 🔧 Available Scripts

### `npm start`
Runs the app in development mode at [http://localhost:3000](http://localhost:3000)

### `npm test`
Launches the test runner in interactive watch mode

### `npm run build`
Builds the app for production to the `build` folder

### `npm run eject`
Ejects from Create React App (one-way operation)

## 🎨 Customization

### Styling
The application uses Tailwind CSS with a custom dark theme. Colors and spacing can be customized in `tailwind.config.js`.

### Charts
Chart configurations can be modified in each component:
- **Recharts**: Customize colors, axes, and tooltips
- **Lightweight Charts**: Modify chart options and series styling

### API Endpoints
Update API endpoints in service files to match your server configuration.

## ⚡ Performance Considerations

- **Lazy Loading**: Components load data only when needed
- **Memoization**: React.useMemo for expensive calculations
- **Pagination**: Large datasets are paginated for performance
- **Debounced Search**: Search inputs are debounced to reduce API calls
- **WebSocket Reconnection**: Automatic reconnection with exponential backoff

## 🌐 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🚀 Deployment

### Production Build

```bash
npm run build
```

This creates an optimized production build in the `build/` directory.

### Docker Deployment

```dockerfile
FROM node:16-alpine as build
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 🤝 Contributing

1. Follow the existing code style and patterns
2. Add TypeScript types for new data structures
3. Include responsive design considerations
4. Test across different screen sizes
5. Update documentation for new features

## 📄 License

This project is part of the IBKR MCP Server suite and follows the same licensing terms.
