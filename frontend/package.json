{"name": "trading-ui", "version": "1.0.0", "private": true, "dependencies": {"@tanstack/react-table": "^8.10.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@types/recharts": "^1.8.29", "@types/ws": "^8.18.1", "axios": "^1.9.0", "date-fns": "^2.30.0", "lightweight-charts": "^5.0.7", "lucide-react": "^0.294.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "recharts": "^2.15.3", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "ws": "^8.18.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^27.5.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0"}}