/**
 * TradingView Chart Component
 * 
 * Implements a financial chart using lightweight-charts library
 * with real-time data updates and trade markers
 */

import React, { useEffect, useRef, useState } from 'react';
import { createChart, IChartApi, ISeriesApi, CandlestickData, Time } from 'lightweight-charts';
import apiService from '../services/api';
import websocketService from '../services/websocket';
import useMarketData from '../hooks/useMarketData';
import { ChartComponentProps, KlineData, TradeFill, MarketDataUpdate } from '../types/trading';

interface TVChartProps extends ChartComponentProps {
  symbol: string;
  interval?: string;
  height?: number;
  onTradeClick?: (trade: TradeFill) => void;
}

const TVChart: React.FC<TVChartProps> = ({
  symbol,
  interval = '1m',
  height = 400,
  onTradeClick
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { data: marketData } = useMarketData(symbol);

  // Initialize chart
  useEffect(() => {
    if (!chartContainerRef.current) return;

    // Create chart
    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height: height,
      layout: {
        background: { color: '#1e1e1e' },
        textColor: '#d1d4dc',
      },
      grid: {
        vertLines: { color: '#2b2b43' },
        horzLines: { color: '#2b2b43' },
      },
      crosshair: {
        mode: 1,
      },
      rightPriceScale: {
        borderColor: '#485c7b',
      },
      timeScale: {
        borderColor: '#485c7b',
        timeVisible: true,
        secondsVisible: false,
      },
    });

    // Create candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#4bffb5',
      downColor: '#ff4976',
      borderDownColor: '#ff4976',
      borderUpColor: '#4bffb5',
      wickDownColor: '#ff4976',
      wickUpColor: '#4bffb5',
    });

    chartRef.current = chart;
    candlestickSeriesRef.current = candlestickSeries;

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current && chart) {
        chart.applyOptions({
          width: chartContainerRef.current.clientWidth,
        });
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chart.remove();
    };
  }, [height]);

  // Load historical data
  useEffect(() => {
    const loadHistoricalData = async () => {
      if (!symbol || !candlestickSeriesRef.current) return;

      setIsLoading(true);
      setError(null);

      try {
        const response = await apiService.getKlines(symbol, interval, 100);
        
        if (response.success && response.data) {
          const chartData: CandlestickData[] = response.data.map((kline: KlineData) => ({
            time: (kline.timestamp / 1000) as Time,
            open: kline.open,
            high: kline.high,
            low: kline.low,
            close: kline.close,
          }));

          candlestickSeriesRef.current.setData(chartData);
          console.log(`Loaded ${chartData.length} historical data points for ${symbol}`);
        } else {
          setError('Failed to load historical data');
        }
      } catch (err) {
        console.error('Error loading historical data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setIsLoading(false);
      }
    };

    loadHistoricalData();
  }, [symbol, interval]);

  // Handle real-time market data updates
  useEffect(() => {
    if (!marketData || !candlestickSeriesRef.current) return;

    // Update the last candlestick with real-time price
    const currentTime = Math.floor(Date.now() / 1000) as Time;
    
    // For real-time updates, we'll update the last bar
    // In a real implementation, you'd need to handle bar completion logic
    candlestickSeriesRef.current.update({
      time: currentTime,
      open: marketData.price, // This should be the actual open price
      high: marketData.price,
      low: marketData.price,
      close: marketData.price,
    });
  }, [marketData]);

  // Handle trade fill events
  useEffect(() => {
    const unsubscribeTradeFills = websocketService.onMessage('trade_fill', (tradeFill: TradeFill) => {
      if (tradeFill.symbol !== symbol || !chartRef.current) return;

      // Add trade marker to chart
      const tradeMarker = {
        time: (tradeFill.timestamp / 1000) as Time,
        position: tradeFill.side === 'BUY' ? 'belowBar' : 'aboveBar' as const,
        color: tradeFill.side === 'BUY' ? '#4bffb5' : '#ff4976',
        shape: tradeFill.side === 'BUY' ? 'arrowUp' : 'arrowDown' as const,
        text: `${tradeFill.side} ${tradeFill.quantity}@${tradeFill.price}`,
        size: 1,
      };

      if (candlestickSeriesRef.current) {
        candlestickSeriesRef.current.setMarkers([tradeMarker]);
      }

      // Call callback if provided
      if (onTradeClick) {
        onTradeClick(tradeFill);
      }

      console.log('Trade fill received:', tradeFill);
    });

    return unsubscribeTradeFills;
  }, [symbol, onTradeClick]);

  if (error) {
    return (
      <div className="chart-error" style={{ height, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <div style={{ color: '#ff4976', textAlign: 'center' }}>
          <div>Error loading chart data</div>
          <div style={{ fontSize: '0.8em', marginTop: '8px' }}>{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="tv-chart-container" style={{ position: 'relative' }}>
      {isLoading && (
        <div 
          className="chart-loading" 
          style={{ 
            position: 'absolute', 
            top: 0, 
            left: 0, 
            right: 0, 
            bottom: 0, 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            backgroundColor: 'rgba(30, 30, 30, 0.8)',
            zIndex: 10,
            color: '#d1d4dc'
          }}
        >
          Loading chart data...
        </div>
      )}
      <div 
        ref={chartContainerRef} 
        style={{ 
          width: '100%', 
          height: `${height}px`,
          backgroundColor: '#1e1e1e'
        }} 
      />
    </div>
  );
};

export default TVChart;
