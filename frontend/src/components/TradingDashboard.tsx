/**
 * Trading Dashboard - Live Trading Cockpit
 * 
 * Main dashboard component that combines chart, market data, and trading controls
 */

import React, { useState, useEffect } from 'react';
import TVChart from './TVChart';
import useWebSocket from '../hooks/useWebSocket';
import useMarketData from '../hooks/useMarketData';
import websocketService from '../services/websocket';
import apiService from '../services/api';
import { TradeFill, Position, Order, MarketDataUpdate } from '../types/trading';

interface TradingDashboardProps {
  defaultSymbol?: string;
}

const TradingDashboard: React.FC<TradingDashboardProps> = ({ 
  defaultSymbol = 'AAPL' 
}) => {
  const [selectedSymbol, setSelectedSymbol] = useState(defaultSymbol);
  const [positions, setPositions] = useState<Position[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [recentTrades, setRecentTrades] = useState<TradeFill[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { isConnected, connectionState } = useWebSocket();
  const { data: marketData } = useMarketData(selectedSymbol);

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Load positions
        const positionsResponse = await apiService.getPositions();
        if (positionsResponse.success && positionsResponse.data) {
          setPositions(positionsResponse.data);
        }

        // Load open orders
        const ordersResponse = await apiService.getOpenOrders();
        if (ordersResponse.success && ordersResponse.data) {
          setOrders(ordersResponse.data);
        }

      } catch (err) {
        console.error('Error loading initial data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load data');
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Subscribe to trade fills and order updates
  useEffect(() => {
    if (!isConnected) return;

    // Subscribe to trade fills
    const unsubscribeTradeFills = websocketService.onMessage('trade_fill', (tradeFill: TradeFill) => {
      setRecentTrades(prev => [tradeFill, ...prev.slice(0, 9)]); // Keep last 10 trades
    });

    // Subscribe to order updates
    const unsubscribeOrderUpdates = websocketService.onMessage('order_update', (order: Order) => {
      setOrders(prev => {
        const index = prev.findIndex(o => o.order_id === order.order_id);
        if (index >= 0) {
          const updated = [...prev];
          updated[index] = order;
          return updated;
        } else {
          return [order, ...prev];
        }
      });
    });

    // Subscribe to portfolio updates
    const unsubscribePortfolioUpdates = websocketService.onMessage('portfolio_update', (portfolio: any) => {
      if (portfolio.positions) {
        setPositions(portfolio.positions);
      }
    });

    // Subscribe to channels
    websocketService.subscribeTradeFills();
    websocketService.subscribeOrderUpdates();
    websocketService.subscribePortfolioUpdates();

    return () => {
      unsubscribeTradeFills();
      unsubscribeOrderUpdates();
      unsubscribePortfolioUpdates();
    };
  }, [isConnected]);

  const handleTradeClick = (trade: TradeFill) => {
    console.log('Trade clicked:', trade);
    // Could open trade details modal or highlight in trade list
  };

  const handleSymbolChange = (symbol: string) => {
    setSelectedSymbol(symbol);
  };

  const formatPrice = (price: number) => {
    return price.toFixed(2);
  };

  const formatPnL = (pnl: number) => {
    const color = pnl >= 0 ? '#4bffb5' : '#ff4976';
    const sign = pnl >= 0 ? '+' : '';
    return <span style={{ color }}>{sign}{pnl.toFixed(2)}</span>;
  };

  return (
    <div className="trading-dashboard" style={{ 
      padding: '20px', 
      backgroundColor: '#1a1a1a', 
      color: '#d1d4dc',
      minHeight: '100vh'
    }}>
      {/* Header */}
      <div className="dashboard-header" style={{ 
        marginBottom: '20px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <h1 style={{ margin: 0, color: '#4bffb5' }}>Live Trading Cockpit</h1>
        <div className="connection-status" style={{
          display: 'flex',
          gap: '10px',
          alignItems: 'center'
        }}>
          <div style={{
            padding: '4px 8px',
            borderRadius: '4px',
            backgroundColor: isConnected ? '#4bffb5' : '#ff4976',
            color: '#000',
            fontSize: '12px'
          }}>
            WS: {connectionState}
          </div>
        </div>
      </div>

      {/* Symbol Selector */}
      <div className="symbol-selector" style={{ marginBottom: '20px' }}>
        <label style={{ marginRight: '10px' }}>Symbol:</label>
        <select 
          value={selectedSymbol}
          onChange={(e) => handleSymbolChange(e.target.value)}
          style={{
            padding: '8px',
            backgroundColor: '#2b2b43',
            color: '#d1d4dc',
            border: '1px solid #485c7b',
            borderRadius: '4px'
          }}
        >
          <option value="AAPL">AAPL</option>
          <option value="GOOGL">GOOGL</option>
          <option value="MSFT">MSFT</option>
          <option value="TSLA">TSLA</option>
          <option value="AMZN">AMZN</option>
        </select>
        {marketData && (
          <span style={{ marginLeft: '20px' }}>
            Price: <strong style={{ color: '#4bffb5' }}>${formatPrice(marketData.price)}</strong>
            {marketData.change_percent && (
              <span style={{ 
                marginLeft: '10px',
                color: marketData.change_percent >= 0 ? '#4bffb5' : '#ff4976'
              }}>
                ({marketData.change_percent > 0 ? '+' : ''}{marketData.change_percent.toFixed(2)}%)
              </span>
            )}
          </span>
        )}
      </div>

      {/* Main Content Grid */}
      <div className="dashboard-grid" style={{
        display: 'grid',
        gridTemplateColumns: '2fr 1fr',
        gap: '20px',
        height: 'calc(100vh - 200px)'
      }}>
        {/* Chart Section */}
        <div className="chart-section" style={{
          backgroundColor: '#2b2b43',
          borderRadius: '8px',
          padding: '20px'
        }}>
          <h3 style={{ marginTop: 0, marginBottom: '15px' }}>
            {selectedSymbol} Chart
          </h3>
          <TVChart 
            symbol={selectedSymbol}
            interval="1m"
            height={500}
            onTradeClick={handleTradeClick}
          />
        </div>

        {/* Side Panel */}
        <div className="side-panel" style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '20px'
        }}>
          {/* Positions */}
          <div className="positions-panel" style={{
            backgroundColor: '#2b2b43',
            borderRadius: '8px',
            padding: '15px',
            flex: 1
          }}>
            <h4 style={{ marginTop: 0, marginBottom: '15px' }}>Positions</h4>
            {isLoading ? (
              <div>Loading positions...</div>
            ) : positions.length > 0 ? (
              <div style={{ fontSize: '14px' }}>
                {positions.map((position, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    padding: '8px 0',
                    borderBottom: '1px solid #485c7b'
                  }}>
                    <div>
                      <div style={{ fontWeight: 'bold' }}>{position.symbol}</div>
                      <div style={{ fontSize: '12px', color: '#888' }}>
                        {position.quantity} @ ${formatPrice(position.avg_cost)}
                      </div>
                    </div>
                    <div style={{ textAlign: 'right' }}>
                      <div>${formatPrice(position.market_value)}</div>
                      <div>{formatPnL(position.unrealized_pnl)}</div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div style={{ color: '#888' }}>No positions</div>
            )}
          </div>

          {/* Recent Trades */}
          <div className="trades-panel" style={{
            backgroundColor: '#2b2b43',
            borderRadius: '8px',
            padding: '15px',
            flex: 1
          }}>
            <h4 style={{ marginTop: 0, marginBottom: '15px' }}>Recent Trades</h4>
            {recentTrades.length > 0 ? (
              <div style={{ fontSize: '14px' }}>
                {recentTrades.map((trade, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    padding: '8px 0',
                    borderBottom: '1px solid #485c7b'
                  }}>
                    <div>
                      <div style={{ fontWeight: 'bold' }}>{trade.symbol}</div>
                      <div style={{ 
                        fontSize: '12px', 
                        color: trade.side === 'BUY' ? '#4bffb5' : '#ff4976' 
                      }}>
                        {trade.side} {trade.quantity}
                      </div>
                    </div>
                    <div style={{ textAlign: 'right' }}>
                      <div>${formatPrice(trade.price)}</div>
                      <div style={{ fontSize: '12px', color: '#888' }}>
                        {new Date(trade.timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div style={{ color: '#888' }}>No recent trades</div>
            )}
          </div>

          {/* Open Orders */}
          <div className="orders-panel" style={{
            backgroundColor: '#2b2b43',
            borderRadius: '8px',
            padding: '15px',
            flex: 1
          }}>
            <h4 style={{ marginTop: 0, marginBottom: '15px' }}>Open Orders</h4>
            {orders.length > 0 ? (
              <div style={{ fontSize: '14px' }}>
                {orders.map((order, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    padding: '8px 0',
                    borderBottom: '1px solid #485c7b'
                  }}>
                    <div>
                      <div style={{ fontWeight: 'bold' }}>{order.symbol}</div>
                      <div style={{ 
                        fontSize: '12px', 
                        color: order.side === 'BUY' ? '#4bffb5' : '#ff4976' 
                      }}>
                        {order.side} {order.quantity}
                      </div>
                    </div>
                    <div style={{ textAlign: 'right' }}>
                      <div>${formatPrice(order.price || 0)}</div>
                      <div style={{ fontSize: '12px', color: '#888' }}>
                        {order.status}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div style={{ color: '#888' }}>No open orders</div>
            )}
          </div>
        </div>
      </div>

      {error && (
        <div style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          backgroundColor: '#ff4976',
          color: '#fff',
          padding: '10px 15px',
          borderRadius: '4px',
          maxWidth: '300px'
        }}>
          {error}
        </div>
      )}
    </div>
  );
};

export default TradingDashboard;
