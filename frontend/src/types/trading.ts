/**
 * TypeScript type definitions for the Trading UI
 */

// Market Data Types
export interface KlineData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface TickData {
  symbol: string;
  price: number;
  bid?: number;
  ask?: number;
  bid_size?: number;
  ask_size?: number;
  volume?: number;
  timestamp: number;
}

export interface MarketDataUpdate {
  symbol: string;
  price: number;
  bid?: number;
  ask?: number;
  volume?: number;
  timestamp: number;
  change?: number;
  change_percent?: number;
}

// Trading Types
export interface Order {
  order_id: string;
  symbol: string;
  side: 'BUY' | 'SELL';
  order_type: 'MARKET' | 'LIMIT' | 'STOP' | 'STOP_LIMIT';
  quantity: number;
  price?: number;
  stop_price?: number;
  time_in_force: 'DAY' | 'GTC' | 'IOC' | 'FOK';
  status: 'PENDING' | 'SUBMITTED' | 'FILLED' | 'PARTIALLY_FILLED' | 'CANCELLED' | 'REJECTED';
  filled_quantity: number;
  remaining_quantity: number;
  avg_fill_price?: number;
  created_at: string;
  updated_at: string;
}

export interface TradeFill {
  execution_id: string;
  order_id: string;
  symbol: string;
  side: 'BUY' | 'SELL';
  quantity: number;
  price: number;
  timestamp: number;
  commission?: number;
}

export interface Position {
  symbol: string;
  quantity: number;
  avg_cost: number;
  market_value: number;
  unrealized_pnl: number;
  realized_pnl: number;
  market_price: number;
  last_updated: string;
}

// Portfolio Types
export interface Portfolio {
  total_value: number;
  cash_balance: number;
  buying_power: number;
  day_pnl: number;
  total_pnl: number;
  positions: Position[];
  last_updated: string;
}

export interface AccountInfo {
  account_id: string;
  account_type: string;
  currency: string;
  net_liquidation: number;
  total_cash_value: number;
  settled_cash: number;
  accrued_cash: number;
  buying_power: number;
  equity_with_loan_value: number;
  previous_day_equity_with_loan_value: number;
  gross_position_value: number;
  reg_t_equity: number;
  reg_t_margin: number;
  sma: number;
}

// Chart Types
export interface ChartData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface TradeMarker {
  time: number;
  position: 'aboveBar' | 'belowBar';
  color: string;
  shape: 'circle' | 'square' | 'arrowUp' | 'arrowDown';
  text: string;
  size: number;
}

// WebSocket Message Types
export interface WebSocketMessage {
  type: string;
  channel: string;
  data: any;
  timestamp: string;
  message_id: string;
}

export interface MarketDataMessage extends WebSocketMessage {
  type: 'market_data';
  data: MarketDataUpdate;
}

export interface TradeFillMessage extends WebSocketMessage {
  type: 'trade_fill';
  data: TradeFill;
}

export interface OrderUpdateMessage extends WebSocketMessage {
  type: 'order_update';
  data: Order;
}

export interface PortfolioUpdateMessage extends WebSocketMessage {
  type: 'portfolio_update';
  data: Portfolio;
}

// UI State Types
export interface ConnectionStatus {
  api: 'connected' | 'disconnected' | 'connecting' | 'error';
  websocket: 'connected' | 'disconnected' | 'connecting' | 'error';
}

export interface TradingViewConfig {
  symbol: string;
  interval: string;
  theme: 'light' | 'dark';
  autosize: boolean;
  height: number;
  width: number;
}

// Error Types
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

// Subscription Types
export interface Subscription {
  id: string;
  channel: string;
  symbol?: string;
  active: boolean;
  created_at: string;
}

// Trading Strategy Types
export interface TradingSignal {
  signal_id: string;
  strategy_id: string;
  symbol: string;
  action: 'BUY' | 'SELL' | 'HOLD';
  confidence: number;
  price_target?: number;
  stop_loss?: number;
  quantity?: number;
  timestamp: string;
  metadata?: any;
}

// Component Props Types
export interface ChartComponentProps {
  symbol: string;
  interval?: string;
  height?: number;
  onTradeClick?: (trade: TradeFill) => void;
}

export interface OrderFormProps {
  symbol?: string;
  onOrderSubmit?: (order: Partial<Order>) => void;
  onCancel?: () => void;
}

export interface PositionTableProps {
  positions: Position[];
  onPositionClick?: (position: Position) => void;
}

export interface OrderTableProps {
  orders: Order[];
  onOrderCancel?: (orderId: string) => void;
  onOrderModify?: (order: Order) => void;
}

// Hook Types
export interface UseWebSocketReturn {
  isConnected: boolean;
  connectionState: string;
  subscribe: (channel: string) => void;
  unsubscribe: (channel: string) => void;
  send: (message: any) => void;
}

export interface UseMarketDataReturn {
  data: MarketDataUpdate | null;
  isLoading: boolean;
  error: string | null;
  subscribe: (symbol: string) => void;
  unsubscribe: (symbol: string) => void;
}

export interface UseTradingReturn {
  portfolio: Portfolio | null;
  positions: Position[];
  orders: Order[];
  isLoading: boolean;
  error: string | null;
  placeOrder: (order: Partial<Order>) => Promise<void>;
  cancelOrder: (orderId: string) => Promise<void>;
  refreshData: () => Promise<void>;
}
