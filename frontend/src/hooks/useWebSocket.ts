/**
 * React Hook for WebSocket Management
 */

import { useEffect, useState, useCallback, useRef } from 'react';
import websocketService from '../services/websocket';
import { UseWebSocketReturn } from '../types/trading';

export const useWebSocket = (): UseWebSocketReturn => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionState, setConnectionState] = useState('DISCONNECTED');
  const unsubscribersRef = useRef<(() => void)[]>([]);

  useEffect(() => {
    // Set up connection handlers
    const unsubscribeConnect = websocketService.onConnect(() => {
      setIsConnected(true);
      setConnectionState('CONNECTED');
    });

    const unsubscribeDisconnect = websocketService.onDisconnect(() => {
      setIsConnected(false);
      setConnectionState('DISCONNECTED');
    });

    const unsubscribeError = websocketService.onError(() => {
      setIsConnected(false);
      setConnectionState('ERROR');
    });

    // Store unsubscribers
    unsubscribersRef.current = [
      unsubscribeConnect,
      unsubscribeDisconnect,
      unsubscribeError
    ];

    // Connect to WebSocket
    websocketService.connect().catch(error => {
      console.error('Failed to connect to WebSocket:', error);
      setConnectionState('ERROR');
    });

    // Cleanup on unmount
    return () => {
      unsubscribersRef.current.forEach(unsubscribe => unsubscribe());
      websocketService.disconnect();
    };
  }, []);

  const subscribe = useCallback((channel: string) => {
    websocketService.subscribe(channel);
  }, []);

  const unsubscribe = useCallback((channel: string) => {
    websocketService.unsubscribe(channel);
  }, []);

  const send = useCallback((message: any) => {
    websocketService.send(message);
  }, []);

  return {
    isConnected,
    connectionState,
    subscribe,
    unsubscribe,
    send
  };
};

export default useWebSocket;
