/**
 * React Hook for Market Data Management
 */

import { useEffect, useState, useCallback, useRef } from 'react';
import websocketService from '../services/websocket';
import { MarketDataUpdate, UseMarketDataReturn } from '../types/trading';

export const useMarketData = (symbol?: string): UseMarketDataReturn => {
  const [data, setData] = useState<MarketDataUpdate | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const subscribedSymbolsRef = useRef<Set<string>>(new Set());
  const unsubscribersRef = useRef<(() => void)[]>([]);

  // Handle market data updates
  useEffect(() => {
    const unsubscribeMarketData = websocketService.onMessage('market_data', (marketData: MarketDataUpdate) => {
      setData(marketData);
      setError(null);
    });

    unsubscribersRef.current.push(unsubscribeMarketData);

    return () => {
      unsubscribersRef.current.forEach(unsubscribe => unsubscribe());
    };
  }, []);

  // Auto-subscribe to symbol if provided
  useEffect(() => {
    if (symbol && !subscribedSymbolsRef.current.has(symbol)) {
      subscribe(symbol);
    }
  }, [symbol]);

  const subscribe = useCallback((symbolToSubscribe: string) => {
    if (subscribedSymbolsRef.current.has(symbolToSubscribe)) {
      return; // Already subscribed
    }

    setIsLoading(true);
    setError(null);

    try {
      websocketService.subscribeMarketData(symbolToSubscribe);
      subscribedSymbolsRef.current.add(symbolToSubscribe);
      console.log(`Subscribed to market data for ${symbolToSubscribe}`);
    } catch (err) {
      setError(`Failed to subscribe to ${symbolToSubscribe}: ${err}`);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const unsubscribe = useCallback((symbolToUnsubscribe: string) => {
    if (!subscribedSymbolsRef.current.has(symbolToUnsubscribe)) {
      return; // Not subscribed
    }

    try {
      websocketService.unsubscribe(`market_data:${symbolToUnsubscribe}`);
      subscribedSymbolsRef.current.delete(symbolToUnsubscribe);
      console.log(`Unsubscribed from market data for ${symbolToUnsubscribe}`);
      
      // Clear data if unsubscribing from current symbol
      if (data?.symbol === symbolToUnsubscribe) {
        setData(null);
      }
    } catch (err) {
      setError(`Failed to unsubscribe from ${symbolToUnsubscribe}: ${err}`);
    }
  }, [data]);

  return {
    data,
    isLoading,
    error,
    subscribe,
    unsubscribe
  };
};

export default useMarketData;
