/**
 * API Service Layer for Trading UI
 * 
 * Handles REST API calls to the FastAPI backend
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface KlineData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface MarketDataSubscription {
  symbols: string[];
  data_types: string[];
}

export interface TradeData {
  symbol: string;
  side: 'BUY' | 'SELL';
  quantity: number;
  price: number;
  timestamp: number;
  order_id: string;
  execution_id: string;
}

export interface PortfolioPosition {
  symbol: string;
  quantity: number;
  avg_cost: number;
  market_value: number;
  unrealized_pnl: number;
  realized_pnl: number;
}

class ApiService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Health check
  async healthCheck(): Promise<ApiResponse> {
    try {
      const response = await this.client.get('/health');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Market Data APIs
  async getKlines(
    symbol: string,
    interval: string = '1m',
    limit: number = 100
  ): Promise<ApiResponse<KlineData[]>> {
    try {
      const response = await this.client.get('/api/v1/klines', {
        params: { symbol, interval, limit }
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async subscribeMarketData(subscription: MarketDataSubscription): Promise<ApiResponse> {
    try {
      const response = await this.client.post('/api/v1/market-data/subscribe', subscription);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async unsubscribeMarketData(symbols: string[]): Promise<ApiResponse> {
    try {
      const response = await this.client.post('/api/v1/market-data/unsubscribe', { symbols });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Trading APIs
  async getPortfolio(): Promise<ApiResponse<PortfolioPosition[]>> {
    try {
      const response = await this.client.get('/api/v1/portfolio');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getOpenOrders(): Promise<ApiResponse> {
    try {
      const response = await this.client.get('/api/v1/orders/open');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async placeOrder(orderData: any): Promise<ApiResponse> {
    try {
      const response = await this.client.post('/api/v1/orders', orderData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async cancelOrder(orderId: string): Promise<ApiResponse> {
    try {
      const response = await this.client.delete(`/api/v1/orders/${orderId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Account APIs
  async getAccountInfo(): Promise<ApiResponse> {
    try {
      const response = await this.client.get('/api/v1/account');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getPositions(): Promise<ApiResponse<PortfolioPosition[]>> {
    try {
      const response = await this.client.get('/api/v1/positions');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Error handling
  private handleError(error: any): Error {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.detail || error.response.data?.message || error.message;
      return new Error(`API Error (${error.response.status}): ${message}`);
    } else if (error.request) {
      // Request was made but no response received
      return new Error('Network Error: No response from server');
    } else {
      // Something else happened
      return new Error(`Request Error: ${error.message}`);
    }
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
