import React, { useMemo } from 'react';
import { BacktestRun, BacktestTrade } from '../../types/backtesting';
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  DollarSign, 
  Percent, 
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  AlertTriangle
} from 'lucide-react';

interface StatsDashboardProps {
  summary: BacktestRun;
  trades: BacktestTrade[];
  className?: string;
}

interface StatCard {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ReactNode;
  color: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'gray';
  trend?: 'up' | 'down' | 'neutral';
}

const StatsDashboard: React.FC<StatsDashboardProps> = ({
  summary,
  trades,
  className = ''
}) => {
  const tradeStats = useMemo(() => {
    if (!trades.length) return null;

    const completedTrades = trades.filter(t => t.exit_date && t.pnl !== null);
    const winningTrades = completedTrades.filter(t => (t.pnl || 0) > 0);
    const losingTrades = completedTrades.filter(t => (t.pnl || 0) < 0);
    
    const totalPnL = completedTrades.reduce((sum, t) => sum + (t.pnl || 0), 0);
    const grossProfit = winningTrades.reduce((sum, t) => sum + (t.pnl || 0), 0);
    const grossLoss = Math.abs(losingTrades.reduce((sum, t) => sum + (t.pnl || 0), 0));
    
    const avgWin = winningTrades.length > 0 ? grossProfit / winningTrades.length : 0;
    const avgLoss = losingTrades.length > 0 ? grossLoss / losingTrades.length : 0;
    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : 0;
    
    // Calculate average trade duration
    const tradesWithDuration = completedTrades.filter(t => t.trade_duration_hours);
    const avgDuration = tradesWithDuration.length > 0 
      ? tradesWithDuration.reduce((sum, t) => sum + (t.trade_duration_hours || 0), 0) / tradesWithDuration.length
      : 0;

    // Calculate largest win/loss
    const largestWin = Math.max(...winningTrades.map(t => t.pnl || 0), 0);
    const largestLoss = Math.min(...losingTrades.map(t => t.pnl || 0), 0);

    return {
      totalTrades: completedTrades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: completedTrades.length > 0 ? (winningTrades.length / completedTrades.length) * 100 : 0,
      totalPnL,
      grossProfit,
      grossLoss,
      avgWin,
      avgLoss,
      profitFactor,
      avgDuration,
      largestWin,
      largestLoss,
    };
  }, [trades]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercent = (value: number, decimals = 2) => {
    return `${value.toFixed(decimals)}%`;
  };

  const formatDuration = (hours: number) => {
    if (hours < 24) return `${hours.toFixed(1)}h`;
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return `${days}d ${remainingHours.toFixed(0)}h`;
  };

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-500/10 text-blue-400 border-blue-500/20',
      green: 'bg-green-500/10 text-green-400 border-green-500/20',
      red: 'bg-red-500/10 text-red-400 border-red-500/20',
      yellow: 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20',
      purple: 'bg-purple-500/10 text-purple-400 border-purple-500/20',
      gray: 'bg-gray-500/10 text-gray-400 border-gray-500/20',
    };
    return colors[color as keyof typeof colors] || colors.gray;
  };

  const StatCard: React.FC<{ stat: StatCard }> = ({ stat }) => (
    <div className={`bg-slate-800 rounded-lg p-4 border ${getColorClasses(stat.color)}`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          {stat.icon}
          <span className="text-sm font-medium text-slate-300">{stat.title}</span>
        </div>
        {stat.trend && (
          <div className={`flex items-center ${
            stat.trend === 'up' ? 'text-green-400' : 
            stat.trend === 'down' ? 'text-red-400' : 'text-gray-400'
          }`}>
            {stat.trend === 'up' ? <TrendingUp className="w-4 h-4" /> : 
             stat.trend === 'down' ? <TrendingDown className="w-4 h-4" /> : null}
          </div>
        )}
      </div>
      <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
      {stat.subtitle && (
        <div className="text-sm text-slate-400">{stat.subtitle}</div>
      )}
    </div>
  );

  const performanceStats: StatCard[] = [
    {
      title: 'Total Return',
      value: formatPercent(summary.total_return * 100),
      subtitle: formatCurrency(summary.final_value - summary.initial_capital),
      icon: <TrendingUp className="w-5 h-5" />,
      color: summary.total_return >= 0 ? 'green' : 'red',
      trend: summary.total_return >= 0 ? 'up' : 'down',
    },
    {
      title: 'Annual Return',
      value: formatPercent(summary.annual_return * 100),
      subtitle: 'Annualized',
      icon: <Calendar className="w-5 h-5" />,
      color: summary.annual_return >= 0 ? 'green' : 'red',
    },
    {
      title: 'Sharpe Ratio',
      value: summary.sharpe_ratio?.toFixed(2) || 'N/A',
      subtitle: 'Risk-adjusted return',
      icon: <Target className="w-5 h-5" />,
      color: (summary.sharpe_ratio || 0) > 1 ? 'green' : (summary.sharpe_ratio || 0) > 0 ? 'yellow' : 'red',
    },
    {
      title: 'Max Drawdown',
      value: formatPercent(Math.abs(summary.max_drawdown) * 100),
      subtitle: 'Peak to trough',
      icon: <TrendingDown className="w-5 h-5" />,
      color: Math.abs(summary.max_drawdown) < 0.1 ? 'green' : Math.abs(summary.max_drawdown) < 0.2 ? 'yellow' : 'red',
    },
    {
      title: 'Volatility',
      value: formatPercent(summary.volatility * 100),
      subtitle: 'Annualized',
      icon: <Activity className="w-5 h-5" />,
      color: summary.volatility < 0.15 ? 'green' : summary.volatility < 0.25 ? 'yellow' : 'red',
    },
    {
      title: 'Calmar Ratio',
      value: summary.calmar_ratio?.toFixed(2) || 'N/A',
      subtitle: 'Return / Max DD',
      icon: <BarChart3 className="w-5 h-5" />,
      color: (summary.calmar_ratio || 0) > 1 ? 'green' : (summary.calmar_ratio || 0) > 0.5 ? 'yellow' : 'red',
    },
  ];

  const tradingStats: StatCard[] = tradeStats ? [
    {
      title: 'Total Trades',
      value: tradeStats.totalTrades,
      subtitle: `${trades.length} total positions`,
      icon: <BarChart3 className="w-5 h-5" />,
      color: 'blue',
    },
    {
      title: 'Win Rate',
      value: formatPercent(tradeStats.winRate),
      subtitle: `${tradeStats.winningTrades}W / ${tradeStats.losingTrades}L`,
      icon: <PieChart className="w-5 h-5" />,
      color: tradeStats.winRate > 60 ? 'green' : tradeStats.winRate > 40 ? 'yellow' : 'red',
    },
    {
      title: 'Profit Factor',
      value: tradeStats.profitFactor.toFixed(2),
      subtitle: 'Gross profit / Gross loss',
      icon: <DollarSign className="w-5 h-5" />,
      color: tradeStats.profitFactor > 1.5 ? 'green' : tradeStats.profitFactor > 1 ? 'yellow' : 'red',
    },
    {
      title: 'Avg Win',
      value: formatCurrency(tradeStats.avgWin),
      subtitle: `${tradeStats.winningTrades} winning trades`,
      icon: <TrendingUp className="w-5 h-5" />,
      color: 'green',
    },
    {
      title: 'Avg Loss',
      value: formatCurrency(tradeStats.avgLoss),
      subtitle: `${tradeStats.losingTrades} losing trades`,
      icon: <TrendingDown className="w-5 h-5" />,
      color: 'red',
    },
    {
      title: 'Avg Duration',
      value: formatDuration(tradeStats.avgDuration),
      subtitle: 'Per trade',
      icon: <Calendar className="w-5 h-5" />,
      color: 'purple',
    },
  ] : [];

  const riskStats: StatCard[] = tradeStats ? [
    {
      title: 'Largest Win',
      value: formatCurrency(tradeStats.largestWin),
      subtitle: 'Best single trade',
      icon: <TrendingUp className="w-5 h-5" />,
      color: 'green',
    },
    {
      title: 'Largest Loss',
      value: formatCurrency(Math.abs(tradeStats.largestLoss)),
      subtitle: 'Worst single trade',
      icon: <TrendingDown className="w-5 h-5" />,
      color: 'red',
    },
    {
      title: 'Risk/Reward',
      value: tradeStats.avgLoss > 0 ? (tradeStats.avgWin / tradeStats.avgLoss).toFixed(2) : 'N/A',
      subtitle: 'Avg win / Avg loss',
      icon: <Target className="w-5 h-5" />,
      color: tradeStats.avgLoss > 0 && (tradeStats.avgWin / tradeStats.avgLoss) > 1.5 ? 'green' : 'yellow',
    },
    {
      title: 'Total P&L',
      value: formatCurrency(tradeStats.totalPnL),
      subtitle: 'All trades',
      icon: <DollarSign className="w-5 h-5" />,
      color: tradeStats.totalPnL >= 0 ? 'green' : 'red',
      trend: tradeStats.totalPnL >= 0 ? 'up' : 'down',
    },
  ] : [];

  return (
    <div className={`bg-slate-800 rounded-lg p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white">Performance Statistics</h3>
        <div className="flex items-center space-x-2 text-sm text-slate-400">
          <Calendar className="w-4 h-4" />
          <span>{summary.start_date} to {summary.end_date}</span>
        </div>
      </div>

      <div className="space-y-8">
        {/* Performance Metrics */}
        <div>
          <h4 className="text-md font-medium text-slate-300 mb-4 flex items-center">
            <BarChart3 className="w-5 h-5 mr-2" />
            Performance Metrics
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {performanceStats.map((stat, index) => (
              <StatCard key={index} stat={stat} />
            ))}
          </div>
        </div>

        {/* Trading Statistics */}
        {tradingStats.length > 0 && (
          <div>
            <h4 className="text-md font-medium text-slate-300 mb-4 flex items-center">
              <PieChart className="w-5 h-5 mr-2" />
              Trading Statistics
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {tradingStats.map((stat, index) => (
                <StatCard key={index} stat={stat} />
              ))}
            </div>
          </div>
        )}

        {/* Risk Metrics */}
        {riskStats.length > 0 && (
          <div>
            <h4 className="text-md font-medium text-slate-300 mb-4 flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2" />
              Risk Metrics
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {riskStats.map((stat, index) => (
                <StatCard key={index} stat={stat} />
              ))}
            </div>
          </div>
        )}

        {/* Strategy Info */}
        <div className="bg-slate-700 rounded-lg p-4">
          <h4 className="text-md font-medium text-slate-300 mb-3">Strategy Configuration</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-slate-400">Strategy Name:</span>
              <span className="text-white ml-2 font-medium">{summary.strategy_name}</span>
            </div>
            <div>
              <span className="text-slate-400">Initial Capital:</span>
              <span className="text-white ml-2 font-medium">{formatCurrency(summary.initial_capital)}</span>
            </div>
            <div>
              <span className="text-slate-400">Final Value:</span>
              <span className="text-white ml-2 font-medium">{formatCurrency(summary.final_value)}</span>
            </div>
            <div>
              <span className="text-slate-400">Created:</span>
              <span className="text-white ml-2 font-medium">
                {new Date(summary.created_at).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatsDashboard;
