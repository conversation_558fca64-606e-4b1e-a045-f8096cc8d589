import React, { useMemo, useState } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  flexRender,
  createColumnHelper,
  SortingState,
  ColumnFiltersState,
} from '@tanstack/react-table';
import { BacktestTrade } from '../../types/backtesting';
import { 
  ChevronUp, 
  ChevronDown, 
  Search, 
  Filter,
  Download,
  TrendingUp,
  TrendingDown,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { format } from 'date-fns';

interface TradeLogTableProps {
  trades: BacktestTrade[];
  className?: string;
}

const columnHelper = createColumnHelper<BacktestTrade>();

const TradeLogTable: React.FC<TradeLogTableProps> = ({
  trades,
  className = ''
}) => {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'winners' | 'losers' | 'open'>('all');

  const formatCurrency = (value: number | null) => {
    if (value === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatPercent = (value: number | null) => {
    if (value === null) return 'N/A';
    return `${(value * 100).toFixed(2)}%`;
  };

  const formatDuration = (hours: number | null) => {
    if (hours === null) return 'N/A';
    if (hours < 24) return `${hours.toFixed(1)}h`;
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return `${days}d ${remainingHours.toFixed(0)}h`;
  };

  const columns = useMemo(() => [
    columnHelper.accessor('symbol', {
      header: 'Symbol',
      cell: (info) => (
        <span className="font-medium text-white">{info.getValue()}</span>
      ),
    }),
    columnHelper.accessor('side', {
      header: 'Side',
      cell: (info) => {
        const side = info.getValue();
        return (
          <span className={`px-2 py-1 rounded text-xs font-medium ${
            side === 'BUY' 
              ? 'bg-green-500/20 text-green-400' 
              : 'bg-red-500/20 text-red-400'
          }`}>
            {side}
          </span>
        );
      },
    }),
    columnHelper.accessor('quantity', {
      header: 'Quantity',
      cell: (info) => info.getValue().toLocaleString(),
    }),
    columnHelper.accessor('entry_price', {
      header: 'Entry Price',
      cell: (info) => formatCurrency(info.getValue()),
    }),
    columnHelper.accessor('exit_price', {
      header: 'Exit Price',
      cell: (info) => formatCurrency(info.getValue()),
    }),
    columnHelper.accessor('entry_date', {
      header: 'Entry Date',
      cell: (info) => format(new Date(info.getValue()), 'MMM dd, yyyy HH:mm'),
    }),
    columnHelper.accessor('exit_date', {
      header: 'Exit Date',
      cell: (info) => {
        const date = info.getValue();
        return date ? format(new Date(date), 'MMM dd, yyyy HH:mm') : 'Open';
      },
    }),
    columnHelper.accessor('pnl', {
      header: 'P&L',
      cell: (info) => {
        const pnl = info.getValue();
        if (pnl === null) return <span className="text-slate-400">Open</span>;
        return (
          <div className="flex items-center space-x-1">
            {pnl > 0 ? (
              <TrendingUp className="w-4 h-4 text-green-400" />
            ) : (
              <TrendingDown className="w-4 h-4 text-red-400" />
            )}
            <span className={pnl >= 0 ? 'text-green-400' : 'text-red-400'}>
              {formatCurrency(pnl)}
            </span>
          </div>
        );
      },
    }),
    columnHelper.accessor('pnl_percent', {
      header: 'P&L %',
      cell: (info) => {
        const pnlPercent = info.getValue();
        if (pnlPercent === null) return <span className="text-slate-400">-</span>;
        return (
          <span className={pnlPercent >= 0 ? 'text-green-400' : 'text-red-400'}>
            {formatPercent(pnlPercent)}
          </span>
        );
      },
    }),
    columnHelper.accessor('trade_duration_hours', {
      header: 'Duration',
      cell: (info) => formatDuration(info.getValue()),
    }),
    columnHelper.accessor('commission', {
      header: 'Commission',
      cell: (info) => formatCurrency(info.getValue()),
    }),
    columnHelper.accessor('trade_type', {
      header: 'Type',
      cell: (info) => (
        <span className="text-slate-300 text-sm">{info.getValue()}</span>
      ),
    }),
  ], []);

  const filteredTrades = useMemo(() => {
    switch (selectedFilter) {
      case 'winners':
        return trades.filter(trade => (trade.pnl || 0) > 0);
      case 'losers':
        return trades.filter(trade => (trade.pnl || 0) < 0);
      case 'open':
        return trades.filter(trade => trade.exit_date === null);
      default:
        return trades;
    }
  }, [trades, selectedFilter]);

  const table = useReactTable({
    data: filteredTrades,
    columns,
    state: {
      sorting,
      columnFilters,
      globalFilter,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 20,
      },
    },
  });

  const exportToCSV = () => {
    const headers = columns.map(col => col.header as string).join(',');
    const rows = filteredTrades.map(trade => [
      trade.symbol,
      trade.side,
      trade.quantity,
      trade.entry_price,
      trade.exit_price || '',
      trade.entry_date,
      trade.exit_date || '',
      trade.pnl || '',
      trade.pnl_percent || '',
      trade.trade_duration_hours || '',
      trade.commission,
      trade.trade_type,
    ].join(','));
    
    const csv = [headers, ...rows].join('\n');
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'backtest_trades.csv';
    a.click();
    URL.revokeObjectURL(url);
  };

  const stats = useMemo(() => {
    const completedTrades = filteredTrades.filter(t => t.pnl !== null);
    const totalPnL = completedTrades.reduce((sum, t) => sum + (t.pnl || 0), 0);
    const winningTrades = completedTrades.filter(t => (t.pnl || 0) > 0).length;
    const winRate = completedTrades.length > 0 ? (winningTrades / completedTrades.length) * 100 : 0;
    
    return {
      total: filteredTrades.length,
      completed: completedTrades.length,
      open: filteredTrades.length - completedTrades.length,
      totalPnL,
      winRate,
    };
  }, [filteredTrades]);

  return (
    <div className={`bg-slate-800 rounded-lg p-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">Trade Log</h3>
        <div className="flex items-center space-x-4">
          {/* Stats Summary */}
          <div className="flex items-center space-x-4 text-sm">
            <div className="text-center">
              <div className="text-slate-400">Total</div>
              <div className="text-white font-semibold">{stats.total}</div>
            </div>
            <div className="text-center">
              <div className="text-slate-400">Win Rate</div>
              <div className="text-white font-semibold">{stats.winRate.toFixed(1)}%</div>
            </div>
            <div className="text-center">
              <div className="text-slate-400">Total P&L</div>
              <div className={`font-semibold ${stats.totalPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {formatCurrency(stats.totalPnL)}
              </div>
            </div>
          </div>
          
          <button
            onClick={exportToCSV}
            className="flex items-center space-x-2 px-3 py-2 bg-primary-600 hover:bg-primary-700 rounded-lg transition-colors"
          >
            <Download className="w-4 h-4" />
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center justify-between mb-4 space-x-4">
        {/* Search */}
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
          <input
            type="text"
            placeholder="Search trades..."
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>

        {/* Filter Buttons */}
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-slate-400" />
          {[
            { key: 'all', label: 'All', count: trades.length },
            { key: 'winners', label: 'Winners', count: trades.filter(t => (t.pnl || 0) > 0).length },
            { key: 'losers', label: 'Losers', count: trades.filter(t => (t.pnl || 0) < 0).length },
            { key: 'open', label: 'Open', count: trades.filter(t => t.exit_date === null).length },
          ].map(filter => (
            <button
              key={filter.key}
              onClick={() => setSelectedFilter(filter.key as any)}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                selectedFilter === filter.key
                  ? 'bg-primary-600 text-white'
                  : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
              }`}
            >
              {filter.label} ({filter.count})
            </button>
          ))}
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            {table.getHeaderGroups().map(headerGroup => (
              <tr key={headerGroup.id} className="border-b border-slate-700">
                {headerGroup.headers.map(header => (
                  <th
                    key={header.id}
                    className="text-left py-3 px-4 text-sm font-medium text-slate-300 cursor-pointer hover:text-white"
                    onClick={header.column.getToggleSortingHandler()}
                  >
                    <div className="flex items-center space-x-1">
                      <span>
                        {flexRender(header.column.columnDef.header, header.getContext())}
                      </span>
                      {header.column.getIsSorted() && (
                        <span className="text-primary-400">
                          {header.column.getIsSorted() === 'desc' ? (
                            <ChevronDown className="w-4 h-4" />
                          ) : (
                            <ChevronUp className="w-4 h-4" />
                          )}
                        </span>
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map(row => (
              <tr
                key={row.id}
                className="border-b border-slate-700/50 hover:bg-slate-700/30 transition-colors"
              >
                {row.getVisibleCells().map(cell => (
                  <td key={cell.id} className="py-3 px-4 text-sm text-slate-300">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {table.getPageCount() > 1 && (
        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-slate-400">
            Showing {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} to{' '}
            {Math.min(
              (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
              table.getFilteredRowModel().rows.length
            )}{' '}
            of {table.getFilteredRowModel().rows.length} trades
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              className="flex items-center space-x-1 px-3 py-2 bg-slate-700 hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors"
            >
              <ChevronLeft className="w-4 h-4" />
              <span>Previous</span>
            </button>
            
            <span className="text-sm text-slate-400">
              Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
            </span>
            
            <button
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              className="flex items-center space-x-1 px-3 py-2 bg-slate-700 hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors"
            >
              <span>Next</span>
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Empty State */}
      {filteredTrades.length === 0 && (
        <div className="text-center py-12">
          <div className="text-slate-400 text-lg mb-2">No trades found</div>
          <div className="text-slate-500 text-sm">
            {selectedFilter !== 'all' 
              ? `No ${selectedFilter} trades match your criteria`
              : 'Run a backtest to see trade data'
            }
          </div>
        </div>
      )}
    </div>
  );
};

export default TradeLogTable;
