import React, { useMemo } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine,
  Area,
  ComposedChart,
} from 'recharts';
import { BacktestEquityPoint } from '../../types/backtesting';
import { format } from 'date-fns';

interface EquityCurveChartProps {
  equityCurve: BacktestEquityPoint[];
  initialCapital: number;
  className?: string;
}

const EquityCurveChart: React.FC<EquityCurveChartProps> = ({
  equityCurve,
  initialCapital,
  className = ''
}) => {
  const chartData = useMemo(() => {
    if (!equityCurve.length) return [];

    let runningMax = initialCapital;
    
    return equityCurve.map((point, index) => {
      const equity = point.equity_value;
      const date = new Date(point.date);
      
      // Update running maximum for drawdown calculation
      if (equity > runningMax) {
        runningMax = equity;
      }
      
      // Calculate drawdown as percentage
      const drawdownPercent = ((equity - runningMax) / runningMax) * 100;
      
      return {
        date: date.getTime(),
        dateStr: format(date, 'MMM dd, yyyy'),
        equity: equity,
        equityFormatted: `$${equity.toLocaleString()}`,
        cumulativeReturn: point.cumulative_return * 100, // Convert to percentage
        drawdown: drawdownPercent,
        drawdownFormatted: `${drawdownPercent.toFixed(2)}%`,
        dailyReturn: (point.daily_return || 0) * 100, // Convert to percentage
      };
    });
  }, [equityCurve, initialCapital]);

  const stats = useMemo(() => {
    if (!chartData.length) return null;

    const finalEquity = chartData[chartData.length - 1].equity;
    const totalReturn = ((finalEquity - initialCapital) / initialCapital) * 100;
    const maxDrawdown = Math.min(...chartData.map(d => d.drawdown));
    const maxEquity = Math.max(...chartData.map(d => d.equity));
    
    // Calculate volatility (annualized)
    const dailyReturns = chartData.map(d => d.dailyReturn / 100);
    const avgReturn = dailyReturns.reduce((sum, ret) => sum + ret, 0) / dailyReturns.length;
    const variance = dailyReturns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / dailyReturns.length;
    const volatility = Math.sqrt(variance * 252) * 100; // Annualized

    return {
      totalReturn,
      maxDrawdown,
      maxEquity,
      volatility,
      finalEquity,
    };
  }, [chartData, initialCapital]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-slate-900 border border-slate-600 rounded-lg p-3 shadow-lg">
          <p className="text-slate-300 text-sm mb-2">{data.dateStr}</p>
          <div className="space-y-1">
            <p className="text-white">
              <span className="text-slate-400">Equity: </span>
              {data.equityFormatted}
            </p>
            <p className="text-white">
              <span className="text-slate-400">Return: </span>
              <span className={data.cumulativeReturn >= 0 ? 'text-success-500' : 'text-danger-500'}>
                {data.cumulativeReturn.toFixed(2)}%
              </span>
            </p>
            <p className="text-white">
              <span className="text-slate-400">Drawdown: </span>
              <span className="text-danger-500">{data.drawdownFormatted}</span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <div className={`bg-slate-800 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">Equity Curve & Drawdown</h3>
        
        {stats && (
          <div className="flex items-center space-x-4 text-sm">
            <div className="text-center">
              <div className="text-slate-400">Total Return</div>
              <div className={`font-semibold ${stats.totalReturn >= 0 ? 'text-success-500' : 'text-danger-500'}`}>
                {stats.totalReturn.toFixed(2)}%
              </div>
            </div>
            <div className="text-center">
              <div className="text-slate-400">Max Drawdown</div>
              <div className="text-danger-500 font-semibold">
                {stats.maxDrawdown.toFixed(2)}%
              </div>
            </div>
            <div className="text-center">
              <div className="text-slate-400">Volatility</div>
              <div className="text-warning-500 font-semibold">
                {stats.volatility.toFixed(2)}%
              </div>
            </div>
          </div>
        )}
      </div>

      {chartData.length > 0 ? (
        <div className="space-y-6">
          {/* Equity Curve */}
          <div>
            <h4 className="text-sm font-medium text-slate-300 mb-2">Equity Curve</h4>
            <ResponsiveContainer width="100%" height={250}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#334155" />
                <XAxis
                  dataKey="date"
                  type="number"
                  scale="time"
                  domain={['dataMin', 'dataMax']}
                  tickFormatter={(value) => format(new Date(value), 'MMM dd')}
                  stroke="#64748b"
                />
                <YAxis
                  tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                  stroke="#64748b"
                />
                <Tooltip content={<CustomTooltip />} />
                <ReferenceLine y={initialCapital} stroke="#6b7280" strokeDasharray="2 2" />
                <Line
                  type="monotone"
                  dataKey="equity"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  dot={false}
                  activeDot={{ r: 4, fill: '#3b82f6' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>

          {/* Drawdown Chart */}
          <div>
            <h4 className="text-sm font-medium text-slate-300 mb-2">Drawdown</h4>
            <ResponsiveContainer width="100%" height={150}>
              <ComposedChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#334155" />
                <XAxis
                  dataKey="date"
                  type="number"
                  scale="time"
                  domain={['dataMin', 'dataMax']}
                  tickFormatter={(value) => format(new Date(value), 'MMM dd')}
                  stroke="#64748b"
                />
                <YAxis
                  tickFormatter={(value) => `${value.toFixed(1)}%`}
                  stroke="#64748b"
                />
                <Tooltip
                  formatter={(value: number) => [`${value.toFixed(2)}%`, 'Drawdown']}
                  labelFormatter={(value) => format(new Date(value), 'MMM dd, yyyy')}
                  contentStyle={{
                    backgroundColor: '#1e293b',
                    border: '1px solid #475569',
                    borderRadius: '8px',
                  }}
                />
                <Area
                  type="monotone"
                  dataKey="drawdown"
                  stroke="#ef4444"
                  fill="#ef4444"
                  fillOpacity={0.3}
                />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-center h-64 text-slate-400">
          <div className="text-center">
            <div className="text-lg mb-2">No equity curve data available</div>
            <div className="text-sm">Run a backtest to see the equity curve</div>
          </div>
        </div>
      )}

      {/* Summary Stats */}
      {stats && (
        <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-slate-700">
          <div className="text-center">
            <div className="text-slate-400 text-sm">Initial Capital</div>
            <div className="text-white font-semibold">${initialCapital.toLocaleString()}</div>
          </div>
          <div className="text-center">
            <div className="text-slate-400 text-sm">Final Equity</div>
            <div className="text-white font-semibold">${stats.finalEquity.toLocaleString()}</div>
          </div>
          <div className="text-center">
            <div className="text-slate-400 text-sm">Peak Equity</div>
            <div className="text-white font-semibold">${stats.maxEquity.toLocaleString()}</div>
          </div>
          <div className="text-center">
            <div className="text-slate-400 text-sm">Data Points</div>
            <div className="text-white font-semibold">{chartData.length}</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EquityCurveChart;
