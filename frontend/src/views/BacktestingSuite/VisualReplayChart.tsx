import React, { useEffect, useRef, useState } from 'react';
import { create<PERSON>hart, IChartApi, ISeriesApi, LineStyle, ColorType } from 'lightweight-charts';
import { BacktestTrade, BacktestEquityPoint } from '../../types/backtesting';
import { Play, Pause, RotateCcw, Settings } from 'lucide-react';

interface VisualReplayChartProps {
  trades: BacktestTrade[];
  equityCurve: BacktestEquityPoint[];
  className?: string;
}

interface TradeMarker {
  time: number;
  position: 'aboveBar' | 'belowBar';
  color: string;
  shape: 'circle' | 'square' | 'arrowUp' | 'arrowDown';
  text: string;
  size: number;
}

const VisualReplayChart: React.FC<VisualReplayChartProps> = ({
  trades,
  equityCurve,
  className = ''
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const equitySeriesRef = useRef<ISeriesApi<'Line'> | null>(null);
  
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [showTrades, setShowTrades] = useState(true);
  const [showEquityCurve, setShowEquityCurve] = useState(true);

  // Initialize chart
  useEffect(() => {
    if (!chartContainerRef.current) return;

    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height: 400,
      layout: {
        background: { type: ColorType.Solid, color: '#0f172a' },
        textColor: '#e2e8f0',
      },
      grid: {
        vertLines: { color: '#1e293b' },
        horzLines: { color: '#1e293b' },
      },
      crosshair: {
        mode: 1,
      },
      rightPriceScale: {
        borderColor: '#334155',
      },
      timeScale: {
        borderColor: '#334155',
        timeVisible: true,
        secondsVisible: false,
      },
    });

    chartRef.current = chart;

    // Create equity curve series
    const equitySeries = chart.addLineSeries({
      color: '#3b82f6',
      lineWidth: 2,
      title: 'Equity Curve',
    });

    equitySeriesRef.current = equitySeries;

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current && chart) {
        chart.applyOptions({
          width: chartContainerRef.current.clientWidth,
        });
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chart.remove();
    };
  }, []);

  // Update chart data
  useEffect(() => {
    if (!chartRef.current || !equitySeriesRef.current) return;

    if (showEquityCurve && equityCurve.length > 0) {
      // Convert equity curve data to chart format
      const chartData = equityCurve.map(point => ({
        time: new Date(point.date).getTime() / 1000,
        value: point.equity_value,
      }));

      equitySeriesRef.current.setData(chartData);

      // Add trade markers if enabled
      if (showTrades && trades.length > 0) {
        const markers: TradeMarker[] = trades
          .filter(trade => trade.entry_date)
          .map(trade => {
            const entryTime = new Date(trade.entry_date).getTime() / 1000;
            const isLong = trade.side === 'BUY';
            const pnl = trade.pnl || 0;
            const isProfit = pnl > 0;

            return {
              time: entryTime,
              position: isLong ? 'belowBar' : 'aboveBar',
              color: isProfit ? '#10b981' : '#ef4444',
              shape: isLong ? 'arrowUp' : 'arrowDown',
              text: `${trade.side} ${trade.symbol}\\nP&L: $${pnl.toFixed(2)}`,
              size: 1,
            };
          });

        equitySeriesRef.current.setMarkers(markers as any);
      }
    }
  }, [equityCurve, trades, showEquityCurve, showTrades]);

  // Playback animation
  useEffect(() => {
    if (!isPlaying || currentIndex >= equityCurve.length) return;

    const interval = setInterval(() => {
      setCurrentIndex(prev => {
        const next = prev + 1;
        if (next >= equityCurve.length) {
          setIsPlaying(false);
          return prev;
        }
        return next;
      });
    }, 1000 / playbackSpeed);

    return () => clearInterval(interval);
  }, [isPlaying, currentIndex, equityCurve.length, playbackSpeed]);

  // Update chart with current playback position
  useEffect(() => {
    if (!chartRef.current || !equitySeriesRef.current || !isPlaying) return;

    const currentData = equityCurve.slice(0, currentIndex + 1).map(point => ({
      time: new Date(point.date).getTime() / 1000,
      value: point.equity_value,
    }));

    equitySeriesRef.current.setData(currentData);

    // Show trades up to current point
    if (showTrades) {
      const currentTime = new Date(equityCurve[currentIndex]?.date).getTime() / 1000;
      const visibleTrades = trades.filter(trade => {
        const tradeTime = new Date(trade.entry_date).getTime() / 1000;
        return tradeTime <= currentTime;
      });

      const markers: TradeMarker[] = visibleTrades.map(trade => {
        const entryTime = new Date(trade.entry_date).getTime() / 1000;
        const isLong = trade.side === 'BUY';
        const pnl = trade.pnl || 0;
        const isProfit = pnl > 0;

        return {
          time: entryTime,
          position: isLong ? 'belowBar' : 'aboveBar',
          color: isProfit ? '#10b981' : '#ef4444',
          shape: isLong ? 'arrowUp' : 'arrowDown',
          text: `${trade.side} ${trade.symbol}\\nP&L: $${pnl.toFixed(2)}`,
          size: 1,
        };
      });

      equitySeriesRef.current.setMarkers(markers as any);
    }
  }, [currentIndex, isPlaying, trades, showTrades, equityCurve]);

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleReset = () => {
    setIsPlaying(false);
    setCurrentIndex(0);
  };

  const handleSpeedChange = (speed: number) => {
    setPlaybackSpeed(speed);
  };

  return (
    <div className={`bg-slate-800 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">Visual Replay Chart</h3>
        
        <div className="flex items-center space-x-4">
          {/* Playback Controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handlePlayPause}
              className="flex items-center justify-center w-8 h-8 bg-primary-600 hover:bg-primary-700 rounded-full transition-colors"
            >
              {isPlaying ? (
                <Pause className="w-4 h-4 text-white" />
              ) : (
                <Play className="w-4 h-4 text-white ml-0.5" />
              )}
            </button>
            
            <button
              onClick={handleReset}
              className="flex items-center justify-center w-8 h-8 bg-slate-600 hover:bg-slate-700 rounded-full transition-colors"
            >
              <RotateCcw className="w-4 h-4 text-white" />
            </button>
          </div>

          {/* Speed Control */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-slate-400">Speed:</span>
            <select
              value={playbackSpeed}
              onChange={(e) => handleSpeedChange(Number(e.target.value))}
              className="bg-slate-700 text-white text-sm rounded px-2 py-1 border border-slate-600"
            >
              <option value={0.5}>0.5x</option>
              <option value={1}>1x</option>
              <option value={2}>2x</option>
              <option value={4}>4x</option>
            </select>
          </div>

          {/* Display Options */}
          <div className="flex items-center space-x-2">
            <Settings className="w-4 h-4 text-slate-400" />
            <label className="flex items-center space-x-1">
              <input
                type="checkbox"
                checked={showTrades}
                onChange={(e) => setShowTrades(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-slate-400">Trades</span>
            </label>
            <label className="flex items-center space-x-1">
              <input
                type="checkbox"
                checked={showEquityCurve}
                onChange={(e) => setShowEquityCurve(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-slate-400">Equity</span>
            </label>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      {isPlaying && (
        <div className="mb-4">
          <div className="flex items-center justify-between text-sm text-slate-400 mb-1">
            <span>Progress</span>
            <span>{currentIndex + 1} / {equityCurve.length}</span>
          </div>
          <div className="w-full bg-slate-700 rounded-full h-2">
            <div
              className="bg-primary-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentIndex + 1) / equityCurve.length) * 100}%` }}
            />
          </div>
        </div>
      )}

      {/* Chart Container */}
      <div ref={chartContainerRef} className="w-full" />

      {/* Chart Info */}
      <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
        <div className="text-center">
          <div className="text-slate-400">Total Trades</div>
          <div className="text-white font-semibold">{trades.length}</div>
        </div>
        <div className="text-center">
          <div className="text-slate-400">Winning Trades</div>
          <div className="text-success-500 font-semibold">
            {trades.filter(t => (t.pnl || 0) > 0).length}
          </div>
        </div>
        <div className="text-center">
          <div className="text-slate-400">Losing Trades</div>
          <div className="text-danger-500 font-semibold">
            {trades.filter(t => (t.pnl || 0) < 0).length}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VisualReplayChart;
