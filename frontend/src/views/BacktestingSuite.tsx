import React, { useState, useEffect } from 'react';
import { BacktestRun, BacktestDetails } from '../types/backtesting';
import { backtestingApi } from '../api/backtesting';
import VisualReplayChart from './BacktestingSuite/VisualReplayChart';
import EquityCurveChart from './BacktestingSuite/EquityCurveChart';
import StatsDashboard from './BacktestingSuite/StatsDashboard';
import TradeLogTable from './BacktestingSuite/TradeLogTable';
import { 
  RefreshCw, 
  Play, 
  Settings, 
  BarChart3, 
  TrendingUp,
  AlertCircle,
  Loader2,
  ChevronDown,
  Calendar,
  DollarSign
} from 'lucide-react';

const BacktestingSuite: React.FC = () => {
  const [backtestRuns, setBacktestRuns] = useState<BacktestRun[]>([]);
  const [selectedRunId, setSelectedRunId] = useState<string | null>(null);
  const [backtestDetails, setBacktestDetails] = useState<BacktestDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [detailsLoading, setDetailsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'chart' | 'trades'>('overview');

  // Load all backtest runs on component mount
  useEffect(() => {
    loadBacktestRuns();
  }, []);

  // Load details when a run is selected
  useEffect(() => {
    if (selectedRunId) {
      loadBacktestDetails(selectedRunId);
    }
  }, [selectedRunId]);

  const loadBacktestRuns = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await backtestingApi.getAllBacktests(50, 0);
      
      if (response.status === 'success' && response.data) {
        setBacktestRuns(response.data);
        // Auto-select the most recent run if none selected
        if (!selectedRunId && response.data.length > 0) {
          setSelectedRunId(response.data[0].id);
        }
      } else {
        setError(response.message || 'Failed to load backtest runs');
      }
    } catch (err) {
      setError('Failed to connect to the API. Please check if the server is running.');
      console.error('Error loading backtest runs:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadBacktestDetails = async (runId: string) => {
    try {
      setDetailsLoading(true);
      setError(null);
      const response = await backtestingApi.getBacktestDetails(runId);
      
      if (response.status === 'success' && response.data) {
        setBacktestDetails(response.data);
      } else {
        setError(response.message || 'Failed to load backtest details');
      }
    } catch (err) {
      setError('Failed to load backtest details');
      console.error('Error loading backtest details:', err);
    } finally {
      setDetailsLoading(false);
    }
  };

  const runNewBacktest = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Example backtest configuration
      const config = {
        strategy_config: {
          name: 'Buy and Hold Demo',
          type: 'buy_and_hold',
          symbols: ['SPY', 'QQQ'],
          rebalance_frequency: 'monthly'
        },
        start_date: '2023-01-01',
        end_date: '2023-12-31',
        initial_capital: 100000,
        save_to_db: true
      };

      const response = await backtestingApi.runBacktest(config);
      
      if (response.status === 'success') {
        // Reload the runs list to include the new backtest
        await loadBacktestRuns();
        
        // Select the new run if we got a run_id
        if (response.run_id) {
          setSelectedRunId(response.run_id);
        }
      } else {
        setError(response.message || 'Failed to run backtest');
      }
    } catch (err) {
      setError('Failed to run backtest');
      console.error('Error running backtest:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercent = (value: number) => {
    return `${(value * 100).toFixed(2)}%`;
  };

  const selectedRun = backtestRuns.find(run => run.id === selectedRunId);

  if (loading && backtestRuns.length === 0) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-primary-500 mx-auto mb-4" />
          <div className="text-white text-lg">Loading Backtesting Suite...</div>
          <div className="text-slate-400 text-sm mt-2">Fetching backtest data</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Backtesting Analytics Suite</h1>
            <p className="text-slate-400">Analyze and visualize your trading strategy performance</p>
          </div>
          
          <div className="flex items-center space-x-4">
            <button
              onClick={loadBacktestRuns}
              disabled={loading}
              className="flex items-center space-x-2 px-4 py-2 bg-slate-700 hover:bg-slate-600 rounded-lg transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
            
            <button
              onClick={runNewBacktest}
              disabled={loading}
              className="flex items-center space-x-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 rounded-lg transition-colors disabled:opacity-50"
            >
              <Play className="w-4 h-4" />
              <span>Run Demo Backtest</span>
            </button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg flex items-center space-x-3">
            <AlertCircle className="w-5 h-5 text-red-400" />
            <div>
              <div className="text-red-400 font-medium">Error</div>
              <div className="text-red-300 text-sm">{error}</div>
            </div>
          </div>
        )}

        {/* Backtest Selection */}
        {backtestRuns.length > 0 && (
          <div className="mb-6">
            <div className="flex items-center space-x-4">
              <label className="text-slate-300 font-medium">Select Backtest:</label>
              <div className="relative">
                <select
                  value={selectedRunId || ''}
                  onChange={(e) => setSelectedRunId(e.target.value)}
                  className="appearance-none bg-slate-800 border border-slate-600 rounded-lg px-4 py-2 pr-10 text-white focus:outline-none focus:ring-2 focus:ring-primary-500 min-w-[300px]"
                >
                  {backtestRuns.map(run => (
                    <option key={run.id} value={run.id}>
                      {run.strategy_name} - {run.start_date} to {run.end_date} 
                      ({formatPercent(run.total_return)})
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400 pointer-events-none" />
              </div>
              
              {selectedRun && (
                <div className="flex items-center space-x-4 text-sm">
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4 text-slate-400" />
                    <span className="text-slate-300">
                      {new Date(selectedRun.created_at).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <DollarSign className="w-4 h-4 text-slate-400" />
                    <span className="text-slate-300">
                      {formatCurrency(selectedRun.initial_capital)}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Main Content */}
        {detailsLoading ? (
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin text-primary-500 mx-auto mb-4" />
              <div className="text-white text-lg">Loading backtest details...</div>
            </div>
          </div>
        ) : backtestDetails && selectedRun ? (
          <div className="space-y-6">
            {/* Navigation Tabs */}
            <div className="flex items-center space-x-1 bg-slate-800 rounded-lg p-1">
              {[
                { key: 'overview', label: 'Overview', icon: BarChart3 },
                { key: 'chart', label: 'Visual Analysis', icon: TrendingUp },
                { key: 'trades', label: 'Trade Log', icon: Settings },
              ].map(tab => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                    activeTab === tab.key
                      ? 'bg-primary-600 text-white'
                      : 'text-slate-300 hover:text-white hover:bg-slate-700'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>

            {/* Tab Content */}
            {activeTab === 'overview' && (
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                <StatsDashboard 
                  summary={selectedRun} 
                  trades={backtestDetails.trades}
                  className="xl:col-span-2"
                />
                <EquityCurveChart 
                  equityCurve={backtestDetails.equity_curve}
                  initialCapital={selectedRun.initial_capital}
                  className="xl:col-span-2"
                />
              </div>
            )}

            {activeTab === 'chart' && (
              <div className="space-y-6">
                <VisualReplayChart 
                  trades={backtestDetails.trades}
                  equityCurve={backtestDetails.equity_curve}
                />
                <EquityCurveChart 
                  equityCurve={backtestDetails.equity_curve}
                  initialCapital={selectedRun.initial_capital}
                />
              </div>
            )}

            {activeTab === 'trades' && (
              <TradeLogTable trades={backtestDetails.trades} />
            )}
          </div>
        ) : backtestRuns.length === 0 ? (
          <div className="text-center py-20">
            <BarChart3 className="w-16 h-16 text-slate-600 mx-auto mb-4" />
            <div className="text-white text-xl mb-2">No Backtest Data Available</div>
            <div className="text-slate-400 mb-6">
              Run your first backtest to start analyzing your trading strategies
            </div>
            <button
              onClick={runNewBacktest}
              disabled={loading}
              className="flex items-center space-x-2 px-6 py-3 bg-primary-600 hover:bg-primary-700 rounded-lg transition-colors disabled:opacity-50 mx-auto"
            >
              <Play className="w-5 h-5" />
              <span>Run Demo Backtest</span>
            </button>
          </div>
        ) : (
          <div className="text-center py-20">
            <AlertCircle className="w-16 h-16 text-slate-600 mx-auto mb-4" />
            <div className="text-white text-xl mb-2">Select a Backtest</div>
            <div className="text-slate-400">
              Choose a backtest from the dropdown above to view detailed analytics
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BacktestingSuite;
