import React, { useState } from 'react';
import TradingDashboard from './components/TradingDashboard';
import BacktestingSuite from './views/BacktestingSuite';
import './App.css';

type AppView = 'trading' | 'backtesting';

function App() {
  const [currentView, setCurrentView] = useState<AppView>('trading');

  return (
    <div className="App">
      {/* Navigation Header */}
      <div style={{
        backgroundColor: '#1a1a1a',
        padding: '10px 20px',
        borderBottom: '1px solid #485c7b',
        display: 'flex',
        gap: '20px',
        alignItems: 'center'
      }}>
        <h1 style={{
          color: '#4bffb5',
          margin: 0,
          fontSize: '24px',
          fontWeight: 'bold'
        }}>
          IBKR Trading Platform
        </h1>
        <div style={{ display: 'flex', gap: '10px' }}>
          <button
            onClick={() => setCurrentView('trading')}
            style={{
              padding: '8px 16px',
              backgroundColor: currentView === 'trading' ? '#4bffb5' : '#2b2b43',
              color: currentView === 'trading' ? '#000' : '#d1d4dc',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontWeight: 'bold'
            }}
          >
            Live Trading
          </button>
          <button
            onClick={() => setCurrentView('backtesting')}
            style={{
              padding: '8px 16px',
              backgroundColor: currentView === 'backtesting' ? '#4bffb5' : '#2b2b43',
              color: currentView === 'backtesting' ? '#000' : '#d1d4dc',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontWeight: 'bold'
            }}
          >
            Backtesting
          </button>
        </div>
      </div>

      {/* Main Content */}
      {currentView === 'trading' ? (
        <TradingDashboard defaultSymbol="AAPL" />
      ) : (
        <BacktestingSuite />
      )}
    </div>
  );
}

export default App;
