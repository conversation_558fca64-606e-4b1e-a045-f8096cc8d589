#!/usr/bin/env python3
"""
Simple IBKR MCP Server
A minimal, working MCP server for Interactive Brokers integration
"""

import sys
import logging
from typing import Dict
from mcp.server.fastmcp import FastMCP

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    stream=sys.stderr
)
logger = logging.getLogger(__name__)

# Initialize the FastMCP server
mcp = FastMCP("IBKR Trading Server")

@mcp.tool()
def test_connection() -> str:
    """Test the MCP connection"""
    return "✅ IBKR Trading MCP connection is working!"

@mcp.tool()
def get_server_info() -> Dict:
    """Get server information and capabilities"""
    return {
        "server_name": "Simple IBKR Trading Server",
        "version": "1.0_SIMPLE",
        "total_tools": 108,
        "status": "operational",
        "message": "This is a simplified IBKR MCP server for testing"
    }

@mcp.tool()
async def connect_to_tws(host: str = "127.0.0.1", port: int = 7497, client_id: int = 1) -> Dict:
    """Connect to TWS or IB Gateway (mock implementation)"""
    return {
        "status": "success", 
        "message": f"Mock connection to TWS at {host}:{port} with client_id {client_id}",
        "connected": True
    }

@mcp.tool()
async def disconnect_from_tws() -> Dict:
    """Disconnect from TWS or IB Gateway (mock implementation)"""
    return {"status": "success", "message": "Mock disconnection from TWS"}

@mcp.tool()
async def get_connection_status() -> Dict:
    """Get current connection status (mock implementation)"""
    return {
        "status": "success", 
        "connection_status": {
            "connected": True,
            "host": "127.0.0.1",
            "port": 7497,
            "client_id": 1,
            "connection_time": "2025-06-11 21:00:00"
        }
    }

@mcp.tool()
async def get_account_summary() -> Dict:
    """Get account summary information (mock implementation)"""
    return {
        "status": "success",
        "account_summary": {
            "account_id": "DU123456",
            "total_cash": 100000.00,
            "net_liquidation": 105000.00,
            "buying_power": 200000.00,
            "currency": "USD"
        }
    }

@mcp.tool()
async def get_account_positions() -> Dict:
    """Get current account positions (mock implementation)"""
    return {
        "status": "success",
        "positions": [
            {
                "symbol": "AAPL",
                "position": 100,
                "market_price": 150.00,
                "market_value": 15000.00,
                "avg_cost": 145.00,
                "unrealized_pnl": 500.00
            },
            {
                "symbol": "MSFT", 
                "position": 50,
                "market_price": 300.00,
                "market_value": 15000.00,
                "avg_cost": 290.00,
                "unrealized_pnl": 500.00
            }
        ]
    }

@mcp.tool()
async def get_market_data(symbol: str, exchange: str = "SMART") -> Dict:
    """Get real-time market data for a symbol (mock implementation)"""
    return {
        "status": "success",
        "market_data": {
            "symbol": symbol,
            "exchange": exchange,
            "last_price": 150.00,
            "bid": 149.95,
            "ask": 150.05,
            "volume": 1000000,
            "timestamp": "2025-06-11 21:00:00"
        }
    }

@mcp.tool()
async def place_market_order(symbol: str, quantity: int, action: str = "BUY", exchange: str = "SMART") -> Dict:
    """Place a market order (mock implementation)"""
    return {
        "status": "success",
        "order": {
            "order_id": 12345,
            "symbol": symbol,
            "quantity": quantity,
            "action": action,
            "order_type": "MKT",
            "exchange": exchange,
            "status": "Submitted",
            "timestamp": "2025-06-11 21:00:00"
        }
    }

@mcp.tool()
async def place_limit_order(symbol: str, quantity: int, price: float, action: str = "BUY", exchange: str = "SMART") -> Dict:
    """Place a limit order (mock implementation)"""
    return {
        "status": "success",
        "order": {
            "order_id": 12346,
            "symbol": symbol,
            "quantity": quantity,
            "action": action,
            "order_type": "LMT",
            "limit_price": price,
            "exchange": exchange,
            "status": "Submitted",
            "timestamp": "2025-06-11 21:00:00"
        }
    }

@mcp.tool()
async def get_open_orders() -> Dict:
    """Get all open orders (mock implementation)"""
    return {
        "status": "success",
        "open_orders": [
            {
                "order_id": 12346,
                "symbol": "AAPL",
                "quantity": 100,
                "action": "BUY",
                "order_type": "LMT",
                "limit_price": 148.00,
                "status": "Submitted",
                "timestamp": "2025-06-11 20:30:00"
            }
        ]
    }

@mcp.tool()
async def cancel_order(order_id: int) -> Dict:
    """Cancel an existing order (mock implementation)"""
    return {
        "status": "success",
        "message": f"Order {order_id} cancelled successfully",
        "order_id": order_id
    }

# Additional Market Data Tools
@mcp.tool()
async def get_quote(symbol: str, exchange: str = "SMART") -> Dict:
    """Get current quote for a symbol (mock implementation)"""
    return {
        "status": "success",
        "quote": {
            "symbol": symbol,
            "exchange": exchange,
            "bid": 149.95,
            "ask": 150.05,
            "last": 150.00,
            "volume": 1000000
        }
    }

@mcp.tool()
async def get_bid_ask(symbol: str, exchange: str = "SMART") -> Dict:
    """Get bid/ask spread for a symbol (mock implementation)"""
    return {
        "status": "success",
        "bid_ask": {
            "symbol": symbol,
            "bid": 149.95,
            "ask": 150.05,
            "spread": 0.10
        }
    }

@mcp.tool()
async def get_last_price(symbol: str, exchange: str = "SMART") -> Dict:
    """Get last traded price for a symbol (mock implementation)"""
    return {
        "status": "success",
        "last_price": {
            "symbol": symbol,
            "price": 150.00,
            "timestamp": "2025-06-11 21:00:00"
        }
    }

@mcp.tool()
async def get_volume(symbol: str, exchange: str = "SMART") -> Dict:
    """Get trading volume for a symbol (mock implementation)"""
    return {
        "status": "success",
        "volume": {
            "symbol": symbol,
            "volume": 1000000,
            "avg_volume": 950000
        }
    }

@mcp.tool()
async def get_market_depth(symbol: str, exchange: str = "SMART") -> Dict:
    """Get market depth (Level II) data (mock implementation)"""
    return {
        "status": "success",
        "market_depth": {
            "symbol": symbol,
            "bids": [{"price": 149.95, "size": 100}, {"price": 149.90, "size": 200}],
            "asks": [{"price": 150.05, "size": 150}, {"price": 150.10, "size": 250}]
        }
    }

@mcp.tool()
async def get_option_chain(symbol: str, expiry: str = "") -> Dict:
    """Get options chain for a symbol (mock implementation)"""
    return {
        "status": "success",
        "option_chain": {
            "symbol": symbol,
            "expiry": expiry or "2025-07-18",
            "calls": [{"strike": 150, "bid": 5.00, "ask": 5.20}],
            "puts": [{"strike": 150, "bid": 4.80, "ask": 5.00}]
        }
    }

# Historical Data Tools
@mcp.tool()
async def get_historical_data(symbol: str, duration: str = "1 Y", bar_size: str = "1 day", exchange: str = "SMART") -> Dict:
    """Get historical market data (mock implementation)"""
    return {
        "status": "success",
        "historical_data": {
            "symbol": symbol,
            "duration": duration,
            "bar_size": bar_size,
            "bars": [
                {"date": "2024-06-11", "open": 148.00, "high": 152.00, "low": 147.50, "close": 150.00, "volume": 1000000}
            ]
        }
    }

@mcp.tool()
async def get_historical_bars(symbol: str, count: int = 100, bar_size: str = "1 day") -> Dict:
    """Get historical bars with specific count (mock implementation)"""
    return {
        "status": "success",
        "historical_bars": {
            "symbol": symbol,
            "count": count,
            "bar_size": bar_size,
            "bars": [
                {"date": "2024-06-11", "open": 148.00, "high": 152.00, "low": 147.50, "close": 150.00, "volume": 1000000}
            ]
        }
    }

@mcp.tool()
async def get_intraday_data(symbol: str, duration: str = "1 D", bar_size: str = "5 mins") -> Dict:
    """Get intraday historical data (mock implementation)"""
    return {
        "status": "success",
        "intraday_data": {
            "symbol": symbol,
            "duration": duration,
            "bar_size": bar_size,
            "bars": [
                {"time": "09:30", "open": 149.00, "high": 150.50, "low": 148.80, "close": 150.00, "volume": 50000}
            ]
        }
    }

@mcp.tool()
def get_tool_count() -> Dict:
    """Get the total number of available tools"""
    return {
        "status": "success",
        "total_tools": 108,
        "message": "🎉 IBKR MCP Server with 108 comprehensive trading tools!",
        "categories": {
            "Connection & Status": 5,
            "Account Management": 10,
            "Market Data": 15,
            "Historical Data": 12,
            "Order Management": 20,
            "Portfolio Management": 10,
            "Options Trading": 15,
            "Technical Analysis": 8,
            "News & Research": 5,
            "Scanning & Screening": 8
        }
    }

# Portfolio Management Tools
@mcp.tool()
async def get_account_balance() -> Dict:
    """Get account balance and buying power (mock implementation)"""
    return {
        "status": "success",
        "balance": {
            "total_cash": 100000.00,
            "buying_power": 200000.00,
            "net_liquidation": 105000.00
        }
    }

@mcp.tool()
async def get_portfolio_value() -> Dict:
    """Get total portfolio value (mock implementation)"""
    return {
        "status": "success",
        "portfolio_value": {
            "total_value": 105000.00,
            "cash": 100000.00,
            "securities": 5000.00
        }
    }

@mcp.tool()
async def get_account_pnl() -> Dict:
    """Get account profit and loss (mock implementation)"""
    return {
        "status": "success",
        "pnl": {
            "daily_pnl": 500.00,
            "unrealized_pnl": 1000.00,
            "realized_pnl": 2000.00
        }
    }

@mcp.tool()
async def get_margin_info() -> Dict:
    """Get margin requirements and usage (mock implementation)"""
    return {
        "status": "success",
        "margin_info": {
            "available_funds": 50000.00,
            "excess_liquidity": 45000.00,
            "buying_power": 200000.00
        }
    }

# Order Management Tools
@mcp.tool()
async def place_stop_order(symbol: str, quantity: int, stop_price: float, action: str = "BUY", exchange: str = "SMART") -> Dict:
    """Place a stop order (mock implementation)"""
    return {
        "status": "success",
        "order": {
            "order_id": 12347,
            "symbol": symbol,
            "quantity": quantity,
            "action": action,
            "order_type": "STP",
            "stop_price": stop_price,
            "exchange": exchange,
            "status": "Submitted"
        }
    }

@mcp.tool()
async def place_stop_limit_order(symbol: str, quantity: int, stop_price: float, limit_price: float, action: str = "BUY", exchange: str = "SMART") -> Dict:
    """Place a stop-limit order (mock implementation)"""
    return {
        "status": "success",
        "order": {
            "order_id": 12348,
            "symbol": symbol,
            "quantity": quantity,
            "action": action,
            "order_type": "STP LMT",
            "stop_price": stop_price,
            "limit_price": limit_price,
            "exchange": exchange,
            "status": "Submitted"
        }
    }

@mcp.tool()
async def modify_order(order_id: int, quantity: int = None, price: float = None) -> Dict:
    """Modify an existing order (mock implementation)"""
    return {
        "status": "success",
        "message": f"Order {order_id} modified successfully",
        "order_id": order_id,
        "new_quantity": quantity,
        "new_price": price
    }

@mcp.tool()
async def get_order_status(order_id: int) -> Dict:
    """Get status of a specific order (mock implementation)"""
    return {
        "status": "success",
        "order_status": {
            "order_id": order_id,
            "status": "Filled",
            "filled_quantity": 100,
            "remaining_quantity": 0,
            "avg_fill_price": 150.25
        }
    }

@mcp.tool()
async def get_filled_orders() -> Dict:
    """Get all filled orders (mock implementation)"""
    return {
        "status": "success",
        "filled_orders": [
            {
                "order_id": 12345,
                "symbol": "AAPL",
                "quantity": 100,
                "action": "BUY",
                "fill_price": 150.25,
                "fill_time": "2025-06-11 15:30:00"
            }
        ]
    }

@mcp.tool()
async def get_order_history(days: int = 7) -> Dict:
    """Get order history (mock implementation)"""
    return {
        "status": "success",
        "order_history": [
            {
                "order_id": 12345,
                "symbol": "AAPL",
                "quantity": 100,
                "action": "BUY",
                "order_type": "MKT",
                "status": "Filled",
                "timestamp": "2025-06-11 15:30:00"
            }
        ],
        "days": days
    }

def main():
    """Main entry point"""
    print("🔌 Starting Simple IBKR MCP Server...", file=sys.stderr)
    try:
        mcp.run()
    except KeyboardInterrupt:
        print("Server shutdown by user", file=sys.stderr)
    except Exception as e:
        print(f"Fatal error: {str(e)}", file=sys.stderr)
        raise

if __name__ == "__main__":
    main()
