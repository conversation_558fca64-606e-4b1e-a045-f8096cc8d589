# Basic Contracts

## FX Pairs

```
contract = Contract()
contract.symbol = "EUR"
contract.secType = "CASH"
contract.currency = "GBP"
contract.exchange = "IDEALPRO"
```



## Cryptocurrency

```
contract = Contract()
contract.symbol = "ETH"
contract.secType = "CRYPTO"
contract.currency = "USD"
contract.exchange = "PAXOS"
```



## Stocks

```
contract = Contract()
contract.symbol = "SPY"
contract.secType = "STK"
contract.currency = "USD"
contract.exchange = "ARCA"
```

For certain smart-routed stock contracts that have the same **symbol**, **currency** and **exchange**, you would also need to specify the **primary exchange** attribute to uniquely define the contract. This should be defined as the native exchange of a contract, and is good practice to include for all stocks:

```
contract = Contract()
contract.symbol = "SPY"
contract.secType = "STK"
contract.currency = "USD"
contract.exchange = "SMART"
contract.primaryExchange = "ARCA"
```

To request market data, the routing exchange and primary exchange can be specified in a single 'exchange' field if a valid component exchange separator separates them, for instance exchange = "SMART:ARCA". The default separators are colon ":" and slash "/". Other component exchange separators can be defined using the field defined in TWS Global Configuration under API -> Settings. The component exchange separator syntax in TWS versions prior to 971 can only be used to request market data and not to place orders.

## Contracts specified by FIGI

Contracts could also be defined by FIGI. In the example below, FIGI code specifies AAPL STK contract.

```
contract = Contract()
contract.secIdType = "FIGI"
contract.secId = "BBG000B9XRY4"
contract.exchange = "SMART"
```



## Stock Contract with IPO price

An example Stock contract with IPO price.

```
contract = Contract()
contract.symbol = "EMCGU"
contract.secType = "STK"
contract.currency = "USD"
contract.exchange = "SMART"
```



## Indexes

ISINs for indices which are available in IB's database are available in the API as of TWS 965+.

```
contract = Contract()
contract.symbol = "DAX"
contract.secType = "IND"
contract.currency = "EUR"
contract.exchange = "EUREX"
```



## CFDs

```
contract = Contract()
contract.symbol = "IBDE30"
contract.secType = "CFD"
contract.currency = "EUR"
contract.exchange = "SMART"
```



## Futures

A regular futures contract is commonly defined using an expiry and the symbol field defined as the symbol of the underlying. Historical data for futures is available up to 2 years after they expire by setting the includeExpired flag within the Contract class to True.

```
contract = Contract()
contract.symbol = "GBL"
contract.secType = "FUT"
contract.exchange = "EUREX"
contract.currency = "EUR"
contract.lastTradeDateOrContractMonth = "202303"
```

By contract the 'local symbol' field is IB's symbol for the future itself (the Symbol within the TWS' Contract Description dialog). Since a local symbol uniquely defines a future, an expiry is not necessary.

```
contract = Contract()
contract.secType = "FUT"
contract.exchange = "EUREX"
contract.currency = "EUR"
contract.localSymbol = "FGBL MAR 23"
```

Occasionally, you can expect to have more than a single future contract for the same underlying with the same expiry. To rule out the ambiguity, the contract's **multiplier** can be given as shown below:

```
contract = Contract()
contract.symbol = "DAX"
contract.secType = "FUT"
contract.exchange = "EUREX"
contract.currency = "EUR"
contract.lastTradeDateOrContractMonth = "202303"
contract.multiplier = "1"
```

Continuous futures are available from the API with **TWS v971** and higher. Continuous futures cannot be used with real-time data or to place orders, but only for historical data.

```
contract = Contract()
contract.symbol = "GBL"
contract.secType = "CONTFUT"
contract.exchange = "EUREX"
```

The security type "FUT+CONTFUT" can be used to request contract details about the futures and continuous futures on an underlying. This security type cannot be used with other functionality.

```
contract = Contract()
contract.symbol = "GBL"
contract.secType = "FUT+CONTFUT"
contract.exchange = "EUREX"
```



## Options

Options, like futures, also require an expiration date plus a **strike** and a **multiplier**:

```
contract = Contract()
contract.symbol = "GOOG"
contract.secType = "OPT"
contract.exchange = "BOX"
contract.currency = "USD"
contract.lastTradeDateOrContractMonth = "20190315"
contract.strike = 1180
contract.right = "C"
contract.multiplier = "100"
```

It is not unusual to find many option contracts with an almost identical description (i.e. underlying symbol, strike, last trading date, multiplier, etc.). Adding more details such as the **trading class** will help:

```
contract = Contract()
contract.symbol = "SANT"
contract.secType = "OPT"
contract.exchange = "MEFFRV"
contract.currency = "EUR"
contract.lastTradeDateOrContractMonth = "20190621"
contract.strike = 7.5
contract.right = "C"
contract.multiplier = "100"
contract.tradingClass = "SANEU"
```

The OCC options symbol can be used to define an option contract in the API through the option's 'local symbol' field.

```
contract = Contract()
#Watch out for the spaces within the local symbol!
contract.localSymbol = "P BMW  20221216 72 M"
contract.secType = "OPT"
contract.exchange = "EUREX"
contract.currency = "EUR"
```



## Futures Options

**Important: In TWS versions prior to 972**, if defining a futures option that has a price magnifier using the strike price, the strike will be the strike price displayed in TWS divided by the price magnifier. (e.g. displayed in dollars not cents for ZW)
In **TWS versions 972 and greater**, the strike prices will be shown in TWS and the API the same way (without a price magnifier applied)
For some futures options (e.g GE) it will be necessary to define a trading class, or use the local symbol, or conId.

```
contract = Contract()
contract.symbol = "GBL"
contract.secType = "FOP"
contract.exchange = "EUREX"
contract.currency = "EUR"
contract.lastTradeDateOrContractMonth = "20230224"
contract.strike = 138
contract.right = "C"
contract.multiplier = "1000"
```



## Bonds

Bonds can be specified by defining the symbol as the CUSIP or ISIN.

```
contract = Contract()
# enter CUSIP as symbol
contract.symbol= "912828C57"
contract.secType = "BOND"
contract.exchange = "SMART"
contract.currency = "USD"
```

Bonds can also be defined with the conId and exchange as with any security type.

```
contract = Contract()
contract.conId = *********
contract.exchange = "SMART"
```



## Mutual Funds

Order placement for Mutual Funds is currently supported from the API both in paper account and live account. Note: It is recommended to understand mutual fund orders on TWS UI before implementing via API due to restrictions enforced on trading mutual funds. For example to buy ARBIX one requires to use cash quantity i.e. cashQty while selling ARBIX can be implemented using total quantity i.e. totalQuantity.

```
contract = Contract()
contract.symbol = "VINIX"
contract.secType = "FUND"
contract.exchange = "FUNDSERV"
contract.currency = "USD"
```



## Commodities

```
contract = Contract()
contract.symbol = "XAUUSD"
contract.secType = "CMDTY"
contract.exchange = "SMART"
contract.currency = "USD"
```



## Standard Warrants

Warrants, like options, require an **expiration date**, a **right**, a **strike** and a **multiplier**. For some warrants it will be necessary to define a localSymbol or conId to uniquely identify the contract.

```
contract = Contract()
contract.symbol = "GOOG"
contract.secType = "WAR"
contract.exchange = "FWB"
contract.currency = "EUR"
contract.lastTradeDateOrContractMonth = "20201117"
contract.strike = 1500.0
contract.right = "C"
contract.multiplier = "0.01"
```



## Dutch Warrants and Structured Products

To unambiguously define a Dutch Warrant or Structured Product (IOPTs) the conId or localSymbol field must be used.

- It is important to note that if reqContractDetails is used with an incompletely-defined IOPT contract definition, that thousands of results can be returned and the API connection broken.
- IOPT contract definitions will often change and it will be necessary to restart TWS or IB Gateway to download the new contract definition.

```
contract = Contract()
contract.localSymbol = "B881G"
contract.secType = "IOPT"
contract.exchange = "SBF"
contract.currency = "EUR"
```

# Spreads

Spread contracts, also known as combos or combinations, combine two or more instruments. To define a combination contract it is required to know the conId of the [IBApi.Contract](https://interactivebrokers.github.io/tws-api/classIBApi_1_1Contract.html) in question. The conId of an instrument can easily be obtained via the [IBApi.EClientSocket.reqContractDetails](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#ade440c6db838b548594981d800ea5ca9) request.

The spread contract's symbol can be either the symbol of one of the contract legs or, for two-legged combinations the symbols of both legs separated by a comma as shown in the examples below.

## Stock Spread

Beginning with TWS v971, Stock/Stock combos will have ticker symbols in alphabetical order when they are both used in the symbol field, e.g. "AMD,IBKR".

```
contract = Contract()
contract.symbol = "IBKR,MCD"
contract.secType = "BAG"
contract.currency = "USD"
contract.exchange = "SMART"
  
leg1 = ComboLeg()
leg1.conId = 43645865#IBKR STK
leg1.ratio = 1
leg1.action = "BUY"
leg1.exchange = "SMART"
 
leg2 = ComboLeg()
leg2.conId = 9408#MCD STK
leg2.ratio = 1
leg2.action = "SELL"
leg2.exchange = "SMART"

contract.comboLegs = []
contract.comboLegs.append(leg1)
contract.comboLegs.append(leg2)
```

**Note:** EFPs are simply defined as a bag contract of stock and corresponding SSF with a ratio of 100:1.

## Options Spread

```
contract = Contract()
contract.symbol = "DBK"
contract.secType = "BAG"
contract.currency = "EUR"
contract.exchange = "EUREX"

leg1 = ComboLeg()
leg1.conId = 577164786 #DBK Jun21'24 2 CALL @EUREX
leg1.ratio = 1
leg1.action = "BUY"
leg1.exchange = "EUREX"
 
leg2 = ComboLeg()
leg2.conId = 577164767 #DBK Dec15'23 2 CALL @EUREX
leg2.ratio = 1
leg2.action = "SELL"
leg2.exchange = "EUREX"
 
contract.comboLegs = []
contract.comboLegs.append(leg1)
contract.comboLegs.append(leg2)
```



## Guaranteed Futures Spread

```
contract = Contract()
contract.symbol = "VIX"
contract.secType = "BAG"
contract.currency = "USD"
contract.exchange = "CFE"
  
leg1 = ComboLeg()
leg1.conId = 326501438 # VIX FUT 201903
leg1.ratio = 1
leg1.action = "BUY"
leg1.exchange = "CFE"

leg2 = ComboLeg()
leg2.conId = 323072528 # VIX FUT 2019049
leg2.ratio = 1
leg2.action = "SELL"
leg2.exchange = "CFE"

contract.comboLegs = []
contract.comboLegs.append(leg1)
contract.comboLegs.append(leg2)
```



## Smart-Routed Futures Spread

Futures spreads can also be defined as Smart-routed (non-guaranteed) combos. When placing an order for a non-guaranteed combo from the API, the non-guaranteed flag must be set to 1. Historical data for smart-routed futures spreads is generally available from the API with the requisite market data subscriptions.

```
contract = Contract()
contract.symbol = "WTI" # WTI,COIL spread. Symbol can be defined as first leg symbol ("WTI") or currency ("USD")
contract.secType = "BAG"
contract.currency = "USD"
contract.exchange = "SMART"
  
leg1 = ComboLeg()
leg1.conId = 55928698 # WTI future June 2017
leg1.ratio = 1
leg1.action = "BUY"
leg1.exchange = "IPE"

leg2 = ComboLeg()
leg2.conId = 55850663 # COIL future June 2017
leg2.ratio = 1
leg2.action = "SELL"
leg2.exchange = "IPE"
 
contract.comboLegs = []
contract.comboLegs.append(leg1)
contract.comboLegs.append(leg2)
```



## Inter-Commodity Futures

For Inter-Commodity futures, the 'Local Symbol' field in TWS is used for the 'Symbol' field in the API contract definition, e.g. "CL.BZ". They are always guaranteed combos, which is the default in the API.

```
contract = Contract()
contract.symbol = "COL.WTI" #symbol is 'local symbol' of intercommodity spread. 
contract.secType = "BAG"
contract.currency = "USD"
contract.exchange = "IPE"  

leg1 = ComboLeg()
leg1.conId = 183405603 #WTI�Dec'23�@IPE
leg1.ratio = 1
leg1.action = "BUY"
leg1.exchange = "IPE"
 
leg2 = ComboLeg()
leg2.conId = 254011009 #COIL�Dec'23�@IPE
leg2.ratio = 1
leg2.action = "SELL"
leg2.exchange = "IPE"
 
contract.comboLegs = []
contract.comboLegs.append(leg1)
contract.comboLegs.append(leg2)
```

