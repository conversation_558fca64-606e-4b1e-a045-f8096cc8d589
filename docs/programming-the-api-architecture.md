# Programming the API: Architecture

## EClientSocket and EWrapper Classes

We are ready to write our code once the TWS runs and actively listens for incoming connections.
This brings us to the TWS API's two major classes: the `IBApi.EWrapper` interface and the `IBApi.EClientSocket`.

## Implementing the EWrapper Interface

The `IBApi.EWrapper` interface is the mechanism through which the TWS delivers information to the API client application. By implementing this interface, the client application will be able to receive and handle the information coming from the TWS. Refer to your programming language's documentation for more details on implementing interfaces.

```python
class TestWrapper(wrapper.EWrapper):
```

## The EClientSocket Class

The class used to send messages to TWS is `IBApi.EClientSocket`. Unlike `EWrapper`, this class is not overridden as the provided functions in `EClientSocket` are invoked to send messages to TWS. To use `EClientSocket`, it may first be necessary to implement the `IBApi.EWrapper` interface as part of its constructor parameters, so the application can handle all returned messages. Messages sent from TWS as a response to function calls in `IBApi.EClientSocket` requires an EWrapper implementation to be processed to meet the needs of the API client.

Another crucial element is the `IBApi.The EReaderSignal object was passed to the `EClientSocket's constructor. Except for Python, this object is used in APIs to signal that a message is ready for processing in the queue. (In Python, the `Queue` class handles this task directly.) We will discuss this object in more detail in the E-Reader Thread section.

```python
class TestClient(EClient):
    def __init__(self, wrapper):
        EClient.__init__(self, wrapper)
```

...

```python
class TestApp(TestWrapper, TestClient):
    def __init__(self):
        TestWrapper.__init__(self)
        TestClient.__init__(self, wrapper=self)
```

> Note: The `EReaderSignal` class is not used for the `Python API`. The `Python Queue` module is used for inter-thread communication and data exchange.