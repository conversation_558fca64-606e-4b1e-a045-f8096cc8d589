# Connectivity

A socket connection between the API client application and TWS is established with the `IBApi.EClientSocket.eConnect` function. TWS acts as a server to receive requests from the API application (the client) and responds by taking appropriate actions. The first step is for the API client to connect to TWS on a socket port where T<PERSON> is already listening. It is possible to have multiple TWS instances running on the same computer if each is configured with a different API socket port number. Also, each TWS session can receive up to 32 different client applications simultaneously. The `client ID` field specified in the API connection distinguishes different API clients.

## Establishing an API connection

Once our two main objects have been created, `EWrapper` and `ESocketClient`, the client application can connect via the `IBApi.EClientSocket` object:

```python
app.connect("127.0.0.1", **args.port**, **clientId**=0)
```

`eConnect` starts by requesting from the operating system that a TCP socket be opened to the specified IP address and socket port. If the socket cannot be opened, the operating system (not TWS) returns an error, which the API client receives as error code 502 by `IBApi.EWrapper.error` (Note: since this error is not generated by TWS, it is not captured in TWS log files). Most commonly, error `502` will indicate that <PERSON><PERSON> is not running with the API enabled or is listening for connections on a different socket port. If connecting across a network, the error can also occur if a firewall or antivirus program blocks connections, or if the router's IP address is not listed in the "Trusted IPs" in TWS.

After the socket has been opened, there must be an initial handshake in which information about the highest version supported by TWS and the API is exchanged. This is important because API messages can have different lengths and fields in other versions, and a version number is necessary to interpret received messages correctly.

*   For this reason, it is important that the main EReader object is not created until after a connection has been established. The initial connection results in a negotiated common version between TWS and the API client, which the EReader thread will need to interpret subsequent messages.

After the highest version number that can be used for communication is established, TWS will return certain pieces of data that correspond specifically to the logged-in TWS user's session. This includes (1) the account number(s) accessible in this TWS session, (2) the next valid order identifier (`ID`), and (3) the time of connection. In the most common mode of operation, the `EClient.The AsyncEConnect field is set to false, and the initial handshake is completed immediately after establishing the socket connection. TWS will then immediately provide the API client with this information.

*   Important: The `IBApi.EWrapper.nextValidID` callback is commonly used to indicate that the connection is completed, and other messages can be sent from the API client to TWS. There is the possibility that TWS could drop function calls before this time.

An alternative, deprecated mode of connection is used in exceptional cases in which the variable `AsyncEconnect` is set to `true`, and the call to `startAPI` is only called from the `connectAck()` function. All IB samples use the mode `AsyncEconnect = False`.

## The EReader Thread

API programs always have at least two execution threads. One thread is used to send messages to TWS, and another is used to read returned messages. The second thread uses the API `EReader` class to read from the socket and add messages to a queue. Every time a new message is added to the message queue, a notification flag is triggered to inform other threads that a message is waiting to be processed. In the two-thread design of an API program, the message queue is also processed by the first thread. An additional thread is created to perform this task in a three-thread design. The thread responsible for the message queue will decode messages and invoke the appropriate functions in `EWrapper`. The two-threaded design is used in the IB Python sample `Program.py` and the C++ sample `TestCppClient`, while the 'Testbed' samples in the other languages use a three-threaded design. Commonly, in a Python asynchronous network application, the asyncio module will be used to create a more sequential-looking code design.

The class that has functionality for reading and parsing raw messages from TWS is the `IBApi.EReader` class.

The code below is included in Client::connect() in Python IB API, so the `EReader` thread is automatically started upon connection. There is no need for the user to start the reader.

```python
# You don't need to run this in your code!
self.reader = reader.EReader(self.conn, self.msg_queue)
self.reader.start() # start thread
```

Once the client is connected, a reader thread will be automatically created to handle incoming messages and put them into a message queue for further processing. The user is required to trigger `Client::run()` below, where the message queue is processed in an infinite loop and the `EWrapper` call-back functions are automatically triggered.

```python
app.run()
```

Now it is time to revisit the role of `IBApi.EReaderSignal` initially introduced in The `EClientSocket` Class. As mentioned in the previous paragraph, after the `EReader` thread places a message in the queue, a notification is issued to make known that a message is ready for processing. In the (C++, C#/.NET, Java) APIs, this is done via the `IBApi.EReaderSignal` object we initiated within the `IBApi.EWrapper`'s implementer. In the Python API, it is handled automatically by the `Queue` class.

The client application is now ready to work with the Trader Workstation! After the connection, the API program will receive events such as `IBApi.EWrapper.nextValidId` and `IBApi.EWrapper.managedAccounts`. In TWS (not IB Gateway), if there is an active network connection, there will also immediately be callbacks to `IBApi::EWrapper::error` with `errorId` as `-1` and `errorCode=2104,2106`, `errorMsg = "Market Data Server is ok"` to indicate there is an active connection to the IB market data server. Callbacks to `IBApi::EWrapper::error` with `errorId` as `-1` do not represent true 'errors' but only notifications that a connection has been made successfully to the IB market data farms.

By contrast, IB Gateway will not connect to market data farms until the IB client requests it. Until this time, the connection indicator in the IB Gateway GUI will show a yellow 'inactive' color rather than an 'active' green indication.

When initially making requests from an API application, it is important to verify that a response is received rather than proceeding assuming that the network connection is okay and the subscription request (portfolio updates, account information, etc.) was made successfully.

## Accepting an API connection from TWS

For security reasons, by default, the API is not configured to accept connection requests from API applications automatically. After a connection attempt, a dialogue will appear in TWS asking the user to confirm that a connection can be made manually:

![Accept incoming connection attempt?](path/to/image.jpg)

To prevent the TWS from asking the end user to accept the connection, it is possible to configure it to automatically accept the connection from a trusted IP address and/or the local machine. This can easily be done via the TWS API settings:

![TWS API Settings](path/to/image.jpg)
*(Note: Path to image is a placeholder)*

> Note: You must ensure the connection has been fully established before attempting to make any requests to the TWS. Failure to do so will result in the TWS closing the connection. Typically, this can be done by waiting for a callback from an event and the end of the initial connection handshake, such as `IBApi.EWrapper.nextValidId` or `IBApi.EWrapper.managedAccounts`.

In rare cases in which IB Gateway or TWS has a momentary delay in establishing a connection to the IB servers, messages sent immediately after receiving the `nextValidId` could be dropped and must be resent. If the API client has not received the expected callbacks from issued requests, the connection should not proceed unless it is ok.

## Broken API socket connection

If there is a problem with the socket connection between TWS and the API client, for instance, if TWS suddenly closes, this will trigger an exception in the EReader thread, which reads from the socket. This exception will also occur if an API client attempts to connect with a client ID already in use.

The socket `EOF` is handled slightly differently in different API languages. For instance, in Java, it is caught and sent to the client application to `IBApi::EWrapper::error` with `errorCode 507`: "Bad Message". In C#, it is caught and sent to `IBApi::EWrapper::error` with `errorCode -1`. The client application needs to handle this error message and use it to indicate that an exception has been thrown in the socket connection. Associated functions such as `IBApi::EWrapper::connectionClosed` and `IBApi::EClient::IsConnected` functions are not called automatically by the API code but must be handled at the API client-level\*.

*   This has been changed in API version 973.04