# IBKR MCP Server Documentation

Welcome to the Interactive Brokers Model Context Protocol Server documentation.

## Documentation Structure

### 📚 [API Documentation](./api/)
- REST API endpoints
- WebSocket API
- MCP protocol specifications
- Authentication and authorization

### 🏗️ [Architecture Documentation](./architecture/)
- System architecture overview
- Component diagrams
- Database schema
- Service interactions

### 🚀 [Deployment Documentation](./deployment/)
- Installation guides
- Configuration management
- Environment setup
- Production deployment

### 👤 [User Guide](./user-guide/)
- Getting started
- Trading workflows
- Risk management
- Troubleshooting

## Quick Links

- [Installation Guide](./deployment/installation.md)
- [API Reference](./api/reference.md)
- [Configuration Guide](./deployment/configuration.md)
- [Architecture Overview](./architecture/overview.md)

## Support

For technical support and questions:
- Check the [Troubleshooting Guide](./user-guide/troubleshooting.md)
- Review [Common Issues](./user-guide/common-issues.md)
- Submit issues via the project repository

## Contributing

See the [Contributing Guide](../CONTRIBUTING.md) for information on how to contribute to this project.
