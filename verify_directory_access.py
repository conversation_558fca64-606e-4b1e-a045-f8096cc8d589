#!/usr/bin/env python3
"""
Directory Access Verification Script
Tests Claude Desktop's access to specified folders through filesystem MCP servers
"""

import os
import json
from pathlib import Path

def test_directory_access():
    """Test access to all specified directories"""
    
    base_path = "/Users/<USER>/IBKR/b-team"
    
    # Define directories to test
    test_directories = [
        "Trading_Strategies",
        "IBKR-Reports", 
        "IBKR-Reports/FOREX",
        "IBKR-Reports/Futures"  # Note: corrected from "Future" to "Futures"
    ]
    
    print("🔍 CLAUDE DESKTOP DIRECTORY ACCESS VERIFICATION")
    print("=" * 60)
    print(f"Base Path: {base_path}")
    print("=" * 60)
    
    results = {}
    
    for directory in test_directories:
        full_path = os.path.join(base_path, directory)
        print(f"\n📁 Testing: {directory}")
        print(f"   Full path: {full_path}")
        
        # Test if directory exists
        if os.path.exists(full_path):
            print("   ✅ Directory exists")
            
            # Test if directory is readable
            if os.access(full_path, os.R_OK):
                print("   ✅ Directory is readable")
                
                # Count files in directory
                try:
                    items = os.listdir(full_path)
                    file_count = len([item for item in items if os.path.isfile(os.path.join(full_path, item))])
                    dir_count = len([item for item in items if os.path.isdir(os.path.join(full_path, item))])
                    
                    print(f"   📊 Contains: {file_count} files, {dir_count} directories")
                    
                    # List first few items as sample
                    if items:
                        sample_items = items[:3]
                        print(f"   📄 Sample items: {', '.join(sample_items)}")
                    
                    results[directory] = {
                        "status": "SUCCESS",
                        "readable": True,
                        "files": file_count,
                        "directories": dir_count,
                        "sample_items": items[:5]
                    }
                    
                except PermissionError:
                    print("   ❌ Permission denied - cannot list contents")
                    results[directory] = {
                        "status": "PERMISSION_ERROR",
                        "readable": False
                    }
                except Exception as e:
                    print(f"   ❌ Error accessing directory: {e}")
                    results[directory] = {
                        "status": "ERROR",
                        "error": str(e)
                    }
            else:
                print("   ❌ Directory is not readable")
                results[directory] = {
                    "status": "NOT_READABLE",
                    "readable": False
                }
        else:
            print("   ❌ Directory does not exist")
            results[directory] = {
                "status": "NOT_FOUND",
                "readable": False
            }
    
    # Test configuration file
    print("\n" + "=" * 60)
    print("🔧 CLAUDE DESKTOP CONFIGURATION VERIFICATION")
    print("=" * 60)
    
    config_file = os.path.join(base_path, "claude_desktop_config.json")
    print(f"📝 Config file: {config_file}")
    
    if os.path.exists(config_file):
        print("   ✅ Configuration file exists")
        
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            print("   ✅ Configuration file is valid JSON")
            
            # Check MCP servers
            if 'mcpServers' in config:
                print(f"   📊 Found {len(config['mcpServers'])} MCP servers configured")
                
                # Check filesystem servers
                filesystem_servers = [
                    name for name, server in config['mcpServers'].items() 
                    if 'filesystem' in name.lower()
                ]
                
                print(f"   📁 Filesystem servers: {len(filesystem_servers)}")
                for server_name in filesystem_servers:
                    server_config = config['mcpServers'][server_name]
                    if 'args' in server_config and len(server_config['args']) > 2:
                        path = server_config['args'][-1]  # Last argument is usually the path
                        print(f"      - {server_name}: {path}")
                
                results['config_verification'] = {
                    "status": "SUCCESS",
                    "total_servers": len(config['mcpServers']),
                    "filesystem_servers": len(filesystem_servers),
                    "filesystem_paths": {
                        name: config['mcpServers'][name]['args'][-1] 
                        for name in filesystem_servers
                        if 'args' in config['mcpServers'][name] and len(config['mcpServers'][name]['args']) > 2
                    }
                }
            else:
                print("   ❌ No mcpServers section found in configuration")
                results['config_verification'] = {
                    "status": "ERROR",
                    "error": "No mcpServers section found"
                }
                
        except json.JSONDecodeError as e:
            print(f"   ❌ Configuration file is not valid JSON: {e}")
            results['config_verification'] = {
                "status": "JSON_ERROR",
                "error": str(e)
            }
        except Exception as e:
            print(f"   ❌ Error reading configuration file: {e}")
            results['config_verification'] = {
                "status": "READ_ERROR",
                "error": str(e)
            }
    else:
        print("   ❌ Configuration file does not exist")
        results['config_verification'] = {
            "status": "NOT_FOUND"
        }
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    successful_dirs = sum(1 for result in results.values() if isinstance(result, dict) and result.get('status') == 'SUCCESS')
    total_dirs = len(test_directories)
    
    print(f"✅ Successful directory access: {successful_dirs}/{total_dirs}")
    
    if results.get('config_verification', {}).get('status') == 'SUCCESS':
        print("✅ Configuration file is valid and properly structured")
    else:
        print("❌ Configuration file has issues")
    
    # Final recommendation  
    print("\n🎯 RECOMMENDATIONS:")
    if successful_dirs == total_dirs:
        print("✅ All directories are accessible - Claude Desktop should work properly")
    else:
        print("❌ Some directories are not accessible - review file permissions")
    
    if results.get('config_verification', {}).get('status') == 'SUCCESS':
        print("✅ Configuration is properly set up")
    else:
        print("❌ Configuration needs attention")
    
    return results

if __name__ == "__main__":
    results = test_directory_access()
    
    # Save results to file
    results_file = "directory_access_verification_results.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Detailed results saved to: {results_file}")
