# Python virtual environment
.venv_ibkr/
.venv/
venv/
ENV/
env/
ibkr-mcp-server/venv/
ibkr-mcp-server/.venv_ibkr/

# Python bytecode
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Jupyter Notebook
.ipynb_checkpoints

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*~
.spyderproject
.spyproject
.ropeproject

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs and databases
*.log
*.sql
*.sqlite
*.db

# Environment variables - exclude ALL .env files
.env*
!.env.example

# Credential and secret files
*credentials*.json
*service-account*.json
*key*.json
*secret*.json
*config*.json
*.pem
*.key
*.crt
*.p12
*.pfx
*password*
*token*
*secret*
.secrets/
credentials/

# Project specific - Interactive Brokers
tws.xml
*.jar
.jts/

# Temporary files
*.bak
*.tmp
.pytest_cache/

# Node.js dependencies (for frontend)
node_modules/
frontend/node_modules/
*/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.tgz
.yarn-integrity

# Project specific directories
reports/
tests/
logs/
temp/
cache/
.coverage
htmlcov/

# Node.js dependencies (for frontend)
frontend/node_modules/
frontend/build/
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local

# Package manager files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity