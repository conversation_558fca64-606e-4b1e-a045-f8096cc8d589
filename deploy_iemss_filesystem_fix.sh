#!/bin/bash

# 🔧 IEMSS Filesystem MCP Server Fix Deployment Script
# Adds dedicated filesystem servers for IEMSS FOREX directories

set -e  # Exit on any error

echo "🔧 Deploying IEMSS Filesystem MCP Server Fix..."
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if <PERSON> is running
check_claude_running() {
    if pgrep -f "<PERSON>" > /dev/null; then
        print_warning "<PERSON> is currently running"
        echo "Please quit Claude Desktop (Cmd+Q) before continuing."
        echo "Press Enter when <PERSON> Desktop is closed..."
        read
    else
        print_success "Claude Desktop is not running - good to proceed"
    fi
}

# Verify IEMSS directories exist
verify_directories() {
    print_status "Verifying IEMSS FOREX directories exist..."
    
    DIR1="/Users/<USER>/IBKR/b-team/IBKR-Reports/FOREX/IEMSS Elite FOREX 5-min Scalping"
    DIR2="/Users/<USER>/IBKR/b-team/IBKR-Reports/FOREX/IEMSS Elite FOREX Scalping"
    
    if [ -d "$DIR1" ]; then
        print_success "✅ IEMSS Elite FOREX 5-min Scalping directory exists"
    else
        print_error "❌ IEMSS Elite FOREX 5-min Scalping directory not found: $DIR1"
        exit 1
    fi
    
    if [ -d "$DIR2" ]; then
        print_success "✅ IEMSS Elite FOREX Scalping directory exists"
    else
        print_error "❌ IEMSS Elite FOREX Scalping directory not found: $DIR2"
        exit 1
    fi
}

# Create backup
create_backup() {
    print_status "Creating backup of current configuration..."
    
    BACKUP_FILE="/Users/<USER>/IBKR/b-team/claude_config_backup_$(date +%Y%m%d_%H%M%S).json"
    
    if [ -f "/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json" ]; then
        cp "/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json" "$BACKUP_FILE"
        print_success "Backup created: $BACKUP_FILE"
    else
        print_warning "No existing configuration found in Application Support"
    fi
}

# Deploy configuration
deploy_config() {
    print_status "Deploying IEMSS filesystem fix configuration..."
    
    CONFIG_FILE="/Users/<USER>/IBKR/b-team/claude_desktop_config_iemss_fixed.json"
    
    if [ ! -f "$CONFIG_FILE" ]; then
        print_error "Configuration file not found: $CONFIG_FILE"
        exit 1
    fi
    
    # Deploy to Application Support (primary location)
    cp "$CONFIG_FILE" "/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json"
    print_success "Configuration deployed to Application Support"
    
    # Deploy to ~/.config (secondary location)
    mkdir -p ~/.config/claude_desktop
    cp "$CONFIG_FILE" ~/.config/claude_desktop/config.json
    print_success "Configuration deployed to ~/.config"
}

# Verify deployment
verify_deployment() {
    print_status "Verifying deployment..."
    
    CONFIG_FILE="/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json"
    
    if command -v jq &> /dev/null; then
        # Check if both IEMSS servers are present
        IEMSS_5MIN=$(jq -r '.mcpServers."filesystem-iemss-forex-5min".args[2]' "$CONFIG_FILE")
        IEMSS_SCALPING=$(jq -r '.mcpServers."filesystem-iemss-forex-scalping".args[2]' "$CONFIG_FILE")
        
        if [[ "$IEMSS_5MIN" == *"5-min Scalping"* ]]; then
            print_success "✅ filesystem-iemss-forex-5min server configured correctly"
        else
            print_error "❌ filesystem-iemss-forex-5min server configuration error"
        fi
        
        if [[ "$IEMSS_SCALPING" == *"FOREX Scalping"* ]]; then
            print_success "✅ filesystem-iemss-forex-scalping server configured correctly"
        else
            print_error "❌ filesystem-iemss-forex-scalping server configuration error"
        fi
        
        # Count total servers
        SERVER_COUNT=$(jq '.mcpServers | keys | length' "$CONFIG_FILE")
        print_success "Total MCP servers configured: $SERVER_COUNT"
        
    else
        print_warning "jq not available - skipping detailed verification"
    fi
}

# Main function
main() {
    echo "This script will:"
    echo "1. Add dedicated filesystem MCP servers for IEMSS FOREX directories"
    echo "2. Fix the path issues that prevent Claude Desktop access"
    echo "3. Deploy the updated configuration"
    echo ""
    echo "New servers being added:"
    echo "- filesystem-iemss-forex-5min (5-min Scalping directory)"
    echo "- filesystem-iemss-forex-scalping (Scalping directory)"
    echo ""
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Deployment cancelled by user"
        exit 0
    fi
    
    echo ""
    print_status "Starting IEMSS filesystem fix deployment..."
    
    # Step 1: Check if Claude Desktop is running
    check_claude_running
    
    # Step 2: Verify directories exist
    verify_directories
    
    # Step 3: Create backup
    create_backup
    
    # Step 4: Deploy configuration
    deploy_config
    
    # Step 5: Verify deployment
    verify_deployment
    
    echo ""
    echo "================================================"
    print_success "IEMSS Filesystem MCP Server Fix deployed successfully!"
    echo ""
    echo "✅ Added filesystem servers:"
    echo "   - filesystem-iemss-forex-5min"
    echo "   - filesystem-iemss-forex-scalping"
    echo ""
    echo "📁 Directories now accessible:"
    echo "   - /Users/<USER>/IBKR/b-team/IBKR-Reports/FOREX/IEMSS Elite FOREX 5-min Scalping/"
    echo "   - /Users/<USER>/IBKR/b-team/IBKR-Reports/FOREX/IEMSS Elite FOREX Scalping/"
    echo ""
    echo "🚀 Next steps:"
    echo "1. Start Claude Desktop"
    echo "2. The IEMSS FOREX directories should now be accessible"
    echo "3. Test access to both directories through Claude Desktop"
    echo ""
    print_success "Deployment complete! 🎉"
}

# Run main function
main "$@"
